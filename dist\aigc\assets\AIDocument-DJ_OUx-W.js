import{k as P,A as p,aq as C,V as $,f as i,w as n,E as M,o,b as f,m as h,a as r,c as d,d as Q,s as _,e as W,F as g,W as Y,Y as Z,p as z,a9 as H,j as E,Z as K,h as X,ar as ee,Q as F,_ as te}from"./index-BBeD0eDz.js";/* empty css                  *//* empty css               *//* empty css                 */import{_ as le}from"./input-delete-BxT9zbwn.js";import{P as ae}from"./ProductSelector-BTeBhjvS.js";import{A as oe}from"./AddBtn-KywrLb53.js";import{i as se}from"./aiVideo-B86MWzMI.js";const ne={class:"flex-1 overflow-y-auto"},re={class:"input-wrap w-full"},ue=["onClick"],ie={key:0,class:"flex"};const de={class:"switch-item"},ce={class:"checkbox-wrapper"},me=P({__name:"AIDocument",props:{initialData:{type:Object,default:()=>({})},showGenerateButton:{type:Boolean,default:!0},postLoading:{type:Boolean,default:!1},maxLength:{type:Number,default:5},showAutoSubtitle:{type:Boolean,default:!0}},emits:["generate","change"],setup(c,{expose:S,emit:D}){const k=D,L=H("theme",1),x=c,m=p(),b=p(null),B=p([]),w=p([]),N=p([]);(async()=>{try{const e=await se({});console.log(e,"内容字数及时长,  文案语气,视频风格类型");const{duration:l,tone:u,videostyle:V}=e;B.value=l,w.value=u,N.value=V}catch(e){console.error("获取内容字数及时长,  文案语气,视频风格类型失败",e)}})();const t=C({product_id:"",ativity:"",theme:[""],duration:"",tone:"",videostyle:"",num:1,subtitle:1}),O=C({product_id:[{required:!0,message:"请选择产品",trigger:"change"}],theme:[{required:!0,message:"请填写视频主题",trigger:"change"},{type:"array",min:1,message:"请至少填写一个视频主题",trigger:"change"}],duration:[{required:!0,message:"请选择内容字数长度",trigger:"change"}],tone:[{required:!0,message:"请选择文案语气",trigger:"change"}],videostyle:[{required:!0,message:"请选择视频风格",trigger:"change"}],num:[{required:!0,message:"请设置生成条数",trigger:"change"}]}),T=e=>{console.log("已选择的产品:",e),t.product_id=e},U=()=>{t.theme.push("")},q=e=>{t.theme.splice(e,1)};$(()=>t,e=>{let l=JSON.parse(JSON.stringify(e));l.theme=JSON.stringify(l.theme),k("change",l)},{deep:!0});const I=async()=>{const e=await j();console.log("result",e),e.valid&&(console.log("表单数据:",e.data),k("generate",e.data))},j=async()=>{if(!m.value)return{valid:!1,data:null};try{if(await m.value.validate(),t.product_id){if(!t.theme.length||t.theme.some(e=>!e.trim())||t.theme.some(e=>e.trim().length>300)||t.theme.length>x.maxLength)return F.error("请填写视频主题，每个主题不超过300个字，最多可添加"+x.maxLength+"个主题"),{valid:!1,data:null}}else return F.error("请选择产品"),{valid:!1,data:null};return{valid:!0,data:t}}catch{return{valid:!1,data:null}}};return S({resetForm:()=>{m.value&&(m.value.resetFields(),b.value&&b.value.resetTags())},setFormData:e=>{e&&(e.videostyle||(e.videostyle=""),e.tone||(e.tone=""),Object.assign(t,e))}}),(e,l)=>{const u=Q,V=W,v=Z,y=Y,_e=ee,R=K,G=X,J=M;return o(),i(J,{ref_key:"formRef",ref:m,model:t,rules:O,"label-position":"top",class:"ai-form h-full flex flex-col"},{default:n(()=>[f("div",ne,[r(u,{label:"选择产品：",prop:"product_id"},{default:n(()=>[r(ae,{class:"w-full",ref_key:"productSelectorRef",ref:b,selectId:t.product_id,onChange:T,showLabel:!1},null,8,["selectId"])]),_:1}),r(u,{label:"视频主题：",prop:"theme"},{default:n(()=>[t.theme.length<c.maxLength?(o(),i(oe,{key:0,class:"add-btn",onClick:U})):h("",!0),(o(!0),d(g,null,_(t.theme,(a,s)=>(o(),d("div",re,[r(V,{type:"textarea",modelValue:t.theme[s],"onUpdate:modelValue":A=>t.theme[s]=A,placeholder:`请输入视频主题，可参考如下格式：\r
新店活动，例如：进店即有好礼相送\r
产品推广，例如：**元包邮\r
促销活动，例如：买三送一，低到9块9\r
解决方案，例如：3分钟解决烦恼，懒人必备神器\r
知识科普，例如：1分钟学会小技巧/***避坑指南\r
案例展示，例如：真实买家秀大赏，**案例分享`,rows:8,maxlength:"300","show-word-limit":""},null,8,["modelValue","onUpdate:modelValue"]),t.theme.length>1?(o(),d("img",{key:0,class:"delete-icon w-[16px]",src:le,alt:"",onClick:A=>q(s)},null,8,ue)):h("",!0)]))),256))]),_:1}),r(u,{label:"内容字数长度：",prop:"duration"},{default:n(()=>[r(y,{modelValue:t.duration,"onUpdate:modelValue":l[0]||(l[0]=a=>t.duration=a),class:"w-full"},{default:n(()=>[(o(!0),d(g,null,_(B.value,(a,s)=>(o(),i(v,{key:s,label:a,value:s},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),z(L)===1?(o(),d("div",ie,[r(u,{label:"文案语气：",class:"flex-auto",prop:"tone"},{default:n(()=>[r(y,{modelValue:t.tone,"onUpdate:modelValue":l[1]||(l[1]=a=>t.tone=a),class:"w-full"},{default:n(()=>[(o(!0),d(g,null,_(w.value,(a,s)=>(o(),i(v,{key:s,label:a,value:Number(s)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(u,{label:"视频风格：",class:"flex-auto ml-4",prop:"videostyle"},{default:n(()=>[r(y,{modelValue:t.videostyle,"onUpdate:modelValue":l[2]||(l[2]=a=>t.videostyle=a),class:"w-full"},{default:n(()=>[(o(!0),d(g,null,_(N.value,(a,s)=>(o(),i(v,{key:s,label:a,value:Number(s)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])):(o(),i(u,{key:1,label:"文案风格：",class:"flex-auto",prop:"videostyle"},{default:n(()=>[r(y,{modelValue:t.videostyle,"onUpdate:modelValue":l[3]||(l[3]=a=>t.videostyle=a),class:"w-full"},{default:n(()=>[(o(!0),d(g,null,_(w.value,(a,s)=>(o(),i(v,{key:s,label:a,value:Number(s)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})),h("",!0),c.showAutoSubtitle?(o(),i(u,{key:3,prop:"subtitle"},{default:n(()=>[f("div",de,[l[9]||(l[9]=f("span",{class:"switch-label"},"自动字幕：",-1)),f("div",ce,[r(R,{modelValue:t.subtitle,"onUpdate:modelValue":l[5]||(l[5]=a=>t.subtitle=a),"true-value":1,"false-value":0},{default:n(()=>l[8]||(l[8]=[E("有字幕")])),_:1},8,["modelValue"])]),l[10]||(l[10]=f("span",{class:"switch-desc"},"勾选后自动生成字幕",-1))])]),_:1})):h("",!0)]),c.showGenerateButton?(o(),i(u,{key:0,class:"pt-[10px]"},{default:n(()=>[r(G,{loading:c.postLoading,type:"primary",class:"generate-btn",onClick:I},{default:n(()=>l[11]||(l[11]=[E("开始生成")])),_:1},8,["loading"])]),_:1})):h("",!0)]),_:1},8,["model","rules"])}}}),Be=te(me,[["__scopeId","data-v-f6b199ea"]]);export{Be as A};
