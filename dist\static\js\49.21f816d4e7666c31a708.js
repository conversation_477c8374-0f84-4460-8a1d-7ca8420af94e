webpackJsonp([49],{Gspt:function(t,e,a){"use strict";e.g=function(t){return Object(n.a)({url:"/api/withdrawal/getDefault",method:"POST",data:t})},e.b=function(t){return Object(n.a)({url:"/api/withdrawal/account/list",method:"POST",data:t})},e.d=function(t){return Object(n.a)({url:"/api/withdrawal/account/store",method:"POST",data:t})},e.a=function(t){return Object(n.a)({url:"/api/withdrawal/account/delete",method:"POST",data:t})},e.c=function(t){return Object(n.a)({url:"/api/withdrawal/account/set_default",method:"POST",data:t})},e.e=function(t){return Object(n.a)({url:"/api/withdrawal/apply",method:"POST",data:t})},e.h=function(t){return Object(n.a)({url:"/api/withdrawal/history",method:"POST",data:t})},e.f=function(t){return Object(n.a)({url:"/api/withdrawal/cancel",method:"POST",data:t})};var n=a("vLgD")},XUJZ:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("Gspt"),o={name:"withdrawal-account-add",props:{dialogVisible:{required:!1,type:Boolean,default:function(){return!1}}},data:function(){return{formData:{},tableLoading:!1,rules:{type:[{required:!0,message:"请选择账号类型",trigger:"blur"}],name:[{required:!0,message:"请输入收款账户名",trigger:"blur"}],bank_name:[{required:!0,message:"开户行名称不能为空",trigger:"blur"}],bank_number:[{required:!0,message:"收款账号不能为空",trigger:"blur"}],is_default:[{required:!1,message:"请选择是否设为默认账号",trigger:"blur"}]}}},created:function(){this.formData.type=1},mounted:function(){this.formData.type=1},methods:{setFormData:function(t){this.formData=t},store:function(){var t=this;this.$refs.dataForm.validate(function(e){if(e){t.tableLoading=!0;var a={type:t.formData.type,name:t.formData.name,bank_name:t.formData.bank_name,bank_number:t.formData.bank_number,is_default:t.formData.is_default};t.formData.id&&(a.id=t.formData.id),Object(n.d)(a).then(function(e){t.tableLoading=!1,t.$emit("getInfoList"),t.close()}).catch(function(){t.tableLoading=!1})}})},close:function(){this.$emit("closepop")}}},i={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{title:t.formData.id?"修改账号信息":"新增账号信息",visible:t.dialogVisible,width:"700px","before-close":t.close},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-card",{staticClass:"box-card",attrs:{"v-loading":t.tableLoading}},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],ref:"dataForm",staticClass:"demo-form-inline",attrs:{inline:!0,model:t.formData,rules:t.rules,size:"small"}},[a("div",{staticClass:"container"},[a("el-form-item",{attrs:{label:"账号类型:","label-width":"100px",prop:"type"}},[a("el-radio",{attrs:{label:1},model:{value:t.formData.type,callback:function(e){t.$set(t.formData,"type",e)},expression:"formData.type"}},[t._v("个人")]),t._v(" "),a("el-radio",{attrs:{label:2},model:{value:t.formData.type,callback:function(e){t.$set(t.formData,"type",e)},expression:"formData.type"}},[t._v("企业")])],1),t._v(" "),a("el-form-item",{attrs:{label:"收款账户名:",prop:"name","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"收款账户名"},model:{value:t.formData.name,callback:function(e){t.$set(t.formData,"name",e)},expression:"formData.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"开户行名称:",prop:"bank_name","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入开户行名称"},model:{value:t.formData.bank_name,callback:function(e){t.$set(t.formData,"bank_name",e)},expression:"formData.bank_name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"收款账号:",prop:"bank_number","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入收款账号"},model:{value:t.formData.bank_number,callback:function(e){t.$set(t.formData,"bank_number",e)},expression:"formData.bank_number"}})],1),t._v(" "),a("el-form-item",{staticStyle:{display:"block"},attrs:{label:"",prop:"is_default","label-width":"100px"}},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.formData.is_default,callback:function(e){t.$set(t.formData,"is_default",e)},expression:"formData.is_default"}},[t._v("设为默认账号")])],1)],1),t._v(" "),a("div",{staticClass:"view-btn"},[a("el-button",{attrs:{type:"primary"},on:{click:t.store}},[t._v(t._s(t.formData.id?"修改":"新增"))]),t._v(" "),a("el-button",{attrs:{type:"warning"},on:{click:t.close}},[t._v("取消")])],1)])],1)],1)},staticRenderFns:[]};var r={name:"applyInvoice",components:{withdrawalAccountAdd:a("VU/8")(o,i,!1,function(t){a("krWD")},"data-v-25f97089",null).exports},data:function(){return{routeQuery:this.$G7.decrypt(this.$route.query.info),tableLoading:!1,addressLoading:!1,submitLoading:!1,formQuery:{},tableData:[],addressData:[],invoiceAddVisible:!1,amount:0,accountForm:{amount:""}}},created:function(){null!==this.routeQuery&&(this.amount=this.routeQuery.amount),this.getInfoList()},methods:{getInfoList:function(){var t=this;this.tableLoading=!0,Object(n.b)({}).then(function(e){t.tableLoading=!1,t.tableData=e.data}).catch(function(){t.tableLoading=!1})},setAccountDefault:function(t){var e=this;this.$confirm("设置该账号为默认, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(n.c)({id:t.id}).then(function(t){e.getInfoList()}),e.$message.success("设置成功")}).catch(function(){})},infoAdd:function(){this.$refs.info.setFormData({}),this.invoiceAddVisible=!0},infoEdit:function(t){this.$refs.info.setFormData(t),this.invoiceAddVisible=!0},accountDestory:function(t){var e=this;this.$confirm("此操作将永久删除该账号, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(n.a)({id:t.id}).then(function(t){e.getInfoList()}),e.$message({type:"success",message:"删除成功!"})}).catch(function(){})},handleSelectionChange:function(){},submit:function(){var t=this;return this.accountForm.amount?this.accountForm.account_id?void this.$refs.accountForm.validate(function(e){if(!e)return!1;t.submitLoading=!0,Object(n.e)(t.accountForm).then(function(e){t.submitLoading=!1,t.$message.success(e.message),t.$router.go(-1)}).catch(function(){t.submitLoading=!1})}):this.$message.error("请选择一个银行账号"):this.$message.error("请输入提现金额")},cancel:function(){this.$router.go(-1)}}},l={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-card",{staticClass:"el-box"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("申请提现")])]),t._v(" "),a("el-container",[a("el-main",[a("div",{staticStyle:{"margin-bottom":"30px"}},[a("p",{staticStyle:{color:"red",display:"inline-block"}},[t._v("1. 请仔细核对提现金额和银行账号信息，一经提交无法修改和删除")])]),t._v(" "),a("el-form",{ref:"accountForm",staticClass:"demo-ruleForm",attrs:{model:t.accountForm,"label-width":"100px","label-position":"top","hide-required-asterisk":"true"}},[a("div",{staticClass:"header-invoice"},[a("el-form-item",{attrs:{label:"提现金额",prop:"amount",rules:[{required:!0,message:"金额不能为空"},{type:"number",message:"金额必须为数字值"}]}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入提现金额"},model:{value:t.accountForm.amount,callback:function(e){t.$set(t.accountForm,"amount",t._n(e))},expression:"accountForm.amount"}})],1)],1),t._v(" "),a("div",{staticClass:"header-invoice"},[a("h1",[t._v("提现账号信息")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:t.infoAdd}},[t._v("新增提现账号信息")])],1),t._v(" "),a("div",{staticClass:"table",staticStyle:{"margin-top":"20px"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:""},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{width:"55"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.row.id},model:{value:t.accountForm.account_id,callback:function(e){t.$set(t.accountForm,"account_id",e)},expression:"accountForm.account_id"}},[t._v(" ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"name",label:"账户名"}}),t._v(" "),a("el-table-column",{attrs:{label:"账号类型"},scopedSlots:t._u([{key:"default",fn:function(e){return[1===e.row.type?a("span",[t._v("个人")]):t._e(),t._v(" "),2===e.row.type?a("span",[t._v("企业")]):t._e()]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"bank_name",label:"开户行"}}),t._v(" "),a("el-table-column",{attrs:{prop:"name",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[1!==e.row.is_default?a("span",[a("el-button",{staticStyle:{color:"#ff4d51"},attrs:{type:"text"},on:{click:function(a){return t.setAccountDefault(e.row)}}},[t._v("设为默认")]),t._v(" "),a("el-divider",{attrs:{direction:"vertical"}})],1):t._e(),t._v(" "),a("el-button",{staticStyle:{color:"#3a8ee6"},attrs:{type:"text"},on:{click:function(a){return t.infoEdit(e.row)}}},[t._v("修改")]),t._v(" "),a("el-divider",{attrs:{direction:"vertical"}}),t._v(" "),a("el-button",{staticStyle:{color:"#ef3420"},attrs:{type:"text"},on:{click:function(a){return t.accountDestory(e.row)}}},[t._v("删除")])]}}])})],1)],1),t._v(" "),a("div",{staticClass:"footer"},[a("p"),t._v(" "),a("p"),t._v(" "),a("div",[a("el-button",{attrs:{type:"primary",loading:t.submitLoading},on:{click:function(e){return t.submit()}}},[t._v("下一步")]),t._v(" "),a("el-button",{attrs:{type:"warning"},on:{click:t.cancel}},[t._v("取消")])],1)])])],1)],1)],1),t._v(" "),a("withdrawalAccountAdd",{ref:"info",attrs:{dialogVisible:t.invoiceAddVisible},on:{getInfoList:function(e){return t.getInfoList()},closepop:function(e){t.invoiceAddVisible=!1}}})],1)},staticRenderFns:[]};var s=a("VU/8")(r,l,!1,function(t){a("ZTyU")},"data-v-760ae962",null);e.default=s.exports},ZTyU:function(t,e){},krWD:function(t,e){}});