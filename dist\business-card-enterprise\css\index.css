* {
    margin: 0;
    padding: 0;
    font-family: PingFang SC;
    font-weight: 500;
    color: #222222;
}

body {
    width: 100vw;
    overflow-x: hidden;
}

:root {
    --main-color: #B5885E;
}

.phone {
    display: none;
}

.goBack {
    position: fixed;
    right: 20px;
    bottom: 30px;
    z-index: 99;
}

.clear-float::after {
    content: "";
    display: block;
    clear: both;
}

.intro-text {
    color: #666;
}

.banner {
    width: 100%;
    height: 40vw;
    background: url("../img/bg1.jpg") no-repeat #eeedf8;
    padding-top: 6.46vw;
    box-sizing: border-box;
    background-size: 100% 100%;
}

.container {
    width: calc(100% - 15.625% - 15.625%);
    margin: 0 15.625% 0 15.625%;
}

.banner h1 {
    line-height: 1.5;
    font-weight: 800;
    font-size: 2.6vw;
    color: #222222;
    background: linear-gradient(0deg, #F4C98B 0%, #F6F1E7 50%, #F4C98B 100%);

    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.banner h2 {
    font-size: 1.67vw;
    margin-top: 1.61vw;
    color: #222222;
}

.button {
    width: 10vw;
    height: 3.28vw;
    line-height: 3.28vw;
    border-radius: 1.64vw;
    font-size: 1.04vw;
    display: inline-block;
    text-align: center;
}

.default-button {
    color: #8F643C;
    border: 1px solid #8F643C;
}

.primary-button {
    color: #fff;
    background: var(--main-color);
}

.banner .button {
    margin-top: 3.3vw;
    background: #FFFFFF;
    color: #070404;
}

.banner .version-selector {
    width: 8.85vw;
    height: 2.08vw;
    line-height: 2.08vw;
    border-radius: 1.04vw;
    text-align: center;
    font-size: 1.04vw;
    margin-bottom: 1.56vw;
    border-color: #F4D09C;
    color: #F4D09C;
}

.banner .right {
    width: 21.77vw;
    height: auto;
    position: relative;
    top: -3.6vw;
}

.radio-left-top {
    border-radius: 12.5vw 0 0 0;
}

.radio-right-top {
    border-radius: 0 12.5vw 0 0;
}

.what-we-can-do {
    background: #FFFFFF;
    padding: 6.82vw 0 5.52vw;
    margin-top: -12.3vw;
}

.title {
    font-size: 2.08vw;
    font-weight: 800;
    line-height: 1.5;
}

.title-white {
    color: #fff;
}

.title-blue {
    color: #111111;
}

.title-en {
    text-transform: uppercase;
    margin-top: .9vw;
    font-size: 1.04vw;
    font-weight: 800;
}

.left {
    float: left;
}

.right {
    float: right;
}

.what-we-can-do .left {
    width: 58%;
}

.what-we-can-do .right {
    margin-top: 2.5vw;
}

.what-we-can-do .advance {
    font-size: 1.56vw;
    font-family: PingFang SC;
    font-weight: 400;
    margin-top: 4.375vw;
}

.what-we-can-do .advance+.advance {
    margin-top: .83vw;
}

.what-we-can-do .text {
    font-size: .83vw;
    font-weight: 400;
    margin-top: 1.93vw;
    line-height: 1.5;
}

.what-we-can-do .button {
    margin-top: 2.4vw;
}

.circle {
    background: var(--main-color);
    border-radius: 50%;
    position: relative;
    z-index: 2;
}

.what-we-can-do .circle-container p {
    color: #FFFFFF;
    position: relative;
    z-index: 4;
}

.what-we-can-do .circle-container p:first-child {
    font-size: .83vw;
    font-weight: 400;
}

.what-we-can-do .circle-container p.large {
    font-size: 1.56vw;
    font-weight: 800;
    margin-top: 1.04vw;
    width: max-content;
}

.circle::before {
    content: "";
    display: block;
    position: absolute;
    background: #ffda97;
    border-radius: 50%;
    z-index: 2;
}

.what-we-can-do .circle-container {
    position: relative;
    width: 18.85vw;
    height: 18.91vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 2.5vw;
    box-sizing: border-box;
    /* align-items: center; */
}

.what-we-can-do .circle-container .circle-main {
    width: 18.85vw;
    height: 18.91vw;
    background: var(--main-color);
    border-radius: 50%;
    opacity: 0.9;
    position: absolute;
    z-index: 2;
    left: 0;
    top: 0;
}

.what-we-can-do .circle-container .circle-one {
    display: block;
    position: absolute;
    background: #ffda97;
    border-radius: 50%;
    z-index: 3;
    top: 0;
    left: -1.61vw;
    width: 6vw;
    height: 6vw;
}

.what-we-can-do .circle-container .circle-two {
    display: block;
    position: absolute;
    background: #ffda97;
    border-radius: 50%;
    z-index: 1;
    top: 2.19vw;
    right: -4.11vw;
    width: 14.47vw;
    height: 14.49vw;
}

.save-to-address-book {
    padding: 8.49vw 0 8.42vw;
    background: #FAFAFA;
    background-size: 100% 100%;
    position: relative;
}

.save-to-address-book .container {
    display: flex;
    align-items: center;
}

.save-to-address-book .circle {
    width: 8.54vw;
    height: 8.54vw;
    border-radius: 50%;
    margin-top: 6.2vw;
}

.save-to-address-book .circle::before {
    width: 4.06vw;
    height: 4.06vw;
    right: -2.29vw;
}

.save-to-address-book .img-box {
    width: 41.2vw;
    height: 22.55vw;
    height: auto;
}

.save-to-address-book .price-view {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 4vw;
}

.save-to-address-book .price-view .t-icon {
    width: 2.19vw;
    margin-bottom: 1.5vw;
}

.save-to-address-book .price-view .title {
    font-weight: 800;
    font-size: 2.08vw;
    color: #111111;
    line-height: 1;
    margin-bottom: 2.71vw;
}

.save-to-address-book .price-view .tips li {
    margin-bottom: 0.94vw;
    font-size: 0.94vw;
    color: #111111;
    position: relative;
    padding-left: 1.31vw;
}

.save-to-address-book .price-view .tips li::before {
    content: "";
    display: block;
    position: absolute;
    width: 0.31vw;
    height: 0.31vw;
    background: #BC946E;
    border-radius: 50%;
    left: 0vw;
    top: 0.5vw;
}

.core-functions {
    background: var(--main-color);
    margin-top: -13.39vw;
    padding: 6.41vw 0 17.92vw;
    position: relative;
}

li {
    list-style: none;
}

.core-functions .tab-box {
    margin: 3.75vw auto 0;
    width: 100%;
}

.core-functions .tab-wrap {
    width: 100%;
    overflow: auto;
}

.core-functions .tab {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    justify-content: space-between;
}

.core-functions .tab li {
    text-align: center;
    padding-bottom: 1.56vw;
    position: relative;
    cursor: pointer;
}

.core-functions .tab li+li {
    margin-left: 3vw;
}

.core-functions .tab li p {
    color: #fff;
    font-size: 1vw;
    font-weight: 500;
    margin-top: 1.2vw;
    white-space: nowrap;
}

.core-functions .tab li::after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 1.93vw;
    height: 3px;
    background: #FFFFFF;
    left: 0;
    right: 0;
    margin: auto;
    display: none;
}

.core-functions .tab li.active::after {
    display: block;
}

.core-functions .tab-panel {
    padding-top: 6.77vw;
    overflow: hidden;
}

.core-functions .tab-panel li {
    text-align: center;
    display: none;
}

.core-functions .tab-panel li.active {
    display: block;
}

.core-functions .tab-panel li img {
    position: relative;
}

.core-functions .tab-panel li img:nth-child(1) {
    z-index: 1;
    width: 20vw;
}

.core-functions .tab-panel li img:nth-child(2) {
    margin-top: -5.6vw;
    width: 58vw;
}

.advantage {
    background: #F3F3F3;
    padding: 8.02vw 0 4.25vw;
    margin-top: -12.45vw;
    position: relative;
    z-index: 1;
}

.advantage ul {
    margin-top: 5.16vw;
}

.advantage li {
    float: left;
    width: calc(100% / 5 - 2.6vw + 2.6vw / 5);
    background-color: #fff;
    text-align: center;
    padding: 3vw 1.6vw 6vw;
    border-radius: 3.125vw;
    margin-right: 2.6vw;
    box-sizing: border-box;
    position: relative;
    margin-bottom: 2vw;
    height: 20.83vw;
}

.advantage li:last-child {
    margin-right: 0;
}

.advantage .title-advantage {
    font-size: 1.2vw;
    font-weight: 500;
    color: #000000;
    margin-top: 1.8vw;
}

.advantage .line {
    width: 1.3vw;
    height: 2px;
    background: #222222;
    margin: 1.4vw auto 0;
}

.advantage .intro-text {
    margin-top: 1.6vw;
    line-height: 1.5;
    font-size: .83vw;
}

.advantage a {
    display: inline-block;
    font-size: 0.73vw;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 2.29vw;
}

.concat-us {
    background: url("../img/bg3.png") no-repeat;
    background-size: cover;
    padding: 4.01vw 0 2.66vw;
    text-align: center;
}

.concat-us .form-title {
    font-size: 1.15vw;
    font-weight: 500;
    color: #FFFFFF;
}

.concat-us .form-en-title {
    font-size: 0.63vw;
    font-weight: 500;
    color: #FFFFFF;
    margin-top: 0.63vw;
}

.concat-us .form {
    width: 42.7vw;
    margin: 2.08vw auto 0;
    display: flex;
    justify-content: space-between;
}

.concat-us .input-wrap {
    width: 12.5vw;
    position: relative;
}

.concat-us .form input {
    width: 100%;
    height: 2.08vw;
    background: transparent;
    outline: transparent;
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 2.08vw;
    padding: 0 1vw;
    box-sizing: border-box;
    color: #fff;
    font-size: 0.73vw;
}

.concat-us .form input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.concat-us .button {
    margin: 1.77vw auto 0;
    /* padding: 0.6vw 5.6vw; */
    width: 15.63vw;
    height: 2.71vw;
    line-height: 2.71vw;
    background: var(--main-color);
    border-radius: 15.63vw;
    border: 1px solid var(--main-color);
    font-size: 1.04vw;
    font-weight: 500;
    color: #FFFFFF;
    display: inline-block;
}

.button {
    cursor: pointer;
}

.button:active {
    transform: scale(0.8);
    transition: .5s;
}

.advantage .line {
    min-width: 16px;
}

.advantage li img {
    height: 2.08vw;
    width: 3.07vw;
    object-fit: scale-down;
    min-width: 40px;
}

.core-functions .tab li img {
    width: 100%;
    height: 1.4vw;
    object-fit: scale-down;
}

.what-we-can-do .circle.card-phone {
    display: none;
}

.save-to-address-book-phone {
    display: none;
}

.form .input-error {
    border-color: #F56C6C;
}

.el-form-item__error {
    color: #F56C6C;
    font-size: 12px;
    line-height: 1;
    padding-top: 14px;
    position: absolute;
    top: 100%;
    left: 20px;
}

@media screen and (max-width: 1400px) {
    .concat-us .form {
        width: 80%;
    }

    .concat-us .form input {
        height: 3vw;
    }

    .concat-us .button {
        height: 2.2vw;
        line-height: 2.2vw;
    }

    .advantage li {
        height: 26vw;
    }

    .what-we-can-do .circle::before {
        display: none;
    }
}

@media screen and (max-width: 1300px) {
    .concat-us .form-title {
        font-size: 2vw;
    }

    .concat-us .form-en-title {
        font-size: 1vw;
    }
}

@media screen and (max-width: 1200px) {
    .core-functions .tab li img {
        max-width: 35px;
        height: 5vw;
    }

    .advantage li {
        width: calc(100% / 4 - 2.6vw + 2.6vw / 4);
        height: 30vw;
    }

    .advantage li:nth-child(4n) {
        margin-right: 0;
    }

    .advantage li img {
        width: 50px;
        height: 30px;
    }

    .advantage .title-advantage {
        font-size: 1.6vw;
    }

    .advantage .intro-text {
        margin-top: 3vw;
    }
}

@media screen and (max-width: 1000px) {
    .core-functions .tab li img {
        max-width: 20px;
    }

    .title {
        font-size: 3vw;
    }

    .what-we-can-do .advance {
        font-size: 2vw;
    }

    .concat-us .form input {
        height: 4vw;
        padding: 0 2vw;
    }

    .concat-us .button {
        height: 3vw;
        line-height: 3vw;
    }

    .advantage li {
        width: calc(100% / 3 - 2.6vw + 2.6vw / 3);
        height: 30vw;
    }

    .advantage li:nth-child(3n) {
        margin-right: 0;
    }

    .advantage li:nth-child(4n) {
        margin-right: 2.6vw;
    }

    .advantage .title-advantage {
        font-size: 2vw;
    }

    .concat-us .form {
        margin-top: 3vw;
    }

    .concat-us .form-title {
        font-size: 2.5vw;
    }

    .banner h1 {
        font-size: 3vw;
    }

    .core-functions {
        margin-top: -14vw;
    }
}

@media screen and (max-width: 900px) {

    .concat-us .form,
    .concat-us .button {
        margin-top: 4vw;
    }

    .advantage li {
        height: 36vw;
    }

    .concat-us .form-title {
        font-size: 3vw;
    }

    .what-we-can-do .right {
        margin-top: 12vw;
    }

    .what-we-can-do .circle {
        width: 20vw;
        height: 20vw;
    }

    .save-to-address-book .left {
        width: 100%;
    }

    .save-to-address-book .right {
        top: 14vw;
    }

    .save-to-address-book .circle {
        width: 14vw;
        height: 14vw;
        margin-top: 10vw;
    }

    .save-to-address-book .circle::before {
        width: 7vw;
        height: 7vw;
    }

    .core-functions .tab-panel li img:nth-child(1) {
        width: 43vw;
    }

    .concat-us .form .input-wrap {
        width: 100%;
        margin-bottom: 30px;
    }

    .el-form-item__error {
        padding-top: 9px;
    }

    .concat-us .form input {
        height: 5vw;
        display: block;
        width: 100%;
        border-radius: 2.5vw;
    }

    .concat-us .form input:last-child {
        margin-bottom: 0;
    }

    .concat-us .form {
        display: block;
        width: 60%;
    }

    .concat-us .button {
        width: 30%;
        height: 4vw;
        line-height: 4vw;
        font-size: 2vw;
    }

    .concat-us .form-en-title {
        margin-top: 2vw;
    }

    .radio-right-top {
        border-radius: 0 10vw 0 0;
    }

    .advantage li {
        margin-bottom: 3vw;
    }

    .core-functions .tab-box {
        width: 100%;
    }
}

@media screen and (max-width: 800px) {
    body>div:not(:first-child).banner {
        background-image: url("../img/phone1.jpg");
        padding-top: 12.93vw;
        padding-bottom: 41.2vw;
        height: auto;
        /* position: relative; */
    }

    .banner h1 {
        font-size: 6.67vw;
        font-weight: 800;
    }

    .pc {
        display: none;
    }

    .phone {
        display: block;
    }

    .banner h1.phone {
        line-height: 1.5;
    }

    .title {
        font-size: 5.87vw;
    }

    .banner h2 {
        font-size: 4.27vw;
        font-weight: 400;
        margin-top: 4.67vw;
    }

    .banner .right {
        width: 27.87vw;
        height: max-content;
        position: absolute;
        right: 0;
        top: 31vw;
    }

    .button {
        border: none;
        border-radius: 5.5vw;
        background: #B5885E;
        color: #FFFFFF;
        font-weight: 500;
        font-size: 3.2vw;
        width: 40vw;
        height: 10.67vw;
        line-height: 10.67vw;
        border-radius: 5.33vw;
    }

    .banner .button {
        /* padding: 2.93vw 6.53vw; */
        margin-top: 7.07vw;
        width: 25.6vw;
        height: 8.8vw;
        line-height: 8.8vw;
    }

    .banner .version-selector {
        width: 26.67vw;
        height: 7.2vw;
        line-height: 7.2vw;
        border-radius: 3.6vw;
        font-weight: 800;
        font-size: 3.2vw;
        margin-bottom: 3vw;
    }

    .what-we-can-do {
        margin-top: -30.4vw;
        padding-top: 11.33vw;
        padding-bottom: 7.47vw;
    }


    .radio-left-top {
        border-radius: 10.67vw 0px 0px 0px;
    }

    .radio-right-top {
        border-radius: 0px 10.67vw 0px 0px;
    }

    .title,
    .title-en,
    .what-we-can-do .advance {
        text-align: center;
    }

    .title {
        font-weight: 800;
    }

    .title-en {
        margin-top: 2.53vw;
        font-size: 2.13vw;
    }

    .container {
        width: calc(100% - 8vw);
        margin: 0 auto;
    }

    .what-we-can-do .advance {
        margin-top: 9.33vw;
        font-size: 4vw;
    }

    .what-we-can-do .right {
        display: none;
    }

    .what-we-can-do .circle.card-phone {
        display: flex;
        margin: 9.07vw auto 0;
        width: 37.07vw;
        height: 37.07vw;
        box-shadow: 8.67vw 0 #5A81E7;
    }

    .what-we-can-do .left {
        width: 100%;
    }

    .what-we-can-do .circle::before {
        display: block;
        width: 15.33vw;
        height: 15.33vw;
    }

    .what-we-can-do .circle p:first-child {
        font-size: 2.13vw;
        margin-top: 3vw;
    }

    .what-we-can-do .circle p:last-child {
        font-size: 4vw;
        margin-top: 2vw;
        line-height: 1;
    }

    .what-we-can-do .text {
        margin-top: 8.67vw;
        font-size: 3.6vw;
        color: #666666;
    }

    .what-we-can-do .text+div {
        display: flex;
        justify-content: center;
    }

    .what-we-can-do .button {
        /* padding: 3.47vw 12.67vw;
        border-radius: 5.33vw; */
        margin: 6vw auto 0;
    }

    .what-we-can-do .circle-container {
        width: 47.2vw;
        height: 47.2vw;
        padding: 0 11.2vw;
        margin-left: 13.6vw;
        margin-top: 6.13vw;
    }

    .what-we-can-do .circle-container .circle-main {
        width: 47.2vw;
        height: 47.2vw;
    }

    .what-we-can-do .circle-container .circle-one {
        top: 2.13vw;
        left: -1.61vw;
        width: 15.33vw;
        height: 15.33vw;
    }

    .what-we-can-do .circle-container .circle-two {
        top: 6.4vw;
        right: -23.87vw;
        width: 37.05vw;
        height: 37.11vw;
    }

    .what-we-can-do .circle-container p:first-child {
        font-size: 3.47vw;
        width: max-content;
    }

    .what-we-can-do .circle-container p.large {
        font-size: 4.53vw;
    }

    body>div:not(:first-child).save-to-address-book {
        padding-top: 16vw;
        padding-bottom: 8vw;
        overflow: hidden;
    }

    .save-to-address-book .container {
        flex-direction: column-reverse;
        align-items: flex-start;
    }

    .save-to-address-book.second .container {
        flex-direction: column;
    }

    .save-to-address-book .container .img-box {
        width: 100%;
    }

    .save-to-address-book .price-view {
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 5.6vw;
        height: max-content;
    }

    .save-to-address-book .price-view .t-icon {
        width: 5.6vw;
        margin-bottom: 4vw;
    }

    .save-to-address-book .price-view .title {
        font-size: 5.33vw;
        margin-bottom: 6vw;
    }

    .save-to-address-book .price-view .tips li {
        margin-bottom: 4vw;
        font-size: 3.47vw;
        padding-left: 2.07vw;
    }

    .save-to-address-book .price-view .tips li::before {
        width: 1.07vw;
        height: 1.07vw;
        top: 2vw;
    }

    .save-to-address-book .price-view .button {
        background: transparent;
        color: var(--main-color);
    }

    .save-to-address-book .title {
        text-align: left;
    }

    .save-to-address-book-phone {
        display: block;
    }

    .save-to-address-book-pc {
        display: none;
    }

    .save-to-address-book .title p {
        font-size: 5.87vw;
        line-height: 1.5;
        font-weight: 800;
    }

    .save-to-address-book .title p:last-of-type {
        margin-top: 7.87vw;
    }

    .save-to-address-book .title-en {
        text-align: left;
    }

    .save-to-address-book .right {
        width: 70vw;
        right: -14.87vw;
        top: 37.87vw;
    }

    .save-to-address-book .circle {
        width: 22.27vw;
        height: 22.27vw;
    }

    .save-to-address-book .circle::before {
        width: 10.4vw;
        height: 10.4vw;
        right: -5.87vw;
    }

    .core-functions {
        margin-top: -18.5vw;
        padding-top: 18.4vw;
        padding-bottom: 38vw;
    }

    .core-functions .tab-box {
        margin-top: 14.8vw;
    }

    .core-functions .tab {
        padding-bottom: 3vw;
    }

    .core-functions .tab li img {
        width: 5.33vw;
        height: 4.53vw;
        max-width: none;
        object-fit: contain;
    }

    .core-functions .tab li+li {
        margin-left: 6.4vw;
    }

    .core-functions .tab-panel li img:nth-child(1) {
        width: 63.87vw;
    }

    .core-functions .tab-panel li img:nth-child(2) {
        width: 91.2vw;
        transform: translateY(-4vw);
    }

    .core-functions .tab li p {
        font-size: 3.47vw;
    }

    .advantage {
        margin-top: -27.87vw;
        padding-top: 11.33vw;
        padding-bottom: 22vw;
        border-radius: 10.67vw 0px 0px 0px
    }

    .advantage ul {
        margin-top: 11.73vw;
        display: grid;
        grid-template-columns: repeat(2, calc(50% - 3.47vw + 3.47vw / 2));
        grid-column-gap: 3.47vw;
        grid-row-gap: 3.47vw;
    }

    .advantage ul::after {
        display: none;
    }

    .advantage li {
        width: 100%;
        height: auto;
        padding: 7.2vw 4.53vw 15.47vw;
        margin: 0;
    }

    .advantage a {
        bottom: 6.27vw;
        font-size: 3.73vw;
    }

    .advantage li img {
        width: 7.47vw;
        height: 6.27vw;
        object-fit: contain;
    }

    .advantage .title-advantage {
        font-size: 4vw;
        margin-top: 4.8vw;
    }

    .advantage .line {
        margin-top: 4.4vw;
        width: 4vw;
    }

    .advantage .intro-text {
        font-size: 3.2vw;
        color: #666666;
        font-weight: 400;
        margin-top: 4vw;
    }

    .concat-us {
        background-image: url("../img/phone3.png");
        margin-top: -10.2vw;
        position: relative;
        z-index: 1;
        padding-top: 12.53vw;
        padding-bottom: 13.33vw;
        border-radius: 0px 10.67vw 0px 0px;
    }

    .concat-us .form-title {
        font-size: 5.07vw;
    }

    .concat-us .form-en-title {
        font-size: 2.13vw;
    }

    .concat-us .form {
        margin-top: 7.73vw;
        width: calc(100% - 8vw);
    }

    .concat-us .form .input-wrap {
        margin-bottom: 6.67vw;
    }

    .concat-us .form input {
        height: 10.67vw;
        border-radius: 5.33vw;
        padding: 0 6.4vw;
        font-size: 3.73vw;
    }

    .concat-us .button {
        height: 10.67vw;
        width: calc(100% - 8vw);
        box-sizing: border-box;
        margin-top: 9.87vw;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 3.73vw;
    }

    .el-form-item__error {
        font-size: 2.6vw;
    }
}

@media screen and (max-width: 560px) {
    .goBack {
        width: 60px;
        height: 60px;
    }
}

.animate__zoom {
    animation-name: zoom;
    animation-duration: 1s;
}

@keyframes zoom {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes shakeXBig {

    0%,
    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    50% {
        -webkit-transform: translate3d(-100px, 0, 0);
        transform: translate3d(-100px, 0, 0)
    }
}

.animate__shakeXBig {
    -webkit-animation-name: shakeXBig;
    animation-name: shakeXBig
}

.message-box {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgb(0, 0, 0);
    color: #fff;
    padding: 20px 30px;
    border-radius: 4px;
}

#message-text {
    color: #fff;
}