import{Q as d}from"./index-BBeD0eDz.js";const h=t=>{const e=new Map;return t.reverse().filter(o=>!e.has(o.id)&&e.set(o.id,!0)).reverse()},m=t=>{const e=Math.floor(t/60),o=Math.floor(t%60),r=e.toString().padStart(2,"0"),n=o.toString().padStart(2,"0");return`${r}:${n}`},S=async t=>{try{if(navigator.clipboard&&navigator.clipboard.writeText)return await navigator.clipboard.writeText(t),!0;const e=document.createElement("textarea");if(e.style.position="fixed",e.style.opacity="0",e.style.left="-9999px",e.value=t,document.body.appendChild(e),e.select(),navigator.userAgent.match(/ipad|iphone/i)){const r=document.createRange();r.selectNodeContents(e);const n=window.getSelection();n==null||n.removeAllRanges(),n==null||n.addRange(r),e.setSelectionRange(0,999999)}const o=document.execCommand("copy");return document.body.removeChild(e),o?d.success("复制成功"):d.error("复制失败，请手动复制"),o}catch(e){return console.error("复制失败:",e),d.error("复制失败，请手动复制"),!1}};function f(t){if(/^(#([0-9a-f]{3}|[0-9a-f]{4}|[0-9a-f]{6}|[0-9a-f]{8})|rgb(a?)\((\d+%?(,\s?)?){3,4}\)|hsla?\(\d+%?(,\s?\d+%?){2}(,\s?\d*\.?\d+)?\)|transparent|currentColor)$/i.test(t.trim()))return"color";if(/^data:(image|video)\//.test(t))return t.startsWith("data:image/")?"image":"video";const r=(a=>{var c;return((c=a.split(/[?#]/)[0].split(".").pop())==null?void 0:c.toLowerCase())||""})(t);return new Set(["jpg","jpeg","png","gif","webp","svg","bmp","ico"]).has(r)?"image":new Set(["mp4","mov","avi","mkv","webm","flv","wmv","m4v"]).has(r)?"video":"other"}function w(){const t=new Set,e=new Date,o=e.getFullYear(),r=e.getMonth()+1;for(let n=0;n<12;n++){const s=r-n;if(s<=0){const a=String(s+12).padStart(2,"0");t.add(`${o-1}-${a}`)}else{const a=String(s).padStart(2,"0");t.add(`${o}-${a}`)}}return Array.from(t)}function v(){const t=new Date,e=t.getFullYear(),o=t.getMonth()+1,r=t.getDate(),n=t.getHours(),s=t.getMinutes(),a=t.getSeconds(),i=o.toString().padStart(2,"0"),c=r.toString().padStart(2,"0"),g=n.toString().padStart(2,"0"),l=s.toString().padStart(2,"0"),u=a.toString().padStart(2,"0");return`${e}${i}${c}${g}${l}${u}`}function y(t,e="newW"){const o=window.screen.availWidth,r=window.screen.availHeight,n=Math.round(o*.8),s=Math.round(r*.8),a=Math.round((o-n)/2),i=Math.round((r-s)/2),c=`
    width=${n},
    height=${s},
    left=${a},
    top=${i},
    location=no,
    menubar=no,
    toolbar=no,
    status=no,
    resizable=yes,
    scrollbars=yes
  `.replace(/\s+/g,"");window.open(t,e,c)}export{f as a,v as b,S as c,m as f,w as g,y as o,h as u};
