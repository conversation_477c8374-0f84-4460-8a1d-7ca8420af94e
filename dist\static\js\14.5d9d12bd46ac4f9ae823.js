webpackJsonp([14],{DAU2:function(t,e){},Vwx0:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("mvHQ"),o=a.n(i),l=a("vLgD");var n={name:"position",components:{Pagination:a("cMGX").a},data:function(){return{query:{total:0,page:1,numbers:10},tableData:[],tableLoading:!1,dialogForm:{title:"",beduty:""},productData:[],dialogButtonLoading:!1,dialogFormTitle:"",dialogFormLabelWidth:"120px",dialogFormVisible:!1}},created:function(){this.getList()},methods:{getList:function(){var t,e=this;this.tableLoading=!0,(t=this.query,Object(l.a)({url:"/api/position/index",method:"POST",data:t})).then(function(t){console.log(t),200===t.code&&(e.tableData=t.data.data,e.query.numbers=parseInt(t.data.per_page),e.query.page=t.data.current_page,e.query.total=t.data.total),e.tableLoading=!1})},handelCreate:function(){this.dialogFormTitle="添加职务",this.dialogForm.title="",this.dialogForm.beduty="",delete this.dialogForm.id,this.dialogFormVisible=!0},handelUpdate:function(t){console.log("row",t),this.dialogFormTitle="编辑职务",this.dialogForm=JSON.parse(o()(t)),console.log("dialogForm",this.dialogForm),this.dialogFormVisible=!0},handelDelete:function(t){var e=this;this.$confirm("此操作将删除该职务, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var a;(a=t,Object(l.a)({url:"/api/position/delete",method:"POST",data:a})).then(function(t){200===t.code&&(e.$message.success("删除成功"),e.getList())})})},handelSubmit:function(){var t=this;this.dialogButtonLoading=!0;var e={title:this.dialogForm.title,beduty:this.dialogForm.beduty};this.dialogForm.id&&(e.id=this.dialogForm.id),console.log(e),e.id?function(t){return Object(l.a)({url:"/api/position/update",method:"POST",data:t})}(e).then(function(e){t.dialogButtonLoading=!1,200===e.code&&(t.dialogFormVisible=!1,t.$message.success("编辑成功"),t.getList())}).catch(function(e){t.$message.error("编辑失败"),t.dialogButtonLoading=!1}):function(t){return Object(l.a)({url:"/api/position/create",method:"POST",data:t})}(e).then(function(e){t.dialogButtonLoading=!1,200===e.code&&(t.dialogFormVisible=!1,t.$message.success("添加成功"),t.getList())}).catch(function(e){t.$message.error("添加失败"),t.dialogButtonLoading=!1})}}},r={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("职务列表")]),t._v(" "),a("el-button",{staticStyle:{float:"right",padding:"0"},attrs:{type:"text",icon:"el-icon-refresh",size:"small"},on:{click:t.getList}},[t._v("刷新")]),t._v(" "),a("el-button",{staticStyle:{float:"right",padding:"0","margin-right":"15px"},attrs:{type:"text",icon:"el-icon-plus",size:"small"},on:{click:t.handelCreate}},[t._v("添加职务")])],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%","min-height":"400px"},attrs:{data:t.tableData,stripe:""}},[a("el-table-column",{attrs:{label:"序号",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.$index+1))]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"title",label:"职务名称",width:"180"}}),t._v(" "),a("el-table-column",{attrs:{prop:"beduty",label:"岗位职责"}}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",prop:"create_time",width:"180"}}),t._v(" "),a("el-table-column",{attrs:{label:"更新时间",prop:"update_time",width:"180"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",icon:"el-icon-edit",size:"small"},on:{click:function(a){return t.handelUpdate(e.row)}}},[t._v("编辑")]),t._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-delete",size:"small"},on:{click:function(a){return t.handelDelete(e.row)}}},[t._v("删除")])]}}])})],1),t._v(" "),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.query.total>0,expression:"query.total>0"}],staticStyle:{"text-align":"left"},attrs:{total:t.query.total,page:t.query.page,limit:t.query.numbers},on:{"update:page":function(e){return t.$set(t.query,"page",e)},"update:limit":function(e){return t.$set(t.query,"numbers",e)},pagination:t.getList}})],1),t._v(" "),a("el-dialog",{staticClass:"role-dialog",attrs:{title:t.dialogFormTitle,visible:t.dialogFormVisible,width:"50%"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[a("el-form",{ref:"form",attrs:{model:t.dialogForm}},[a("el-form-item",{attrs:{label:"职务名称","label-width":t.dialogFormLabelWidth}},[a("el-input",{staticStyle:{"max-width":"300px"},attrs:{placeholder:"请输入职务名称"},model:{value:t.dialogForm.title,callback:function(e){t.$set(t.dialogForm,"title",e)},expression:"dialogForm.title"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"岗位职责","label-width":t.dialogFormLabelWidth}},[a("el-input",{staticStyle:{"max-width":"500px"},attrs:{type:"textarea",placeholder:"请输入岗位职责",rows:5},model:{value:t.dialogForm.beduty,callback:function(e){t.$set(t.dialogForm,"beduty",e)},expression:"dialogForm.beduty"}})],1)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small"},on:{click:function(e){t.dialogFormVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary",size:"small",loading:t.dialogButtonLoading},on:{click:t.handelSubmit}},[t._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var s=a("VU/8")(n,r,!1,function(t){a("DAU2")},"data-v-156eff8d",null);e.default=s.exports}});