var et=Object.defineProperty;var tt=(n,y,r)=>y in n?et(n,y,{enumerable:!0,configurable:!0,writable:!0,value:r}):n[y]=r;var he=(n,y,r)=>tt(n,typeof y!="symbol"?y+"":y,r);import{k as pe,l as ee,A as m,V as se,c as S,o as _,a as u,I as Ve,w as p,b as o,e as Te,h as Fe,j,_ as ve,q as ye,J as Se,f as O,aT as lt,m as J,F as re,s as be,n as de,t as ae,aU as Ne,K as Be,p as D,aV as Ie,aW as Ke,aX as Oe,a7 as xe,Q as M,a4 as Ye,ag as Me,a2 as qe,y as at,E as st,d as ot,ae as it,g as Ce,i as ne,aY as nt,a3 as rt,aZ as ut,a_ as ct,aG as dt,Z as He,v as De,P as pt,B as vt,C as ft,a$ as mt,b0 as yt,b1 as gt,a1 as je,N as Pe}from"./index-BBeD0eDz.js";/* empty css                *//* empty css               *//* empty css                 *//* empty css                *//* empty css               *//* empty css                */import{h as le}from"./request-Ciyrqj7N.js";import{E as _t,_ as ht,V as bt}from"./video-cover-DBcJ77EJ.js";/* empty css                  */const kt={class:"update-folder-dialog"},wt={class:"folder-dialog-content"},xt={class:"dialog-footer"},Ct=pe({__name:"FolderDialog",props:{modelValue:{type:Boolean},initialName:{},isEdit:{type:Boolean},folderId:{},isFolder:{type:Boolean},from:{},loading:{type:Boolean},title:{},nowFolder:{}},emits:["update:modelValue","confirm","cancel"],setup(n,{emit:y}){const r=n,d=y,T=ee({get:()=>r.modelValue,set:c=>d("update:modelValue",c)}),g=m(""),C=m(),Y=ee(()=>{let c=r.nowFolder&&r.nowFolder.type==2?"文件夹":"文件";return r.title&&(c=r.title),`${r.isEdit?"重命名":"新建"}${c}`}),b=ee(()=>{let c=r.nowFolder&&r.nowFolder.type==2?"文件夹":"文件";return r.title&&(c=r.title),`请输入${c}名称`});se(()=>r.initialName,c=>{c&&(g.value=c)},{immediate:!0}),se(()=>r.modelValue,c=>{c&&setTimeout(()=>{var f;(f=C.value)==null||f.focus()},100),c&&!r.initialName&&(g.value="")});const h=()=>{T.value=!1,d("cancel")},E=async()=>{if(g.value.trim()){const c=g.value.trim();d("confirm",c,r.folderId),T.value=!1}};return(c,f)=>{const U=Te,L=Fe,v=Ve;return _(),S("div",kt,[u(v,{modelValue:T.value,"onUpdate:modelValue":f[1]||(f[1]=F=>T.value=F),title:Y.value||"新建",width:"560px","show-close":!0,"close-on-click-modal":!1,"close-on-press-escape":!0,class:"folder-dialog"},{footer:p(()=>[o("div",xt,[u(L,{size:"large",onClick:h},{default:p(()=>f[2]||(f[2]=[j("取消")])),_:1}),u(L,{size:"large",type:"primary",onClick:E,disabled:!g.value.trim(),loading:c.loading},{default:p(()=>f[3]||(f[3]=[j(" 确定 ")])),_:1},8,["disabled","loading"])])]),default:p(()=>[o("div",wt,[u(U,{ref_key:"folderInputRef",ref:C,modelValue:g.value,"onUpdate:modelValue":f[0]||(f[0]=F=>g.value=F),placeholder:b.value,clearable:"",autofocus:"",size:"large"},null,8,["modelValue","placeholder"])])]),_:1},8,["modelValue","title"])])}}}),Xe=ve(Ct,[["__scopeId","data-v-20b4af2f"]]),Vt=n=>le.get("/bucket/catelist",{params:n}),$t=n=>le.get("/bucket/getlst",{params:n}),Re=n=>le.post("/bucket/addcate",n),At=n=>le.post("/bucket/delcate",n),It=n=>le.get("/bucket/lst",{params:n}),Ue=n=>le.post("/bucket/add",n),Tt=n=>le.post("/bucket/resetflod",n),Ze=n=>le.post("/bucket/del",n),Ft=(n,y)=>le.post("/bucket/upload",n,y),St=(n,y)=>le.post("/bucket/uploadall",n,y),Bt=n=>le.post("/bucket/uploadvision",n),Mt=n=>le.get("/bucket/info",{params:n}),Ut=n=>le.post("/bucket/editfile",n),we=[{label:"素材库",key:"material",value:1},{label:"我收藏的视频",key:"video",value:2}],Ee=[{label:"全部",key:"all",value:0},{label:"视频",key:"video",value:1},{label:"图片",key:"image",value:2},{label:"音频",key:"audio",value:3}],Je=()=>Ee.reduce((n,y)=>(n[y.key]=y.value,n),{}),Ge=n=>{console.log("🚀 ~ getFileTypeValue ~ fileTypeParam: ",n);const y=Je();if(Array.isArray(n)){if(n.length===0||n.includes("all"))return y.all;{const r=Ee.reduce((d,T)=>(n.includes(T.key)&&d.push(T.value),d),[]);return r.length===0?y.all:r}}else return y[n]??y.all},Et={class:"category-list h-full"},Lt={class:"h-[calc(100%-38px-28px)] overflow-y-auto list-wrap"},zt=["onClick"],Dt={class:"flex items-center"},jt={class:"name overflow-hidden text-ellipsis whitespace-nowrap"},Pt={class:"count"},Rt={class:"el-dropdown-link"},Qt={key:0,class:"flex jusitify-center items-center"},Wt=pe({__name:"CategoryList",props:{activeCategory:{},bigType:{},currentCateId:{},fileType:{}},emits:["select"],setup(n,{expose:y,emit:r}){const d=n,T=r,g=m([]),C=m(!1),Y=async()=>{try{C.value=!0;const a=Ge(d.fileType),V=Array.isArray(a)?a.join(","):a;g.value=await Vt({id:d.currentCateId,bigtype:d.bigType,filetype:V})}catch(a){console.log("🚀 ~ getCategories ~ error: ",a)}finally{C.value=!1}};se(()=>d.bigType,a=>{a!==void 0&&Y()},{immediate:!0});const b=(a,V)=>{T("select",a,V)},h=m(!1),E=m(""),c=m(null),f=a=>{a?(c.value=a,E.value=a.title):(c.value=null,E.value=""),h.value=!0},U=m(!1),L=async(a,V)=>{if(a.trim()){let Z={},P=null;if(c.value?(P=Re,Z={id:c.value.id,pid:0,title:a,bigtype:d.bigType}):(P=Re,Z={pid:0,title:a,bigtype:d.bigType},V!==void 0&&(Z.id=V)),!P)return;try{U.value=!0,await P(Z),M.success(`分类"${a}"${c.value?"重命名":"创建"}成功`),await Y()}catch(H){M.error(c.value?"重命名失败":"创建失败"),console.error("分类操作错误: ",H)}finally{U.value=!1,h.value=!1}}},v=async a=>{a!==void 0&&Ye.confirm("确定要删除该分类吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await At({id:a}),M.success("删除成功");const V=d.bigType===1?"全部素材":"全部成片";T("select",0,V),await Y()}catch(V){console.error("删除分类错误: ",V),M.error("删除分类失败")}finally{}}).catch(()=>{})},F=()=>window.innerWidth<=1e3;return y({getCategories:Y}),(a,V)=>{var K,te;const Z=Be,P=Oe,H=Ke,A=Ne,w=lt,$=Se;return _(),S("div",Et,[ye((_(),O(w,{class:"category-card h-full",shadow:"never"},{default:p(()=>[o("div",Lt,[(_(!0),S(re,null,be(g.value,Q=>(_(),S("div",{key:Q.id,class:de(["category-item flex items-center justify-between",{active:a.activeCategory===Q.id}]),onClick:oe=>b(Q.id,Q.title)},[o("div",Dt,[o("span",jt,ae(Q.title),1),o("span",Pt,"("+ae(Q.num)+")",1)]),(Q==null?void 0:Q.id)!==0?(_(),O(A,{key:0},{dropdown:p(()=>[u(H,null,{default:p(()=>[u(P,{class:"text-[#555] text-[16px] justify-center",onClick:oe=>f(Q)},{default:p(()=>V[3]||(V[3]=[j("重命名")])),_:2},1032,["onClick"]),u(P,{class:"text-[--el-color-primary] text-[16px] justify-center",onClick:oe=>v(Q.id)},{default:p(()=>V[4]||(V[4]=[j("删除")])),_:2},1032,["onClick"])]),_:2},1024)]),default:p(()=>[o("span",Rt,[u(Z,{class:"rotate-90",color:"#999"},{default:p(()=>[u(D(Ie))]),_:1})])]),_:2},1024)):J("",!0)],10,zt))),128)),F()?(_(),S("div",Qt,[o("div",{class:"add-category-btn !mt-0 !w-[100px]",onClick:V[0]||(V[0]=()=>f())},[u(Z,null,{default:p(()=>[u(D(xe))]),_:1}),V[5]||(V[5]=o("span",null,"添加",-1))])])):J("",!0)]),F()?J("",!0):(_(),S("div",{key:0,class:"add-category-btn",onClick:V[1]||(V[1]=()=>f())},[u(Z,null,{default:p(()=>[u(D(xe))]),_:1}),V[6]||(V[6]=o("span",null,"添加",-1))]))]),_:1})),[[$,C.value]]),u(Xe,{modelValue:h.value,"onUpdate:modelValue":V[2]||(V[2]=Q=>h.value=Q),"initial-name":E.value,"is-edit":!!((K=c.value)!=null&&K.id),"folder-id":(te=c.value)==null?void 0:te.id,from:a.bigType,loading:U.value,onConfirm:L,title:"分类"},null,8,["modelValue","initial-name","is-edit","folder-id","from","loading"])])}}}),Qe=ve(Wt,[["__scopeId","data-v-56459604"]]),Nt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGgAAABcCAMAAAC1KQAoAAAAyVBMVEUAAABSwf9Ovv0XnegXnegWnekWnegXnegWnekAnu4XnehSwf8WnehRwv9Rwf8WnehSwf9Swf9Qwf8WnegWnegWnekXnegXnukVnOhRwP9Twf8WnegWnOkUmecVmupRwf9Vwv8RleVCt/ksqvFSwf8XnegXnehSwf9Swf9Rwf9Swf8Xnej////D6f/4/P4rqfDw+f0mpexPv/5Iu/scoOr1+/7p9fzb8PtDuPk4sfWNz/Mup+q65f7k8/zU7fqc1fWV0vRNtO1Dr+xhHuGPAAAAKnRSTlMA5/r35/yusm8H7eDavHf++PTy8eO5j4eCfG9oLSUeHBUO8OfNxsWkmIfSttDlAAABT0lEQVRo3u3TiU7CQBCA4UF7UE65LxHwdhYF2iKHt+//UDYVNykxtdeOCZnvAebPbHbANxkYpcZxZIWSmT+DBAYFEVvVPIWYLEMkonchHkMkZVoQQ14kZ0J0k4ZIoat4IUmP/iNaIhUDotJEKtXI96SLdPIQkUjJoAqVqEIFqpCuhWtedYYypJjetvwQgZZFFBJtqpA+BEGjsx96el8tH1JbrrYLEXC9F/qYTzPy+BkY3ITgPrKTQSmwkxYMvU0ztA0JvWQZeg0JTbM0/8/QYvbtPkvPu6GLn5C7sVEpe+OHXAeVc1wv5CCBtQYzJHEBayRxDjaSOAL8FYc4JHGIQxKHOCRxiEMShzgkcYhDEoc4JHGIQxKH/lSnCuU4xKFDDtWRRBEukUQZ7pBEBUYnSKA2BughgT54blC5W/BVFL9erQ87o165mFOkWK6MwfMFz+WmVQyhgo0AAAAASUVORK5CYII=",Kt={class:"video-container"},Ot=["src"],Yt=pe({__name:"ImagePreview",props:{visible:{type:Boolean,default:!1},imgUrl:{type:String,default:""},title:{type:String,default:"视频播放"}},emits:["update:visible","close","ended"],setup(n,{expose:y,emit:r}){const d=n,T=r,g=m(null),C=m(d.visible);se(()=>d.visible,b=>{C.value=b}),se(()=>C.value,b=>{T("update:visible",b),b||(T("close"),g.value&&g.value.pause())});const Y=()=>{C.value=!1};return Me(()=>{g.value&&g.value.pause()}),y({play:()=>{g.value&&g.value.play()},pause:()=>{g.value&&g.value.pause()},reset:()=>{g.value&&(g.value.currentTime=0)}}),(b,h)=>{const E=Ve;return _(),O(E,{modelValue:C.value,"onUpdate:modelValue":h[0]||(h[0]=c=>C.value=c),title:n.title,"align-center":"","before-close":Y,class:"video-player-dialog","destroy-on-close":""},{footer:p(()=>h[1]||(h[1]=[])),default:p(()=>[o("div",Kt,[o("img",{src:n.imgUrl,alt:"",class:"max-w-[100%] max-h-[100%] object-contain"},null,8,Ot)])]),_:1},8,["modelValue","title"])}}}),qt=ve(Yt,[["__scopeId","data-v-e7474222"]]),Ht={class:"video-container"},Xt=["src"],Zt=pe({__name:"audioPlayer",props:{visible:{type:Boolean,default:!1},audioUrl:{type:String,default:""},title:{type:String,default:"视频播放"}},emits:["update:visible","close","ended"],setup(n,{expose:y,emit:r}){const d=n,T=r,g=m(null),C=m(d.visible);se(()=>d.visible,b=>{C.value=b}),se(()=>C.value,b=>{T("update:visible",b),b||(T("close"),g.value&&g.value.pause())});const Y=()=>{C.value=!1};return Me(()=>{g.value&&g.value.pause()}),y({play:()=>{g.value&&g.value.play()},pause:()=>{g.value&&g.value.pause()},reset:()=>{g.value&&(g.value.currentTime=0)}}),(b,h)=>{const E=Ve;return _(),O(E,{modelValue:C.value,"onUpdate:modelValue":h[0]||(h[0]=c=>C.value=c),title:n.title,width:"400px","align-center":"","before-close":Y,class:"video-player-dialog","destroy-on-close":""},{footer:p(()=>h[1]||(h[1]=[])),default:p(()=>[o("div",Ht,[o("audio",{src:n.audioUrl,controls:""},null,8,Xt)])]),_:1},8,["modelValue","title"])}}}),Jt=ve(Zt,[["__scopeId","data-v-6be63a78"]]);function Gt(){const n=at(),y=m([]),r=m({page:1,limit:10}),d=m(0),T=m(0),g=m(!1),C=(c={},f=!1)=>new Promise(async(U,L)=>{g.value=!0;try{const v=await It({...r.value,...c});f&&r.value.page>1?y.value=[...y.value,...v.data]:y.value=v.data,d.value=v.total,T.value=v.last_page,r.value.page>v.last_page&&(r.value.page=v.last_page,await C()),U(v)}catch(v){console.log("🚀 ~ fetchLibraryList ~ error: ",v)}finally{g.value=!1}});return{listQ:r,totalPage:T,materialList:y,loading:g,total:d,fetchMaterialList:C,loadMore:async(c={})=>{g.value||r.value.page>=T.value||(console.log("🚀 ~ loadMore ~ listQ.value.page: ",r.value.page),r.value.page++,await C(c,!0))},refresh:async(c={})=>{r.value.page=1,await C(c)},getMaterialListById:c=>new Promise(async(f,U)=>{try{const L=await $t(c);f(L)}catch(L){console.log("🚀 ~ getMaterialListById ~ error: ",L)}}),publishVideo:c=>new Promise(async(f,U)=>{if(c.length===0)return M.warning("请选择视频");let L=qe.service({lock:!0,text:"加载中...",background:"rgba(255, 255, 255, 0.7)"});try{let v=[];for(let F=0;F<c.length;F++){let a=c[F];const V=await Ue({title:a.name,type:1,bigtype:2,filetype:1,yjcp_id:a.id,url:a.url,size:a.size,times:a.times,cover:a.imgurl,fbl:a.fbl,fps:a.fps,zhb:a.zhb});v.push(V),F===c.length-1&&(L.close(),f(!0),n.push(`/short-video/publish?videos=${v.join(",")}&type=1`))}}catch(v){L.close(),console.log("🚀 ~ getMaterialListById ~ error: ",v)}})}}const el={class:"flex justify-between gap-[20px] content"},tl={class:"video-player w-[40%] min-w-[400px] min-h-[400px] max-h-[640px]"},ll=["src","poster"],al={class:"video-info w-[60%] min-w-[400px] min-h-[400px] max-h-[640px]"},sl={class:"flex items-center w-full"},ol={class:"overflow-y-auto w-full h-full pt-[10px]"},il={class:"flex items-center w-full flex-wrap flex-1"},nl={class:"flex justify-center w-full btn-group"},rl=pe({__name:"VideoPreview",props:{visible:{type:Boolean},video:{}},emits:["update:visible","refresh"],setup(n,{emit:y}){const r=n,d=y,T=ee({get(){return r.visible},set(A){d("update:visible",A)}}),g=m(!1),C=m({id:"",url:"",cover:"",title:"",label:"",label_sd:""}),Y=async()=>{if(r.video.id)try{g.value=!0;const A=await Mt({id:r.video.id});if(A){C.value=A;const w=C.value.label?C.value.label.split(","):[],$=C.value.label_sd?C.value.label_sd.split(","):[];w.length===0&&$.length===0?h.value=[]:h.value=[...w.map(K=>({label:K,type:"system"})),...$.map(K=>({label:K,type:"user"}))]}}catch(A){console.log(A)}finally{g.value=!1}},b=m(""),h=m([]);se(()=>r.video,A=>{b.value=A.title,Y()});const E=m(!1),c=async()=>{if(!b.value){M.warning("视频名称不能为空");return}if(!r.video.id){M.warning("视频id不能为空");return}const A=h.value.map($=>{if($.type==="system")return $.label}).filter($=>$).join(","),w=h.value.map($=>{if($.type==="user")return $.label}).filter($=>$).join(",");try{E.vlaue=!1,await Ut({id:r.video.id,title:b.value,label:A,label_sd:w})&&(M.success("修改成功"),d("refresh"),T.value=!1)}catch($){M.error("修改失败"),console.log($)}finally{}},f=m(""),U=A=>{h.value=h.value.filter(w=>w.label!==A.label)},L=m(!1),v=()=>{if(f.value){if(h.value.some(A=>A.label===f.value)){M.warning("标签已存在");return}h.value.unshift({label:f.value,type:"user"}),L.value=!1,f.value=""}},F=m(null),a=m(""),V=m(null),Z=A=>{A.type!=="system"&&(F.value=A,a.value=A.label,rt(()=>{var w,$;($=(w=V.value)==null?void 0:w.input)==null||$.focus()}))},P=()=>{F.value=null,a.value=""},H=()=>{if(!F.value)return;const A=a.value.trim();if(!A){M.warning("标签名称不能为空");return}if(A===F.value.label){P();return}if(h.value.some($=>$.label===A)){M.warning("标签已存在");return}const w=h.value.findIndex($=>{var K;return $.label===((K=F.value)==null?void 0:K.label)});w!==-1&&(h.value[w].label=A),P()};return(A,w)=>{const $=Te,K=ot,te=Fe,Q=Be,oe=it,ue=st,ge=Ve;return _(),O(ge,{modelValue:T.value,"onUpdate:modelValue":w[5]||(w[5]=X=>T.value=X),width:"1000px","align-center":"",class:"video-player-dialog","destroy-on-close":""},{header:p(()=>w[6]||(w[6]=[o("div",{class:"dialog-header"},"视频详情",-1)])),default:p(()=>[o("div",el,[o("div",tl,[o("video",{src:C.value.url,poster:C.value.cover,controls:"",class:"object-contain w-full h-full"},null,8,ll)]),o("div",al,[u(ue,{"label-width":"80px",class:"flex flex-col h-full","label-position":"top"},{default:p(()=>[u(K,{label:"视频名称："},{default:p(()=>[o("div",sl,[u($,{modelValue:b.value,"onUpdate:modelValue":w[0]||(w[0]=X=>b.value=X),placeholder:"请输入视频名称"},null,8,["modelValue"])])]),_:1}),u(K,{label:"素材标签：",class:"flex-1"},{default:p(()=>[o("div",ol,[o("div",il,[L.value?(_(),O($,{key:0,modelValue:f.value,"onUpdate:modelValue":w[1]||(w[1]=X=>f.value=X),placeholder:"请输入标签",class:"mr-[10px] mb-[10px]",onBlur:v,onKey:v},null,8,["modelValue"])):(_(),O(te,{key:1,class:"mr-[10px] mb-[10px] add-btn",icon:D(xe),onClick:w[2]||(w[2]=X=>L.value=!0)},{default:p(()=>w[7]||(w[7]=[j("添加")])),_:1},8,["icon"])),(_(!0),S(re,null,be(h.value,(X,fe)=>(_(),O(oe,{size:"large",key:fe,class:"mr-[10px] mb-[10px] relative",onDblclick:ie=>Z(X)},{default:p(()=>{var ie;return[((ie=F.value)==null?void 0:ie.label)===X.label?(_(),O($,{key:0,modelValue:a.value,"onUpdate:modelValue":w[3]||(w[3]=ce=>a.value=ce),size:"small",class:"edit-input",onBlur:H,onKeyup:[Ce(H,["enter"]),Ce(P,["esc"])],ref_for:!0,ref_key:"editInputRef",ref:V},null,8,["modelValue"])):(_(),S(re,{key:1},[o("span",null,ae(X.label),1),u(Q,{size:"20",color:"#CACACA",class:"top-0 right-0 del-btn",onClick:ne(ce=>U(X),["stop"])},{default:p(()=>[u(D(nt))]),_:2},1032,["onClick"])],64))]}),_:2},1032,["onDblclick"]))),128))])])]),_:1}),u(K,{"label-width":"0"},{default:p(()=>[o("div",nl,[u(te,{class:"mr-[10px]",onClick:w[4]||(w[4]=X=>T.value=!1)},{default:p(()=>w[8]||(w[8]=[j("取消")])),_:1}),u(te,{type:"primary",onClick:c,loading:E.value},{default:p(()=>w[9]||(w[9]=[j("保存")])),_:1},8,["loading"])])]),_:1})]),_:1})])])]),_:1},8,["modelValue"])}}}),ul=ve(rl,[["__scopeId","data-v-19641f47"]]),cl={class:"material-list flex flex-col","element-loading-background":"#F2F6F9"},dl={class:"flex-1 mt-[10px] overflow-auto"},pl={class:"folder-icon"},vl=["onClick"],fl=["onClick"],ml={class:"material-info text-center"},yl={class:"material-name"},gl={class:"material-details text-[#555] text-[12px]"},_l={class:"text-[--el-color-primary]"},hl={class:"text-[--el-color-primary]"},bl=["onClick"],kl=["onClick"],wl=["src"],xl={key:2,class:"image-thumbnail max-w-[104px] h-[92px]"},Cl=["src","onClick"],Vl={key:3,class:"audio-thumbnail max-w-[104px] h-[92px]"},$l={class:"w-[92px] h-[82px] flex justify-center items-center"},Al=["onClick"],Il={class:"material-info text-center"},Tl={class:"material-name"},Fl=pe({__name:"MaterialList",props:{selectAll:{type:Boolean},checkedItems:{},bigType:{default:1},activeCategory:{},searchKeyword:{},multiSelect:{type:Boolean,default:!0},showSelect:{type:Boolean,default:!0},fileType:{default:"all"},from:{default:"material"},alertType:{default:"import"}},emits:["update:checkedItems","refresh-category"],setup(n,{expose:y,emit:r}){const{listQ:d,materialList:T,loading:g,total:C,fetchMaterialList:Y}=Gt();d.value.limit=26;const b=n,h=r,E=ee(()=>b.alertType==="import"&&b.from==="dialog"),c=m([26,35,44,53]),f=m([{name:"全部素材",path:0,pid:void 0}]),U=ee(()=>f.value[f.value.length-1]),L=ee(()=>Je()),v=async(e=0,t,x,k=!0)=>{var B,I,W;t&&(d.value.page=1);try{const N=Ge(b.fileType),me=Array.isArray(N)?N.join(","):N;g.value=!0;const $e={flod_id:e,limit:d.value.limit,bigtype:b.bigType,category_id:b.activeCategory||0,title:b.searchKeyword,filetype:me,type:+E.value};if(await Y($e),k){const ke=(B=we.find(Ae=>Ae.value===b.bigType))==null?void 0:B.label;if(+e==0)f.value=[{path:0,name:"全部"+ke,pid:0}];else{if(((I=f.value[f.value.length-1])==null?void 0:I.path)===e)return;f.value.push({name:x||"全部"+ke,path:e,pid:(W=f.value[f.value.length-1])==null?void 0:W.path})}}}catch(N){console.log("🚀 ~ getMaterials ~ error: ",N)}finally{g.value=!1}};se(()=>[b.activeCategory,b.bigType,b.fileType],([e,t,x])=>{e!==void 0&&t!==void 0&&x!==void 0&&v(0,!0)},{immediate:!0});const F=()=>{var e;v(((e=U.value)==null?void 0:e.path)||0,!1)};se(()=>b.multiSelect,e=>{!e&&a.value&&a.value.length>1&&h("update:checkedItems",[a.value[a.value.length-1]])});const a=ee({get(){return b.checkedItems},set(e){if(!b.multiSelect&&e)if(e.length>0){const t=b.checkedItems||[],x=e.filter(k=>!t.includes(k));if(x.length>0){h("update:checkedItems",[x[x.length-1]]);return}else if(e.length<t.length){h("update:checkedItems",e);return}else{h("update:checkedItems",[e[e.length-1]]);return}}else{h("update:checkedItems",[]);return}else h("update:checkedItems",e)}}),V=e=>{var x;d.value.page=e;const t=((x=U.value)==null?void 0:x.path)||0;v(t)},Z=e=>{var x;d.value.limit=e,d.value.page=1;const t=((x=U.value)==null?void 0:x.path)||0;v(t)},P=m(!1),H=m(""),A=m(""),w=()=>{$.value={id:void 0,title:"",type:2},A.value="文件夹",P.value=!0,H.value=""},$=m({}),K=m(!1),te=async(e,t)=>{var x;if(t){$.value.type===1&&(e=e+"."+Q.value);const k=await Tt({id:t,title:e});M.success(k.message||"重命名成功"),P.value=!1,ie.value=!1,await v()}else try{K.value=!0;const k=((x=f.value[f.value.length-1])==null?void 0:x.path)||0,B=await Ue({id:t,title:e,category_id:b.activeCategory,flod_id:k,type:2,bigtype:b.bigType});M.success(B.message||"创建成功"),P.value=!1,h("refresh-category"),await v(k,!0,e,!1)}catch(k){M.error("创建失败"),console.error("创建文件夹错误: ",k)}finally{K.value=!1}$.value={}},Q=m(""),oe=e=>{if(console.log("🚀 ~ renameMaterial ~ material: ",e),e){switch($.value={id:e.id,title:e.title,type:e.type},e.type){case 1:A.value="文件",Q.value=e.title.split(".").pop()||"";break;case 2:A.value="文件夹";break}e.type===1?H.value=e.title.split(".")[0]:H.value=e.title,P.value=!0,console.log("🚀 ~ renameMaterial ~ nowFolder.value: ",$.value)}},ue=async(e,t,x=0,k=0)=>{const B=t===1?"素材":"文件夹";let I="确定要删除该"+B+"吗？";t===2&&x+k>0&&(I="该"+B+"下有"+x+"个文件夹和"+k+"个文件，确定要删除吗？"),Ye.confirm(I,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{var N;const W=await Ze({id:e});M.success(W.message||"删除成功"),await v(((N=f.value[f.value.length-1])==null?void 0:N.path)||0,!0),h("refresh-category")}).catch(()=>{})},ge=(e,t)=>{d.value.page=1,v(e,!0,t)},X=(e,t,x,k)=>{if(t!==f.value.length-1){if(e===0)f.value=[{name:"全部素材",path:0,pid:void 0}],v(0,!0);else{const B=f.value.findIndex(I=>I.path===e);f.value=f.value.slice(0,B+1)}v(e,!0,k)}},fe=m(!1),ie=m(!1),ce=m(!1),_e=m(!1),G=m({url:"",cover:"",title:"",label:"",id:""}),s=e=>{e.filetype===L.value.video?fe.value=!0:e.filetype===L.value.image?ce.value=!0:e.filetype===L.value.audio&&(_e.value=!0),G.value={url:e.url,cover:e.cover||"",title:e.title}},l=async e=>{window.open(`https://apidev.china9.cn/api/bucket/download?id=${e}`,"_blank")},R=(e,t)=>{if(!b.showSelect)return;t.stopPropagation();const x=a.value||[],k=e.id;if(x.includes(k)){const I=x.filter(W=>W!==k);a.value=I}else b.multiSelect?a.value=[...x,k]:a.value=[k]},z=e=>{ie.value=!0,G.value={id:e.id,url:e.url,cover:e.cover,title:e.title,label:e.label}};return y({materials:T,getMaterials:v,lastBreadcrumb:U}),(e,t)=>{var Le,ze;const x=ct,k=ut,B=Be,I=He,W=Oe,N=Ke,me=Ne,$e=dt,ke=pt,Ae=Se;return ye((_(),S("div",cl,[u(k,{separator:"/",class:"px-[20px]"},{default:p(()=>[(_(!0),S(re,null,be(f.value,(i,q)=>(_(),O(x,{key:i.path,class:de({"cursor-pointer":q!==f.value.length-1}),onClick:na=>X(i.path,q,i.pid,i.name)},{default:p(()=>[j(ae(i.name),1)]),_:2},1032,["class","onClick"]))),128))]),_:1}),o("div",dl,[u($e,{modelValue:a.value,"onUpdate:modelValue":t[4]||(t[4]=i=>a.value=i),class:"material-grid"},{default:p(()=>[E.value?J("",!0):(_(),S("div",{key:0,class:"material-item add-folder",onClick:w},[o("div",pl,[u(B,null,{default:p(()=>[u(D(xe))]),_:1})]),t[12]||(t[12]=o("div",{class:"material-info"},[o("div",{class:"material-name"},"添加文件夹")],-1))])),(_(!0),S(re,null,be(D(T),i=>(_(),S(re,{key:i.id},[i.type===2?(_(),S("div",{key:0,class:de(["material-item",{selected:a.value&&Array.isArray(a.value)&&a.value.includes(i.id)}]),onClick:q=>R(i,q)},[e.showSelect?ye((_(),S("div",{key:0,class:"select-mask",onClick:t[0]||(t[0]=ne(()=>{},["stop"]))},[u(I,{value:i.id},null,8,["value"])],512)),[[De,e.selectAll||a.value&&Array.isArray(a.value)&&a.value.includes(i.id)]]):J("",!0),o("div",{class:"folder-icon max-w-[104px] h-[92px]",onClick:ne(q=>ge(i.id,i.title),["stop"])},t[13]||(t[13]=[o("img",{src:Nt,alt:"文件夹"},null,-1)]),8,fl),o("div",ml,[o("div",yl,ae(i.title),1),o("div",gl,[o("span",_l,ae(i.flod_num),1),t[14]||(t[14]=j("文件夹·")),o("span",hl,ae(i.file_num),1),t[15]||(t[15]=j("文件 "))])]),o("div",{class:"material-actions",onClick:t[1]||(t[1]=ne(()=>{},["stop"]))},[u(me,{trigger:"click"},{dropdown:p(()=>[u(N,null,{default:p(()=>[u(W,{class:"text-[--el-color-primary] text-[16px] justify-center",onClick:q=>oe(i)},{default:p(()=>t[16]||(t[16]=[j("重命名 ")])),_:2},1032,["onClick"]),u(W,{class:"text-[#555] text-[16px] justify-center",onClick:q=>ue(i.id,i.type,i.flod_num,i.file_num)},{default:p(()=>t[17]||(t[17]=[j("删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),default:p(()=>[u(B,{class:"more-icon rotate-90",size:"20"},{default:p(()=>[u(D(Ie))]),_:1})]),_:2},1024)])],10,vl)):(_(),S("div",{key:1,class:de(["material-item",{selected:a.value&&Array.isArray(a.value)&&a.value.includes(i.id)}]),onClick:q=>R(i,q)},[e.showSelect?ye((_(),S("div",{key:0,class:"select-mask",onClick:t[2]||(t[2]=ne(()=>{},["stop"]))},[u(I,{value:i.id},null,8,["value"])],512)),[[De,e.selectAll||a.value&&Array.isArray(a.value)&&a.value.includes(i.id)]]):J("",!0),i.filetype===1?(_(),S("div",{key:1,class:"video-thumbnail max-w-[104px] h-[92px]",onClick:ne(q=>s(i),["stop"])},[o("img",{src:i.cover||D(_t),alt:"视频缩略图"},null,8,wl),t[18]||(t[18]=o("div",{class:"video-play-icon"},[o("img",{src:ht,alt:""})],-1))],8,kl)):i.filetype===2?(_(),S("div",xl,[o("img",{src:i.url,alt:"图片",onClick:ne(q=>s(i),["stop"])},null,8,Cl)])):i.filetype===3?(_(),S("div",Vl,[o("div",$l,[o("i",{class:"iconfont icon-yinpinwenjian1 text-[80px] text-[#A5BAFD]",onClick:ne(q=>s(i),["stop"])},null,8,Al)])])):J("",!0),o("div",Il,[o("div",Tl,ae(i.title),1),i.filetype===1?(_(),S("div",{key:0,class:de(["material-details",{"!text-[--el-color-success]":i.vision_status===1,"!text-[--el-color-warning]":i.vision_status===0}])},ae(i.vision_status?"分析完成":"分析中"),3)):J("",!0)]),o("div",{class:"material-actions",onClick:t[3]||(t[3]=ne(()=>{},["stop"]))},[u(me,{trigger:"click"},{dropdown:p(()=>[u(N,null,{default:p(()=>[i.type===1?(_(),S(re,{key:0},[i.vision_status===1?(_(),O(W,{key:0,class:"text-[--el-color-primary] text-[16px] justify-center",onClick:q=>z(i)},{default:p(()=>t[19]||(t[19]=[j("详情 ")])),_:2},1032,["onClick"])):(_(),O(W,{key:1,class:"text-[--el-color-primary] text-[16px] justify-center",onClick:q=>oe(i)},{default:p(()=>t[20]||(t[20]=[j("重命名 ")])),_:2},1032,["onClick"]))],64)):(_(),O(W,{key:1,class:"text-[--el-color-primary] text-[16px] justify-center",onClick:q=>oe(i)},{default:p(()=>t[21]||(t[21]=[j("重命名 ")])),_:2},1032,["onClick"])),u(W,{class:"text-[--el-color-primary] text-[16px] justify-center",onClick:q=>l(i.id)},{default:p(()=>t[22]||(t[22]=[j("下载")])),_:2},1032,["onClick"]),u(W,{class:"text-[#555] text-[16px] justify-center",onClick:q=>ue(i.id,i.type)},{default:p(()=>t[23]||(t[23]=[j("删除 ")])),_:2},1032,["onClick"])]),_:2},1024)]),default:p(()=>[u(B,{class:"more-icon rotate-90",size:"20"},{default:p(()=>[u(D(Ie))]),_:1})]),_:2},1024)])],10,bl))],64))),128))]),_:1},8,["modelValue"])]),D(C)>D(d).limit?(_(),O(ke,{key:0,"current-page":D(d).page,"onUpdate:currentPage":t[5]||(t[5]=i=>D(d).page=i),"page-size":D(d).limit,"onUpdate:pageSize":t[6]||(t[6]=i=>D(d).limit=i),"page-sizes":c.value,total:D(C),onSizeChange:Z,onCurrentChange:V},null,8,["current-page","page-size","page-sizes","total"])):J("",!0),u(Xe,{modelValue:P.value,"onUpdate:modelValue":t[7]||(t[7]=i=>P.value=i),"initial-name":H.value,"is-edit":!!((Le=$.value)!=null&&Le.id),nowFolder:$.value,"folder-id":(ze=$.value)==null?void 0:ze.id,"is-folder":!0,title:A.value,loading:K.value,onConfirm:te},null,8,["modelValue","initial-name","is-edit","nowFolder","folder-id","title","loading"]),u(bt,{visible:fe.value,"onUpdate:visible":t[8]||(t[8]=i=>fe.value=i),"video-url":G.value.url,poster:G.value.cover,title:G.value.title},null,8,["visible","video-url","poster","title"]),u(ul,{visible:ie.value,"onUpdate:visible":t[9]||(t[9]=i=>ie.value=i),video:G.value,onRefresh:F},null,8,["visible","video"]),u(qt,{visible:ce.value,"onUpdate:visible":t[10]||(t[10]=i=>ce.value=i),"img-url":G.value.url,title:G.value.title},null,8,["visible","img-url","title"]),u(Jt,{visible:_e.value,"onUpdate:visible":t[11]||(t[11]=i=>_e.value=i),"audio-url":G.value.url,title:G.value.title},null,8,["visible","audio-url","title"])])),[[Ae,D(g)]])}}}),We=ve(Fl,[["__scopeId","data-v-1424ad92"]]),Sl={IMAGE:{size:5*1024*1024,num:5},VIDEO:{size:100*1024*1024,num:100},AUDIO:{size:15*1024*1024,num:15}};class Bl{constructor(){he(this,"queue",[]);he(this,"isProcessing",!1);he(this,"maxRetries",3);he(this,"processingInterval",2e3)}addToQueue(y,r){this.queue.push({id:y,fileName:r,retryCount:0}),this.processQueue()}async processQueue(){if(!(this.isProcessing||this.queue.length===0)){for(this.isProcessing=!0;this.queue.length>0;){const y=this.queue[0];try{console.log(`开始分析视频: ${y.fileName}`);const r=await Bt({id:y.id});console.log(`视频分析完成: ${y.fileName}`,r),this.queue.shift()}catch(r){console.error(`视频分析失败: ${y.fileName}`,r),y.retryCount<this.maxRetries?(y.retryCount++,console.log(`视频 ${y.fileName} 将在稍后重试，当前重试次数: ${y.retryCount}`)):(this.queue.shift(),M.warning(`视频 ${y.fileName} 分析失败，已达到最大重试次数`))}await new Promise(r=>setTimeout(r,this.processingInterval))}this.isProcessing=!1}}}const Ml=new Bl,Ul={class:"h-full"},El={class:"material-market-list-container h-[calc(100%-60px)]"},Ll={key:0,class:"material-market-container h-full"},zl={class:"content h-full"},Dl={class:"filter-container"},jl={class:"filter-item"},Pl={class:"filter-options flex items-center justify-between pl-[15px]"},Rl={class:"flex items-center"},Ql={key:0,class:"relative"},Wl=["accept"],Nl={class:"content-wrapper h-[calc(100%-68px)]"},Kl={class:"sidebar h-full"},Ol={class:"main-content flex flex-col"},Yl={class:"flex-1 overflow-y-auto"},ql={key:1,class:"material-market-container h-full mt-[10px] pr-[16px]"},Hl={class:"content h-full"},Xl={class:"filter-container"},Zl={class:"filter-item flex !flex-col"},Jl={class:"label !w-full flex mb-[20px]"},Gl={class:"filter-options flex items-center justify-between w-full"},ea={class:"flex items-center"},ta={key:0,class:"relative"},la=["accept"],aa={class:"content-wrapper h-auto"},sa={class:"main-content flex flex-col"},oa={class:"flex-1 overflow-y-auto"},ia=pe({__name:"List",props:{activeIndex:{default:"material"},from:{default:"material"},type:{default:"all"},alertType:{default:"import"},multiSelect:{type:Boolean,default:!0}},emits:["update:activeIndex","select"],setup(n,{expose:y,emit:r}){const d=n,T=r,g=ee({get(){return d.activeIndex},set(s){T("update:activeIndex",s)}}),C=ee({get(){return we.map(s=>({label:s.label,key:s.key,value:s.value}))},set(s){}}),Y=()=>window.innerWidth<=1e3;se(()=>d.alertType,s=>{s==="export"?C.value=we.map(l=>({label:"导出到"+l.label,key:l.key,value:l.value})):C.value=we.map(l=>({label:l.label,key:l.key,value:l.value}))},{immediate:!0});const b=()=>{H.value&&H.value.click()},h=ee(()=>{var s;return(s=C.value.find(l=>l.key===g.value))==null?void 0:s.value}),E=m(),c=m(0),f=()=>{var s;E.value&&((s=E.value)==null||s.getCategories())},U=m(""),L=()=>{var s;v.value&&((s=v.value)==null||s.getMaterials(0,!0))},v=m(),F=m(!1),a=m([]),V=m(!1),Z=s=>{var l;v.value&&(a.value=s?(l=v.value)==null?void 0:l.materials.map(R=>R.id):[],V.value=!1)};se(a,s=>{var z,e,t,x;if(!s||!v.value)return;const l=s.length;F.value=(z=v.value)!=null&&z.materials.length?l===((e=v.value)==null?void 0:e.materials.length):!1,V.value=l>0&&l<((t=v.value)==null?void 0:t.materials.length);let R=(x=v.value)==null?void 0:x.materials.filter(k=>s.includes(k.id));T("select",R)},{immediate:!0});const P=async()=>{var s,l;if(a.value&&a.value.length>0){const R=a.value.join(","),z=await Ze({id:R});M.success(z.message||"删除成功"),await((s=v.value)==null?void 0:s.getMaterials()),await((l=E.value)==null?void 0:l.getCategories()),a.value=[],F.value=!1,V.value=!1}else M.warning("请至少选择一个素材")},H=m(null),A=ee(()=>Ee.reduce((s,l)=>(s[l.key]={value:l.value,mimeType:`${l.key}/*`,label:l.label},s),{})),w=ee(()=>d.type!=="all"&&A.value[d.type]?A.value[d.type].mimeType:Object.values(A.value).map(s=>s.mimeType).join(",")),$=s=>{if(!s)return;const l=s.type.split("/")[0],R=A.value[l];return R?{api:l==="video"?Ft:St,type:R.value}:void 0},K=async(s,l,R)=>{var z,e,t,x,k,B;try{const I=await Ue({title:s.name,category_id:c.value,flod_id:((e=(z=v.value)==null?void 0:z.lastBreadcrumb)==null?void 0:e.path)||0,type:1,filetype:R,bigtype:h.value,url:l.url,cover:l.cover,size:s.size,times:l.times,fbl:l.fbl,fps:l.fps,zhb:l.zhb});M.success(I.message||"上传成功");try{await((k=v.value)==null?void 0:k.getMaterials(((x=(t=v.value)==null?void 0:t.lastBreadcrumb)==null?void 0:x.path)||0,!0)),await((B=E.value)==null?void 0:B.getCategories())}catch(W){console.error("刷新材料列表失败:",W)}return I}catch(I){throw console.error("文件保存失败:",I),M.error("文件保存失败，请重试"),I}},te=m(!1);Me(()=>{te.value&&(te.value=!1)});const Q=async(s,l,R)=>{if(!s||!l)return Promise.reject("无效的文件或API");try{const z=new FormData;z.append("file",s);const e=await l(z);let t={url:e.url,cover:e.cover,times:e.times};R===1?e.url&&(t=e):e&&(t={url:e,cover:e,times:void 0,fbl:void 0,fps:void 0,zhb:void 0});const x=await K(s,t,R);return R===1&&x&&Ml.addToQueue(x,s.name),Promise.resolve()}catch(z){return console.error("文件上传失败:",z),M.error(s.name+"上传失败，请重试"),Promise.reject(z)}},oe=async s=>{var x;const l=s.target;if(!l.files||l.files.length===0)return;if(l.files.length>10){M.warning("最多只能同时上传10个文件"),l.value="";return}const R=Array.from(l.files),z=qe.service({lock:!0,text:"上传中...",background:"rgba(255, 255, 255, 0.7)",target:".el-loading-mask"}),e=[];for(const k of R){const B=k.type.split("/")[0];if(!A.value[B]||!w.value.includes(B+"/*")){M.warning(`不支持的文件类型: ${k.type}`);continue}const I=B.toUpperCase(),W=(x=Sl[I])==null?void 0:x.size;if(!W){console.warn(`未找到文件类型 ${B} 的大小限制配置`);continue}if(k.size>W){M.warning(`文件 ${k.name} 超出大小限制 (${(W/1024/1024).toFixed(0)}MB)`);continue}const N=$(k);if(!(N!=null&&N.api)||!(N!=null&&N.type)){console.warn(`无法确定文件 ${k.name} 的上传配置`);continue}e.push(Q(k,N.api,N.type).catch(me=>(console.error(`处理文件 ${k.name} 失败:`,me),null)))}if(e.length===0){z.close(),l.value="";return}const t=setTimeout(()=>{console.warn("上传超时，强制关闭loading状态"),z.close()},3e4);try{const k=await Promise.all(e);console.log(k,"上传结果")}catch(k){console.error("部分文件上传失败:",k)}finally{clearTimeout(t),z.close(),l.value=""}},ue=s=>{c.value=s},ge=s=>{g.value=s,c.value=0,a.value=[],F.value=!1,V.value=!1,U.value=""},X=ee(()=>v.value?v.value.materials.filter(s=>{var l;return(l=a.value)==null?void 0:l.includes(s.id)}):[]);y({checkedIds:a,checkedItems:X,getFolderId:()=>{var s,l;return((l=(s=v.value)==null?void 0:s.lastBreadcrumb)==null?void 0:l.path)||0},getCategoryId:()=>c.value,refresh:()=>{v.value&&v.value.getMaterials()}}),m(!1);const _e=m(window.innerWidth<=1e3),G=()=>{_e.value=window.innerWidth<=1e3};return vt(()=>{window.addEventListener("resize",G),G()}),ft(()=>{window.removeEventListener("resize",G)}),(s,l)=>{const R=yt,z=mt,e=He,t=Fe,x=gt,k=Te,B=Se;return _(),S("div",Ul,[u(z,{"default-active":g.value,class:"el-menu-demo",mode:"horizontal",onSelect:ge},{default:p(()=>[(_(!0),S(re,null,be(C.value,I=>(_(),O(R,{key:I.key,index:I.key},{default:p(()=>[j(ae(I.label),1)]),_:2},1032,["index"]))),128))]),_:1},8,["default-active"]),o("div",El,[Y()?(_(),S("div",ql,[o("div",Hl,[o("div",Xl,[o("div",Zl,[o("div",Jl,[l[9]||(l[9]=o("span",{class:"mr-[10px] leading-[34px] flex-shirink-0 whitespace-nowrap"},"主题分类：",-1)),u(Qe,{"active-category":c.value,onSelect:ue,"big-type":h.value,"file-type":d.type,ref_key:"categoryListRef",ref:E},null,8,["active-category","big-type","file-type"])]),o("div",Gl,[o("div",ea,[s.multiSelect?(_(),O(e,{key:0,modelValue:F.value,"onUpdate:modelValue":l[3]||(l[3]=I=>F.value=I),label:"全选",size:"large",indeterminate:V.value,onChange:Z},null,8,["modelValue","indeterminate"])):J("",!0),s.from==="material"?(_(),O(x,{key:1,title:"确定要删除选中的素材吗？",onConfirm:P},{reference:p(()=>[u(t,{link:"",class:"batch-delete-btn ml-[20px]",icon:D(je)},{default:p(()=>l[10]||(l[10]=[j("批量删除")])),_:1},8,["icon"])]),_:1})):J("",!0),u(k,{modelValue:U.value,"onUpdate:modelValue":l[4]||(l[4]=I=>U.value=I),class:de(["search-input",s.multiSelect?"ml-[32px]":""]),placeholder:"请输入关键词（按回车搜索）",clearable:"","prefix-icon":D(Pe),size:"large",onKeyup:Ce(L,["enter"])},null,8,["modelValue","class","prefix-icon"])]),s.alertType==="import"?(_(),S("div",ta,[o("input",{type:"file",ref_key:"fileInput",ref:H,onChange:oe,class:"absolute inset-0 opacity-0 cursor-pointer",accept:w.value,multiple:!0,style:{display:"none"}},null,40,la),u(t,{type:"primary",class:"upload-btn w-[120px] h-[38px]",onClick:b},{default:p(()=>[l[11]||(l[11]=o("i",{class:"iconfont mr-1 icon-shangchuan text-[18px]"},null,-1)),j(" "+ae(s.from==="material"?"上传":"上传素材"),1)]),_:1})])):J("",!0)])])]),o("div",aa,[o("div",sa,[o("div",oa,[ye(u(We,{"active-category":c.value,"search-keyword":U.value,"checked-items":a.value,"onUpdate:checkedItems":l[5]||(l[5]=I=>a.value=I),ref_key:"materialListRef",ref:v,"show-select":d.alertType==="import","select-all":F.value,"big-type":h.value,"file-type":d.type,"multi-select":s.multiSelect,onRefreshCategory:f,"alert-type":d.alertType,from:d.from},null,8,["active-category","search-keyword","checked-items","show-select","select-all","big-type","file-type","multi-select","alert-type","from"]),[[B,te.value]])])])])])])):(_(),S("div",Ll,[o("div",zl,[o("div",Dl,[o("div",jl,[l[8]||(l[8]=o("div",{class:"label"},[o("span",null,"主题分类：")],-1)),o("div",Pl,[o("div",Rl,[s.multiSelect?(_(),O(e,{key:0,modelValue:F.value,"onUpdate:modelValue":l[0]||(l[0]=I=>F.value=I),label:"全选",size:"large",indeterminate:V.value,onChange:Z},null,8,["modelValue","indeterminate"])):J("",!0),s.from==="material"?(_(),O(x,{key:1,title:"确定要删除选中的素材吗？",onConfirm:P},{reference:p(()=>[u(t,{link:"",class:"batch-delete-btn ml-[20px]",icon:D(je)},{default:p(()=>l[6]||(l[6]=[j("批量删除")])),_:1},8,["icon"])]),_:1})):J("",!0),u(k,{modelValue:U.value,"onUpdate:modelValue":l[1]||(l[1]=I=>U.value=I),class:de(["search-input",s.multiSelect?"ml-[32px]":""]),placeholder:"请输入关键词（按回车搜索）",clearable:"","prefix-icon":D(Pe),size:"large",onKeyup:Ce(L,["enter"])},null,8,["modelValue","class","prefix-icon"])]),s.alertType==="import"?(_(),S("div",Ql,[o("input",{type:"file",ref_key:"fileInput",ref:H,onChange:oe,class:"absolute inset-0 opacity-0 cursor-pointer",accept:w.value,multiple:!0,style:{display:"none"}},null,40,Wl),u(t,{type:"primary",class:"upload-btn w-[120px] h-[38px]",onClick:b},{default:p(()=>[l[7]||(l[7]=o("i",{class:"iconfont mr-1 icon-shangchuan text-[18px]"},null,-1)),j(" "+ae(s.from==="material"?"上传":"上传素材"),1)]),_:1})])):J("",!0)])])]),o("div",Nl,[o("div",Kl,[u(Qe,{"active-category":c.value,onSelect:ue,"big-type":h.value,"file-type":d.type,ref_key:"categoryListRef",ref:E},null,8,["active-category","big-type","file-type"])]),o("div",Ol,[o("div",Yl,[ye(u(We,{"active-category":c.value,"search-keyword":U.value,"checked-items":a.value,"onUpdate:checkedItems":l[2]||(l[2]=I=>a.value=I),ref_key:"materialListRef",ref:v,"show-select":d.alertType==="import","select-all":F.value,"big-type":h.value,"file-type":d.type,"multi-select":s.multiSelect,onRefreshCategory:f,"alert-type":d.alertType,from:d.from},null,8,["active-category","search-keyword","checked-items","show-select","select-all","big-type","file-type","multi-select","alert-type","from"]),[[B,te.value]])])])])])]))])])}}}),ha=ve(ia,[["__scopeId","data-v-ed1658c8"]]);export{Sl as F,ha as L,St as a,Ue as b,Ft as c,Bt as d,Je as e,Ee as f,Ge as g,we as m,Gt as u,Ml as v};
