import{k as F,A as p,l as $,B as K,c as m,b as u,q as N,a as o,al as R,w as r,aA as U,e as H,h as I,J as q,f as D,F as G,a5 as J,P as M,p as O,X as Q,o as c,ay as X,j as g,y as W,_ as Y}from"./index-BBeD0eDz.js";/* empty css                *//* empty css              */import{_ as Z}from"./index-BUwi-MV-.js";/* empty css               *//* empty css                    *//* empty css                     */import{E as ee}from"./empty-CSpEo1eL.js";const te={class:"h-full flex flex-col"},ae={class:"flex flex-col items-center mb-[10px]"},le={class:"w-full flex items-center justify-between mt-[10px]"},oe={class:"left-wrap"},se={class:"flex items-center"},ne={class:"w-[300px]"},ie={class:"flex-1 flex flex-col overflow-hidden"},ue=F({__name:"history",props:{listApi:{type:Function,default:null},delApi:{type:Function,default:null},copyApi:{type:Function,default:null},searchKey:{type:String,default:"title"},field:{type:Object,default:()=>({})},timeText:{type:String,default:""},editPath:{type:String,default:""},videoPath:{type:String,default:""},showCopy:{type:Boolean,default:!1},limit:{type:Number,default:14}},emits:["refresh","click"],setup(s,{emit:h}){const l=s,x=h,v=W(),d=p(!1),f=p(0),t=p({status:2,perPage:l.limit,page:1}),_=p([]),n=async()=>{d.value=!0;try{if(!l.listApi)return;const e=await l.listApi(t.value);console.log(e,"历史列表"),_.value=e.data,f.value=e.total,t.value.page>e.last_page&&(t.value.page=e.last_page,await n())}catch(e){console.log(e)}finally{d.value=!1}},C=$(()=>l.timeText?l.timeText:t.value.status===1?"最后编辑时间":"生成时间"),w=e=>{t.value.status=e,n()},A=e=>{t.value.perPage=e,n()},P=e=>{t.value.page=e,n()},b=(e=!0)=>{e&&(t.value.page=1,t.value[l.searchKey]=""),n()},k=e=>{t.value.status===1?v.push(`${l.editPath}${e.id}`):v.push(`${l.videoPath}${e.id}`),x("click",e)};return K(()=>{n()}),(e,a)=>{const y=X,E=R,S=H,V=I,B=Z,T=J,j=M,L=Q,z=q;return c(),m("div",te,[u("div",ae,[o(E,{modelValue:t.value.status,"onUpdate:modelValue":a[0]||(a[0]=i=>t.value.status=i),onChange:w},{default:r(()=>[o(y,{value:2},{default:r(()=>a[4]||(a[4]=[g("已生成项目")])),_:1}),o(y,{value:1},{default:r(()=>a[5]||(a[5]=[g("草稿")])),_:1})]),_:1},8,["modelValue"]),u("div",le,[u("div",oe,[U(e.$slots,"search-left",{},void 0,!0)]),u("div",se,[u("div",ne,[o(S,{modelValue:t.value[s.searchKey],"onUpdate:modelValue":a[1]||(a[1]=i=>t.value[s.searchKey]=i),placeholder:"请输入项目名称",clearable:""},null,8,["modelValue"])]),o(V,{class:"ml-[10px]",type:"primary",icon:"Search",onClick:n},{default:r(()=>a[6]||(a[6]=[g("搜索")])),_:1})])])]),N((c(),m("div",ie,[f.value>0?(c(),m(G,{key:0},[o(T,{class:"flex-1"},{default:r(()=>[o(B,{projectList:_.value,timeText:C.value,onClick:k,field:s.field,delApi:s.delApi,copyApi:s.copyApi,onRefresh:b,showCopy:s.showCopy},null,8,["projectList","timeText","field","delApi","copyApi","showCopy"])]),_:1}),o(j,{"current-page":t.value.page,"onUpdate:currentPage":a[2]||(a[2]=i=>t.value.page=i),"page-size":t.value.perPage,"onUpdate:pageSize":a[3]||(a[3]=i=>t.value.perPage=i),total:f.value,onSizeChange:A,onCurrentChange:P},null,8,["current-page","page-size","total"])],64)):(c(),D(L,{key:1,class:"flex-1",description:"暂无记录",image:O(ee),"image-size":293},null,8,["image"]))])),[[z,d.value]])])}}}),_e=Y(ue,[["__scopeId","data-v-f0af8ad4"]]);export{_e as H};
