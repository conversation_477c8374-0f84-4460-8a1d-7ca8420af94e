webpackJsonp([31],{"8JQp":function(e,t,s){"use strict";var n={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"title flex flex-align-center flex-between"},[t("h1",[this._v("管理智能体-工作内容设置")]),this._v(" "),this._t("default")],2)},staticRenderFns:[]};var a=s("VU/8")({name:"Header"},n,!1,function(e){s("avPf")},"data-v-985ae7f8",null);t.a=a.exports},"M+KZ":function(e,t,s){"use strict";var n={name:"layout",components:{Header:s("8JQp").a},methods:{}},a={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"work-content-settings-index"},[t("Header",[this._t("header")],2),this._v(" "),this.$slots.breadcrumb?t("div",{staticClass:"breadcrumb-wrap"},[this._t("breadcrumb")],2):this._e(),this._v(" "),t("div",{staticClass:"work-content-wrap"},[t("div",{staticClass:"work-card"},[this._t("body")],2)])],1)},staticRenderFns:[]};var r=s("VU/8")(n,a,!1,function(e){s("zUSS"),s("rT6D")},"data-v-52a05615",null);t.a=r.exports},avPf:function(e,t){},nsWc:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=s("Xxa5"),a=s.n(n),r=s("exGp"),i=s.n(r),o=s("fZjL"),c=s.n(o),l=s("Dd8w"),u=s.n(l),d=s("NYxO"),p=s("8JQp"),g=s("pI5c"),h=s("zL8q"),f=s("FCNb"),m=s("mW/y"),v=s("M+KZ"),b={name:"positionSettings",components:{Header:p.a,YunpanPermission:f.a,Layout:v.a},data:function(){return{needAlert:0,userData:[],OrgPersonTree:{},selectUserVisible:!1,permkey:0,selectUserData:[],selectedRow:{},submitLoading:!1,page:{page:1,perPage:10},total:0,loading:!1,showAdd:!1,addLoading:!1,delBatchIds:[],delBatchLoading:!1}},computed:u()({},Object(d.e)(["user"]),{companyInfo:function(){return this.user.companyInfo},selectedUserIds:function(){return this.selectUserData.length?this.selectUserData.map(function(e){return e.id}):[]},selectedUserUniqueIds:function(){return this.selectUserData.length?this.selectUserData.map(function(e){return e.user_unique_id}):[]},selectedUserNames:function(){return this.selectUserData.length?this.selectUserData.map(function(e){return e.name}).join("、"):""}}),methods:{getUsers:function(e){var t=this,s=this;this.selectedRow=e,0===c()(this.OrgPersonTree).length?(this.loadingInstance=h.Loading.service({text:"加载中",target:".table-wrap"}),Object(g._1)().then(function(e){200===e.code?(s.OrgPersonTree=e.data[0],s.selectUserVisible=!0):console.log(e),t.loadingInstance.close()})):s.selectUserVisible=!0},getSelectUser:function(e){this.selectUserData=e},submitUser:function(){var e=this;return i()(a.a.mark(function t(){var s,n;return a.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e.submitLoading=!0,e.$set(e.selectedRow,"user",e.selectedUserNames||"暂无分配"),e.$set(e.selectedRow,"ids",e.selectedUserIds),e.$set(e.selectedRow,"userIds",e.selectedUserUniqueIds),s={gw_id:e.selectedRow.position,users:e.selectUserData.map(function(e){return{id:e.id,user_unique_id:e.user_unique_id}})},t.next=7,Object(m.C)(s);case 7:n=t.sent,200===n.code?e.$message.success("分配成功"):e.$message.error(n.message||"分配失败"),e.submitLoading=!1,e.selectUserVisible=!1;case 12:case"end":return t.stop()}},t,e)}))()},getPositionEmployee:function(){var e=this;return i()(a.a.mark(function t(){var s,n,r;return a.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,e.loading=!0,t.next=4,Object(m.u)(e.page);case 4:s=t.sent,e.loading=!1,n=s.code,r=s.data,200===n&&(e.total=r.total,r.data&&(e.userData=r.data.map(function(e){return{position:e.id,name:e.name,userlist:e.userlist,user:e.userlist.length?e.userlist.map(function(e){return e.name}).join("、"):"暂无分配",ids:e.userlist.map(function(e){return e.user_id}),userIds:e.userlist.map(function(e){return e.user_unique_id})}}))),t.next=13;break;case 10:t.prev=10,t.t0=t.catch(0),console.log(t.t0);case 13:return t.prev=13,e.loading=!1,t.finish(13);case 16:case"end":return t.stop()}},t,e,[[0,10,13,16]])}))()},handleSizeChange:function(e){this.page.perPage=e,this.getPositionEmployee()},handleCurrentChange:function(e){this.page.page=e,this.getPositionEmployee()},addPosition:function(e){e&&(this.selectedRow=e),this.showAdd=!0},cancelAdd:function(){this.showAdd=!1,this.selectedRow={}},savePosition:function(){var e=this;return i()(a.a.mark(function t(){var s,n,r,i,o,c,l;return a.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(s=e.selectedRow,n=s.name,r=s.position,n){t.next=4;break}return e.$message.warning("请输入岗位名称"),t.abrupt("return");case 4:return i=r?m.D:m.c,t.prev=5,e.addLoading=!0,t.next=9,i({name:n,id:r});case 9:o=t.sent,c=o.code,l=o.msg,200===c?(e.$message.success("添加成功"),e.getPositionEmployee(),e.showAdd=!1,e.selectedRow={}):e.$message.error(l||"添加失败"),t.next=17;break;case 14:t.prev=14,t.t0=t.catch(5),console.log(t.t0);case 17:return t.prev=17,e.addLoading=!1,t.finish(17);case 20:case"end":return t.stop()}},t,e,[[5,14,17,20]])}))()},delPosition:function(e){var t=this;return i()(a.a.mark(function s(){return a.a.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:if(e.position){s.next=3;break}return t.$message.warning("请选择要删除的岗位"),s.abrupt("return");case 3:if(!t.addLoading){s.next=6;break}return t.$message.warning("请勿重复操作"),s.abrupt("return");case 6:t.$confirm("提示：删除此岗位后，该岗位下已设置的工作内容与已分配的员工将同时删除，确认是否要进行删除？","删除岗位",{confirmButtonText:"确认删除",cancelButtonText:"取消",center:!0,customClass:"work-confirm",confirmButtonClass:"del-btn footer-btn",cancelButtonClass:"cancel-btn footer-btn"}).then(i()(a.a.mark(function s(){var n,r,i;return a.a.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.prev=0,t.addLoading=!0,s.next=4,Object(m.h)({id:e.position});case 4:n=s.sent,r=n.code,i=n.msg,200===r?(t.$message.success("删除成功"),t.getPositionEmployee()):t.$message.error(i||"删除失败"),s.next=12;break;case 9:s.prev=9,s.t0=s.catch(0),console.log(s.t0);case 12:return s.prev=12,t.addLoading=!1,s.finish(12);case 15:case"end":return s.stop()}},s,t,[[0,9,12,15]])}))).catch(function(){});case 7:case"end":return s.stop()}},s,t)}))()},changeAlert:function(e){var t=this;return i()(a.a.mark(function s(){var n,r,i;return a.a.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return t.needAlert=e,s.prev=1,s.next=4,Object(m.z)({status:e});case 4:n=s.sent,r=n.code,i=n.msg,200===r?t.$message.success("设置成功"):t.$message.error(i||"设置失败"),s.next=12;break;case 9:s.prev=9,s.t0=s.catch(1),console.log(s.t0);case 12:case"end":return s.stop()}},s,t,[[1,9]])}))()},initAlert:function(){var e=this;return i()(a.a.mark(function t(){var s,n,r,i;return a.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(m.A)();case 3:s=t.sent,n=s.code,r=s.msg,i=s.data,200===n?(console.log(i),e.needAlert=i.messageck):e.$message.error(r||"获取失败"),t.next=11;break;case 8:t.prev=8,t.t0=t.catch(0),console.log(t.t0);case 11:case"end":return t.stop()}},t,e,[[0,8]])}))()},handleSelectedChange:function(e){this.delBatchIds=e.map(function(e){return e.position})},delBatch:function(){var e=this;return i()(a.a.mark(function t(){return a.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(0!==e.delBatchIds.length){t.next=3;break}return e.$message.warning("请选择要删除的岗位"),t.abrupt("return");case 3:if(!e.delBatchLoading){t.next=6;break}return e.$message.warning("请勿重复操作"),t.abrupt("return");case 6:e.$confirm("提示：删除此岗位后，该岗位下已设置的工作内容与已分配的员工将同时删除，确认是否要进行删除？","删除岗位",{confirmButtonText:"确认删除",cancelButtonText:"取消",center:!0,customClass:"work-confirm",confirmButtonClass:"del-btn footer-btn",cancelButtonClass:"cancel-btn footer-btn"}).then(i()(a.a.mark(function t(){var s,n,r;return a.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,e.delBatchLoading=!0,t.next=4,Object(m.j)({id:e.delBatchIds.join(",")});case 4:s=t.sent,n=s.code,r=s.msg,200===n?(e.$message.success("删除成功"),e.getPositionEmployee()):e.$message.error(r||"删除失败"),t.next=13;break;case 9:t.prev=9,t.t0=t.catch(0),console.log(t.t0),e.$message.error("删除失败");case 13:return t.prev=13,e.delBatchLoading=!1,t.finish(13);case 16:case"end":return t.stop()}},t,e,[[0,9,13,16]])}))).catch(function(){});case 7:case"end":return t.stop()}},t,e)}))()}},mounted:function(){this.getPositionEmployee(),this.initAlert()}},w={render:function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("Layout",{staticClass:"position-settings"},[s("template",{slot:"breadcrumb"},[s("div",{staticClass:"breadcrumb"},[s("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},[s("el-breadcrumb-item",{attrs:{to:{path:"/console/workContentSettings"}}},[e._v("管理智能体-工作内容设置")]),e._v(" "),s("el-breadcrumb-item",[e._v("岗位分配与编辑")])],1)],1)]),e._v(" "),s("template",{slot:"header"},[s("div",{staticClass:"flex-align-center"},[s("div",{staticClass:"flex-align-center alert-setting"},[s("p",[e._v("未完成任务时云经理提醒")]),e._v(" "),s("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:e.changeAlert},model:{value:e.needAlert,callback:function(t){e.needAlert=t},expression:"needAlert"}}),e._v(" "),s("el-button",{staticClass:"self-primary-btn",attrs:{type:"primary"},on:{click:function(){e.addPosition()}}},[e._v("添加岗位")]),e._v(" "),s("el-dialog",{attrs:{title:"岗位名称",visible:e.showAdd,width:"400px","custom-class":"add-alert work-alert"},on:{"update:visible":function(t){e.showAdd=t}}},[s("el-input",{staticClass:"add-input",attrs:{placeholder:"请输入自定义岗位名称"},model:{value:e.selectedRow.name,callback:function(t){e.$set(e.selectedRow,"name",t)},expression:"selectedRow.name"}}),e._v(" "),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{staticClass:"cancel-btn footer-btn",on:{click:e.cancelAdd}},[e._v("取 消")]),e._v(" "),s("el-button",{staticClass:"save-btn footer-btn",attrs:{type:"primary",loading:e.addLoading},on:{click:e.savePosition}},[e._v("确认"+e._s(e.selectedRow.position?"修改":"添加"))])],1)],1)],1)])]),e._v(" "),s("template",{slot:"body"},[s("div",{staticClass:"table-wrap"},[s("div",{staticClass:"operation"},[s("el-button",{staticClass:"del_btn",attrs:{type:"danger",size:"small",loading:e.delBatchLoading},on:{click:e.delBatch}},[e._v("批量删除")])],1),e._v(" "),s("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.userData,"header-cell-class-name":"header-cell-class","cell-class-name":"cell-class",height:"calc(100% - 52px)"},on:{"selection-change":e.handleSelectedChange}},[s("el-table-column",{attrs:{type:"selection",width:"55"}}),e._v(" "),s("el-table-column",{attrs:{prop:"name",label:"工作岗位"}}),e._v(" "),s("el-table-column",{attrs:{prop:"user",label:"当前岗位员工"}}),e._v(" "),s("el-table-column",{attrs:{label:"操作",width:"274"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[s("el-button",{staticClass:"action-btn",attrs:{type:"text"},on:{click:function(t){return e.getUsers(n)}}},[e._v("分配")]),e._v(" "),s("el-button",{staticClass:"action-btn",attrs:{type:"text"},on:{click:function(t){return e.addPosition(n)}}},[e._v("编辑岗位")]),e._v(" "),s("el-button",{staticClass:"action-btn",attrs:{type:"text"},on:{click:function(t){return e.delPosition(n)}}},[e._v("删除岗位")])]}}])})],1)],1),e._v(" "),s("div",{staticStyle:{"text-align":"center","margin-top":"20px",height:"auto"}},[s("el-pagination",{attrs:{"current-page":e.page.page,"page-sizes":[e.page.perPage,2*e.page.perPage,3*e.page.perPage,4*e.page.perPage],"page-size":e.page.perPage,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),s("el-dialog",{attrs:{title:"分配人员",visible:e.selectUserVisible,width:"50%","custom-class":"self"},on:{"update:visible":function(t){e.selectUserVisible=t}}},[e.OrgPersonTree&&e.OrgPersonTree.child?s("yunpan-permission",{key:e.permkey,attrs:{inidata:e.selectedRow.ids,OrgPersonTree:e.OrgPersonTree},on:{changeCheckItems:e.getSelectUser}}):e._e(),e._v(" "),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{attrs:{size:"small"},on:{click:function(t){e.selectUserVisible=!1}}},[e._v("取 消")]),e._v(" "),s("el-button",{attrs:{type:"primary",size:"small",loading:e.submitLoading},on:{click:e.submitUser}},[e._v("确 定")])],1)],1)],1)],2)},staticRenderFns:[]};var _=s("VU/8")(b,w,!1,function(e){s("zARu")},"data-v-f197c4ea",null);t.default=_.exports},rT6D:function(e,t){},zARu:function(e,t){},zUSS:function(e,t){}});