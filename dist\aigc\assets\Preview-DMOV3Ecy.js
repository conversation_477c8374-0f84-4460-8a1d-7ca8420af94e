import{_ as N,V as R}from"./video-cover-DBcJ77EJ.js";import{_ as T}from"./PageBack-BpcOadQW.js";import{I as U}from"./ImportFromMaterial-lo8a_mzn.js";import{k as D,A as a,C as L,B as J,y as S,c as v,a as r,b as u,w as p,Q as m,o as f,m as W,F as H,h as O,j as x,p as $,a4 as z,_ as G}from"./index-BBeD0eDz.js";/* empty css               */import{_ as Q}from"./video-Bj5f7V7O.js";import{s as q}from"./aiVideo-B86MWzMI.js";import{i as K,e as X,d as Y}from"./videoClip-Crxw3Jsi.js";import"./request-Ciyrqj7N.js";import{u as Z}from"./List-CZfQXqH2.js";/* empty css               *//* empty css                *//* empty css                *//* empty css                 *//* empty css                *//* empty css                  */const ee={class:"mix-preview flex flex-col h-full"},te={class:"flex flex-wrap items-center gap-2 flex-nowrap"},oe={class:"video-list flex-col h-full mt-4 flex-1 bg-white p-[20px] rounded-[10px] overflow-auto flex justify-center items-center"},ae={key:0,class:"video-item video-loading flex flex-col justify-center items-center cursor-pointer bg-[#F2F6F9] transition-all duration-300 ease-in-out aspect-[9/16] w-full max-w-[360px] sm:w-[360px] m-auto"},se=["src"],le=D({__name:"Preview",setup(re){const i=S(),s=a({}),y=a(0),n=a(),o=a(null),h=a(!1),k=async()=>{try{h.value=!0;const t=await K({id:n.value},!0);if(t.code===200){const e=t.data;s.value=e,y.value=e.status,o.value&&(o.value.postMessage({type:"stop"}),o.value.terminate(),o.value=null),e.status===0&&(o.value=new Worker(new URL(""+new URL("timerWorker-D76cJf-z.js",import.meta.url).href,import.meta.url)),o.value.onmessage=()=>{k()},o.value.postMessage({type:"start",interval:10*1e3}))}else m.error("获取项目详情失败"),i.push("/smart-material/user-portrait-history")}catch(t){console.log(t),m.error("获取项目详情失败"),i.push("/smart-material/user-portrait-history")}finally{h.value=!1}};L(()=>{o.value&&(o.value.postMessage({type:"stop"}),o.value.terminate(),o.value=null)});const g=a(!1),V=async()=>{if(!g.value){g.value=!0;try{await q({id:n.value,name:s.value.name}),m.success("保存成功")}catch(t){console.log(t)}finally{g.value=!1}}},_=a(!1),C=()=>{z.confirm("确定要删除该项目吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{_.value=!0;const t=await Y({id:n.value},!0);console.log("🚀 ~ handleDelete ~ 完整删除接口响应: ",t),m.success(t.message||"删除成功"),i.push("/smart-material/user-portrait-history")}catch(t){console.log(t),m.error("删除失败")}finally{_.value=!1}})},M=()=>{i.push(`/smart-material/precise-edit?id=${n.value}&reEdit=true`)},{publishVideo:P}=Z(),I=async()=>{const t=JSON.parse(JSON.stringify(s.value));t.imgurl=t.coverurl,await P([t])},w=a(!1),j=()=>{w.value=!1};J(()=>{let t=i.currentRoute.value.params.id;t?(n.value=t,k()):i.back()});const b=a(!1),d=a({}),E=()=>{d.value={url:s.value.url,thumbnail:s.value.coverurl,title:s.value.name},b.value=!0};return(t,e)=>{const c=O,F=U,A=T,B=R;return f(),v("div",ee,[r(A,{backText:"精准成片预览",title:s.value.name,"onUpdate:title":e[1]||(e[1]=l=>s.value.name=l),editable:!0,onTitleChange:V},{right:p(()=>[u("div",te,[y.value===1?(f(),v(H,{key:0},[r(c,{type:"danger",loading:_.value,onClick:C,class:"btn-responsive"},{default:p(()=>e[3]||(e[3]=[x("删除")])),_:1},8,["loading"]),r(c,{type:"primary",onClick:M,class:"btn-responsive"},{default:p(()=>e[4]||(e[4]=[x("重新编辑")])),_:1}),r(c,{type:"primary",onClick:I,class:"btn-responsive"},{default:p(()=>e[5]||(e[5]=[x("发布")])),_:1}),r(F,{show:w.value,"onUpdate:show":e[0]||(e[0]=l=>w.value=l),alertType:"export",activeIndex:"video",exportApi:$(X),exportParams:{id:n.value},onClose:j},{default:p(({openDialog:l})=>[r(c,{type:"primary",onClick:l,class:"btn-responsive"},{default:p(()=>e[6]||(e[6]=[x("收藏至素材库")])),_:2},1032,["onClick"])]),_:1},8,["show","exportApi","exportParams"])],64)):W("",!0)])]),_:1},8,["title"]),u("div",oe,[y.value===0?(f(),v("div",ae,e[7]||(e[7]=[u("img",{alt:"",class:"video-icon w-[30%] mb-[24px]",src:Q},null,-1),u("p",{class:"tips-2 text-[#999999] text-[14px] mt-[8px]"},"正在合成视频...",-1)]))):(f(),v("div",{key:1,class:"video-item relative rounded-[10px] overflow-hidden cursor-pointer",onClick:E},[u("img",{src:s.value.coverurl,alt:""},null,8,se),e[8]||(e[8]=u("img",{class:"absolute top-0 bottom-0 right-0 left-0 m-auto",src:N,alt:""},null,-1))]))]),r(B,{visible:b.value,"onUpdate:visible":e[2]||(e[2]=l=>b.value=l),"video-url":d.value.url,poster:d.value.thumbnail,title:d.value.title||"视频播放",onEnded:t.handleVideoEnded},null,8,["visible","video-url","poster","title","onEnded"])])}}}),ke=G(le,[["__scopeId","data-v-830abd68"]]);export{ke as default};
