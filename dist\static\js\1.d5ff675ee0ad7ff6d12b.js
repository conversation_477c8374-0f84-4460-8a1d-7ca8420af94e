webpackJsonp([1],{TU7B:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=a("Dd8w"),r=a.n(s),n=a("NYxO"),i={name:"IframeView",data:function(){return{src:"",loading:!0}},computed:r()({},Object(n.e)(["user","zhUserRole"]),{userInfo:function(){return console.log("userInfo",this.user),this.user&&this.user.memberInfo},companyInfo:function(){return this.user&&this.user.companyInfo}}),created:function(){this.setSrc()},watch:{$route:function(){this.setSrc()}},methods:{setSrc:function(e){var t=this,a=t.$route.meta;t.src="";var s=e;a&&a.iframeUrl&&(s=a.iframeUrl+"?v="+(new Date).getTime(),a.params&&a.params.length&&a.params.forEach(function(e){s+="&"+e.key+"="+t.handleParams(e.value)})),t.loading=!0,t.src=s},handleParams:function(e){switch(e){case"user_uid":return this.userInfo.uid}},handleIframeLoad:function(){var e=this.$refs.iframeRef;console.log("iframe加载完成"),this.loading=!1;try{var t=e.contentDocument;console.log("iframe内容:",t)}catch(e){console.warn("跨域访问被阻止",e)}}}},o={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"iframe-container"},[this.loading?t("div",{staticClass:"loading"},[t("img",{staticClass:"loading-icon",attrs:{src:a("QvV8"),alt:""}}),this._v(" "),t("span",{staticClass:"text"},[this._v("加载中...")])]):this._e(),this._v(" "),t("iframe",{ref:"iframeRef",staticClass:"iframe-wrap",attrs:{src:this.src},on:{load:this.handleIframeLoad}})])},staticRenderFns:[]};var c=a("VU/8")(i,o,!1,function(e){a("kkxA")},"data-v-5d85e95d",null);t.default=c.exports},kkxA:function(e,t){}});