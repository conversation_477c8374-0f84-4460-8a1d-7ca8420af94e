const sdk = window.DouyinOpenJSBridge;
const timestamp = String(parseInt(Date.now() / 1000)); // 类型为 String
const nonceStr = ""; // 生成签名用的随机字符串
const url = location.href;
const clientKey = "awydsjl513vfgkrd"; // 客户端key

// 初始化抖音开放平台
function initDouyinOpen() {
  return new Promise((resolve, reject) => {
    // 请求接口回来签名 /newapi/share/createsign
    $.ajax({
      url: "/newapi/share/createsign",
      type: "POST",
      data: JSON.stringify({ url }),
      contentType: "application/json",
      success: function(result) {
        console.log(result, '获取到的参数');
        if (result.code == 200) {
          const { signature, timestamp, nonce_str } = result.data;
          console.log(signature, timestamp, nonce_str, '签名参数');
          sdk.config({
            params: {
              client_key: clientKey, // clientKey在你的网页应用申请通过后得到
              signature, // 服务端计算的签名，该签名被抖音开放平台验证通过后方可调用jsb方法
              timestamp, // 时间戳
              nonce_str,
              url, // 为应用申请的 JSB 安全域名下的链接，需要携带协议。e.g. https://jsb.security.domain/page.html
            },
            success: res => {
              console.log("抖音开放平台初始化成功-config:", res);
              // 初始化成功回调
            },
          });
          
          sdk.ready(() => {
            // Config Ready回调
            console.log("抖音开放平台初始化成功-ready");
            resolve(true)
          });
          sdk.error((res) => {
            // Config error回调，res示例：{ status_code: 5, status_msg: '错误信息' }
            console.log("抖音开放平台初始化失败-error:", res);
            reject(res)
          });
        } else {
          console.log("抖音开放平台初始化失败");
          reject(res)
        }
      },
      error: function(err) {
        reject(err)
      }
    })
  })
}

// 调用抖音开放平台的登录
function login() {
  return new Promise((resolve, reject) => {
    // 授权JSB
    sdk.showOpenAuth({
      params: {
        client_key: clientKey, // clientKey 在你的网页应用申请通过后得到
        state: "", // 自定义状态，详见下方说明
        scopes: {
          user_info: 0, // 0: 必选；1: 可选，默认不选中； 2: 可选，默认选中
        },
        response_type: "code", // 默认填‘code’，详见下方说明
      },
      success: (result) => {
        const { ticket, response } = result;
        // 授权成功回调
        console.log("抖音开放平台登录成功-showOpenAuth:", { ticket, response });
        console.log("抖音开放平台登录成功-showOpenAuth-result:", result);
        getUserInfo(ticket).then(res => {
          // 成功回调
          resolve(res)
        }).catch(err => {
          // 失败回调
          reject(err)
        })
      },
      error: res => {
        console.log("抖音开放平台登录失败-showOpenAuth:", res);
        // 失败回调
        reject(res)
      }
    });
  })
}

// 获取用户信息
function getUserInfo(code) {
  return new Promise((resolve, reject) => {
    $.ajax({
      url: "/newapi/share/dyuserinfo",
      type: "POST",
      data: JSON.stringify({ code }),
      contentType: "application/json",
      success: function(result) {
        console.log(result, '获取到的用户信息');
        if (result.code == 200) {
          resolve(result.data.data)
        } else {
          reject(result)
        }
      },
      error: function(err) {
        console.log(err, '获取用户信息失败');
        reject(err)
      }
    }) 
  })
}
