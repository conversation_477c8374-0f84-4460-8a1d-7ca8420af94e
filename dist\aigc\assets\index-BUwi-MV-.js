import{k as L,A as k,c as r,o as s,f as y,p as i,X as b,F as P,s as $,b as n,t as v,i as D,m as h,w as _,a as u,$ as F,K as M,a1 as S,aM as I,y as N,Q as o,a4 as w,_ as V}from"./index-BBeD0eDz.js";/* empty css              */import{E as z}from"./empty-CSpEo1eL.js";const R=""+new URL("video-cover-y-CJr19drz.png",import.meta.url).href,J={class:"project-list"},K=["onClick"],O={class:"project-thumbnail"},Q=["src"],U={class:"project-info"},X={class:"project-title"},q={class:"project-meta"},G={key:0,class:"meta-item"},H=L({__name:"index",props:{projectList:{type:Array,default:()=>[]},toPath:{type:String,default:""},field:{type:Object,default:()=>({title:"title",thumbnail:"thumbnail",lastEditTime:"lastEditTime"})},timeText:{type:String,default:"最后编辑时间"},showCopy:{type:Boolean,default:!0},showDelete:{type:Boolean,default:!0},delApi:{type:Function,default:null},copyApi:{type:Function,default:null}},emits:["refresh","click"],setup(e,{emit:C}){const E=N(),l=e,d=C,x=t=>{l.toPath?E.push(`${l.toPath}/${t.id}`):d("click",t)},f=k(!1),B=t=>{if(console.log("复制项目",t),f.value)return o.warning("正在复制中...");if(!l.copyApi)return o.warning("复制接口未实现");w.confirm("确定要复制该项目吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{f.value=!0;try{await l.copyApi({id:t}),o.success("复制成功"),d("refresh")}catch{}finally{f.value=!1}}).catch(()=>{})},m=k(!1),T=t=>{if(console.log("删除项目",t),m.value)return o.warning("正在删除中...");if(!l.delApi)return o.warning("删除接口未实现");w.confirm("确定要删除该项目吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{m.value=!0;try{await l.delApi({id:t}),o.success("删除成功"),d("refresh",!1)}catch{}finally{m.value=!1}}).catch(()=>{})};return(t,c)=>{const j=b,p=M;return s(),r("div",J,[e.projectList.length===0?(s(),y(j,{key:0,class:"w-full",description:"暂无记录",image:i(z),"image-size":293},null,8,["image"])):(s(!0),r(P,{key:1},$(e.projectList,(a,A)=>(s(),r("div",{key:A,class:"project-item",onClick:g=>x(a)},[n("div",O,[n("img",{src:a[e.field.thumbnail]||i(R),alt:"项目缩略图"},null,8,Q)]),n("div",U,[n("div",X,v(a[e.field.title]),1),n("div",{class:"project-actions",onClick:c[0]||(c[0]=D(()=>{},["stop"]))},[e.showCopy?(s(),y(p,{key:0,onClick:g=>B(a.id)},{default:_(()=>[u(i(F))]),_:2},1032,["onClick"])):h("",!0),e.showDelete?(s(),y(p,{key:1,onClick:g=>T(a.id)},{default:_(()=>[u(i(S))]),_:2},1032,["onClick"])):h("",!0)])]),n("div",q,[a[e.field.lastEditTime]?(s(),r("div",G,[u(p,null,{default:_(()=>[u(i(I))]),_:1}),n("span",null,v(a[e.field.lastEditTime]),1)])):h("",!0)])],8,K))),128))])}}}),ee=V(H,[["__scopeId","data-v-35683707"]]);export{ee as _};
