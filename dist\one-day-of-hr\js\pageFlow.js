/* 
pageFlow 页面流程配置
  title 模块页面标题
  bg 页模块面背景图，没有特殊设置可不写
  icon 模块图标
  btnText 模块按钮文字
  desc 模块描述
  type 模块类型 clock: 考勤打卡 job: 人才招聘 approve: 审批 report: 日报
  children 模块子页面配置(流程)
    title 子页面标题
    img 子页面显示界面
    desc 子页面龙龙提示语
    left 子页面手指显示的左边距离
    top 子页面手指显示的顶部距离
    tips 子页面手指显示的提示语
    areaW 子页面可点击区域宽度
    areaH 子页面可点击区域高度
    areaL 子页面可点击区域左边距离
    areaT 子页面可点击区域顶部距离
*/

var pageFlow = [
  {
    title: '考勤打卡',
    bg: './images/clock-bg.png',
    icon: './images/clock.png',
    btnText: '点击进行打卡',
    desc: '“早上，公司的HR王蕾来到公司开始一天的工作，她拿出手机进行上班打卡。”',
    type: 'clock',
    children: [
      {
        title: '进入打卡',
        img: './images/2.png',
        desc: '早上，王蕾在公司拿出手机进行上班打卡',
        left: 90,
        top: 560,
        tips: '点击进入考勤打卡',
        /* 点击区域配置 */
        areaW: 70,
        areaH: 100,
        areaL: 62,
        areaT: 520
      },
      {
        title: '打卡界面',
        img: './images/3.png',
        desc: '进入考勤界面可在打卡范围内一键打卡，当月考勤情况也一目了然。同时可以查看到公司员工的在司时长，了解员工勤奋度情况。',
        left: 140,
        top: 390,
        tips: '点击进入考勤统计',
        /* 点击区域配置 */
        areaW: 400,
        areaH: 60,
        areaL: 62,
        areaT: 360
      },
      {
        title: '我的考勤',
        img: './images/4.png',
        desc: '王蕾进入考勤统计查看个人详细的考勤信息',
        left: 250,
        top: 150,
        tips: '点击切换团队考勤',
        /* 点击区域配置 */
        areaW: 140,
        areaH: 50,
        areaL: 190,
        areaT: 120
      },
      {
        title: '团队考勤',
        img: './images/5.png',
        desc: '之后作为HR，王蕾查看了团队考勤统计',
        left: 400,
        top: 150,
        tips: '点击切换员工考勤',
        /* 点击区域配置 */
        areaW: 140,
        areaH: 50,
        areaL: 330,
        areaT: 120
      },
      {
        title: '员工考勤',
        img: './images/6.png',
        desc: '并对各个员工的考勤情况查看了解',
        left: 200,
        top: 660,
        tips: '点击查看员工考勤详情',
        /* 点击区域配置 */
        areaW: 400,
        areaH: 90,
        areaL: 62,
        areaT: 610
      },
      {
        title: '员工考勤详情',
        img: './images/7.png',
        desc: '查看完成后，王蕾开始一天忙碌的工作',
        left: 68,
        top: 96,
        tips: '点击返回',
        /* 点击区域配置 */
        areaW: 40,
        areaH: 40,
        areaL: 50,
        areaT: 80
      }
    ]
  },
  {
    title: '人才招聘',
    icon: './images/job.png',
    btnText: '点击查看面试信息',
    desc: '“上午，HR王蕾收到了新的面试信息”',
    type: 'job',
    num: 1,
    children: [
      {
        title: '面试信息',
        img: './images/9.png',
        desc: '面试投递信息统计，进入查看详情',
        left: 140,
        top: 360,
        tips: '点击进入面试列表',
        /* 点击区域配置 */
        areaW: 120,
        areaH: 130,
        areaL: 68,
        areaT: 290
      },
      {
        title: '面试列表',
        img: './images/10.png',
        desc: '面试人员列表展示',
        left: 260,
        top: 640,
        tips: '点击进入面试详情',
        /* 点击区域配置 */
        areaW: 112,
        areaH: 170,
        areaL: 210,
        areaT: 580
      },
      {
        title: '面试信息详情',
        img: './images/11.png',
        desc: '展示面试人员详细信息，下划查看更多',
        left: 360,
        top: 180,
        tips: '点击查看测评报告',
        /* 点击区域配置 */
        areaW: 220,
        areaH: 48,
        areaL: 260,
        areaT: 160
      },
      {
        title: '测评报告',
        img: './images/12.png',
        desc: '系统会根据面试人员答题情况进行自动生成分析报告并推荐人才岗位',
        left: 160,
        top: 180,
        tips: '切换面试信息',
        /* 点击区域配置 */
        areaW: 220,
        areaH: 48,
        areaL: 40,
        areaT: 160
      },
      {
        title: '填写评语',
        img: './images/13.png',
        desc: '根据面试者信息，填写面试评语',
        left: 240,
        top: 784,
        tips: '点击提交评语',
        /* 点击区域配置 */
        areaW: 358,
        areaH: 48,
        areaL: 82,
        areaT: 764
      },
      {
        title: '快速入职',
        img: './images/14.png',
        desc: '可针对当前人才进行面试情况标记，通过面试者可分配部门快速入职',
        left: 240,
        top: 760,
        tips: '点击确定快速入职',
        /* 点击区域配置 */
        areaW: 72,
        areaH: 48,
        areaL: 216,
        areaT: 734
      }
    ]
  },
  {
    title: '审批',
    icon: './images/approve.png',
    btnText: '点击查看审批信息',
    desc: '“午休过后，王蕾针对审批信息进行了查看处理”',
    type: 'approve',
    num: 22,
    children: [
      {
        title: '审批推送',
        img: './images/16.png',
        desc: '当有申请时，审批人可在手机上收到推送信息，一键点击进行查看',
        left: 250,
        top: 400,
        tips: '点击推送消息',
        /* 点击区域配置 */
        areaW: 400,
        areaH: 62,
        areaL: 62,
        areaT: 360
      },
      {
        title: '审批列表',
        img: './images/17.png',
        desc: '查看当前未审批的信息，点击查看详情',
        left: 280,
        top: 390,
        tips: '点击进入审批详情',
        /* 点击区域配置 */
        areaW: 400,
        areaH: 164,
        areaL: 62,
        areaT: 290
      },
      {
        title: '审批详情',
        img: './images/18.png',
        desc: '查看审批详情后进行审批操作',
        left: 160,
        top: 800,
        tips: '点击同意审批',
        /* 点击区域配置 */
        areaW: 200,
        areaH: 54,
        areaL: 62,
        areaT: 780
      },
      {
        title: '工资发放申请',
        img: './images/19.png',
        desc: '下午，王蕾制作好了上月的工资，通过手机进入工资发放模块',
        left: 420,
        top: 460,
        tips: '点击进入',
        /* 点击区域配置 */
        areaW: 80,
        areaH: 110,
        areaL: 386,
        areaT: 410
      },
      {
        title: '发放详情',
        img: './images/20.png',
        desc: '选择申请单位与申请月份后，系统自动带入制作好的工资信息，一键提交工资发放申请',
        left: 260,
        top: 800,
        tips: '点击提交',
        /* 点击区域配置 */
        areaW: 400,
        areaH: 60,
        areaL: 62,
        areaT: 760
      }
    ]
  },
  {
    title: '日报',
    icon: './images/report.png',
    btnText: '点击填写日报',
    desc: '“下班时间，HR王蕾高效高质量的完成了当日工作，开始填写工作日志”',
    type: 'report',
    num: 0,
    children: [
      {
        title: '工作台',
        img: './images/22.png',
        desc: '点击进入日报模块',
        left: 90,
        top: 400,
        tips: '点击进入',
        /* 点击区域配置 */
        areaW: 80,
        areaH: 80,
        areaL: 62,
        areaT: 370
      },
      {
        title: '日报列表',
        img: './images/23.png',
        desc: '可选择不同的日报类型，进行汇报填写',
        left: 240,
        top: 240,
        tips: '点击进入',
        /* 点击区域配置 */
        areaW: 120,
        areaH: 180,
        areaL: 200,
        areaT: 160
      },
      {
        title: '日报详情',
        img: './images/24.png',
        desc: '日报可上传图片以及附件，并发送给对应汇报人。之后点击【提交】结束今日工作',
        left: 340,
        top: 790,
        tips: '点击提交日报',
        /* 点击区域配置 */
        areaW: 334,
        areaH: 60,
        areaL: 130,
        areaT: 760
      }
    ]
  },
]