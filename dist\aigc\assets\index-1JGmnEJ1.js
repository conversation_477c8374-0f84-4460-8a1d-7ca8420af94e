import{k as N,A as r,V as D,B as P,f as W,o as u,a5 as K,w as g,q as J,a as y,J as X,p as V,c as b,m as U,F as j,s as G,a6 as O,b as a,t as q,i as H,_ as Y,H as T,j as C,h as Q,r as $,n as Z,y as tt,Q as et}from"./index-BBeD0eDz.js";/* empty css               *//* empty css                */import{I as at}from"./ImportFromMaterial-lo8a_mzn.js";import{_ as lt,V as ot}from"./video-cover-DBcJ77EJ.js";import{f as it,u as st}from"./common-DBXWCL9C.js";import{u as rt}from"./List-CZfQXqH2.js";import{A as nt}from"./AIDocument-DJ_OUx-W.js";import{a as ut,s as ct,g as dt}from"./aiVideo-B86MWzMI.js";import{u as mt}from"./aiDocument-d1UPkFF7.js";/* empty css               *//* empty css                */import"./request-Ciyrqj7N.js";/* empty css                 *//* empty css                *//* empty css                  */import"./input-delete-BxT9zbwn.js";import"./ProductSelector-BTeBhjvS.js";import"./ProductDrawer-1eJ7VyQy.js";import"./index-B2D9HeJa.js";import"./AddBtn-KywrLb53.js";import"./aiDocument-BqsEIq7y.js";const pt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAAAXNSR0IArs4c6QAAASdJREFUKFN90bFKA0EUBdB7d1kRA6kELYWAgpV1PkCCWNgItjY7M6lSKtqITSSdhGVnCw1bCLEW1M7CwiKQH9BCi4D4DXlPJiSNsE4zMJy5w5tLzFeWZVtxHJ8C2AWwBuCH5LOILKvqu3PujMF67w8A3AF4EZGBqn6RbERRdANgiWSepqnjPHGsqhfW2qvFS71er1av10sAK6o6sNYO6b0PB6vGmL0FrNoDnohIxzl3/x8uy7IWsEyn02a73X6rwnmebwMYzZJJdtI0rUz23h8CuA44TL9urW1VJRdF8aSqE/b7/c0kScYALo0x3b8XvPcnAM7jON6Z/XNRFPuqOgTwSvJWRD4BbAA4JtkUkSPn3MMMz4tpqGposEUyNPgN4BFA1xjzEcwvUuR7zjvEauUAAAAASUVORK5CYII=",vt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAOhJREFUOE/d0r0uhUEQxvHfFAqJaChEQqLUiM9W4h5cAxKVwkXIqUXLFbgEHy2FSFCKhFAQjUShWCbZNzl5nXP0tttn9p+dZ+YJXaeUMoYLzHTruMdKRLw1epRSljFShXlsY7cFdrCPq6p/JHiGUczhDl8tqLkOYRbXeI9GLaV8ZosR8dILLKVMZMsRMZz1XyCmcIlJFDxjCY9/gTdY/AE268+HOK3awB/T50IXeISTqv0X8Lb62apTzeHkrtP3QI+55HyYsct1PGC1hqMn+IQdnPdJzhr2ImK6HYB1HGC8D/iKjYg4zvo38bRs+4HYwzcAAAAASUVORK5CYII=",At={class:"material-grid"},ft=["src","alt"],ht={class:"material-duration flex items-center justify-between"},gt=["onClick"],_t=["onClick"],yt=N({__name:"VideoMaterial",props:{canAdd:{type:Boolean,default:!0},materialList:{type:Array,default:()=>[]},imageWidth:{type:String,default:"84px"},plusWidth:{type:Number,default:16}},emits:["select","update:materialList","change","update:total"],setup(n,{emit:S}){const{materialList:c,fetchMaterialList:L,listQ:v,total:A,loading:o,loadMore:f}=rt(),w=n,i=r([]),d=r(!1),m=r({url:"",thumbnail:"",name:"",duration:""}),R=l=>{m.value={url:l.url||"",thumbnail:l.cover||"",name:l.title||"",duration:l.times||""},d.value=!0},M=()=>{console.log("视频播放结束")},I=l=>{i.value.splice(l,1),t("update:materialList",i.value)},x=l=>{i.value=[...i.value,...l],i.value=st(i.value),t("update:materialList",i.value),t("change",i.value.map(s=>s.id).join(","))},t=S;D(()=>w.materialList,l=>{i.value=l},{immediate:!0});let e={vision_status:1,type:1,filetype:1,limit:30};v.value={...v.value,...e};const h=r(null),F=({scrollTop:l})=>{const s=h.value;if(!s)return;const _=s.wrapRef;if(!_)return;const k=_.scrollHeight,E=_.clientHeight;l+E>=k-50&&(console.log("触底了"),f())};return P(async()=>{await L(),t("update:materialList",c.value),t("update:total",A.value)}),(l,s)=>{const _=at,k=K,E=X;return u(),W(k,{ref_key:"scrollbarRef",ref:h,class:"material-wrapper h-full",onScroll:F},{default:g(()=>[J((u(),b("div",At,[n.canAdd?(u(),W(_,{key:0,theme:"img","upload-type":"local",label:"",imgWidth:n.imageWidth,plusWidth:n.plusWidth,onSubmit:x,type:"video"},null,8,["imgWidth","plusWidth"])):U("",!0),(u(!0),b(j,null,G(V(c),(p,B)=>(u(),b("div",{key:B,class:"material-item",style:O({width:n.imageWidth,height:n.imageWidth})},[a("img",{src:p.cover,alt:p.title,class:"material-thumbnail"},null,8,ft),a("div",ht,[a("span",null,q(V(it)(p.times)),1),n.canAdd?(u(),b("img",{key:0,class:"cursor-pointer d-icon",onClick:H(z=>I(B),["stop"]),src:vt,alt:""},null,8,gt)):U("",!0)]),a("div",{class:"play-icon",onClick:H(z=>R(p),["stop"])},s[1]||(s[1]=[a("img",{src:lt,alt:""},null,-1)]),8,_t)],4))),128))])),[[E,V(o)]]),y(ot,{visible:d.value,"onUpdate:visible":s[0]||(s[0]=p=>d.value=p),"video-url":m.value.url,poster:m.value.thumbnail,title:m.value.name||"视频播放",onEnded:M},null,8,["visible","video-url","poster","title"])]),_:1},512)}}}),bt=Y(yt,[["__scopeId","data-v-39876188"]]),Lt={key:0,class:"header-left-wrap flex items-center"},Ct={class:"ai-video-container"},St={class:"left-form-area w-2/5 h-full"},wt={class:"right-material-area w-3/5 bg-secondary h-full flex flex-col"},Rt={class:"material-header"},Mt={class:"upload-count"},It={class:"text-[--el-color-primary]"},xt={class:"flex-1 overflow-hidden"},Ft=N({__name:"index",setup(n){const S=mt(),c=tt(),L=r([]),v=r(0),A=r(),o=r({material_id:""}),f=r(!1),w=()=>{o.value.id&&(f.value||(f.value=!0,ut({id:o.value.id}).then(t=>{console.log(t,"生成视频"),c.push("/smart-material/ai-preview/"+o.value.id)}).finally(()=>{f.value=!1})))},i=r(""),d=r(!1),m=async()=>{d.value=!0;try{const t=await ct(o.value);i.value=new Date().toLocaleTimeString(),console.log(t,"保存成功"),t.id&&(o.value.id=t.id)}catch{}finally{d.value=!1}},R=t=>{o.value={...o.value,...t},m()},M=t=>{o.value.material_id=t,m()},I=async t=>{try{const e=await dt({id:t});console.log(e,"项目详情"),e.subtitle==1?e.subtitle=!0:e.subtitle=!1,A.value.setFormData(e)}catch(e){console.log(e),et.error("获取项目详情失败"),c.go(-1)}};D(()=>c.currentRoute.value.query.id,t=>{console.log(t,"111111"),t?I(t):(console.log(o.value,"222222"),o.value.id&&(o.value={material_id:""},A.value.resetForm()))},{immediate:!0});const x=r("");return D(()=>c.currentRoute.value.query.document,t=>{x.value=t,t&&S.getAiDocumentInfo(t).then(e=>{console.log(e,"文案详情"),A.value.setFormData({theme:e.map(h=>h.content)})})},{immediate:!0}),(t,e)=>{const h=Q,F=$("router-link"),l=T;return u(),W(l,{title:"AI一键成片",class:"h-full"},{"title-left":g(()=>[i.value?(u(),b("div",Lt,[a("img",{class:Z(["mr-[5px] update-img",{loading:d.value}]),src:pt,alt:""},null,2),a("span",null,q(i.value)+" 已保存",1)])):U("",!0)]),"title-right":g(()=>[y(F,{to:"/smart-material/ai-history"},{default:g(()=>[y(h,{type:"primary",icon:"Clock"},{default:g(()=>e[2]||(e[2]=[C("历史成片")])),_:1})]),_:1})]),default:g(()=>[a("div",Ct,[a("div",St,[y(nt,{ref_key:"aiDocumentRef",ref:A,initialData:o.value,postLoading:f.value,onGenerate:w,onChange:R},null,8,["initialData","postLoading"])]),a("div",wt,[a("div",Rt,[e[5]||(e[5]=a("div",{class:"header-title"},[C("素材列表"),a("span",{class:"text-[#999] text-[14px] ml-[6px]"},"上传的素材需要经过AI分析完成后，才可用于AI成片中使用")],-1)),a("div",Mt,[e[3]||(e[3]=C("已分析完成")),a("span",It,q(v.value)+"个",1),e[4]||(e[4]=C("素材"))])]),a("div",xt,[y(bt,{materialList:L.value,"onUpdate:materialList":e[0]||(e[0]=s=>L.value=s),imageWidth:"140px",plusWidth:24,onChange:M,total:v.value,"onUpdate:total":e[1]||(e[1]=s=>v.value=s),"can-add":!0},null,8,["materialList","total"])])])])]),_:1})}}}),Zt=Y(Ft,[["__scopeId","data-v-7b0a0bdc"]]);export{Zt as default};
