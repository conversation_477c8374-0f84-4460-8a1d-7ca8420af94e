import{H as a}from"./history-D-4DMQfo.js";import{c as o,d as p,h as r}from"./aiVideo-B86MWzMI.js";import{k as s,c as m,a as l,w as d,p as t,o as c,b as n}from"./index-BBeD0eDz.js";/* empty css                *//* empty css              */import"./index-BUwi-MV-.js";import"./empty-CSpEo1eL.js";/* empty css               *//* empty css                    *//* empty css                     */import"./request-Ciyrqj7N.js";const f={class:"h-full flex flex-col"},H=s({__name:"history",setup(h){const i={title:"name",thumbnail:"imgurl",lastEditTime:"updated_at"};return(u,e)=>(c(),m("div",f,[l(a,{"list-api":t(r),"del-api":t(p),field:i,showCopy:!0,searchKey:"name","edit-path":"/smart-material/ai-image?id=","video-path":"/smart-material/ai-preview/","copy-api":t(o)},{"search-left":d(()=>e[0]||(e[0]=[n("span",{class:"text-[#999999] text-[14px]"},"当前历史成片包含一键成片与文案成片项目",-1)])),_:1},8,["list-api","del-api","copy-api"])]))}});export{H as default};
