webpackJsonp([106],{n4Bb:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});s("8PcR");var i={render:function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[e.resume_list.length?s("el-col",{staticClass:"list-wrap",attrs:{xxl:16,xl:18,lg:24,md:24,sm:24,xs:24}},e._l(e.resume_list,function(t){return s("div",{key:t.id,staticClass:"list-item flex-center flex-between"},[s("div",{staticClass:"list-item-left flex-1 flex-center flex-between"},[s("div",{staticClass:"flex-center flex-between"},[s("img",{attrs:{src:t.picurl,alt:""}}),e._v(" "),s("div",{staticClass:"flex-1"},[s("p",{staticClass:"name"},[e._v(e._s(t.enterprise_name))]),e._v(" "),s("div",{staticClass:"desc"},[s("p",[e._v("开始时间："+e._s(t.entry_date))]),e._v(" "),s("p",[e._v("结束时间："+e._s(t.departure_time))])])])])]),e._v(" "),s("div",{staticClass:"list-item-right"},[s("p",[e._v("职位")]),e._v(" "),s("p",[e._v(e._s(t.job))])])])}),0):s("el-empty")],1)},staticRenderFns:[]};var l=s("VU/8")({name:"ArchivesWorkRecord",props:["data"],computed:{resume_list:function(){return this.data.resume_list||[]}}},i,!1,function(e){s("ppCE")},"data-v-6c5e2654",null);t.default=l.exports},ppCE:function(e,t){}});