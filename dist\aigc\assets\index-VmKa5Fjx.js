import{A as g,l as H,y as J,k as K,B as W,f as b,w as p,H as X,o as m,b as T,q as G,a as n,d as Z,W as ee,p as e,aK as ae,c as P,s as Y,Y as te,F as M,aL as le,e as oe,E as ne,J as se,a5 as ie,P as re,X as pe,_ as ue}from"./index-BBeD0eDz.js";/* empty css                *//* empty css              */import{_ as ce}from"./index-BUwi-MV-.js";/* empty css                  *//* empty css                    *//* empty css               */import{E as de}from"./empty-CSpEo1eL.js";import{c as U,d as B,h as I}from"./aiVideo-B86MWzMI.js";import{c as me,d as _e,g as ge}from"./videoClip-Crxw3Jsi.js";import"./request-Ciyrqj7N.js";const fe=()=>{const w=J(),s=g(1),i=[{label:"一键成片",value:1,listApi:I,delApi:B,copyApi:U,listParams:{iswenan:1},editPath:"/smart-material/ai-image?id=",previewPath:"/smart-material/ai-preview/"},{label:"文案成片",value:2,listApi:I,delApi:B,copyApi:U,listParams:{iswenan:2},editPath:"/smart-material/ai-image?id=",previewPath:"/smart-material/ai-preview/"},{label:"精准成片",value:3,listApi:ge,delApi:_e,copyApi:me,editPath:"/smart-material/precise-edit?id=",previewPath:"/smart-material/precise-preview/"}],x=[{label:"草稿",value:1},{label:"生成中",value:3},{label:"已生成",value:4}],V={title:"name",thumbnail:"imgurl",lastEditTime:"created_at"},h=H(()=>{var a;return((a=i.find(l=>l.value==s.value))==null?void 0:a.delApi)||B}),o=H(()=>{var a;return((a=i.find(l=>l.value==s.value))==null?void 0:a.copyApi)||U}),C=H(()=>{var a;return((a=i.find(l=>l.value==s.value))==null?void 0:a.listApi)||I}),f=g(!1),u=g(0),c=g({status:"",perPage:14,page:1,date:"",name:""}),y=g([]),_=async()=>{var a;f.value=!0;try{const l={...c.value,...(a=i.find(r=>r.value==s.value))==null?void 0:a.listParams},d=await C.value(l);console.log(d,"res"),y.value=d.data||[],u.value=d.total||0}catch(l){console.log(l)}finally{f.value=!1}},E=a=>{c.value.page=a,_()},j=a=>{c.value.perPage=a,_()},k=a=>{var r,A;let l=((r=i.find(v=>v.value==s.value))==null?void 0:r.editPath)||"/smart-material/ai-image?id=",d=((A=i.find(v=>v.value==s.value))==null?void 0:A.previewPath)||"/smart-material/ai-preview/";a.status==1?w.push(`${l}${a.id}`):w.push(`${d}${a.id}`)},L=()=>{c.value.page=1,_()},S=()=>{c.value.page=1,_()},z=g(!0),O=g("创建时间");return{projectSource:s,sourceOptions:i,statusOptions:x,loading:f,EmptyImage:de,total:u,projectList:y,listQ:c,handleCurrentChange:E,handleSizeChange:j,getProjectList:_,handleProjectClick:k,refreshList:L,delApi:h,copyApi:o,field:V,handleSourceChange:S,showCopy:z,timeText:O}},ve={class:"h-full project-wrapper flex flex-col"},he={class:"project-filter"},Ce={class:"flex-1 flex flex-col overflow-hidden"},ye=K({__name:"index",setup(w){const{projectSource:s,sourceOptions:i,statusOptions:x,projectList:V,getProjectList:h,listQ:o,total:C,loading:f,refreshList:u,delApi:c,copyApi:y,showCopy:_,field:E,timeText:j,handleSizeChange:k,handleCurrentChange:L,handleProjectClick:S,EmptyImage:z}=fe();return W(()=>{h()}),(O,a)=>{const l=te,d=ee,r=Z,A=le,v=oe,$=ne,D=ce,F=ie,R=re,N=pe,Q=X,q=se;return m(),b(Q,{class:"h-full",title:"我的视频"},{default:p(()=>[T("div",ve,[T("div",he,[n($,{class:"project-filter-form",model:e(o),"label-width":"auto",inline:""},{default:p(()=>[n(r,{label:"来源",prop:"source"},{default:p(()=>[n(d,{class:"!w-[120px]",modelValue:e(s),"onUpdate:modelValue":a[0]||(a[0]=t=>ae(s)?s.value=t:null),placeholder:"请选择来源",onChange:e(u)},{default:p(()=>[(m(!0),P(M,null,Y(e(i),t=>(m(),b(l,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),n(r,{label:"状态",prop:"status"},{default:p(()=>[n(d,{class:"!w-[120px]",modelValue:e(o).status,"onUpdate:modelValue":a[1]||(a[1]=t=>e(o).status=t),placeholder:"请选择状态",clearable:"",onChange:e(u)},{default:p(()=>[(m(!0),P(M,null,Y(e(x),t=>(m(),b(l,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),n(r,{label:"创建时间",prop:"date",onChange:e(u)},{default:p(()=>[n(A,{modelValue:e(o).date,"onUpdate:modelValue":a[2]||(a[2]=t=>e(o).date=t),type:"datetimerange","start-placeholder":"开始日期","end-placeholder":"结束日期",placeholder:"选择日期","value-format":"YYYY-MM-DD HH:mm:ss",clearable:"",onChange:e(u)},null,8,["modelValue","onChange"])]),_:1},8,["onChange"]),n(r,{label:"名称",prop:"name"},{default:p(()=>[n(v,{modelValue:e(o).name,"onUpdate:modelValue":a[3]||(a[3]=t=>e(o).name=t),placeholder:"请输入项目名称",clearable:"",onChange:e(u)},null,8,["modelValue","onChange"])]),_:1})]),_:1},8,["model"])]),G((m(),P("div",Ce,[e(C)>0?(m(),P(M,{key:0},[n(F,{class:"flex-1"},{default:p(()=>[n(D,{projectList:e(V),timeText:e(j),onClick:e(S),field:e(E),delApi:e(c),copyApi:e(y),onRefresh:e(h),showCopy:e(_)},null,8,["projectList","timeText","onClick","field","delApi","copyApi","onRefresh","showCopy"])]),_:1}),n(R,{"current-page":e(o).page,"onUpdate:currentPage":a[4]||(a[4]=t=>e(o).page=t),"page-size":e(o).perPage,"onUpdate:pageSize":a[5]||(a[5]=t=>e(o).perPage=t),total:e(C),onSizeChange:e(k),onCurrentChange:e(L)},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])],64)):(m(),b(N,{key:1,class:"flex-1",description:"暂无记录",image:e(z),"image-size":293},null,8,["image"]))])),[[q,e(f)]])])]),_:1})}}}),ze=ue(ye,[["__scopeId","data-v-2a71b5eb"]]);export{ze as default};
