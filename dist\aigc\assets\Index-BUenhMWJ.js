import{k as c,A as d,c as m,b as i,a as e,w as a,K as u,H as _,y as f,o as x,p as s,a7 as y,_ as g}from"./index-BBeD0eDz.js";import{H as v}from"./history-D-4DMQfo.js";import{c as h,d as w,g as H}from"./videoClip-Crxw3Jsi.js";/* empty css                *//* empty css              */import"./index-BUwi-MV-.js";import"./empty-CSpEo1eL.js";/* empty css               *//* empty css                    *//* empty css                     */import"./request-Ciyrqj7N.js";const A=""+new URL("add-icon-CVFFdQgU.png",import.meta.url).href,C={class:"smart-edit-view h-full flex flex-col"},I=c({__name:"Index",setup(k){const o=f(),r=()=>{o.push("/smart-material/precise-edit")},p=d({title:"name",thumbnail:"imgurl",lastEditTime:"updated_at"});return(B,t)=>{const l=u,n=_;return x(),m("div",C,[i("div",{class:"add-product flex justify-center items-center",onClick:r},[e(l,{class:"add-plus"},{default:a(()=>[e(s(y))]),_:1}),t[0]||(t[0]=i("span",null,"新建AI精准成片",-1)),t[1]||(t[1]=i("img",{class:"add-icon",src:A,alt:""},null,-1))]),e(n,{title:"历史成片项目",class:"project-list-section flex-1"},{default:a(()=>[e(v,{"list-api":s(H),"del-api":s(w),"copy-api":s(h),"show-copy":!0,field:p.value,"edit-path":"/smart-material/precise-edit?id=","video-path":"/smart-material/precise-preview/"},null,8,["list-api","del-api","copy-api","field"])]),_:1})])}}}),K=g(I,[["__scopeId","data-v-ffe376aa"]]);export{K as default};
