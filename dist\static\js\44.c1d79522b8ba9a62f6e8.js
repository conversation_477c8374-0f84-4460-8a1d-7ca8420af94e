webpackJsonp([44],{"0ybV":function(e,t,s){"use strict";var a=s("pI5c"),i={name:"amendPwd",props:{dialogVisible:{required:!1,type:Boolean,default:function(){return!1}}},watch:{storeState:{handler:function(e,t){this.$store.state.user&&this.$store.state.user.memberInfo&&(this.uuu=this.$store.state.user.memberInfo)},immediate:!0,deep:!0}},data:function(){return{formData:{},rules:{code:[{required:!0,message:"验证码不能为空",trigger:"blur"}],password:[{required:!0,message:"密码不能为空",trigger:"blur"}],password_confirmation:[{required:!0,message:"确认密码不能为空",trigger:"blur"}]},btn_loading:!1,time:0,uuu:""}},computed:{storeState:function(){return this.$store.state.dialogPwdVisibleG}},created:function(){},mounted:function(){this.$store.state.user&&this.$store.state.user.memberInfo&&(this.uuu=this.$store.state.user.memberInfo)},methods:{sendMesCode:function(){var e=this;0===this.time&&(this.time=60,this.timer(),Object(a._10)({phone:this.uuu.phone,scene:"verify"}).then(function(t){200===t.code&&e.$message.success("验证码发送成功，请注意查收")}))},submit:function(){var e=this;this.$refs.formData.validate(function(t){t&&(e.btn_loading=!0,Object(a._32)(e.formData).then(function(t){e.btn_loading=!1,200===t.code&&(e.close(),e.$message.success(t.message),setTimeout(function(){e.$router.replace({path:"/home",query:{type:"logout"}})},1e3))}).catch(function(){e.btn_loading=!1}))})},timer:function(){var e=this;setTimeout(function(){e.time--,0!==e.time&&e.timer()},1e3)},close:function(){this.$emit("closepop")}}},o={render:function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-dialog",{ref:"dialog",attrs:{title:"修改密码",visible:e.dialogVisible&&!!e.$store.state.user,width:"600px","before-close":e.close},on:{"update:visible":function(t){return e.$set(e.dialogVisible&&!!e.$store.state,"user",t)}}},[s("div",[e.$store.state.dialogPwdVisibleG>0?s("div",{staticClass:"tips"},[e._v("系统检测到您的密码较为简单，为保证您的账号安全，请及时进行修改")]):e._e(),e._v(" "),s("el-form",{ref:"formData",staticClass:"form",attrs:{"label-position":"left","label-width":"100px",inline:!0,model:e.formData,rules:e.rules,size:"small"}},[s("div",{staticClass:"container"},[s("el-form-item",{attrs:{label:"已验证手机"}},[e._v(e._s(e._f("phoneEncryption")(e.uuu.phone)))]),e._v(" "),s("el-form-item",{attrs:{label:"短信验证码",prop:"code"}},[s("div",{staticStyle:{display:"flex"}},[s("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入验证码"},model:{value:e.formData.code,callback:function(t){e.$set(e.formData,"code",t)},expression:"formData.code"}}),e._v(" "),s("el-button",{ref:"mesCodeBtn",staticStyle:{"margin-left":"20px"},attrs:{disabled:e.time>0,type:"primary"},on:{click:e.sendMesCode}},[e._v(e._s(0===e.time?"发送验证码":"发送验证码("+e.time+"s)"))])],1)]),e._v(" "),s("el-form-item",{attrs:{label:"新密码",prop:"password"}},[s("el-input",{attrs:{"show-password":"",placeholder:"请输入新密码"},model:{value:e.formData.password,callback:function(t){e.$set(e.formData,"password",t)},expression:"formData.password"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"确认新密码",prop:"password_confirmation"}},[s("el-input",{attrs:{"show-password":"",placeholder:"再次输入密码"},model:{value:e.formData.password_confirmation,callback:function(t){e.$set(e.formData,"password_confirmation",t)},expression:"formData.password_confirmation"}})],1)],1),e._v(" "),s("el-button",{staticStyle:{"margin-top":"50px",width:"120px"},attrs:{type:"primary",loading:e.btn_loading},on:{click:e.submit}},[e._v("提交")])],1)],1)])},staticRenderFns:[]};var r=s("VU/8")(i,o,!1,function(e){s("g5vJ")},"data-v-db3ff460",null);t.a=r.exports},VZzB:function(e,t){},g5vJ:function(e,t){},os4h:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=s("Dd8w"),i=s.n(a),o=s("pI5c"),r=s("0ybV"),n=s("NYxO"),l={name:"index",components:{detailPwd:r.a},data:function(){return{gcc:this.$store.state.user.companyInfo.enterprise_name,dialogPwdVisible:!1}},computed:i()({},Object(n.e)(["weakPass"])),methods:{cancelCancelled:function(){var e=this;Object(o.n)({}).then(function(t){e.$message.success("申请撤销成功")})}}},c={render:function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"app-container"},[s("el-card",{staticClass:"box-card"},[e.weakPass?s("el-alert",{attrs:{title:"您的密码较为简单，为保证您的账号安全，请及时进行修改。",type:"warning"}}):e._e(),e._v(" "),s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[e._v("安全设置")])]),e._v(" "),s("ul",[s("li",{staticClass:"pwd-view"},[s("el-image",{attrs:{src:"../../../static/image/console/center/c_2.png"}}),e._v(" "),s("p",[e._v("登录密码")]),e._v(" "),s("p",[e._v("建议您定期更换密码，设置一个包含字母，符号或数字中至少两项长度超过6位的密码。")]),e._v(" "),s("el-button",{attrs:{type:"text"},on:{click:function(t){e.dialogPwdVisible=!0}}},[e._v("修改")]),e._v(" "),s("img",{staticClass:"img-set",attrs:{src:"/static/image/console/center/c_3.png"}})],1),e._v(" "),s("el-divider")],1)],1),e._v(" "),s("detail-pwd",{attrs:{dialogVisible:e.dialogPwdVisible},on:{closepop:function(t){e.dialogPwdVisible=!1}}})],1)},staticRenderFns:[]};var d=s("VU/8")(l,c,!1,function(e){s("VZzB")},"data-v-99296c5c",null);t.default=d.exports}});