document.ontouchmove = function (e) {
    pageAnimate();
}
window.onwheel=function (){
    pageAnimate();
}
function pageAnimate(){
    let tradional = document.querySelector('.traditional .content');
    addAnmiate(tradional, 'fadeInUpBig', false);
    let fun = document.querySelector('.function .function-list');
    addAnmiate(fun, 'fadeInUpBig', true);
    let num = document.querySelectorAll('.intelligent-integration');
    addAnmiate(num, 'slowNumber');
    let quick = document.querySelector('.safe-content.quick');
    addAnmiate(quick, 'fadeInUpBig', false);
    let lowCost = document.querySelector('.safe-content.low-cost');
    addAnmiate(lowCost, 'fadeInUpBig', false);
}
function addAnmiate(element, anmiateName, same) {
    if (anmiateName === 'slowNumber') {
        let offsetTop = element[0].offsetTop;
        let windowHeight = window.outerHeight;
        let scrollTop = document.documentElement.scrollTop;
        let nums = document.querySelectorAll('.percent .num');
        if (offsetTop - windowHeight < scrollTop + 100 && scrollTop < offsetTop + 100) {
            nums.forEach(function (e, i) {
                e.classList.add('slowNumber');
                anmiateNum();
            })
        } else {
            nums.forEach(function (e, i) {
                e.classList.remove('slowNumber');
            })
        }
    } else {
        let offsetTop = element.offsetTop;
        let windowHeight = window.outerHeight;
        let scrollTop = document.documentElement.scrollTop;
        let list = element.querySelectorAll('li');
        if (offsetTop - windowHeight < scrollTop && scrollTop < offsetTop) {
            list.forEach(function (li, index) {
                if (same) {
                    li.classList.add(anmiateName);
                } else {
                    if (index % 2 === 0) {
                        li.classList.add('fadeInLeftBig');
                    } else {
                        li.classList.add('fadeInRightBig');
                    }
                }
            })
        } else {
            list.forEach(function (li, index) {
                if (same) {
                    li.classList.remove(anmiateName);
                } else {
                    if (index % 2 === 0) {
                        li.classList.remove('fadeInLeftBig');
                    } else {
                        li.classList.remove('fadeInRightBig');
                    }
                }
            })
        }
    }
}
