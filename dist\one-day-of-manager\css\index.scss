.flex-xy-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-yx-center {
  display: flex;
  flex-direction: column;
  align-items: center;
}
* {
  margin: 0; padding: 0; box-sizing: border-box;
  /* 取消点击背景色 */
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
  -webkit-user-select: none;
  -moz-user-focus: none;
  -moz-user-select: none;
}
[v-cloak] { display: none; }

@mixin imgStyle {
  display: block;
  width: 100%;
  height: auto;
}

$scale: 0.6;

// 定义方法
@function getPx($number) {
  $result: $number * $scale;
  @return $result * 1px;
}

// 手机端
@function getPxM($number) {
  $result: $number / 100;
  @return $result * 1rem;
}

@mixin btnStyle {
  width: getPx(400);
  height: getPx(90);
  background-color: #5183F6;
  @extend .flex-xy-center;
  font-size: getPx(30);
  color: #fff;
  border-radius: getPx(45);
  cursor: pointer;
  @media screen and (max-width: 768px){
    width: getPxM(400);
    height: getPxM(90);
    font-size: getPxM(30);
  }
}

img {
  @include imgStyle;
}

/* 元素宽高度
------------------------------- */
@for $i from 1 through 2000 {
  .height#{$i} {
    height: #{$i * $scale}px !important;
    flex-shrink: 0;
  }
	.width#{$i} {
	  width:#{$i * $scale}px !important;
    flex-shrink: 0;
	}
  // 圆角
  .radius#{$i} {
    border-radius: #{$i * $scale}px;
    overflow: hidden;
  }
  .top#{$i} {
    top: #{$i * $scale}px;
  }
  .right#{$i} {
    right: #{$i * $scale}px;
  }
  .bottom#{$i} {
    bottom: #{$i * $scale}px;
  }
  .left#{$i} {
    left: #{$i * $scale}px;
  }
  @media screen and (max-width: 768px){
    .height#{$i} {
      height: #{$i / 100}rem !important;
      flex-shrink: 0;
    }
    .width#{$i} {
      width: #{$i / 100}rem !important;
      flex-shrink: 0;
    }
    // 圆角
    .radius#{$i} {
      border-radius: #{$i / 100}rem;
      overflow: hidden;
    }
    .top#{$i} {
      top: #{$i / 100}rem;
    }
    .right#{$i} {
      right: #{$i / 100}rem;
    }
    .bottom#{$i} {
      bottom: #{$i / 100}rem;
    }
    .left#{$i} {
      left: #{$i / 100}rem;
    }
  }
}
.w100 {
	width: 100%;
}
.h100 {
	height: 100%;
}

/* 外边距、内边距全局样式
------------------------------- */
@for $i from -1000 through 1000 {
  .mt#{$i} {
    margin-top: #{$i * $scale}px;
  }
  .mr#{$i} {
    margin-right: #{$i * $scale}px;
  }
  .mb#{$i} {
    margin-bottom: #{$i * $scale}px;
  }
  .ml#{$i} {
    margin-left: #{$i * $scale}px;
  }
  .p#{$i} {
    padding: #{$i * $scale}px;
  }
  .pt#{$i} {
    padding-top: #{$i * $scale}px;
  }
  .pr#{$i} {
    padding-right: #{$i * $scale}px;
  }
  .pb#{$i} {
    padding-bottom: #{$i * $scale}px;
  }
  .pl#{$i} {
    padding-left: #{$i * $scale}px;
  }
  .px#{$i} {
    padding-left: #{$i * $scale}px;
    padding-right: #{$i * $scale}px;
  }
  .py#{$i} {
    padding-top: #{$i * $scale}px;
    padding-bottom: #{$i * $scale}px;
  }
  @media screen and (max-width: 768px){
    .mt#{$i} {
      margin-top: #{$i / 100}rem;
    }
    .mr#{$i} {
      margin-right: #{$i / 100}rem;
    }
    .mb#{$i} {
      margin-bottom: #{$i / 100}rem;
    }
    .ml#{$i} {
      margin-left: #{$i / 100}rem;
    }
    .p#{$i} {
      padding: #{$i / 100}rem;
    }
    .pt#{$i} {
      padding-top: #{$i / 100}rem;
    }
    .pr#{$i} {
      padding-right: #{$i / 100}rem;
    }
    .pb#{$i} {
      padding-bottom: #{$i / 100}rem;
    }
    .pl#{$i} {
      padding-left: #{$i / 100}rem;
    }
    .px#{$i} {
      padding-left: #{$i / 100}rem;
      padding-right: #{$i / 100}rem;
    }
    .py#{$i} {
      padding-top: #{$i / 100}rem;
      padding-bottom: #{$i / 100}rem;
    }
  }
}

.position-r-10 {
  position: relative;
  z-index: 10;
}

body {
  background-color: #929292;
  @extend .flex-xy-center;
  flex-direction: column;
  height: 100vh;
  font-size: 16px;
}
#index-app {
  width: getPx(750);
  height: getPx(1206);
  background: url("../images/bg.png") no-repeat center / cover;
  padding: getPx(44) getPx(30);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  @media screen and (max-width: 768px){
    padding: getPxM(44) getPxM(30);
  }
  .btn {
    @include btnStyle;
    &.btn-plain {
      background: none;
      border: 1px solid #FFFFFF;
      height: getPx(80);
      @media screen and (max-width: 768px){
        height: getPxM(80);
      }
    }
  }
  .logo-box {
    width: getPx(172);
    @extend .position-r-10;
    @media screen and (max-width: 768px){
      width: getPxM(172);
    }
    img {
      @include imgStyle;
    }
  }
  .pointer-box {
    position: relative;
    cursor: pointer;
    .pointer {
      position: absolute;
      .pointer-tips {
        width: max-content;
        padding: 0 getPx(14);
        height: getPx(44);
        background: linear-gradient(90deg, #FE7008, #FD9D37);
        border-radius: getPx(29) getPx(27) getPx(27) 0px;
        @extend .flex-xy-center;
        position: absolute;
        top: getPx(-60);
        left: getPx(0);
        font-size: getPx(22);
        color: #FFFFFF;
        @media screen and (max-width: 768px){
          padding: 0 getPxM(14);
          height: getPxM(44);
          border-radius: getPxM(29) getPxM(27) getPxM(27) 0px;
          font-size: getPxM(22);
        }
      }
    }
    .pointer-area {
      position: absolute;
      // background-color: rgba(0,0,0,0.3);
    }
  }
  .icon-view {
    position: relative;
    .num-view {
      position: absolute;
      top: getPx(2);
      left: getPx(117);
      padding: 0 getPx(10);
      height: getPx(36);
      background: #FA5151;
      border-radius: getPx(18);
      border: 1px solid #FFFFFF;
      font-size: getPx(24);
      color: #FFFFFF;
      @media screen and (max-width: 768px){
        top: getPxM(2);
        left: getPxM(117);
        padding: 0 getPxM(10);
        height: getPxM(36);
        font-size: getPxM(24);
      }
    }
  }
  .bg-img {
    position: absolute;
    left: 0;
    bottom: 0;
    width: getPx(750);
    height: getPx(1206);
    @extend .flex-xy-center;
    @media screen and (max-width: 768px){
      width: 100%;
      height: 100%;
    }
    img {
      @include imgStyle;
      height: 100%;
      // 填充
      object-fit: cover;
    }
  }
  .main-container {
    flex: 1;
    overflow: hidden auto;
    @extend .flex-yx-center;
    @extend .position-r-10;
    .main-title {
      font-size: getPx(70);
      color: #222222;
      @media screen and (max-width: 768px){
        font-size: getPxM(70);
      }
    }
    .tips-box {
      position: absolute;
      left: 0;
      bottom: getPx(20);
      @media screen and (max-width: 768px){
        bottom: getPxM(20);
      }
      .text-box {
        background: rgba(238,242,245,0.8);
        border-radius: getPx(20);
        border: getPx(2) solid #FFFFFF;
        font-size: getPx(28);
        color: #222222;
        position: relative;
        z-index: 10;
        @media screen and (max-width: 768px){
          font-size: getPxM(28);
          border-radius: getPxM(20);
          border: getPxM(2) solid #FFFFFF;
        }
      }
      .tips-icon {
        position: absolute;
        top: getPx(-88);
        left: getPx(14);
        z-index: 1;
        @media screen and (max-width: 768px){
          top: getPxM(-88);
          left: getPxM(14);
        }
      }
    }
  }
  .end-view {
    .end-tip {
      font-size: getPx(26);
      color: #E0E0E0;
      @media screen and (max-width: 768px){
        font-size: getPxM(26);
      }
    }
  }
  .form-view {
    .top-info {
      h3 {
        font-size: getPx(50);
        color: #FFFFFF;
        font-weight: bold;
        @media screen and (max-width: 768px){
          font-size: getPxM(50);
        }
      }
      p {
        font-size: getPx(26);
        color: #E0E0E0;
        @media screen and (max-width: 768px){
          font-size: getPxM(26);
        }
      }
    }
    .form-box {
      input {
        width: 100%;
        height: getPx(90);
        line-height: getPx(90);
        border: getPx(2) solid rgba(255, 255, 255, 0.5);
        border-radius: getPx(80);
        color: #fff;
        background: none;
        box-shadow: none;
        margin-bottom: getPx(40);
        padding: 0 getPx(40);
        box-sizing: border-box;
        font-size: getPx(30);
        &:focus {
          outline: none;
        }
        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
          opacity: 1;
        }
        @media screen and (max-width: 768px){
          height: getPxM(90);
          line-height: getPxM(90);
          border: getPxM(2) solid rgba(255, 255, 255, 0.5);
          border-radius: getPxM(80);
          font-size: getPxM(30);
          padding: 0 getPxM(40);
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  body {
    background: url("../images/bg.png") no-repeat center / cover;
    justify-content: flex-start;
    #index-app {
      background: none;
      width: 100%;
      height: 100%;
      .main-container {
        margin-top: getPxM(30);
      }
    }
  }
}