import{_ as Q}from"./PageBack-BpcOadQW.js";import{I as W}from"./ImportFromMaterial-lo8a_mzn.js";import{k as R,A as u,l as Z,V as D,c as m,m as A,b as i,f as q,a as f,Z as G,w as y,j as V,t as U,a5 as J,P as K,o as c,F,s as X,n as Y,p as T,i as O,_ as H,B as ee,y as te,C as le,h as se,Q as E,a4 as ae,a2 as oe}from"./index-BBeD0eDz.js";/* empty css               *//* empty css                 */import{V as ie,E as ne,_ as ue}from"./video-cover-DBcJ77EJ.js";import{_ as de}from"./video-Bj5f7V7O.js";import{f as re}from"./common-DBXWCL9C.js";import{b as ce,e as ve,f as me,s as pe}from"./aiVideo-B86MWzMI.js";import{u as fe}from"./List-CZfQXqH2.js";/* empty css               *//* empty css                */import"./request-Ciyrqj7N.js";/* empty css                *//* empty css                *//* empty css                  */const ge={class:"video-list flex flex-col h-full"},he={key:0,class:"select-all-container"},ye={class:"select-all-wrapper"},Ve={class:"selected-count"},_e={class:"count-number"},xe={key:1,class:"h-[20px]"},be={class:"video-grid-container flex-1"},Ce={class:"video-grid"},ke=["onClick"],Ae={class:"video-thumbnail"},Ie=["src"],Le={key:0,class:"video-duration w-full"},Se=["onClick"],we={class:"video-info flex items-center justify-between"},Pe={class:"video-date"},Ee={key:1,class:"video-item video-loading flex flex-col justify-center items-center"},Ue={class:"video-thumbnail"},je={class:"tips-1"},Me=R({__name:"VideoList",props:{videoList:{type:Array,default:()=>[]},modelValue:{type:Array,default:()=>[]},selectedVideos:{type:Array,default:()=>[]},total:{type:Number,default:0},status:{type:Number,default:0},field:{type:Object,default:()=>({thumbnail:"thumbnail",duration:"duration",date:"date",url:"url",title:"title"})}},emits:["update:modelValue","select","page-change","size-change","update:selectedVideos"],setup(_,{emit:M}){const d=_,n=M,v=u(1),p=u(16),o=Z(()=>{const l=(v.value-1)*p.value,e=l+p.value;return d.videoList.slice(l,e)}),a=u([]),g=u(!1),b=()=>{if(o.value.length===0){g.value=!1;return}const l=o.value.map(e=>e.id);g.value=l.every(e=>a.value.includes(e))};D(()=>d.modelValue,l=>{l!==void 0&&(Array.isArray(l)?a.value=[...l]:typeof l=="string"&&l?a.value=[l]:a.value=[],b())},{immediate:!0}),D([()=>v.value,()=>p.value,()=>o.value],()=>{b()});const L=l=>{if(d.status!==1)return;const e=a.value.indexOf(l.id);e===-1?a.value.push(l.id):a.value.splice(e,1),n("update:modelValue",a.value);const h=d.videoList.filter(r=>a.value.includes(r.id));n("select",h),n("update:selectedVideos",h),b()},z=()=>{n("update:modelValue",a.value);const l=d.videoList.filter(e=>a.value.includes(e.id));n("select",l),b()},S=l=>{const e=o.value.map(r=>r.id);l?e.forEach(r=>{a.value.includes(r)||a.value.push(r)}):a.value=a.value.filter(r=>!e.includes(r)),n("update:modelValue",a.value);const h=d.videoList.filter(r=>a.value.includes(r.id));n("update:selectedVideos",h),n("select",h)},B=l=>{v.value=l,n("page-change",l)},$=l=>{p.value=l;const e=Math.ceil(d.videoList.length/l);v.value>e&&(v.value=1),n("size-change",l)},w=u(!1),I=u({id:"",url:"",thumbnail:"",title:""}),N=l=>{I.value={id:l.id,url:l.url||"",thumbnail:l.thumbnail,title:l.title||""},w.value=!0},P=()=>{console.log("视频播放结束")};return(l,e)=>{const h=G,r=J,s=K;return c(),m("div",ge,[_.status===1?(c(),m("div",he,[i("div",ye,[f(h,{modelValue:g.value,"onUpdate:modelValue":e[0]||(e[0]=t=>g.value=t),onChange:S,class:"select-all-checkbox"},{default:y(()=>e[6]||(e[6]=[V("当页全选")])),_:1},8,["modelValue"]),i("div",Ve,[e[7]||(e[7]=V("已选择")),i("span",_e,U(a.value.length),1),e[8]||(e[8]=V("条视频"))])])])):A("",!0),_.status!==1?(c(),m("div",xe)):A("",!0),i("div",be,[f(r,null,{default:y(()=>[i("div",Ce,[(c(!0),m(F,null,X(o.value,(t,C)=>(c(),m(F,null,[t[_.field.status]==1?(c(),m("div",{key:0,class:Y(["video-item",{selected:a.value.includes(t.id)}]),onClick:k=>L(t)},[i("div",Ae,[i("img",{class:"video-cover",src:t[_.field.thumbnail]||T(ne),alt:"视频缩略图"},null,8,Ie),t.times?(c(),m("div",Le,U(T(re)(t.times)),1)):A("",!0),i("div",{class:"video-play-icon",onClick:O(k=>N(t),["stop"])},e[9]||(e[9]=[i("img",{src:ue,alt:""},null,-1)]),8,Se)]),i("div",we,[i("div",Pe,U(t.name),1),_.status===1?(c(),m("div",{key:0,class:"video-select-checkbox",onClick:e[2]||(e[2]=O(()=>{},["stop"]))},[f(h,{modelValue:a.value,"onUpdate:modelValue":e[1]||(e[1]=k=>a.value=k),value:t.id,onChange:z},{default:y(()=>e[10]||(e[10]=[V("选中")])),_:2},1032,["modelValue","value"])])):A("",!0)])],10,ke)):(c(),m("div",Ee,[i("div",Ue,[e[11]||(e[11]=i("img",{class:"video-icon",src:de,alt:""},null,-1)),i("p",je,U(t[_.field.tips]),1),e[12]||(e[12]=i("p",{class:"tips-2"},"正在合成视频...",-1))])]))],64))),256))])]),_:1})]),_.total>0?(c(),q(s,{key:2,"current-page":v.value,"onUpdate:currentPage":e[3]||(e[3]=t=>v.value=t),"page-size":p.value,"onUpdate:pageSize":e[4]||(e[4]=t=>p.value=t),total:d.videoList.length,onSizeChange:$,onCurrentChange:B},null,8,["current-page","page-size","total"])):A("",!0),f(ie,{visible:w.value,"onUpdate:visible":e[5]||(e[5]=t=>w.value=t),"video-url":I.value.url,poster:I.value.thumbnail,title:I.value.title||"视频播放",onEnded:P},null,8,["visible","video-url","poster","title"])])}}}),ze=H(Me,[["__scopeId","data-v-7d210a9e"]]),Be={class:"mix-preview flex flex-col h-full"},$e={key:0,class:"tips-wrap"},Ne={class:"flex items-center"},Fe=R({__name:"preview",setup(_){const{publishVideo:M}=fe(),d=te(),n=u({}),v=u(0),p=u(),o=u([]),a=u([]),g=u([]),b=async()=>{try{const s=await ce({id:p.value});console.log(s,"视频列表"),n.value=s.completefilm,g.value=s.list,v.value=s.status,s.status==1&&clearInterval(L.value),g.value.length==0&&d.go(-1)}catch(s){console.log(s)}},L=u(null),z=()=>{L.value=setInterval(()=>{b()},5e3)},S=u(!1),B=async()=>{if(!S.value){S.value=!0;try{await pe({id:p.value,name:n.value.name}),E.success("保存成功")}catch(s){console.log(s)}finally{S.value=!1}}},$=s=>{console.log("选中视频:",s)},w=()=>{if(console.log("批量删除",o.value),o.value.length==0)return E.warning("请选择要删除的视频");const s=o.value.length==g.value.length;let t="确定要删除选中的视频吗？";s&&(t="该项目的所有视频删除后，此项目将变更为草稿状态，是否确认删除？"),ae.confirm(t,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{let C=o.value.join(","),k=oe.service({lock:!0,text:"删除中...",background:"rgba(255, 255, 255, 0.7)"});me({id:C}).then(j=>{E.success("删除成功"),s?d.go(-1):b()}).catch(j=>{console.log(j)}).finally(()=>{k.close()})})},I=()=>{console.log("重新编辑"),d.push(`/smart-material/ai-image?id=${p.value}`)},N=()=>{if(console.log("全部发布",o.value),o.value.length==0)return E.warning("请选择要发布的视频");M(a.value)},P=u(!1),l=()=>{P.value=!1},e=s=>{if(o.value.length==0)return E.warning("请选择要导出的视频");s(o.value)},h=s=>{console.log("当前页码:",s)},r=s=>{console.log("每页显示数量:",s)};return ee(()=>{let s=d.currentRoute.value.params.id;s?(p.value=s,b(),z()):d.back()}),le(()=>{clearInterval(L.value)}),(s,t)=>{const C=se,k=W,j=Q;return c(),m("div",Be,[f(j,{backText:"AI一键成片预览",title:n.value.name,"onUpdate:title":t[1]||(t[1]=x=>n.value.name=x),editable:!0,onTitleChange:B},{left:y(()=>[v.value===1?(c(),m("p",$e,[t[4]||(t[4]=V("已生成")),i("span",null,U(g.value.length)+"条",1),t[5]||(t[5]=V("视频"))])):A("",!0)]),right:y(()=>[i("div",Ne,[v.value===1?(c(),m(F,{key:0},[f(C,{type:"danger",onClick:w},{default:y(()=>t[6]||(t[6]=[V("批量删除")])),_:1}),f(C,{type:"primary",onClick:I},{default:y(()=>t[7]||(t[7]=[V("重新编辑")])),_:1}),f(C,{type:"primary",onClick:N},{default:y(()=>t[8]||(t[8]=[V("批量发布")])),_:1}),f(k,{show:P.value,"onUpdate:show":t[0]||(t[0]=x=>P.value=x),alertType:"export",activeIndex:"video",exportApi:T(ve),exportParams:{id:o.value.join(",")},onClose:l},{default:y(({openDialog:x})=>[f(C,{type:"primary",onClick:Te=>e(x)},{default:y(()=>t[9]||(t[9]=[V("收藏至素材库")])),_:2},1032,["onClick"])]),_:1},8,["show","exportApi","exportParams"])],64)):A("",!0)])]),_:1},8,["title"]),f(ze,{class:"mt-4 flex-1",videoList:g.value,status:v.value,field:{thumbnail:"imgurl",date:"created_at",url:"url",title:"name",status:"isstatus",tips:"timestr"},modelValue:o.value,"onUpdate:modelValue":t[2]||(t[2]=x=>o.value=x),selectedVideos:a.value,"onUpdate:selectedVideos":t[3]||(t[3]=x=>a.value=x),onSelect:$,onPageChange:h,onSizeChange:r},null,8,["videoList","status","modelValue","selectedVideos"])])}}}),st=H(Fe,[["__scopeId","data-v-8012d189"]]);export{st as default};
