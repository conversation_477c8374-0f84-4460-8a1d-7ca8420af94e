/*/~ eslint-disable ~/

//token
var access_token=Cookies.get("access_token") || Cookies.get("token");
var zhyLeftNavApiDomain="https://apidev.china9.cn/";
var zhyImageDomain="https://zcloud.obs.cn-north-4.myhuaweicloud.com/";
if ("undefined" == typeof environment){
  zhyLeftNavApiDomain="https://apidev.china9.cn/";
}else {
  if (environment==="pro"){
    zhyLeftNavApiDomain="https://api.china9.cn/";
  }else {
    zhyFooterApiDomain="https://apidev.china9.cn/";
  }
}

var version = '202407251658';

var leftLoadNum = 0;

function getCssPromise(href, idName) {
  return new Promise(resolve => {
    $.get(href, function (res) {
      var style = document.createElement('style');
      style.id = idName;
      style.innerHTML = res;
      if(idName === 'elementCss') {
        // 获取head中的第一个元素
        var firstElement = document.head.firstChild;
        // 将新的style元素插入到第一个元素之前
        document.head.insertBefore(style, firstElement);
      }else{
        document.head.appendChild(style);
      }
      resolve({code: 200, id: idName})
    })
  })
}

//  先加载样式，防止先出现没有样式的内容
if (!document.getElementById("ziHaiLeftCloud")) {
  var elementCssUrl = 'https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/index.css?v=' + version;
  var commonCssUrl = 'https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/ziHaiLeftCloud.css?v=' + version;
  if (environment === "local") {
    commonCssUrl = '/static/js/common/ziHaiLeftCloud.css';
    elementCssUrl = '/static/js/common/index.css';
  }
  var cssList = [
    getCssPromise(commonCssUrl, 'ziHaiLeftCloud'),
    getCssPromise(elementCssUrl, 'elementCss')
  ];
  Promise.all(cssList).then((res) => {
    var success = res.filter(item => item.code === 200);
    if (success.length === 2) {
      zhyHeaderload = true
      console.log('css加载完成');
      checkLeftEle();
    }
  });
}

function checkLeftEle() {
  setTimeout(function () {
    if (leftLoadNum < 2) {
      if (document.getElementById("zhyConsoleLeftSide")) {
        addLeftNav();
      }else{
        leftLoadNum++;
        checkLeftEle();
      }
    }
  }, 500)
}


// 左侧栏
function addLeftNav() {
  setTimeout(function (){
    $.post(zhyLeftNavApiDomain+"api/app/my", {access_token}, function (result) {
      if (result.data) {
        let str1 = '<ul id="products" style="width: 200px;height: 100%;overflow: auto">' +
          '<li>' +
          '     <a style="cursor: default">' +
          '       <img style="transform: scale(1.2)" src="'+zhyImageDomain+'static/cl.png">' +
          '       <p>产品与服务</p>' +
          '     </a>' +
          '</li>';
        result.data.forEach(function (v, i) {
          str1 += '<li>' +
            '     <a href="' + v.entrance_url + '">' +
            '       <img src="' + v.icon + '">' +
            '       <p>' + v.name + '</p>' +
            '     </a>' +
            '</li>'
        })
        str1 += '</ul>';
        $("#zhyConsoleLeftSide").css({overflow: "hidden"}).html(str1);

        //  侧边栏显示隐藏
        $("#zhyConsoleLeftSide li").mouseover(function () {
          $("#zhyConsoleLeftSide").css("width", "200px")
        }).mouseout(function () {
          $("#zhyConsoleLeftSide").css("width", "50px")
        });
      }
    });
  },200)
}
window.addEventListener("hashchange", function () {
  addLeftNav();
})
*/