import{k as E,A as d,f as F,o as f,I as B,w as n,a as l,b as p,E as D,d as N,e as $,h as j,j as w,V as L,B as R,Q as U,c as b,F as q,s as T,n as M,t as P,K as z,p as h,a7 as K,_ as O}from"./index-BBeD0eDz.js";/* empty css               *//* empty css                *//* empty css               *//* empty css                  */import{u as C}from"./shortVideo-BIt_F0ml.js";import{o as Q}from"./common-DBXWCL9C.js";const W={class:"dialog-footer flex items-center justify-center mb-[20px]"},G=E({__name:"AddAccountForm",props:{platform:{type:Object,default:()=>({})}},emits:["success"],setup(i,{expose:v,emit:g}){const{addAccount:A}=C(),r=i,x=g,a=d(!1),c=d({title:[{required:!0,message:"请输入账号名称",trigger:"blur"}],app_id:[{required:!0,message:"请输入AppId/app_id",trigger:"blur"}],app_secret:[{required:!0,message:"请输入AppSecret/app_token",trigger:"blur"}]}),u=d(),t=d({title:"",app_id:"",app_secret:""}),o=async()=>{await u.value.validate()&&(r.platform.id==5?t.value.type=1:r.platform.id==4&&(t.value.type=2),await A(t.value),x("success"),a.value=!1)};return v({openDialog:()=>{a.value=!0,t.value={title:"",app_id:"",app_secret:""}}}),(V,e)=>{const _=$,y=N,I=D,k=j,S=B;return f(),F(S,{title:`添加${i.platform.title}账号`,modelValue:a.value,"onUpdate:modelValue":e[4]||(e[4]=s=>a.value=s),width:"600px"},{default:n(()=>[l(I,{ref_key:"formRef",ref:u,class:"p-[20px]",model:t.value,rules:c.value,"label-width":"150px","label-position":"left"},{default:n(()=>[l(y,{label:"账号名称",prop:"title"},{default:n(()=>[l(_,{modelValue:t.value.title,"onUpdate:modelValue":e[0]||(e[0]=s=>t.value.title=s),placeholder:"请输入账号名称"},null,8,["modelValue"])]),_:1}),l(y,{label:"AppId/app_id",prop:"app_id"},{default:n(()=>[l(_,{modelValue:t.value.app_id,"onUpdate:modelValue":e[1]||(e[1]=s=>t.value.app_id=s),placeholder:"请输入AppId/app_id"},null,8,["modelValue"])]),_:1}),l(y,{label:"AppSecret/app_token",prop:"app_secret"},{default:n(()=>[l(_,{modelValue:t.value.app_secret,"onUpdate:modelValue":e[2]||(e[2]=s=>t.value.app_secret=s),placeholder:"请输入AppSecret/app_token"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),p("div",W,[l(k,{class:"w-[120px] !h-[40px] !text-[15px]",onClick:e[3]||(e[3]=s=>a.value=!1)},{default:n(()=>e[5]||(e[5]=[w("取消")])),_:1}),l(k,{class:"w-[120px] !h-[40px] !text-[15px]",type:"primary",onClick:o},{default:n(()=>e[6]||(e[6]=[w("保存")])),_:1})])]),_:1},8,["title","modelValue"])}}}),H={class:"platform-wrapper"},J={class:"platform-cards flex"},X=["onClick"],Y={class:"flex items-center"},Z=["src","alt"],ee={class:"platform-name"},te=E({__name:"AddAccount",props:{mediaType:{type:[Number,String],default:""}},emits:["success"],setup(i,{emit:v}){const g=v,{platformList:A,fetchPlatformList:r}=C(),x=i;L(()=>x.mediaType,o=>{let m={};o&&(m.type=o),r(m)},{immediate:!0});const a=d(),c=d(),u=o=>{console.log("添加账号:",o),a.value=o,o.auth?Q(o.auth):c.value.openDialog()},t=()=>{g("success")};return R(()=>{console.log("AddAccount组件已挂载"),window.addEventListener("message",o=>{console.log("接收到消息:",o.data),o.data==="授权成功"&&(console.log("接收到添加账号成功的消息:",o.data),U({message:"授权成功",type:"success",duration:1500,onClose:()=>{console.log("消息框关闭"),t()}}))})}),(o,m)=>{const V=z;return f(),b("div",H,[p("div",J,[(f(!0),b(q,null,T(h(A),e=>(f(),b("div",{key:e.value,class:"platform-card flex items-center justify-between",onClick:_=>u(e)},[p("div",Y,[p("div",{class:M(["platform-icon",e.value])},[p("img",{src:e.icon,alt:e.title},null,8,Z)],2),p("div",ee,P(e.title),1)]),l(V,{class:"add-icon"},{default:n(()=>[l(h(K))]),_:1})],8,X))),128))]),l(G,{ref_key:"addAccountFormRef",ref:c,platform:a.value,onSuccess:t},null,8,["platform"])])}}}),ie=O(te,[["__scopeId","data-v-9826ac13"]]);export{ie as A};
