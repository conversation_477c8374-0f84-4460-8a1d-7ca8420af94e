import{_ as P}from"./PageBack-BpcOadQW.js";import{I as B}from"./ImportFromMaterial-lo8a_mzn.js";import{k as T,A as a,C as A,B as R,y as V,c,a as i,b as d,w as n,Q as p,o as v,m as E,F as L,h as N,j as f,p as U,a4 as D,_ as W}from"./index-BBeD0eDz.js";/* empty css               */import{_ as z}from"./video-Bj5f7V7O.js";import{s as G}from"./aiVideo-B86MWzMI.js";import{h as J,o as Q,m as S}from"./index-vH7ypFZe.js";/* empty css               *//* empty css                */import"./List-CZfQXqH2.js";/* empty css                *//* empty css                 *//* empty css                */import"./request-Ciyrqj7N.js";import"./video-cover-DBcJ77EJ.js";/* empty css                  */const $={class:"mix-preview flex flex-col h-full"},q={class:"flex flex-wrap items-center gap-2"},H={class:"video-list flex-col h-full mt-4 flex-1 bg-white p-[20px] rounded-[10px] overflow-auto"},K={key:0,class:"video-item video-loading flex flex-col justify-center items-center cursor-pointer bg-[#F2F6F9] transition-all duration-300 ease-in-out aspect-[9/16] w-full max-w-[360px] sm:w-[360px] m-auto"},O=["src","poster"],X=T({__name:"Preview",setup(Y){const s=V(),r=a({}),x=a(0),l=a(),o=a(null),w=a(!1),h=async()=>{try{w.value=!0;const t=await J({id:l.value});if(t.code===200){const e=t.data;r.value=e,x.value=e.status,o.value&&(o.value.postMessage({type:"stop"}),o.value.terminate(),o.value=null),e.status===0&&(o.value=new Worker(new URL(""+new URL("timerWorker-D76cJf-z.js",import.meta.url).href,import.meta.url)),o.value.onmessage=()=>{h()},o.value.postMessage({type:"start",interval:10*1e3}))}else p.error("获取项目详情失败"),s.push("/smart-material/user-portrait-history")}catch(t){console.log(t),p.error("获取项目详情失败"),s.push("/smart-material/user-portrait-history")}finally{w.value=!1}};A(()=>{o.value&&(o.value.postMessage({type:"stop"}),o.value.terminate(),o.value=null)});const _=a(!1),k=async()=>{if(!_.value){_.value=!0;try{await G({id:l.value,name:r.value.name}),p.success("保存成功")}catch(t){console.log(t)}finally{_.value=!1}}},y=a(!1),b=()=>{D.confirm("确定要删除该项目吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{y.value=!0;const t=await S({id:l.value});p.success(t.message||"删除成功"),s.push("/smart-material/user-portrait-history")}catch(t){console.log(t),p.error("删除失败")}finally{y.value=!1}})},C=()=>{s.push(`/smart-material/user-portrait?id=${l.value}`)},F=()=>{s.push("/short-video/publish")},g=a(!1),M=()=>{g.value=!1};return R(()=>{let t=s.currentRoute.value.params.id;t?(l.value=t,h()):s.back()}),(t,e)=>{const m=N,j=B,I=P;return v(),c("div",$,[i(I,{backText:"AI数字人视频预览",title:r.value.name,"onUpdate:title":e[1]||(e[1]=u=>r.value.name=u),editable:!0,onTitleChange:k},{right:n(()=>[d("div",q,[x.value===1?(v(),c(L,{key:0},[i(m,{type:"danger",loading:y.value,onClick:b,class:"btn-responsive"},{default:n(()=>e[2]||(e[2]=[f("删除")])),_:1},8,["loading"]),i(m,{type:"primary",onClick:C,class:"btn-responsive"},{default:n(()=>e[3]||(e[3]=[f("重新编辑")])),_:1}),i(m,{type:"primary",onClick:F,class:"btn-responsive"},{default:n(()=>e[4]||(e[4]=[f("全部发布")])),_:1}),i(j,{show:g.value,"onUpdate:show":e[0]||(e[0]=u=>g.value=u),alertType:"export",activeIndex:"video",exportApi:U(Q),exportParams:{id:l.value},onClose:M},{default:n(({openDialog:u})=>[i(m,{type:"primary",onClick:u,class:"btn-responsive"},{default:n(()=>e[5]||(e[5]=[f("收藏至素材库")])),_:2},1032,["onClick"])]),_:1},8,["show","exportApi","exportParams"])],64)):E("",!0)])]),_:1},8,["title"]),d("div",H,[x.value===0?(v(),c("div",K,e[6]||(e[6]=[d("img",{alt:"",class:"video-icon w-[30%] mb-[24px]",src:z},null,-1),d("p",{class:"tips-2 text-[#999999] text-[14px] mt-[8px]"},"正在合成视频...",-1)]))):(v(),c("video",{key:1,src:r.value.url,poster:r.value.cover,class:"video-item flex flex-col justify-center items-center cursor-pointer bg-[#F2F6F9] transition-all duration-300 ease-in-out aspect-[9/16] w-full max-w-[360px] sm:w-[360px] m-auto",controls:"",controlsList:"nodownload"},null,8,O))])])}}}),fe=W(X,[["__scopeId","data-v-516eced9"]]);export{fe as default};
