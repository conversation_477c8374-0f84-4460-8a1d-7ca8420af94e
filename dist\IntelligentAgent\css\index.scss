@import url("../font/小米字体MiSans-Heavy-embed.css");

$titleH4Size: 2.08vw;
$titleH5Size: 0.83vw;
$funcTitleSize: 1.04vw;
$funcItemImgHeight: 3.44vw;
$formTitleSize: 1.35vw;
$formEnTitleSize: 0.73vw;
$bannerHeight: 34.95vw;
$introMarginTop: 3.02vw;
$introPaddingTop: 6.56vw;
$funcMarginTop: 4.06vw;
$funcItemImgMarginBottom: 1.51vw;
$advMarginTop: 4.01vw;
$advImgW: 7.97vw;
$advImgH: 9.01vw;
$advLiPaddingTop: 1.67vw;
$advLiPaddingBottom: 3.13vw;
$advLiIndexSize: 6.25vw;
$btnWidth: 15.63vw;
$formMarginTop: 2.19vw;
$btnTop: 1.98vw;
$concatPaddingTop: 2.97vw;
$concatPaddingBottom: 3.28vw;
$advArrowSize: 0.57vw;
$advWrapPaddingY: 1.82vw;
$advWrapPaddingX: 2.6vw;
$bannerTagSize: 1.56vw;
$titlePadddingTop: 4.95vw;
$introPaddingBottom: 5.68vw;
$bannerH2Size: 3.44vw;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  text-decoration: none;
  list-style: none;
  line-height: 1;
}
img {
  display: block;
}
.container {
  margin: 0 auto;
  width: 68.75vw;
}
.flex {
  display: flex;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-wrap {
  display: flex;
  flex-wrap: wrap;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-between {
  display: flex;
  justify-content: space-between;
}
.flex-around {
  display: flex;
  justify-content: space-around;
}
.flex-align {
  display: flex;
  align-items: center;
}
.flex-align-stretch {
  display: flex;
  align-items: stretch;
}
.flex-align-start {
  display: flex;
  align-items: start;
}
.flex-align-end {
  display: flex;
  align-items: end;
}
.flex-align-center {
  display: flex;
  align-items: center;
}
.flex-justify {
  display: flex;
  justify-content: center;
}
.flex-justify-end {
  display: flex;
  justify-content: flex-end;
}
.flex-justify-between {
  display: flex;
  justify-content: space-between;
}
.flex-justify-around {
  display: flex;
  justify-content: space-around;
}
.flex-justify-start {
  display: flex;
  justify-content: flex-start;
}
.flex-justify-center {
  display: flex;
  justify-content: center;
}
.flex-1 {
  flex: 1;
  overflow: hidden;
}
.flex-x-center {
  align-items: center;
}
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.img-icon {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
@mixin fs($size, $color: #071b3f, $fontWeight: normal) {
  font-size: $size;
  color: $color;
  font-weight: $fontWeight;
}
@mixin size($width: auto, $height: auto, $bg: transparent) {
  width: $width;
  height: $height;
  background: $bg;
}
@mixin position($top, $right, $bottom, $left) {
  top: $top;
  right: $right;
  bottom: $bottom;
  left: $left;
}
.banner {
  @include size(100%, $bannerHeight, url(../img/banner.png) no-repeat center);
  background-size: 100% 100%;
  .container {
    width: 73%;
  }
  .tag {
    display: inline-flex;
    padding: 0.52vw 1.2vw;
    background: #1b499f;
    border-radius: 1.3vw;
    @include fs(1.56vw, #ffffff, 400);
    margin-top: 11.09vw;
    letter-spacing: 2px;
  }
  h2 {
    @include fs(3.44vw, #ffffff, 800);
    margin-top: 1.56vw;
  }
  h3 {
    @include fs(1.56vw, #ffffff, 400);
    margin-top: 2.14vw;
  }
}
.title {
  padding-top: $titlePadddingTop;
  text-align: center;
  h4 {
    @include fs($titleH4Size);
    font-weight: bold;
  }
  h5 {
    margin-top: 0.89vw;
    @include fs($titleH5Size, #6e7582);
  }
  &.white {
    h4,
    h5 {
      color: #fff;
    }
  }
}
.fake-ele {
  content: "";
  position: absolute;
}
.has-before {
  position: relative;
  &:before {
    @extend .fake-ele;
  }
}
.has-after {
  position: relative;
  &:after {
    @extend .fake-ele;
  }
}
.product-introduction {
  background: #f5f8ff;
  padding-bottom: $introPaddingBottom;
  .content-wrap {
    margin-top: $introMarginTop;
    .intro {
      @include size(53.54vw, auto, #fff);
      padding: $introPaddingTop 10.78vw 4.22vw 2.97vw;
      .has-before::before {
        top: -1.98vw;
        @include size(1.35vw, 3px, #1c6bff);
      }
      p {
        @include fs($titleH5Size, #6e7582);
        line-height: 1.51;
      }
    }
    .img {
      @include position(0, 0, 0, auto);
      @include size(21.98vw, auto);
      margin: auto;
      img {
        @extend.img-icon;
      }
    }
  }
}
.core-function {
  padding-bottom: $advLiPaddingTop;
  .content-wrap {
    margin-top: $funcMarginTop;
    & > div {
      width: 100%;
      .func-item {
        margin-bottom: 4.38vw;
        .img {
          @include size(100%, $funcItemImgHeight);
          margin-bottom: $funcItemImgMarginBottom;
          img {
            @extend .img-icon;
          }
        }
        .func-title {
          @include fs($funcTitleSize);
        }
        .func-desc {
          @include fs($titleH5Size, #6e7582);
          margin-top: 0.94vw;
        }
      }
    }
    .phone{
      display: none;
    }
  }
}
.advantages {
  padding-bottom: 15.16vw;
  background: url("../img/bg1.png") no-repeat center / cover;
  .content-wrap {
    margin-top: $advMarginTop;
    .overview {
      margin: 0 calc(-2.34vw / 2);
      & > li {
        @include size(33.33%, auto);
        padding: 0 calc(2.34vw / 2);
        box-sizing: content-box;
        .wrap {
          @include size(100%, 100%, #fff);
          pointer-events: none; // 禁止事件
          padding: $advLiPaddingTop 0 $advLiPaddingBottom 0;
          .box {
            @include size(100%, 100%);
            @extend .flex-column;
            @extend .flex-center;
            @extend .relative;
            z-index: 1;
            .img {
              @include size($advImgW, $advImgH);
              margin-bottom: 1.35vw;
              img {
                @extend .img-icon;
              }
            }
            .adv-title {
              @include fs($funcTitleSize);
              br{
                display: none;
              }
            }
          }
          &::before {
            content: attr(data-index); // 设置 content 为 data-index 的值
            z-index: 0;
            right: 0.6vw;
            bottom: 0.63vw;
            @include fs($advLiIndexSize, rgba(75, 113, 228, 0.1), 800);
            font-family: "小米字体MiSans-Heavy";
          }
        }
        &.active {
          .content {
            display: block;
          }
        }
      }
    }
    .content {
      margin-top: 2.14vw;
      @extend .container;
      @extend .relative;
      .wrap {
        @extend .relative;
        padding: 1.82vw 2.6vw;
        background: #6b8ffb;
        display: none;
        @extend .absolute;
        width: 100%;
        &::before {
          content: "";
          @include size($advArrowSize, $advArrowSize, #6b8ffb);
          z-index: 0;
          top: 0;
          transform: rotate(45deg) translateY(-50%);
        }
        ul {
          li {
            list-style-type: square;
            @include fs(0.83vw, #fff);
            line-height: 1.5;
            & + li {
              margin-top: 0.52vw;
            }
          }
        }
        &:first-child {
          &::before {
            left: calc(33.33% / 2 - 1.17vw);
          }
        }
        &:nth-child(2) {
          &::before {
            left: 50%;
            transform: rotate(45deg) translateX(-50%);
          }
        }
        &:last-child {
          &::before {
            right: calc(33.33% / 2 - 1.17vw);
          }
        }
        &.active {
          display: block;
        }
      }
    }
  }
  .phone{
    display: none;
  }
}
.concat-us {
  background: url("../img/bg2.png") no-repeat;
  background-size: cover;
  padding: $concatPaddingTop 0 $concatPaddingBottom;
  text-align: center;
  .form-title {
    @include fs($formTitleSize, #fff);
    br{
      display: none;
    }
  }
  .form-en-title {
    @include fs($formEnTitleSize, rgba(255, 255, 255, 0.5));
    margin-top: 0.57vw;
  }
  .form {
    @extend .container;
    @extend .flex-between;
    margin-top: $formMarginTop;
    @extend .flex-wrap;
    .input-wrap {
      width: 25%;
      @extend .relative;
      padding: 0 0.52vw;
      input {
        @include size(100%, $titleH4Size, rgba(255, 255, 255, 0.1));
        border: none;
        outline: transparent;
        border-radius: $titleH4Size;
        padding: 0 0.78vw;
        box-sizing: border-box;
        @include fs($formEnTitleSize, rgba(255, 255, 255, 0.8));
        &::placeholder {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
  .btn-wrap {
    margin-top: $btnTop;
    .consulting-button {
      @include size($btnWidth, $titleH4Size, #1c6bff);
      border-radius: $titleH4Size;
      @include fs($formEnTitleSize, #fff);
      cursor: pointer;
    }
  }
}
@media only screen and (max-width: 1400px) {
  $scale: 1.4;
  .container {
    width: 70vw;
  }
  .title {
    h4 {
      font-size: $titleH4Size * $scale;
    }
    h5 {
      font-size: $titleH5Size * $scale;
    }
  }
  .product-introduction {
    .content-wrap {
      .intro {
        padding-right: 16.78vw;
        width: 60vw;
        p {
          font-size: $titleH5Size * $scale;
        }
      }
    }
  }
  .core-function {
    padding-bottom: $advLiPaddingTop * $scale;
    .content-wrap {
      & > div {
        .func-item {
          .img {
            @include size(100%, $funcItemImgHeight * $scale);
          }
          .func-title {
            font-size: $titleH5Size * $scale;
          }
          .func-desc {
            font-size: $titleH5Size * $scale;
          }
        }
      }
    }
  }
  .advantages {
    .content-wrap {
      .overview > li .wrap .box .adv-title {
        font-size: $titleH5Size * $scale;
      }
      .content {
        .wrap {
          ul {
            li {
              font-size: $titleH5Size * $scale;
            }
          }
        }
      }
    }
  }
  .concat-us {
    padding: $concatPaddingTop * $scale $concatPaddingTop * $scale $concatPaddingBottom * $scale;
    .form-title {
      font-size: $formTitleSize * $scale;
    }
    .form-en-title {
      font-size: $formEnTitleSize * $scale;
    }
    .form {
      margin-top: $formMarginTop * $scale;
      .input-wrap input {
        @include size(100%, $titleH4Size * $scale, rgba(255, 255, 255, 0.1));
        font-size: $formEnTitleSize * $scale;
      }
    }
    .btn-wrap {
      margin-top: $btnTop * $scale;
      .consulting-button {
        font-size: $titleH5Size * $scale;
        @include size($btnWidth * $scale, $titleH4Size * $scale, #1c6bff);
      }
    }
  }
}
@media only screen and (max-width: 1260px) {
  $scale: 1.4;
  .banner {
    height: $bannerHeight * $scale;
    .tag {
      font-size: $bannerTagSize * $scale;
      border-radius: 2vw;
      padding: $advArrowSize * $scale $formTitleSize * $scale;
    }
  }
  .container {
    width: 80vw;
  }
  .title {
    h4 {
      font-size: $titleH4Size * $scale;
    }
    h5 {
      font-size: $titleH5Size * $scale;
    }
  }
  .product-introduction {
    .content-wrap {
      margin-top: $introMarginTop * $scale;
      .intro {
        padding-top: $introPaddingTop * $scale;
        width: 67vw;
        padding-right: 14vw;
        p {
          font-size: $titleH5Size * $scale;
        }
      }
    }
  }
  .core-function {
    padding-bottom: $advLiPaddingTop * $scale;
    .content-wrap {
      margin-top: $funcMarginTop * $scale;
      & > div {
        .func-item {
          .img {
            font-size: $funcItemImgHeight * $scale;
            margin-bottom: $funcItemImgMarginBottom * $scale;
          }
          .func-title {
            font-size: $titleH5Size * $scale * 1.1;
          }
          .func-desc {
            font-size: $titleH5Size * $scale;
          }
        }
      }
    }
  }
  .advantages {
    .content-wrap {
      margin-top: $advMarginTop * $scale;
      .overview > li .wrap {
        padding: $advLiPaddingTop * $scale 0 $advLiPaddingBottom * $scale 0;
        .box {
          .img {
            @include size($advImgW * $scale, $advImgH * $scale);
          }
          .adv-title {
            font-size: $funcTitleSize * $scale;
          }
        }
        &::before {
          font-size: $advLiIndexSize * $scale;
        }
      }
      .content {
        .wrap {
          ul {
            li {
              font-size: $titleH5Size * $scale;
            }
          }
        }
      }
    }
  }
  .concat-us {
    padding: $concatPaddingTop * $scale $concatPaddingTop * $scale $concatPaddingBottom * $scale;
    .form-title {
      font-size: $formTitleSize * $scale;
    }
    .form-en-title {
      font-size: $formEnTitleSize * $scale;
    }
    .form {
      margin-top: $formMarginTop * $scale;
      .input-wrap input {
        @include size(100%, $titleH4Size * $scale, rgba(255, 255, 255, 0.1));
        font-size: $formEnTitleSize * $scale;
      }
    }
    .btn-wrap {
      margin-top: $btnTop * $scale;
      .consulting-button {
        font-size: $titleH5Size * $scale;
        @include size($btnWidth * $scale, $titleH4Size * $scale, #1c6bff);
      }
    }
  }
}
@media only screen and (max-width: 1200px) {
  $scale: 1.6;
  .banner {
    height: $bannerHeight * $scale * 0.8;
    .tag {
      font-size: $bannerTagSize * $scale;
      border-radius: 2vw;
      padding: $advArrowSize * $scale $formTitleSize * $scale;
    }
    h2 {
      margin-top: $formMarginTop * $scale;
      font-size: $formMarginTop * $scale;
    }
    h3 {
      font-size: $formTitleSize * $scale;
      margin-top: $formMarginTop * $scale;
    }
  }
  .title {
    h4 {
      font-size: $titleH4Size * $scale;
    }
    h5 {
      font-size: $titleH5Size * $scale;
    }
  }
  .product-introduction {
    .content-wrap {
      margin-top: $introMarginTop * $scale;
      background-color: #fff;
      .intro {
        padding-top: $introPaddingTop * $scale;
        width: 100%;
        padding-right: 14vw;
        p {
          font-size: $titleH5Size * $scale;
          line-height: 2;
        }
        .has-before::before {
          top: -4vw;
        }
      }
      .img {
        position: static;
        justify-content: end;
        width: 100%;
        padding-right: 1.56vw;
        padding-bottom: 1.56vw;
        img {
          width: 21.98vw;
        }
      }
    }
  }
  .core-function {
    padding-bottom: $advLiPaddingTop * $scale;
    .content-wrap {
      margin-top: $funcMarginTop * $scale;
      & > div {
        .func-item {
          .img {
            font-size: $funcItemImgHeight * $scale;
            margin-bottom: $funcItemImgMarginBottom * $scale;
          }
          .func-title {
            font-size: $titleH5Size * $scale * 1.1;
          }
          .func-desc {
            font-size: $titleH5Size * $scale;
          }
        }
      }
    }
  }
  .advantages {
    padding-bottom: 20.63vw;
    .content-wrap {
      margin-top: $advMarginTop * $scale;
      .overview > li .wrap {
        padding: $advLiPaddingTop * $scale 0 $advLiPaddingBottom * $scale 0;
        .box {
          .img {
            @include size($advImgW * $scale, $advImgH * $scale);
            margin-bottom: 2vw;
          }
          .adv-title {
            font-size: $funcTitleSize * $scale;
          }
        }
        &::before {
          font-size: $advLiIndexSize * $scale;
        }
      }
      .content {
        .wrap {
          padding: $advWrapPaddingY * $scale $advWrapPaddingX * $scale;
          ul {
            li {
              font-size: $titleH5Size * $scale;
              line-height: 1.8;
              & + li {
                margin-top: 1.5vw;
              }
            }
          }
          &::before {
            @include size($advArrowSize * $scale, $advArrowSize * $scale, #6b8ffb);
          }
        }
      }
    }
  }
  .concat-us {
    padding: $concatPaddingTop * $scale * 1.2 $concatPaddingTop * $scale * 1.2 $concatPaddingBottom * $scale * 1.2;
    .form-title {
      font-size: $formTitleSize * $scale;
    }
    .form-en-title {
      font-size: $formEnTitleSize * $scale;
    }
    .form {
      margin-top: $formMarginTop * $scale * 1.2;
      .input-wrap input {
        @include size(100%, $titleH4Size * $scale * 1.2, rgba(255, 255, 255, 0.1));
        font-size: $formEnTitleSize * $scale;
        padding: 0 1.56vw;
      }
    }
    .btn-wrap {
      margin-top: $btnTop * $scale * 1.2;
      .consulting-button {
        font-size: $titleH5Size * $scale;
        @include size($btnWidth * $scale, $titleH4Size * $scale * 1.2, #1c6bff);
      }
    }
  }
}
@media only screen and (max-width: 1100px) {
  $scale: 2;
  .banner {
    height: $bannerHeight * $scale * 0.6;
    padding-top: 70px;
  }
  .title {
    padding-top: $titlePadddingTop * $scale;
    h4 {
      font-size: $titleH4Size * $scale * 0.9;
    }
    h5 {
      font-size: $titleH5Size * $scale;
    }
  }
  .product-introduction {
    padding-bottom: $introPaddingBottom * $scale;
    .content-wrap {
      margin-top: $introMarginTop * $scale;
      background-color: #fff;
      .intro {
        padding-top: $introPaddingTop * $scale;
        width: 100%;
        padding-left: 6vw;
        padding-right: 6vw;
        p {
          font-size: $titleH5Size * $scale;
          line-height: 2;
        }
        .has-before::before {
          top: -4vw;
        }
      }
      .img {
        position: static;
        justify-content: end;
        width: 100%;
        padding-right: 1.56vw;
        padding-bottom: 1.56vw;
        img {
          width: 30vw;
        }
      }
    }
  }
  .core-function {
    padding-bottom: $advLiPaddingTop * $scale;
    .content-wrap {
      margin-top: $funcMarginTop * $scale;
      & > div {
        .func-item {
          margin-bottom: $titlePadddingTop * $scale;
          .img {
            font-size: $funcItemImgHeight * $scale;
            margin-bottom: $funcItemImgMarginBottom * $scale;
          }
          .func-title {
            font-size: $titleH5Size * $scale * 1.1;
          }
          .func-desc {
            font-size: $titleH5Size * $scale;
            margin-top: $advArrowSize * $scale;
          }
        }
      }
    }
  }
  .advantages {
    padding-bottom: 40vw;
    .content-wrap {
      margin-top: $advMarginTop * $scale;
      .overview > li .wrap {
        padding: $advLiPaddingTop * $scale 0 $advLiPaddingBottom * $scale 0;
        .box {
          .img {
            @include size($advImgW * $scale, $advImgH * $scale);
            margin-bottom: 2vw;
          }
          .adv-title {
            font-size: $funcTitleSize * $scale;
            line-height: 2;
            br {
              display: block;
            }
          }
        }
        &::before {
          font-size: $advLiIndexSize * $scale;
        }
      }
      .content {
        .wrap {
          padding: $advWrapPaddingY * $scale $advWrapPaddingX * $scale;
          ul {
            li {
              font-size: $titleH5Size * $scale;
              line-height: 1.8;
              & + li {
                margin-top: 1.5vw;
              }
            }
          }
          &::before {
            @include size($advArrowSize * $scale, $advArrowSize * $scale, #6b8ffb);
          }
        }
      }
    }
  }
  .concat-us {
    padding: $concatPaddingTop * $scale * 1.2 $concatPaddingTop * $scale * 1.2 $concatPaddingBottom * $scale * 1.2;
    .form-title {
      font-size: $formTitleSize * $scale;
    }
    .form-en-title {
      font-size: $formEnTitleSize * $scale;
    }
    .form {
      margin-top: $formMarginTop * $scale * 1.2;
      .input-wrap{
        width: 50%;
        margin-bottom: 2vw;
        input {
          @include size(100%, $titleH4Size * $scale * 1.2, rgba(255, 255, 255, 0.1));
          font-size: $formEnTitleSize * $scale;
          padding: 0 1.56vw;
        }
      }
    }
    .btn-wrap {
      margin-top: $btnTop * $scale * 1.2;
      .consulting-button {
        font-size: $titleH5Size * $scale;
        @include size($btnWidth * $scale, $titleH4Size * $scale * 1.2, #1c6bff);
      }
    }
  }
}
@media only screen and (max-width: 768px) {
  $scale: 2.4;
  .banner {
    height: $bannerHeight * $scale * 0.6;
  }
  .title {
    padding-top: $titlePadddingTop * $scale;
    h4 {
      font-size: $titleH4Size * $scale * 0.8;
    }
    h5 {
      font-size: $titleH5Size * $scale;
    }
  }
  .product-introduction {
    padding-bottom: $introPaddingBottom * $scale;
    .content-wrap {
      margin-top: $introMarginTop * $scale;
      background-color: #fff;
      .intro {
        padding-top: $introPaddingTop * $scale;
        width: 100%;
        padding-left: 6vw;
        padding-right: 6vw;
        p {
          font-size: $titleH5Size * $scale;
          line-height: 2;
        }
        .has-before::before {
          top: -4vw;
        }
      }
      .img {
        position: static;
        justify-content: end;
        width: 100%;
        padding-right: 1.56vw;
        padding-bottom: 1.56vw;
        img {
          width: 30vw;
        }
      }
    }
  }
  .core-function {
    padding-bottom: $advLiPaddingTop * $scale;
    .content-wrap {
      margin-top: $funcMarginTop * $scale;
      & > div {
        .func-item {
          margin-bottom: $titlePadddingTop * $scale;
          .img {
            font-size: $funcItemImgHeight * $scale;
            margin-bottom: $funcItemImgMarginBottom * $scale;
          }
          .func-title {
            font-size: $titleH5Size * $scale * 1.1;
          }
          .func-desc {
            font-size: $titleH5Size * $scale;
            margin-top: $advArrowSize * $scale;
          }
        }
      }
    }
  }
  .advantages {
    padding-bottom: 40vw;
    .content-wrap {
      margin-top: $advMarginTop * $scale;
      .overview > li .wrap {
        padding: $advLiPaddingTop * $scale 0 $advLiPaddingBottom * $scale 0;
        .box {
          .img {
            @include size($advImgW * $scale, $advImgH * $scale);
            margin-bottom: 2vw;
          }
          .adv-title {
            font-size: $funcTitleSize * $scale;
            line-height: 2;
            br {
              display: block;
            }
          }
        }
        &::before {
          font-size: $advLiIndexSize * $scale;
        }
      }
      .content {
        .wrap {
          padding: $advWrapPaddingY * $scale $advWrapPaddingX * $scale;
          ul {
            li {
              font-size: $titleH5Size * $scale;
              line-height: 1.8;
              & + li {
                margin-top: 1.5vw;
              }
            }
          }
          &::before {
            @include size($advArrowSize * $scale, $advArrowSize * $scale, #6b8ffb);
          }
        }
      }
    }
  }
  .concat-us {
    padding: $concatPaddingTop * $scale * 1.2 $concatPaddingTop * $scale * 1.2 $concatPaddingBottom * $scale * 1.2;
    .form-title {
      font-size: $formTitleSize * $scale;
    }
    .form-en-title {
      font-size: $formEnTitleSize * $scale;
    }
    .form {
      margin-top: $formMarginTop * $scale * 1.2;
      .input-wrap{
        width: 50%;
        margin-bottom: 2vw;
        input {
          @include size(100%, $titleH4Size * $scale * 1.2, rgba(255, 255, 255, 0.1));
          font-size: $formEnTitleSize * $scale;
          padding: 0 1.56vw;
        }
      }
    }
    .btn-wrap {
      margin-top: $btnTop * $scale * 1.2;
      .consulting-button {
        font-size: $titleH5Size * $scale;
        @include size($btnWidth * $scale, $titleH4Size * $scale * 1.2, #1c6bff);
      }
    }
  }
}
@media only screen and (max-width: 700px) {
  .banner {
    padding-top: 50px;
  }
}
@media only screen and (max-width: 500px) {
  $scale: 4;
  $fontScale: 6;
  .container{
    width: 90vw;
  }
  .banner {
    height: $bannerHeight * $scale * 0.5;
    background-position: 70% bottom;
    background: url(../img/banner.png) no-repeat 60% / cover;
    .container{
      width: 90vw;
    }
    .tag{
      font-size: $titleH5Size * $scale;
      padding: 2vw 4vw;
      border-radius: 4vw;
      margin-top: 20vw;
    }
    h2 {
      font-size: 5vw;
      margin-top: 8vw;
    }
    h3{
      font-size: 3vw;
      margin-top: 5vw;
    }
  }
  .title {
    padding-top: 11.88vw;
    h4 {
      font-size: 5.6vw;
    }
    h5 {
      font-size: 3vw;
      margin-top: 3vw;
    }
  }
  .product-introduction {
    .content-wrap {
      .intro {
        padding: 6vw;
        padding-top: 12vw;
        p {
          font-size: $titleH5Size * $scale;
          line-height: 2;
        }
        .has-before::before {
          width: 10px;
        }
      }
      .img {
        padding-right: 4vw;
        padding-bottom: 4vw;
        img {
          width: 40vw;
        }
      }
    }
  }
  .core-function {
    .content-wrap {
      margin-top: $funcMarginTop * $scale;
      & > div {
        .func-item {
          padding: 0 4vw;
          .img {
            height: 7vw;
            margin-bottom: $funcItemImgMarginBottom * $scale;
          }
          .func-title {
            font-size: $titleH5Size * $scale;
          }
          .func-desc {
            font-size: $titleH5Size * $scale * 0.9;
            margin-top: $advArrowSize * $scale;
          }
        }
      }
      .phone{
        display: flex;
        flex-wrap: wrap;
        justify-content: start;
      }
      .pc{
        display: none;
      }
    }
  }
  .advantages {
    padding-bottom: 20vw;
    .pc{
      display: none;
    }
    .phone{
      display: block;
      & > ul > li{
        // 给我生成一个带圆角的卡片样式
        background-color: #fff;
        margin-bottom: 10vw;
        padding: 10vw;
        position: relative;
        & + li {
          margin-top: 10vw;
        }
        .adv-title{
          align-items: center;
          p{
            @include fs(4vw, #6b8ffb, bold);
          }
        }
        .desc{
          margin-top: 5vw;
          img {
            // 固定到右下角
            float: right;
            right: 10vw;
            bottom: 10vw;
            width: 40vw;
            z-index: 1;
            margin-top: -30vw;
            margin-right: -15vw;
          }
          ul{
            margin-bottom: 22.58vw;
            li{
              list-style-type: square;
              margin-top: 2vw;
              @include fs(3.5vw);
              line-height: 2;
            }
          }
        }
      }
    }
  }
  .concat-us {
    padding: $concatPaddingTop * $scale * 1.2 10vw $concatPaddingBottom * $scale * 1.2;
    .form-title {
      font-size: $formTitleSize * $scale;
      line-height: 1.5;
      br{
        display: block;
      }
    }
    .form-en-title {
      font-size: $formEnTitleSize * $scale;
      margin-top: 3vw;
    }
    .form {
      width: 100%;
      margin-top: $formMarginTop * $scale * 1.2;
      display: block;
      .input-wrap{
        width: 100%;
        margin-bottom: 4vw;
        input {
          @include size(100%, $titleH4Size * $scale * 1.2, rgba(255, 255, 255, 0.1));
          font-size: $formEnTitleSize * $scale;
          padding: 0 1.56vw;
        }
      }
    }
    .btn-wrap {
      margin-top: $btnTop * $scale * 1.2;
      .consulting-button {
        font-size: $titleH5Size * $scale;
        @include size($btnWidth * $scale, $titleH4Size * $scale * 1.2, #1c6bff);
      }
    }
  }
}


.message-box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgb(0, 0, 0);
  color: #fff;
  padding: 20px 30px;
  border-radius: 4px;
}

#message-text {
  color: #fff;
}