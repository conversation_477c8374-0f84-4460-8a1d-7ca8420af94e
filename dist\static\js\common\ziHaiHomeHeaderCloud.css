#zhyHomeHeader, #zhyHomeHeader * {
  margin: 0;
  padding: 0;
}

#zhy<PERSON>omeHeader,
#zhyHomeHeader div,
#zhyHomeHeader dl,
#zhyHomeHeader dt,
#zhyHomeHeader dd,
#zhyHomeHeader ol,
#zhyHomeHeader ul,
#zhyHomeHeader li,
#zhyHomeHeader h1,
#zhyHomeHeader h2,
#zhyHomeHeader h3,
#zhyHomeHeader h4,
#zhyHomeHeader h5,
#zhyHomeHeader h6,
#zhyHomeHeader pre,
#zhyHomeHeader form,
#zhyHomeHeader fieldset,
#zhyHomeHeader input,
#zhyHomeHeader textarea,
#zhyHomeHeader p,
#zhyHomeHeader blockquote,
#zhyHomeHeader th,
#zhyHomeHeader td,
#zhyHomeHeader p {
  margin: 0;
  padding: 0;
  font-size: 14px;
}

#zhyHomeHeader li {
  list-style: none;
}

#zhyHomeHeader {
  height: 70px;
  margin: 0;
  background-color: white;
  width: 80%;
  padding: 0 10%;
  z-index: 999999;
  font-size: 14px;
  color: #333;
  position: relative;
  box-sizing: content-box;
}

#zhyHomeHeader > div {
  display: flex;
  align-items: center;
  height: 70px;
  margin: 0 auto;
  background-color: white;
  width: 100%;
  z-index: 999999;
  font-size: 14px;
  color: #333;
}

#zhyHomeHeader .el-menu--horizontal>.el-menu-item {
  height: 70px;
  line-height: 70px;
  display: flex !important;
  align-items: center;
}

#zhyFooter .container {
  width: 1320px;
  margin: auto;
}

#zhyHomeHeader .el-menu .el-menu-item.is-active a {
  font-size: 14px;
  color: #333333;
  text-decoration: none;
}

#zhyHomeHeader .zhyHomeHeaderPhone {
  font-size: 14px;
  color: #333333;
  position: static;
}

#zhyHomeHeader .zhyHomeHeader-title {
  display: flex;
  cursor: pointer;
}

#zhyHomeHeader .zhyHomeHeader-title img {
  height: 30px;
  margin-left: 0;
  width: auto;
}

#zhyHomeHeader .el-menu.el-menu--horizontal {
  padding: 0 55px;
}

#zhyHomeHeader .el-menu {
  flex: 1;
}

#zhyHomeHeader .el-menu .el-menu-item {
  padding: 0;
  margin-right: 34px;
  line-height: 70px;
}

#zhyHomeHeader .el-menu .el-menu-item a {
  font-size: 14px;
  color: #909399;
  text-decoration: none;
  display: flex;
  align-items: center;
}

#zhyHomeHeader .el-menu .el-menu-item a i.iconNew {
  color: red;
  text-transform: uppercase;
  font-size: 12px;
  transform: scale(0.8);
  transform-origin: center;
  font-weight: bold;
  margin-left: 2px;
  font-style: normal;
}

#zhyHomeHeader .el-menu .el-menu-item::after {
  content: "";
  display: block;
}

#zhyHomeHeader .el-menu .el-menu-item .iconfont {
  display: inline-block;
  float: right;
  transform: scaleX(1.8) rotate(90deg);
  margin-left: 16px;
  color: #222;
  font-size: 12px;
  font-style: normal;
}

#zhyHomeHeader .el-menu .btn_clear {
  position: absolute;
  left: 0;
  width: 42px;
  height: 70px;
}

#zhyHomeHeader .el-menu.el-menu--horizontal {
  height: 100%;
  border-bottom: 0 solid #bc0210;
}

#zhyHomeHeader .el-menu--horizontal > .el-menu-item {
  border-bottom: 0 solid #bc0210;
}

#zhyHomeHeader .el-menu--horizontal > .el-menu-item.is-active {
  height: 100%;
  border-bottom: 4px solid #bc0210;
}

#zhyHomeHeader .el-menu--horizontal > .el-menu-item:hover a {
  color: #333;
}

#zhyHomeHeader .el-menu--horizontal > .el-menu-item.is-active::after, #zhyHomeHeader .el-menu--horizontal > .el-menu-item:hover {
  display: block;
}

#zhyHomeHeader .input {
  display: none;
  position: relative;
  margin-right: 38px;
}

#zhyHomeHeader .input .el-input--suffix .el-input__inner {
  padding-right: 90px !important;
}

#zhyHomeHeader .input .el-input__suffix {
  right: 60px !important;
}

#zhyHomeHeader .input a {
  position: absolute;
  top: 0;
  right: 20px;
  font-size: 15px;
  height: 36px;
  line-height: 36px;
  color: rgba(0, 0, 0, 0.65);
  text-decoration: none;
}

#zhyHomeHeader .icon {
  display: flex;
  cursor: pointer;
  color: #333333;
  height: 100%;
}

#zhyHomeHeader .icon span {
  display: flex;
  align-items: center;
  margin-right: 20px;
  height: 100%;
}

#zhyHomeHeader .icon span i {
  font-size: 18px;
  margin-right: 5px;
}

#zhyHomeHeader .icon span p {
  font-size: 15px;
  height: 100%;
}

#zhyHomeHeader .icon span a {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 14px;
  color: #333333;
  text-decoration: none;
}

#zhyHomeHeader .icon span:hover a {
  color: #bc0210;
}

#zhyHomeHeader .icon span:hover i {
  color: #bc0210;
}

#zhyHomeHeader .icon span:hover p {
  color: #bc0210;
}

#zhyHomeHeader .login {
  margin-left: 45px;
  margin-right: 30px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-weight: bold;
}

#zhyHomeHeader .login span {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-weight: bold;
}

#zhyHomeHeader .login:hover {
  color: #bc0210;
}

#zhyHomeHeader .regist {
  margin-right: 60px;
  background-color: #bc0210;
  font-size: 14px;
  font-weight: bold;
  border-radius: 0;
  color: #fff;
}

#zhyHomeHeader .regist span {
  font-size: 14px;
  color: #fff;
}

#zhyHomeHeader .avatar-container .avatar-wrapper .el-image {
  margin-right: 12px;
  width: 35px;
  height: 35px;
  cursor: pointer;
  border-radius: 17px;
}

#zhyHomeHeader .avatar-container .avatar-wrapper .image-slot {
  width: 35px;
  height: 35px;
  cursor: pointer;
}

#zhyHomeHeader .dropdown_menu {
  display: flex;
}

#zhyHomeHeader .dropdown_menu.no-child{
  display: block;
}

#zhyHomeHeader .dropdown_menu > li > ul {
  min-width: 120px;
}

#zhyHomeHeader .dropdown_menu > li > ul > li {
  margin-bottom: 15px;
  float: none;
}

#zhyHomeHeader .dropdown_menu > li > ul > li a {
  margin-left: 15px;
  font-weight: normal;
  font-size: 13px;
  color: #666666;
  text-decoration: none;
}

#zhyHomeHeader .dropdown_menu > li > ul > li a {
  margin-left: 0;
}

#zhyHomeHeader .dropdown_menu > li > ul > li a:hover {
  color: #bc0210;
}

#zhyHomeHeader .dropdown_menu > li > ul > li:nth-child(1) {
  color: #333333;
  font-weight: bold;
  font-size: 16px;
  display: flex;
  align-items: center;
}

#zhyHomeHeader .dropdown_menu > li > ul > li:nth-child(1) p {
  width: 9px;
  height: 3px;
  margin-right: 6px;
  background-color: #bc0210;
}

#zhyHomeHeader .dropdown_menu.no-child > li > ul > li:nth-child(1) p{
  display: none;
}

#zhyHomeHeader a {
  text-decoration: none;
  display: block;
}

#zhyHomeHeader .el-popover {
  top: 80px;
  display: none;
}

#zhyHomeHeader .el-popover::before {
  position: absolute;
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  background: #fff;
  left: 5px;
  top: -5px;
  transform: rotate(45deg);
  z-index: -1;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

#zhyHomeHeader .el-menu-item::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  background: transparent;
  top: 68px;
  left: 0;
  z-index: 20;
  display: none;
}

#zhyHomeHeader .el-menu-item:hover .el-popover {
  display: block;
}

#zhyHomeHeader .el-dropdown-menu {
  position: absolute;
  margin: auto;
  top: 50px;
  width: 100px;
  display: none;
}

.el-dropdown-menu__item:focus a, .el-dropdown-menu__item:not(.is-disabled):hover a {
  color: #66b1ff;
}

#zhyHomeHeader .el-dropdown-menu a {
  text-decoration: none;
  font-size: 14px;
}

#zhyHomeHeader .avatar-container:hover .el-dropdown-menu {
  display: block;
}

#zhyHomeHeader .avatar-container::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  left: 0;
  top: 40px;
  z-index: 20;
  cursor: none;
}

#zhyHomeHeader .el-dropdown-menu::before {
  position: absolute;
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  background: #fff;
  right: 0;
  left: 0;
  margin: auto;
  top: -5px;
  transform: rotate(45deg);
  z-index: -1;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

#zhyHomeHeader .el-dropdown-menu::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 1;
  background: #fff;
}

#zhyHomeHeader .el-dropdown-menu li {
  position: relative;
  z-index: 2;
  border: none;
}

#zhyHomeHeader .el-dropdown-menu li span {
  font-size: 14px;
}

#zhyHomeHeader .el-menu .el-menu-item:last-child {
  margin-right: 0;
}

#zhyHomeHeader > .zhyHomeHeaderPhone {
  display: none;
  position: relative;
}

#zhyHomeHeader .zhyHomeHeaderPhone .zhyHomeHeader-menu {
  width: 40px;
  height: 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 20px;
}

#zhyHomeHeader .zhyHomeHeaderPhone .regist {
  margin-left: 0px;
}

#zhyHomeHeader .zhyHomeHeaderPhone .zhyHomeHeader-menu span {
  display: block;
  width: 100%;
  height: 4px;
  border-radius: 4px;
  background: #333;
  pointer-events: none;
  transition: transform .5s;
}

#zhyHomeHeader .zhyHomeHeaderPhone.menu-wrap {
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  padding: 40px 10%;
  box-sizing: border-box;
  display: block;
  height: calc(100vh - 50px);
  overflow: auto;
}

#zhyHomeHeader .zhyHomeHeaderPhone .el-menu {
  width: 100%;
  min-height: 100%;
  border-right: 0;
}

#zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item {
  margin-right: 0;
  margin-bottom: 0;
  position: relative;
  height: auto;
  overflow: hidden;
}

#zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item > div {
  display: flex;
  align-items: center;
}

#zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item a {
  flex-grow: 1;
}

#zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item .iconfont {
  display: flex;
  width: 30px;
  transform: scaleY(1.8) rotate(0);
  margin: 0;
  padding: 0 20px;
  justify-content: center;
  align-items: center;
  font-style: normal;
  transition: transform 0.5s;
}

#zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item .iconfont.show {
  transform: scaleX(1.8) rotate(90deg);
}

#zhyHomeHeader .zhyHomeHeaderPhone .dropdown_menu {
  display: block;
  width: 100%;
  box-sizing: border-box;
  padding-left: 20px;
}

#zhyHomeHeader .zhyHomeHeaderPhone .dropdown_menu > li > ul > li a {
  margin-left: 0;
}

#zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item::after {
  display: none;
}

#zhyHomeHeader .zhyHomeHeaderPhone .avatar-container .avatar-wrapper .el-image {
  margin-right: 0;
}

#zhyHomeHeader .zhyHomeHeaderPhone .zhyHomeHeader-menu.open span:nth-child(2) {
  display: none;
}

#zhyHomeHeader .zhyHomeHeaderPhone .zhyHomeHeader-menu.open span:nth-child(1) {
  transform-origin: top left;
  width: 95%;
  transform: rotate(45deg);
}

#zhyHomeHeader .zhyHomeHeaderPhone .zhyHomeHeader-menu.open span:nth-child(3) {
  transform-origin: bottom left;
  width: 95%;
  transform: rotate(-45deg);
}

@media (max-width: 1680px) {
  #zhyHomeHeader {
    width: 90%;
    padding: 0 5%;
  }

  #zhyHomeHeader .regist {
    margin-right: 0;
  }
}

@media (max-width: 1600px) {
  #zhyHomeHeader {
    padding: 0 4%;
    width: 92%;
  }

  #zhyHomeHeader .el-menu.el-menu--horizontal {
    padding: 0 30px;
  }

  #zhyHomeHeader .el-menu .el-menu-item {
    margin-right: 40px;
  }

  #zhyHomeHeader .login {
    margin-left: 35px;
    margin-right: 20px;
  }

  #zhyHomeHeader .el-button {
    padding: 10px 15px;
  }
}

@media (max-width: 1470px) {
  #zhyHomeHeader .el-menu.el-menu--horizontal {
    padding: 0 40px;
  }

  #zhyHomeHeader .el-menu .el-menu-item {
    margin-right: 40px;
  }

  #zhyHomeHeader .icon svg + p {
    display: none;
  }

  #zhyHomeHeader .el-menu.el-menu--horizontal {
    padding: 0 30px;
  }

  #zhyHomeHeader .login {
    margin-left: 15px;
  }
}

@media (max-width: 1380px) {
  #zhyHomeHeader .login {
    margin: 0 10px 0 0;
  }

  #zhyHomeHeader {
    padding: 0 3%;
    width: 94%;
  }

  #zhyHomeHeader .zhyHomeHeader-title img {
    height: 26px;
  }

  #zhyHomeHeader .el-menu .el-menu-item {
    margin-right: 30px;
  }
}

@media (max-width: 1280px) {
  #zhyHomeHeader .icon span {
    margin-right: 10px;
  }

  #zhyHomeHeader .login {
    margin: 0;
  }

  #zhyHomeHeader .el-menu .el-menu-item .iconfont {
    margin-left: 8px;
  }

  #zhyHomeHeader .el-menu .el-menu-item {
    margin-right: 20px;
  }
}

@media (max-width: 1100px) {
  #zhyHomeHeader .el-menu .el-menu-item {
    margin-right: 20px;
  }

  #zhyHomeHeader .zhyHomeHeaderPc {
    display: none
  }

  #zhyHomeHeader {
    position: fixed;
    top: 0;
    box-shadow: 0 0 10px rgba(30, 144, 255, 0.3);
  }

  #zhyHomeHeader + div:not(.clear) {
    margin-top: 70px;
  }

  #zhyHomeHeader .zhyHomeHeaderPhone:not(.menu-wrap) {
    display: flex !important;
    justify-content: space-between;
  }

  #zhyHomeHeader .zhyHomeHeaderPhone:not(.menu-wrap) .zhyHomeHeader-logo-wrap {
    display: flex;
    align-items: center;
  }

  #zhyHomeHeader .zhyHomeHeaderPhone:not(.menu-wrap) .zhyHomeHeader-logo {
    height: 26px !important;
    width: auto;
  }

  #zhyHomeHeader .zhyHomeHeaderPhone:not(.menu-wrap) .zhyHomeHeader-logo + span {
    margin-left: 20px;
    color: #6a6a6a;
  }

  #zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item a, #zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item .iconfont {
    font-size: 2vw;
  }

  #zhyHomeHeader .zhyHomeHeaderPhone-button-wrap {
    display: flex;
    align-items: center;
  }
}

@media (max-width: 1000px) {
  #zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item, #zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item > div{
    min-height: 50px;
  }
}

@media (max-width: 800px) {
  #zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item a, #zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item .iconfont {
    font-size: 3vw;
  }
}

@media (max-width: 700px) {
  #zhyHomeHeader .zhyHomeHeaderPhone-button-wrap {
    display: none;
  }

  #zhyHomeHeader {
    padding: 0 4%;
    height: 50px;
    width: 94%;
  }

  #zhyHomeHeader > div {
    height: 50px;
  }

  #zhyHomeHeader + div:not(.clear) {
    margin-top: 50px;
  }
}

.goBack{
  position: fixed;
  right: 20px;
  bottom: 30px;
  z-index: 99;
  cursor: pointer;
}

@media (max-width: 600px) {
  .goBack{
    width: 50px;
    height: 50px;
    bottom: 22%;
    right: 10px;
  }
}

@media (max-width: 400px) {
  #zhyHomeHeader .zhyHomeHeaderPhone:not(.menu-wrap) .zhyHomeHeader-logo + span {
    display: none;
  }
}
