import{k as z,l as M,A as N,B as P,f as u,w as c,H as D,o as r,b as o,a,p as n,j as _,t as m,c as p,s as U,Y as F,F as Q,W as $,q as j,J as q,ac as H,m as I,ad as J,h as O,P as W,a4 as Y,Q as G,_ as K}from"./index-BBeD0eDz.js";/* empty css                *//* empty css                     *//* empty css                 *//* empty css                *//* empty css               */import{A as R}from"./AddAccount-C5BKmRJu.js";import{u as X}from"./shortVideo-BFWTeahp.js";import{u as Z}from"./shortVideo-BIt_F0ml.js";/* empty css               *//* empty css                *//* empty css                  */import"./common-DBXWCL9C.js";import"./request-Ciyrqj7N.js";const tt={class:"auth-view h-full flex flex-col"},et={class:"add-account-section"},ot={class:"account-list-section flex-1 flex flex-col overflow-hidden"},at={class:"account-filter flex items-center"},nt={class:"filter-label"},st={class:"text-[--el-color-primary]"},lt={class:"platform-tabs flex items-center"},it={class:"account-table flex-1 overflow-hidden"},ct={class:"account-cell"},rt={class:"account-avatar"},dt=["src"],pt={class:"account-info"},ut={class:"account-name"},_t={class:"account-platform"},mt=["src"],ft={key:0},ht={key:1},gt={class:"operation-cell flex items-center"},vt=z({__name:"auth",setup(xt){const{refreshAccountList:g,getAccountList:d,accountListQ:s,accountTotal:f,accountList:v,accountListLoading:x,delAccount:b}=Z(),h=X(),w=M(()=>h.platformList);h.fetchPlatformList(),N("");const y=i=>{s.value.page=i,d()},C=i=>{s.value.limit=i,d()},k=i=>{console.log("删除授权:",i),Y.confirm(`确定要删除"${i.title}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await b(i.id),G.success("删除成功"),s.value.page=1,d()}catch(t){console.error("删除失败:",t)}finally{console.log("删除操作已完成")}})};return P(()=>{d()}),(i,t)=>{const L=F,S=$,l=J,B=O,E=H,V=W,A=D,T=q;return r(),u(A,{class:"h-full"},{default:c(()=>[o("div",tt,[o("div",et,[t[3]||(t[3]=o("div",{class:"section-title"},"添加账号：",-1)),a(R,{onSuccess:n(g)},null,8,["onSuccess"])]),o("div",ot,[o("div",at,[o("div",nt,[t[4]||(t[4]=_("共")),o("span",st,m(n(f))+"个",1),t[5]||(t[5]=_("账号"))]),t[6]||(t[6]=o("div",{class:"filter-label",style:{"margin-right":"0px"}},"平台：",-1)),o("div",lt,[a(S,{style:{width:"120px"},modelValue:n(s).media_id,"onUpdate:modelValue":t[0]||(t[0]=e=>n(s).media_id=e),placeholder:"全部",clearable:"",onChange:n(d)},{default:c(()=>[(r(!0),p(Q,null,U(w.value,e=>(r(),u(L,{key:e.value,label:e.title,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])])]),t[8]||(t[8]=o("div",{class:"update-time text-right text-[14px] text-[#999999] mb-[4px]"},"发布数据每日24:00更新",-1)),o("div",it,[j((r(),u(E,{data:n(v),height:"100%",style:{width:"100%"}},{default:c(()=>[a(l,{label:"账号","min-width":"240"},{default:c(({row:e})=>[o("div",ct,[o("div",rt,[o("img",{src:e.avatar,alt:"账号头像"},null,8,dt)]),o("div",pt,[o("div",ut,m(e.title),1),o("div",_t,[e.media&&e.media.icon?(r(),p("img",{key:0,src:e.media.icon,alt:""},null,8,mt)):I("",!0)])])])]),_:1}),a(l,{label:"粉丝数",prop:"fans_num",width:"120",align:"center"}),a(l,{label:"作品数",prop:"works_num",width:"120",align:"center"}),a(l,{label:"播放量",prop:"play_num",width:"120",align:"center"}),a(l,{label:"点赞量",prop:"like_num",width:"120",align:"center"}),a(l,{label:"评论数",prop:"comment_num",width:"120",align:"center"}),a(l,{label:"授权时间",prop:"created_at",width:"200",align:"center"}),a(l,{label:"授权有效期",prop:"show_font",width:"120",align:"center"},{default:c(({row:e})=>[e.show_font?(r(),p("span",ft,m(e.show_font),1)):(r(),p("span",ht,"--"))]),_:1}),a(l,{label:"操作",width:"160",fixed:"right"},{default:c(e=>[o("div",gt,[a(B,{type:"primary",link:"",class:"reauth-btn",onClick:bt=>k(e.row)},{default:c(()=>t[7]||(t[7]=[_("删除授权")])),_:2},1032,["onClick"])])]),_:1})]),_:1},8,["data"])),[[T,n(x)]])]),a(V,{"current-page":n(s).page,"onUpdate:currentPage":t[1]||(t[1]=e=>n(s).page=e),"page-size":n(s).limit,"onUpdate:pageSize":t[2]||(t[2]=e=>n(s).limit=e),total:n(f),onSizeChange:C,onCurrentChange:y},null,8,["current-page","page-size","total"])])])]),_:1})}}}),Pt=K(vt,[["__scopeId","data-v-a498a03b"]]);export{Pt as default};
