'use strict'
const merge = require('webpack-merge')
const prodEnv = require('./prod.env')

const vueDomain = 'window.location.hostname'

module.exports = merge(prodEnv, {
  NODE_ENV: '"development"',
  BASE_API: '"/"',
  VUE_DOMAIN: vueDomain,
  ACCOUNT_API: '"https://account.china9.cn"',
  OPEN_API: '"http://open.china9.cn"',
  COMMONJS: '"/static/js/common/"',
  VERSION: '************',
  STATIC_URL: '"https://images.china9.cn/"',
  LOGIN_API: '"/api/login/auth"',
  EMPLOYEE_URL: '"http://localhost:8000/new-router/main"',
  OPEN_API_URL: '"/openApi"',
  JZT_API_URL: '"/jztApi"',
  JZT_NEW_API_URL: '"/jztNewApi"',
  IHR_API_URL: '"/ihrApi"',
  COON_API_URL: '"/coonApi"',
  JZT_NEW_CLIENT_URL: '"jztClientApi"',
  AI_CHAT_API_URL: '"/aiChatApi"',
})
