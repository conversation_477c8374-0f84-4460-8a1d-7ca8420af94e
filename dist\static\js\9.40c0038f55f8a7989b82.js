webpackJsonp([9],{"0gXh":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("Dd8w"),i=a.n(n),s=a("fZjL"),r=a.n(s),o=a("Xxa5"),l=a.n(o),d=a("exGp"),c=a.n(d),u=a("mvHQ"),p=a.n(u),h=a("Aw0F"),v=a("F6wa"),m=a("UP9x"),f=a("OL/w"),g=a("wU6q"),b=a("n7fW"),x=a("ito4"),C=a("JYzS"),w=a("9RLB"),T=a("ev7b"),_=a("0xDb"),y={name:"finance",mixins:[C.a],components:{AiToolsCard:T.a,ECharts:w.a,PageWrap:h.a,CardList:v.a,SpaceChart:m.a,ImportantData:f.a,CardWrap:g.a,VisitLine:b.a},data:function(){var t=this;return{showTitle:!0,itemStyle:{backgroundRepeat:"no-repeat",height:"auto",padding:"26px 30px",cursor:"pointer",backgroundSize:"100% 100%"},titleStyle:{fontSize:"20px",marginTop:0},descStyle:{fontSize:"14px"},cardList:[{bgColor:"url("+a("Y98O")+")",title:"录入收入",titleStyle:{color:"#121F68"},desc:"财务收入快速录入",descStyle:{color:"#323E7B"},btnBg:"#E2EAFF",pathType:"link",path:"https://finance.china9.cn/admin/uncollected/index?ref=addtabs",roles:["uncollected"]},{bgColor:"url("+a("808h")+")",title:"资金日记账",titleStyle:{color:"#554285"},desc:"企业进出款项明细查询",descStyle:{color:"#6B579D"},btnBg:"#EAE7FF",pathType:"link",path:"https://finance.china9.cn/admin/bookkeeping/bookkeeping?ref=addtabs",roles:["bookkeeping"]},{bgColor:"url("+a("vh1B")+")",title:"支出录入",titleStyle:{color:"#064E6B"},desc:"财务支出款项录入",descStyle:{color:"#3C7A92"},btnBg:"#E1ECFF",pathType:"link",path:"https://finance.china9.cn/admin/expenses/index?ref=addtabs",roles:["expenses"]}],importantData:[{name:"银行及现金余额",value:"0"},{name:"本月现金净流量",value:"0"},{name:"当日收入",value:"0"},{name:"当日支出",value:"0"}],expenditureCate:[{name:"常规支出（元）",logo:a("mecT"),value:0,bg:"#E1F5E9",color:"#0FB458"},{name:"成本支出（元）",logo:a("ShLs"),value:0,bg:"#E5EBFC",color:"#417AF5"}],activeExpenditure:"",expenditureTabs:[],expenditureLoading:!1,usedChart:[{value:0,name:"已用",color:"#3765FF"},{value:100,name:"总量",color:"#E6ECF5"}],usedNum:0,total:0,usedChartTitle:{title:"0",subtitle:"总支出（元）",position:{left:"center",top:"36%"}},financeList:[],statusMap:{0:"#FF801A",1:"#8A8A8A"},trendTabs:[{name:"profit",label:"现金净流量"},{name:"income",label:"收入"},{name:"expend",label:"总支出"},{name:"changgui",label:"常规支出"},{name:"cost",label:"成本支出"}],activeTrend:"profit",trendTotalData:[],grid:{top:"10px",left:"100px",right:"20px",bottom:"20%"},loading:!1,trendData:{day:[],data:[{name:"",list:[]}]},morePath:{expenditure:"https://finance.china9.cn/admin/expenses/index?ref=addtabs",finance:"https://finance.china9.cn/admin/uncollected/confirmed?ref=addtabs",trend:"https://finance.china9.cn/admin/bookkeeping/bookkeeping?ref=addtabs"},roles:{expenditure:["expenses"],finance:["confirmed"],trend:["bookkeeping"]},tooltip:{show:!0,formatter:function(e){var a="已用"===e.seriesName?t.usedNum:t.usedChartTitle.title;return e.seriesName+"："+a+"元 <br>"+e.value+"%"},position:["50%","50%"],textStyle:{fontSize:12,color:"#696C7D"}}}},computed:{trend:function(){return{activeTrend:this.activeTrend,trendData:this.trendTotalData}}},watch:{trend:{handler:function(t,e){var a=p()(t),n=p()(e);t&&a!==n&&t.trendData&&this.getTrend()},immediate:!0,deep:!0}},methods:{handleToAI:function(){window.open("https://china9.cn/#/console/ai-tools-page")},getYearMonth:function(){var t=new Date;return t.getFullYear()+"-"+Object(_.e)((t.getMonth()+1).toString(),2,"0")},toggleExpenditure:function(t){this.activeExpenditure=t.name;var e=+this.expenditureTabs[t.index].value;this.usedNum=e,this.usedChartTitle.title?this.usedChart[0].value=(e/+this.usedChartTitle.title*100).toFixed(2):this.usedChart[0].value=0},toMore:function(t){this.toPage({pathType:"link",path:this.morePath[t]})},toggleTrend:function(t){this.activeTrend=t.name},getData:function(){var t=this;return c()(l.a.mark(function e(){var a,n,i,s,r,o,d,c,u,p;return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.loading=!0,e.next=3,Object(x.a)();case 3:a=e.sent,n=a.code,i=a.data,200===n&&i&&(s=i.nowData,r=s.balance,o=s.month_profit,d=s.day_income,c=s.day_expend,u=s.changgui,p=s.cost,t.importantData[0].value=r,t.importantData[1].value=o,t.importantData[2].value=d,t.importantData[3].value=c,t.expenditureCate[0].value=u,t.expenditureCate[1].value=p,t.trendTotalData=i.list),t.loading=!1;case 7:case"end":return e.stop()}},e,t)}))()},getTrend:function(){var t=this;this.trendData={day:[],data:[{name:"",list:[]}]},this.trendData.day=this.trendTotalData.map(function(t){return t.month}),this.trendData.data[0].name=this.trendTabs.find(function(e){return e.name===t.activeTrend}).label,this.trendData.data[0].list=this.trendTotalData.map(function(e){return e[t.activeTrend]})},getFinanceRatio:function(){var t=this;return c()(l.a.mark(function e(){var a,n,s;return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.expenditureLoading=!0,e.next=3,Object(x.b)();case 3:a=e.sent,n=a.code,s=a.data,200===n&&s&&(s.ren&&s.ren.data&&(t.financeList=s.ren.data),s.huan&&(t.expenditureTabs=r()(s.huan).map(function(t){return i()({id:t},s.huan[t])}),t.expenditureTabs.length&&(t.activeExpenditure=t.expenditureTabs[0].id,t.usedNum=+t.expenditureTabs[0].value,t.usedChart[0].value=(+t.expenditureTabs[0].value/+s.sum*100).toFixed(2))),t.usedChartTitle.title=+s.sum),t.expenditureLoading=!1;case 7:case"end":return e.stop()}},e,t)}))()},toCustomer:function(t){this.toPage(t)}},created:function(){this.showTitle=this.$route.meta&&this.$route.meta.showNav},mounted:function(){this.getData(),this.getFinanceRatio()}},k={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("page-wrap",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{"show-title":t.showTitle}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{staticClass:"left",attrs:{lg:14,md:24,sm:24,xs:24}},[a("el-row",{staticClass:"top-card"},[a("card-list",{staticClass:"top",attrs:{cardList:t.cardList,itemStyle:t.itemStyle,titleStyle:t.titleStyle,descStyle:t.descStyle},on:{click:t.toCustomer},scopedSlots:t._u([{key:"content",fn:function(e){var n=e.item;return[a("button",{style:"background-color: "+n.btnBg},[t._v("\n              进入操作\n            ")])]}}])})],1),t._v(" "),a("div",{staticClass:"gap-h-20"}),t._v(" "),a("el-row",[a("card-wrap",{staticClass:"left-center",attrs:{title:"费用及支出（"+t.getYearMonth()+"）","show-more":!0},on:{toMore:function(e){return t.toMore("expenditure")}}},[a("div",{staticClass:"expenditure-wrap flex-align-start"},[a("div",{staticClass:"cate"},t._l(t.expenditureCate,function(e,n){return a("div",{key:n,staticClass:"flex-align",style:"background: "+e.bg},[a("img",{attrs:{src:e.logo,alt:""}}),t._v(" "),a("div",[a("div",{staticClass:"title"},[t._v(t._s(e.name))]),t._v(" "),a("div",{staticClass:"value",style:"color: "+e.color},[t._v("\n                    "+t._s(e.value)+"\n                  ")])])])}),0),t._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.expenditureLoading,expression:"expenditureLoading"}],staticClass:"cate-wrap flex-1"},[t.expenditureTabs.length?a("el-tabs",{on:{"tab-click":t.toggleExpenditure},model:{value:t.activeExpenditure,callback:function(e){t.activeExpenditure=e},expression:"activeExpenditure"}},t._l(t.expenditureTabs,function(e,n){return a("el-tab-pane",{key:n,attrs:{label:e.title,name:e.id}},[t.activeExpenditure===e.id?a("div",{staticClass:"tab-pane flex-align flex-between"},[a("div",{staticClass:"chart"},[a("space-chart",{attrs:{data:t.usedChart,title:t.usedChartTitle,height:194,tooltip:t.tooltip}})],1),t._v(" "),a("div",{staticClass:"legend-wrap flex-align-start"},[a("div",{staticClass:"legend",style:"background: "+t.usedChart[0].color}),t._v(" "),a("div",{staticClass:"legend-content flex-justify-between flex-1"},[a("div",{staticClass:"value-wrap flex-1"},[a("p",{staticClass:"pane-title"},[t._v(t._s(e.label)+"费用")]),t._v(" "),a("p",{staticClass:"pane-value"},[t._v(t._s(t.usedNum)+"元")])]),t._v(" "),a("p",{staticClass:"pane-value"},[t._v("占比"+t._s(t.usedChart[0].value)+"%")])])])]):t._e()])}),1):a("el-empty")],1)])])],1),t._v(" "),a("div",{staticClass:"gap-h-20"}),t._v(" "),a("el-row",[a("important-data",{attrs:{importantData:t.importantData}})],1),t._v(" "),a("div",{staticClass:"gap-h-20"})],1),t._v(" "),a("el-col",{attrs:{lg:10,md:24,sm:24,xs:24}},[a("el-row",[a("ai-tools-card",{staticClass:"ai-tools-card",attrs:{page:t.showTitle?"":"display"},on:{toAiTools:t.handleToAI}})],1),t._v(" "),a("div",{staticClass:"gap-h-20"}),t._v(" "),a("el-row",[a("card-wrap",{staticStyle:{height:"404px"},attrs:{title:"认款","show-more":!0},on:{toMore:function(e){return t.toMore("finance")}}},[a("div",{staticClass:"table-wrap",staticStyle:{"margin-top":"24px"}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.financeList,"header-row-class-name":"table-header","row-class-name":"table-row",height:"280"}},[a("el-table-column",{attrs:{prop:"submission",label:"客户名称"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[n.submission?a("span",{staticClass:"single-line",attrs:{title:n.submission}},[t._v(t._s(n.submission))]):t._e()]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"statusInfo",width:"100",label:"认款状态"},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.row;return[n.statusInfo?a("span",{style:{color:t.statusMap[n.status]}},[t._v("\n                    "+t._s(n.statusInfo)+"\n                  ")]):t._e()]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"username",width:"90",label:"录入人"}}),t._v(" "),a("el-table-column",{attrs:{prop:"submissionTotal",width:"140",label:"收款金额（元）"}})],1)],1)])],1),t._v(" "),a("div",{staticClass:"gap-h-20"})],1)],1),t._v(" "),a("el-row",[a("card-wrap",{attrs:{title:"财务趋势","show-more":!0},on:{toMore:function(e){return t.toMore("trend")}}},[a("el-tabs",{on:{"tab-click":t.toggleTrend},model:{value:t.activeTrend,callback:function(e){t.activeTrend=e},expression:"activeTrend"}},t._l(t.trendTabs,function(e,n){return a("el-tab-pane",{key:n,attrs:{label:e.label,name:e.name}},[e.name===t.activeTrend?a("div",{staticClass:"line-chart-wrap"},[a("visit-line",{attrs:{width:"100%",height:"300px",data:t.trendData,isShowTitle:!1,from:"finance",grid:t.grid}})],1):t._e()])}),1)],1)],1)],1)},staticRenderFns:[]};var S=a("VU/8")(y,k,!1,function(t){a("w5cn")},"data-v-cf919bce",null);e.default=S.exports},ito4:function(t,e,a){"use strict";e.a=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object(i.a)({url:s+"/role/bookkeeping_index",method:"post",data:t,baseURL:"/"})},e.b=function(t){return Object(n.a)({url:s+"/role/expendList",method:"post",data:t})};var n=a("vLgD"),i=a("r5ZN"),s="/api"},w5cn:function(t,e){}});