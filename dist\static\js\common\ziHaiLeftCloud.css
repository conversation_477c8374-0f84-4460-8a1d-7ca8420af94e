#zhyConsoleLeftSide, #zhyConsoleLeftSide * {
  margin: 0;
  padding: 0;
}

#zhyConsoleLeftSide,
#zhyConsoleLeftSide div,
#zhyConsoleLeftSide dl,
#zhyConsoleLeftSide dt,
#zhyConsoleLeftSide dd,
#zhyConsoleLeftSide ol,
#zhyConsoleLeftSide ul,
#zhyConsoleLeftSide li,
#zhyConsoleLeftSide h1,
#zhyConsoleLeftSide h2,
#zhyConsoleLeftSide h3,
#zhyConsoleLeftSide h4,
#zhyConsoleLeftSide h5,
#zhyConsoleLeftSide h6,
#zhyConsoleLeftSide pre,
#zhyConsoleLeftSide form,
#zhyConsoleLeftSide fieldset,
#zhyConsoleLeftSide input,
#zhyConsoleLeftSide textarea,
#zhyConsoleLeftSide p,
#zhyConsoleLeftSide blockquote,
#zhyConsoleLeftSide th,
#zhyConsoleLeftSide td,
#zhyConsoleLeftSide p {
  margin: 0;
  padding: 0;
  font-size: 14px;
}

#zhyConsoleLeftSide li {
  list-style: none;
}

#zhyConsoleLeftSide {
  width: 50px;
  overflow: hidden;
  background-color: #ffffff;
  border-right: 1px #e1e1e1 solid;
  transition: width .3s;
  z-index: 999998;
  position: relative;
}

#zhyMain {
  width: 100%;
}

#zhyConsoleLeftSide ul li {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 50px;
  text-align: center;
}

#zhyConsoleLeftSide ul li a {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px;
  text-decoration: none;
}

#zhyConsoleLeftSide ul li a:hover {
  background-color: #f4f4f4;
}

#zhyConsoleLeftSide ul li a img {
  height: 22px;
  margin-left: 5px;
}

#zhyConsoleLeftSide ul li a p {
  margin-left: 40px;
  color: #333333;
  font-size: 12px;
}
