<template>
  <div class="form-wrapper" v-loading="loading">
    <template v-for="(item, index) in currentConfig.config">
      <Form :ref="`formRef${item.type}`" :item="item" :form="item.form" :loading="loading" @submit="handleSubmit" />
    </template>
  </div>
</template>

<script>
import Form from './Form.vue'
import { getAigcPrivateConfigApi, saveAigcPrivateConfigApi, getEcommercePrivateConfigApi, saveEcommercePrivateConfigApi } from '@/api/consoleNew'

export default {
  name: "FormWrapper",
  components: { Form },
  props: {
    formType: {
      type: String,
      default: 'aigc'
    }
  },
  computed: {
    currentConfig() {
      return this.formConfig[this.formType]
    }
  },
  data() {
    return {
      formData: {},
      loading: false,
      saveLoading: false,
      formConfig: {
        'aigc': {
          // type 1视频合成 2文案生成 3语音文件生成
          detailApi: getAigcPrivateConfigApi,
          saveApi: saveAigcPrivateConfigApi,
          config: [
            { label: '视频合成', type: 1, key: 'url' },
            { label: '文案生成', type: 2, key: 'url' },
            { label: '语音文件生成', type: 3, key: 'url' }
          ]
        },
        'shop': {
          detailApi: getEcommercePrivateConfigApi,
          saveApi: saveEcommercePrivateConfigApi,
          config: [
            { label: '私域电商', key: 'url', type: '' }
          ]
        }
      }
    };
  },
  created() {
    this.getDetail(); // 获取详情数据
  },
  methods: {
    // 获取详情数据
    async getDetail() {
      const that = this
      const { detailApi, config } = that.currentConfig

      try {
        that.loading = true;
        const res = await detailApi();
        console.log(res, '获取表单数据成功')

        if (res.code === 200) {
          // 使用深拷贝确保数据独立性
          let data = JSON.parse(JSON.stringify(res.data))
          if (data && data.length) {
            // 处理aigc
            data.forEach(item => {
              console.log('item', item)
              config.find(v => v.type === item.type).form = { id: item.id, url: item.url }
            })
          } else if (data) {
            // 处理shop
            config.forEach(item => {
              item.form = {id: data.id, url: data.url}
            })
          }
        }
      } catch (error) {
        console.log(error, '获取表单数据失败')
      } finally {
        that.loading = false;
      }
    },

    // 提交表单
    async handleSubmit(submitData) {
      const that = this
      const { saveApi, config } = that.currentConfig
      if (that.saveLoading) return
      try {
        that.saveLoading = true;
        const res = await saveApi(submitData);

        if (res && res.code === 200) {
          that.$message.success('保存成功');
          console.log(`FormWrapper[${that.formType}] 保存成功:`, submitData);
        }
      } catch (error) {
        console.log(error, '保存表单数据失败')
      } finally {
        that.saveLoading = false
      }
    },

    // 重置表单
    resetForm() {
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields();
      }
      let { config } = this.formConfig[this.formType]
      config.forEach(item => {
        this.formData[`${item.key}${item.type}`] = ''
      })
    },

    // 手动设置表单数据
    setFormData(data) {
      if (data && typeof data === 'object') {
        this.form = JSON.parse(JSON.stringify(data));
      }
    }
  }
}
</script>

<style scoped lang="scss">

</style>