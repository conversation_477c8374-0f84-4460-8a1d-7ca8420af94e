import{_ as w,c as f,o as u,a as o,w as l,E as _,r as h,b as n,d as y,e as x,f as k,g as v,h as F,i as T,j as b}from"./index-BBeD0eDz.js";/* empty css                  *//* empty css               */import{a as V}from"./request-Ciyrqj7N.js";import{l as E}from"./index-CbzCmic8.js";function p(r,e){return V.set(r,e)}const L={name:"Login",data(){return{loginForm:{username:"18812345678",password:"jzt12345678"},loginRules:{username:[{required:!0,trigger:"blur",message:"请输入手机号"}],password:[{required:!0,trigger:"blur",message:"请输入密码"}]},loading:!1,passwordType:"password",redirect:void 0}},watch:{$route:{handler:function(r){this.redirect=r.query&&r.query.redirect},immediate:!0}},methods:{showPwd(){this.passwordType==="password"?this.passwordType="":this.passwordType="password",this.$nextTick(()=>{this.$refs.password.focus()})},handleLogin(){this.$refs.loginForm.validate(r=>{if(r)this.loading=!0,E({user_name:this.loginForm.username.trim(),password:this.loginForm.password,type:"company"}).then(e=>{p("token",e.token,{expires:new Date(e.expires_in*1e3)}),p("access_token",e.token,{expires:new Date(e.expires_in*1e3)}),p("gcc",e.gcc,{expires:new Date(e.expires_in*1e3)}),this.$router.push({path:this.redirect||"/"}),this.loading=!1}).catch(e=>{console.log(e)}).finally(()=>{this.loading=!1});else return console.log("error submit!!"),!1})}}},B={class:"login-container"},C={class:"svg-container"},q={class:"svg-container"};function P(r,e,D,I,s,i){const a=h("svg-icon"),d=x,m=y,c=F,g=_;return u(),f("div",B,[o(g,{ref:"loginForm",model:s.loginForm,rules:s.loginRules,class:"login-form","auto-complete":"on","label-position":"left"},{default:l(()=>[e[4]||(e[4]=n("div",{class:"title-container"},[n("h3",{class:"title"},"Login Form")],-1)),o(m,{prop:"username"},{default:l(()=>[n("span",C,[o(a,{"icon-class":"user"})]),o(d,{ref:"username",modelValue:s.loginForm.username,"onUpdate:modelValue":e[0]||(e[0]=t=>s.loginForm.username=t),placeholder:"Username",name:"username",type:"text",tabindex:"1","auto-complete":"on"},null,8,["modelValue"])]),_:1}),o(m,{prop:"password"},{default:l(()=>[n("span",q,[o(a,{"icon-class":"password"})]),(u(),k(d,{key:s.passwordType,ref:"password",modelValue:s.loginForm.password,"onUpdate:modelValue":e[1]||(e[1]=t=>s.loginForm.password=t),type:s.passwordType,placeholder:"Password",name:"password",tabindex:"2","auto-complete":"on",onKeyup:v(i.handleLogin,["enter","native"])},null,8,["modelValue","type","onKeyup"])),n("span",{class:"show-pwd",onClick:e[2]||(e[2]=(...t)=>i.showPwd&&i.showPwd(...t))},[o(a,{"icon-class":s.passwordType==="password"?"eye":"eye-open"},null,8,["icon-class"])])]),_:1}),o(c,{loading:s.loading,type:"primary",style:{width:"100%","margin-bottom":"30px"},onClick:T(i.handleLogin,["prevent"])},{default:l(()=>e[3]||(e[3]=[b("Login")])),_:1},8,["loading","onClick"]),e[5]||(e[5]=n("div",{class:"tips"},[n("span",{style:{"margin-right":"20px"}},"username: admin"),n("span",null," password: any")],-1))]),_:1},8,["model","rules"])])}const z=w(L,[["render",P],["__scopeId","data-v-ddfc51dd"]]);export{z as default};
