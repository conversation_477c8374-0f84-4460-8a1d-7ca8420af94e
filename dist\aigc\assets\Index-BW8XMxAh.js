import{U as Ye,A as g,l as B,k as T,V as H,ap as _e,c as h,o as u,m as j,b as e,a as l,ar as Je,W as Ge,w as c,F as G,s as $,Y as Ke,n as le,p as U,as as ke,at as Ce,t as Y,_ as P,f as J,I as ue,h as ne,j as N,ag as de,e as ve,au as Ie,i as Z,K as ee,r as ae,a7 as De,av as me,aw as pe,Z as ze,Q as F,a2 as Me,a9 as W,a0 as He,ax as Ee,X as Ve,E as Fe,d as Re,a4 as Le,B as re,q,al as Ne,ay as Ue,az as Se,J as Te,a6 as te,g as ie,aA as he,a3 as Oe,aB as Ze,aC as We,an as qe,aD as $e,a1 as et,v as oe,aq as tt,u as lt,H as st,aE as ot,y as nt,S as se}from"./index-BBeD0eDz.js";import"./ProductDrawer-1eJ7VyQy.js";/* empty css               *//* empty css                *//* empty css                    *//* empty css                     *//* empty css               *//* empty css                *//* empty css                  *//* empty css              *//* empty css                 */import{I as it}from"./ImportFromMaterial-lo8a_mzn.js";import{u as at,d as rt,a as ct,g as ut,b as dt,c as vt,e as mt,f as pt,h as ft,s as gt,i as At}from"./index-vH7ypFZe.js";import{F as ce}from"./List-CZfQXqH2.js";import{e as je}from"./index-sm6CVty6.js";import{a as bt}from"./request-Ciyrqj7N.js";import{E as Pe}from"./empty-CSpEo1eL.js";import{_ as xt}from"./AddMaterial-C0wyZ-zd.js";import{A as Be}from"./AddBtn-KywrLb53.js";import{a as ye,c as yt}from"./common-DBXWCL9C.js";import{_ as ht,b as _t,c as wt,A as kt,d as Ct,e as fe,f as It,V as Dt,F as we,Y as zt,B as Mt,E as Et,M as Vt}from"./FontStyle-COQmCunw.js";/* empty css                */import{A as Ft}from"./AIDocumentForm-5TS4hQsu.js";/* empty css                     */import"./input-delete-BxT9zbwn.js";import{T as Rt}from"./TitleTags-CufboRUd.js";import"./video-cover-DBcJ77EJ.js";import"./index-B2D9HeJa.js";import"./ProductSelector-BTeBhjvS.js";import"./aiDocument-BqsEIq7y.js";const Lt=Ye("subtitleSetting",()=>{const C={open:1,font:"思源黑体",size:14,aligning:"center",startime:{mm:"00",ss:"00"},endtime:{mm:"00",ss:"00"},color:"#FFFFFF",content:"示例字幕点击可编辑",x:0,y:0,transparency:100},w=20,n=g([]),r=g(0),A=_=>{n.value=_},o=()=>{n.value.length>=w||(n.value.push(JSON.parse(JSON.stringify(C))),r.value=n.value.length-1)},i=_=>{_>=0&&_<n.value.length&&(n.value.splice(_,1),_===r.value?r.value=Math.max(0,_-1):_<r.value&&r.value--)},a=B(()=>n.value.length===0?null:n.value[r.value]||n.value[0]);return{subtitleList:n,activeSubtitleIndex:r,addSubtitle:o,removeSubtitle:i,activeSubtitle:a,setSubtitleList:A,setActiveSubtitleIndex:_=>{_>=0&&_<n.value.length&&(r.value=_)},updateActiveSubtitle:_=>{n.value.length===0&&o(),n.value[r.value]={...JSON.parse(JSON.stringify(C)),...a.value,..._}},reset:()=>{n.value=[],r.value=0}}}),Nt={class:"subtitle-wrapper h-full flex-col overflow-auto"},Ut={key:0,class:"setting-item flex items-center"},St={class:"setting-item flex items-center"},Tt={class:"setting-controls flex items-center"},jt={class:"align-buttons ml-[12px]"},Pt=["onClick"],Bt={key:0,src:ht,alt:""},Xt={key:1,src:_t,alt:""},Qt={key:2,src:wt,alt:""},Yt={class:"setting-item flex items-center"},Jt={class:"setting-controls"},Gt={class:"setting-item flex items-center"},Kt={class:"setting-controls pl-[30px]"},Ht={class:"flex items-center flex-1"},Ot={class:"text-[#555] text-[16px] w-[40px]"},Zt=T({__name:"index",props:{showClose:{type:Boolean,default:!0}},emits:["change"],setup(C,{expose:w,emit:n}){const r=n,A=[12,14,16,18,20,24,28,32,36,40,44,48,54,60,72],o=Lt(),i=g({open:1,font:"思源黑体",size:14,aligning:"center",color:"#FFFFFF",content:"示例字幕点击可编辑",startime:{mm:"00",ss:"00"},endtime:{mm:"00",ss:"00"},x:0,y:0,style:{},transparency:100});H(()=>i.value,x=>{x&&(o.updateActiveSubtitle(x),r("change",x))},{deep:!0});const a=x=>{x&&(i.value=x,o.updateActiveSubtitle(x))};_e(["思源黑体"]);const v=_e(["left","center","right"]);return w({setDefaultValue:a}),(x,p)=>{const _=Je,b=Ke,y=Ge,d=ke,M=Ce;return u(),h("div",Nt,[C.showClose?(u(),h("div",Ut,[p[5]||(p[5]=e("div",{class:"setting-label !mb-[0px]"},"显示：",-1)),l(_,{modelValue:i.value.open,"onUpdate:modelValue":p[0]||(p[0]=m=>i.value.open=m),"inline-prompt":"","active-text":"开","inactive-text":"关","active-value":1,"inactive-value":0},null,8,["modelValue"])])):j("",!0),e("div",St,[p[6]||(p[6]=e("div",{class:"setting-label !mb-0"},"字号：",-1)),e("div",Tt,[l(y,{modelValue:i.value.size,"onUpdate:modelValue":p[1]||(p[1]=m=>i.value.size=m),class:"font-size-select"},{default:c(()=>[(u(),h(G,null,$(A,m=>l(b,{key:m,label:m,value:m},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),e("div",jt,[(u(!0),h(G,null,$(U(v),m=>(u(),h("div",{class:le(["align-button",{active:i.value.aligning===m}]),onClick:D=>i.value.aligning=m},[m==="left"?(u(),h("img",Bt)):m==="center"?(u(),h("img",Xt)):m==="right"?(u(),h("img",Qt)):j("",!0)],10,Pt))),256))])])]),e("div",Yt,[p[7]||(p[7]=e("div",{class:"setting-label !mb-0"},"文字颜色：",-1)),e("div",Jt,[l(d,{modelValue:i.value.color,"onUpdate:modelValue":p[2]||(p[2]=m=>i.value.color=m)},null,8,["modelValue"])])]),e("div",Gt,[p[8]||(p[8]=e("div",{class:"setting-label !mb-0 不透明度："},"不透明度：",-1)),e("div",Kt,[e("div",Ht,[l(M,{modelValue:i.value.transparency,"onUpdate:modelValue":p[3]||(p[3]=m=>i.value.transparency=m),min:0,max:100,class:"pr-[12px]"},null,8,["modelValue"]),e("span",Ot,Y(i.value.transparency||0),1)])])]),l(kt,{modelValue:i.value.style,"onUpdate:modelValue":p[4]||(p[4]=m=>i.value.style=m)},null,8,["modelValue"])])}}}),Wt=P(Zt,[["__scopeId","data-v-4503cfe6"]]),qt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAYAAADFw8lbAAAAAXNSR0IArs4c6QAAA9tJREFUWEfVmV1IW2cYx39ZZYQ6t5I1rXYbtc2QWSyWUoXSi1ErBL1oECYdZR2K24og9CIoQkrxJiiECQNB2okXFsZueqHgRdi0LYiFrjgFqaWlnR9b2kgrpaQ1RJP2PCEnZCHGnDc2cF7ITXL+z/PLOe/zcZ7XgvraBXwOHALKgE+Bj4APgbdAGFgHXgBPgX+Af4GoikuLgkigaoAjgNWgXuDvA38l4HOWGwEtBeoBB2BElwlG7vhj4E/gWS60uTgsSgDWAh/kYtTANTHgbgJ4M5tuO1Ab8C2wz4BzlUtXgd+Bta3E2UA/A84DxSqeFTSvgd+A/zJptwL9QttDFxIRrOBTWRLRYuA6sJJuIROoPO4fgN3K7vITvtHS3lD6NkgHlcD5qQB7cru/Inv2GpAMsHRQJ3ByOysF+v2OxuLXfaWC7teS8MX3kIJU/5ekrqtAUAykgn4HfKlq9T3ppChIcCVBD2hp4ccdqDg7zSsV7FctbgL6HT0LHFf14vV6j3Z1dZ0tKiqSYEyujY2NTZ/PN+rxeOZVbQMzGtuYgEoX1KnQYCR9z8/Pn19dXX01PDw8mwrU1tZ2zG63f1xVVSWJXHVJI+MT0MPA90ashEKhzuLiYqU8GwqFQiUlJT8b8acxjgjoaeBrI0Kn07nXbrdbe3p66oPB4MvBwcF72fTt7e0nbDZbidfrvRkIBNYnJyelRzWybguo1PMKIyr92tnZ2XNLS0vPXS7XRDb96OjombKysj21tbU3VPxojA8FtAPYq2JAB+3v7/+7tbX1q5aWlunu7u6KaDT61ufzPRoZGTk1NDR03+12H88T9LmAdqsGkg46NzcX7OjoOGOz2X6ZmppyRSKRzbq6uvG1tbVLAwMDE9XV1fvzBA0L6BXValRA0JiAXgb+l/9y3QaFBu1SbekKCBp/9HkHUwH2aDyY8k5PBQCNpyfDCT89j/r9/idut/u0w+EYlpwZiUSizc3NtxYXF9v6+vomGhoaHHlGfTzhGy6hOujMzMy5lZWVFy6XS97Pt1xjY2P1paWln+SR8OMlVLkpEYCampqK3t5efzgczjiqsVqtuzwej3N6evpBU1PTZK4ZJeW6ZFMi3ym1eeXl5dbx8fFvKisrD1kslozDiVgsFltYWHjS2Nh4Y3l5WZwaXck2T4SmaZwF1hSvIgJqmpc7gTXF67KAmmYAIbCmGOnoqcMUQzId1hRjRx3WFINcHdYUo/HUcqcfNuzUfErmSn/s5GFDem2Wcnsiz+MbmQMEjBT97Q4bstmSrutg4iNnT7Kf9QMx0cmYO5SYHMuB2FLio3Qg9g7BBrx+UFS9wwAAAABJRU5ErkJggg==",xe="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAUVBMVEUAAAA1tgM2tgI1tgI2rQA1tgI1tQA2tgM2uAA1tQI2tgP9/vz0+/JdxDW35aWM1W5jxjw8uAri9Nrh9NnM7L/E6ral3o+E0mV8z1tSwCZMvh9oPtxlAAAACnRSTlMA+JiVCNgwwC/XIeIoOwAAAHZJREFUGNNlj40OhSAIhUHCCq1u97/e/0EDKbfq2zyD444CGB31iD114DRBdkJT+lYqrTl2XwmalxMRqNbDqELAR/9/rqoMqDrrmfJgNhbjPc5T+okbbOn8Sh8Psj/6yGlxgyBKcb7128tgt9FvyzmRGJEpWr0BXMYINc6lsT8AAAAASUVORK5CYII=",$t={class:"agreement-content"},el=["innerHTML"],tl={class:"dialog-footer"},ll=`
<h3>音色复刻协议</h3>
<p>欢迎您使用音色复刻服务。在使用本服务前，请您仔细阅读以下条款：</p>

<h4>1. 服务说明</h4>
<p>音色复刻服务是指通过AI技术，基于您提供的音频样本，生成具有相似音色特征的合成语音。</p>

<h4>2. 用户责任</h4>
<p>2.1 您保证上传的音频样本是您本人的声音，或您已获得合法授权使用的声音。</p>
<p>2.2 您不得使用本服务生成可能违反法律法规、侵犯他人权益或用于欺诈等不当用途的内容。</p>

<h4>3. 隐私保护</h4>
<p>3.1 我们重视您的隐私保护，会按照隐私政策收集、使用、存储您的个人信息。</p>
<p>3.2 您上传的音频样本将仅用于音色复刻服务，未经您的授权，不会用于其他目的。</p>

<h4>4. 知识产权</h4>
<p>4.1 您通过本服务生成的音色模型的知识产权归您所有。</p>
<p>4.2 您授权我们在提供服务过程中使用您上传的音频样本。</p>

<h4>5. 免责声明</h4>
<p>5.1 本服务不保证生成的音色与原音色完全一致。</p>
<p>5.2 对于因您违反本协议或法律法规而导致的任何后果，我们不承担任何责任。</p>

<h4>6. 协议修改</h4>
<p>我们保留随时修改本协议的权利，修改后的协议将在网站上公布。</p>

<h4>7. 法律适用</h4>
<p>本协议的订立、执行和解释及争议的解决均适用中华人民共和国法律。</p>
`,sl=T({__name:"AgreementDialog",props:{visible:{type:Boolean},title:{},content:{}},emits:["update:visible","confirm"],setup(C,{emit:w}){const n=C,r=w,A=g(n.visible);H(()=>n.visible,a=>{A.value=a}),H(()=>A.value,a=>{r("update:visible",a)});const o=()=>{A.value=!1},i=()=>{r("confirm"),o()};return(a,v)=>{const x=ne,p=ue;return u(),J(p,{modelValue:A.value,"onUpdate:modelValue":v[0]||(v[0]=_=>A.value=_),title:a.title||"协议内容",width:"60%","close-on-click-modal":!1,"close-on-press-escape":!0,onClose:o},{footer:c(()=>[e("div",tl,[l(x,{onClick:o},{default:c(()=>v[1]||(v[1]=[N("关闭")])),_:1}),l(x,{type:"primary",onClick:i},{default:c(()=>v[2]||(v[2]=[N("我已阅读并同意")])),_:1})])]),default:c(()=>[e("div",$t,[a.content?(u(),h("div",{key:0,innerHTML:a.content},null,8,el)):(u(),h("div",{key:1,innerHTML:ll}))])]),_:1},8,["modelValue","title"])}}}),ol=P(sl,[["__scopeId","data-v-ac2a37d6"]]),nl={class:"agreement-link-wrapper"},il=T({__name:"AgreementLink",props:{title:{},content:{},linkText:{},linkClass:{}},emits:["agree"],setup(C,{emit:w}){const n=w,r=g(!1),A=()=>{r.value=!0},o=()=>{n("agree")};return(i,a)=>(u(),h("div",nl,[e("a",{href:"javascript:;",class:le(["agreement-link",i.linkClass]),onClick:A},Y(i.linkText||"《协议内容》"),3),l(ol,{visible:r.value,"onUpdate:visible":a[0]||(a[0]=v=>r.value=v),title:i.title,content:i.content,onConfirm:o},null,8,["visible","title","content"])]))}}),Xe=P(il,[["__scopeId","data-v-ddf5f05c"]]),al=""+new URL("image1-C5Glcoer.png",import.meta.url).href,rl="data:image/png;base64,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",cl="data:image/png;base64,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",ul="data:image/png;base64,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",dl="data:image/png;base64,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",vl="data:image/png;base64,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",ml="data:image/png;base64,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",pl={class:"add-image-dialog"},fl={class:"dialog-content"},gl={class:"form-item"},Al={class:"form-item"},bl={class:"flex flex-wrap"},xl={class:"pr-[43px] w-[22%] min-w-[240px]"},yl={class:"block h-full"},hl={class:"upload-inner"},_l=["src"],wl={class:"upload-icon"},kl={class:"w-[calc(100%-22%-43px)] flex-shrink-0"},Cl={class:"requirements"},Il={class:"intro flex"},Dl=["src"],zl={class:"intro-list mt-[15px]"},Ml={class:"no mt-[46px]"},El={class:"overflow-hidden mt-[16px] no-list"},Vl={class:"img w-[100%] h-auto text-center"},Fl=["src"],Rl={class:"text text-[14px] text-[#555] mt-[12px] text-center"},Ll={class:"agreement justify-center"},Nl={class:"dialog-footer text-center"},Ul=T({__name:"AvatarSelectModal",props:{visible:{type:Boolean}},emits:["update:visible","create"],setup(C,{emit:w}){const n=w,r=g(""),A=g(null),o=g(null),i=g(null),a=s=>{if(r.value)return F.error("请先上传图片"),!1;if(!s.type.startsWith("image/"))return F.error("请上传图片"),!1;if(s.size>ce.IMAGE.size)return F.error("图片大小不能超过"+ce.IMAGE.num+"M"),!1;o.value=null;const t=Me.service({lock:!0,text:"上传中...",background:"rgba(0, 0, 0, 0.7)"});return o.value=t,!0},v=(s,t)=>{s.code===200&&(F.success("上传成功"),r.value=s.data,t.raw instanceof Blob&&(A.value=URL.createObjectURL(t.raw))),o.value&&(o.value.close(),o.value=null),i.value&&i.value.clearFiles()},x=g({name:"",file:null}),p=s=>{x.value.file=s.raw},_=[{text:"表情干扰",img:rl},{text:"五官遮挡",img:cl},{text:"拍摄比例",img:ul},{text:"衣着不整",img:dl},{text:"动作干扰",img:vl},{text:"多重人脸",img:ml}],b=g(!1),y=g("");(async()=>{const s=await je({id:10124});y.value=s.content})();const M=()=>{n("update:visible",!1),m()},m=()=>{x.value={name:"",file:null},r.value="",A.value=null,b.value=!1},D=g(!1),z=async()=>{if(!x.value.name){F.warning("请输入形象名称");return}if(!x.value.file){F.warning("请上传形象文件");return}if(!b.value){F.warning("请勾选同意《A|数字人使用协议》");return}try{D.value=!0;const s=await at({name:x.value.name,imgurl:r.value});F.success("创建成功"),n("create",{id:s.id,imgurl:s.imgurl,name:s.name,isSelected:!0}),m()}catch(s){F.error("创建失败"),console.log("🚀 ~ handleCreate ~ error:",s)}finally{D.value=!1}},I="https://apidev.china9.cn/api/bucket/uploadall",X=()=>{A.value=null,r.value="",x.value.file=null,i.value&&i.value.clearFiles()};return de(()=>{A.value&&URL.revokeObjectURL(A.value)}),(s,t)=>{const f=ve,R=ae("Close"),V=ee,L=Ie,k=pe,E=me,Q=ze,S=ne,K=ue;return u(),h("div",pl,[l(K,{title:"添加我的形象",modelValue:s.visible,"onUpdate:modelValue":t[3]||(t[3]=O=>n("update:visible",O)),width:"62.5vw","show-close":!0,class:"avatar-select-dialog","header-class":"dialog-header"},{footer:c(()=>[e("div",Nl,[l(S,{onClick:M,size:"large",class:"w-[100px]"},{default:c(()=>t[13]||(t[13]=[N("取消")])),_:1}),l(S,{loading:D.value,type:"primary",onClick:z,size:"large",class:"w-[100px]"},{default:c(()=>t[14]||(t[14]=[N("生成形象")])),_:1},8,["loading"])])]),default:c(()=>[e("div",fl,[e("div",gl,[t[4]||(t[4]=e("div",{class:"label"},"形象名称：",-1)),l(f,{modelValue:x.value.name,"onUpdate:modelValue":t[0]||(t[0]=O=>x.value.name=O),placeholder:"请输入新的数字人形象名称",size:"large"},null,8,["modelValue"])]),e("div",Al,[t[10]||(t[10]=e("div",{class:"label"},"上传形象图片：",-1)),e("div",bl,[e("div",xl,[e("div",yl,[l(L,{ref_key:"uploadRef",ref:i,class:"upload-box h-full",action:I,"on-change":p,limit:1,accept:"image/*",data:{token:U(bt).get("token")},"show-file-list":!1,"on-success":v,"before-upload":a},{default:c(()=>[e("div",hl,[A.value?(u(),h(G,{key:0},[e("img",{src:A.value,alt:"",class:"w-full h-full object-contain"},null,8,_l),e("div",{class:"clear-icon",onClick:Z(X,["stop"])},[l(V,null,{default:c(()=>[l(R)]),_:1})])],64)):(u(),h(G,{key:1},[e("div",wl,[l(V,null,{default:c(()=>[l(U(De))]),_:1})]),t[5]||(t[5]=e("div",{class:"upload-text"},"上传图片",-1)),t[6]||(t[6]=e("div",{class:"upload-hint"},"或拖拽图片到此区域",-1))],64))])]),_:1},8,["data"])])]),e("div",kl,[e("div",Cl,[t[9]||(t[9]=e("div",{class:"requirements-title"},"图片满足以下要求，效果更理想：",-1)),e("div",Il,[e("img",{src:U(al),alt:"",class:"w-[180px] h-[340px] object-cover"},null,8,Dl),e("div",zl,[t[8]||(t[8]=e("ul",null,[e("li",{class:"flex items-center mb-[20px]"},[e("img",{src:xe,class:"mr-[9px]",alt:""}),N(" 单人正面照，面部占比1/3以上 ")]),e("li",{class:"flex items-center mb-[20px]"},[e("img",{src:xe,class:"mr-[9px]",alt:""}),N(" 人物表情自然，五官清晰无遮挡 ")]),e("li",{class:"flex items-center mb-[20px]"},[e("img",{src:xe,class:"mr-[9px]",alt:""}),N(" 良好的亮度，确保面部细节 ")])],-1)),e("div",Ml,[t[7]||(t[7]=e("div",{class:"flex items-center text-[16px] text-[#555]"},[e("i",{class:"iconfont icon-ai55 text-[#ED1F1F] text-[24px]"}),N(" 拍摄不佳案例： ")],-1)),e("div",El,[l(E,{gutter:20},{default:c(()=>[(u(),h(G,null,$(_,(O,ge)=>l(k,{md:4,sm:8,xs:24,key:ge,class:"mb-[8px]"},{default:c(()=>[e("div",Vl,[e("img",{src:O.img,alt:"",class:"w-full"},null,8,Fl)]),e("div",Rl,Y(O.text),1)]),_:2},1024)),64))]),_:1})])])])])])])])]),t[12]||(t[12]=e("div",{class:"form-item"},[e("div",{class:"label"},"免责声明："),e("div",{class:"exemption"},[e("p",null,"1、请确认您使用的视频已获取其本人或团队授权"),e("p",null,"2、您承诺您上传的图片严格遵守中国的法律法规要求，不涉及黄、赌、毒，及任何形式的政治敏感信息等"),e("p",null,"3、如果您违反上述规定，存在任何违法行为，由此产生的所有法律责任由使用者自行承担")])],-1)),e("div",Ll,[l(Q,{modelValue:b.value,"onUpdate:modelValue":t[1]||(t[1]=O=>b.value=O)},{default:c(()=>t[11]||(t[11]=[e("span",{class:"text-[#999999] text-[14px]"},"勾选即表明同意",-1)])),_:1},8,["modelValue"]),l(Xe,{linkText:"《A|数字人使用协议》",title:"数字人使用协议",linkClass:"text-[14px] ml-[5px]",content:y.value,onAgree:t[2]||(t[2]=O=>b.value=!0)},null,8,["content"])])])]),_:1},8,["modelValue"])])}}}),Sl=P(Ul,[["__scopeId","data-v-f81e8c58"]]),Tl={class:"image-list w-full overflow-x-hidden"},jl=["onClick"],Pl=["src","alt"],Bl={class:"name text-[14px] text-[#555] text-center mt-[10px] absolute bottom-[0] left-[0] right-[0] bg-[#fff] p-[8px] overflow-hidden text-ellipsis whitespace-nowrap rounded-b-[6px] flex justify-center items-center z-10"},Xl=["onClick"],Ql={key:0,class:"delete-icon absolute top-0 right-0 left-0 bottom-0 justify-center items-center"},Yl=["onClick"],Jl={class:"select-icon absolute top-[0] right-[0]"},Gl={class:"absolute top-[-15px] right-[-15px]"},Kl={key:2,class:"flex-1"},Hl={class:"dialog-footer"},Ol=T({__name:"ImageList",props:{images:{},isSystem:{type:Boolean}},emits:["refresh"],setup(C,{emit:w}){const n=C,r=w,A=W("formData"),o=W("updateFormData"),i=m=>{o("image_id",m.id),o("image_url",m.imgurl)},a=g(!1),v=()=>{a.value=!0},x=async m=>{a.value=!1,o("image_id",m.id),o("image_url",m.imgurl),r("refresh")},p=m=>{m&&Le.confirm("确认删除该数字人吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{var D;try{await rt({id:m}),F.success("删除成功"),o("image_id",(D=n.images[0])==null?void 0:D.id),r("refresh")}catch{F.error("删除失败")}})},_=g(!1),b=g({id:void 0,name:""}),y=g(""),d=m=>{b.value=m,y.value=m.name,_.value=!0},M=async()=>{if(b.value.id){if(!y.value.trim()){F.warning("名称不能为空");return}try{await ct({id:b.value.id,name:y.value.trim()}),F.success("修改成功"),_.value=!1,r("refresh")}catch{F.error("修改失败")}}};return(m,D)=>{const z=ee,I=pe,X=Ve,s=me,t=ve,f=Re,R=Fe,V=ne,L=ue;return u(),h("div",Tl,[l(s,{class:"image-list",gutter:20},{default:c(()=>[m.isSystem?j("",!0):(u(),J(I,{key:0,lg:8,md:4,sm:6,xs:24},{default:c(()=>[e("div",{class:"image-list-item p-[16px] h-[190px] bg-[#fff] rounded-[6px] border border-dashed border-[#186DF5] mb-[11px] cursor-pointer flex flex-col justify-center items-center",onClick:v},[l(z,{size:40,color:"#186DF5"},{default:c(()=>[l(U(De),{class:"font-bold"})]),_:1}),D[4]||(D[4]=e("div",{class:"text-[16px] text-[--el-color-primary] text-center mt-[11px]"}," 添加形象 ",-1))])]),_:1})),m.images.length?(u(!0),h(G,{key:1},$(m.images,k=>(u(),J(I,{lg:8,md:4,sm:6,xs:24,key:k.id},{default:c(()=>[e("div",{class:le(`image-list-item relative p-[16px] h-[190px] bg-[#fff] rounded-[6px] border border-[#E2E6EC] mb-[11px] cursor-pointer ${U(A).image_id===k.id?"border-primary":""}`),onClick:E=>i(k)},[e("img",{src:k.imgurl,alt:k.name,class:"w-[100%] h-[100%] object-cover"},null,8,Pl),e("div",Bl,[N(Y(k.name)+" ",1),m.isSystem?j("",!0):(u(),h("div",{key:0,class:"edit-icon mt-[2px] ml-[4px] cursor-pointer hover:text-primary",onClick:Z(E=>d(k),["stop"])},[l(z,{size:16},{default:c(()=>[l(U(He))]),_:1})],8,Xl))]),m.isSystem?j("",!0):(u(),h("div",Ql,[e("img",{class:"del-image-btn cursor-pointer w-[42px] h-[42px]",src:qt,onClick:E=>p(k.id)},null,8,Yl)])),e("div",Jl,[e("div",Gl,[l(z,{size:16,class:"text-primary",color:"#fff"},{default:c(()=>[l(U(Ee))]),_:1})])])],10,jl)]),_:2},1024))),128)):(u(),h("div",Kl,[m.isSystem?(u(),J(X,{key:0,image:U(Pe),description:"暂无系统形象","image-size":207},null,8,["image"])):j("",!0)]))]),_:1}),l(Sl,{visible:a.value,"onUpdate:visible":D[0]||(D[0]=k=>a.value=k),onCreate:x},null,8,["visible"]),l(L,{modelValue:_.value,"onUpdate:modelValue":D[3]||(D[3]=k=>_.value=k),title:"修改名称",width:"400px","close-on-click-modal":!1,"close-on-press-escape":!1,class:"avatar-select-dialog"},{footer:c(()=>[e("span",Hl,[l(V,{onClick:D[2]||(D[2]=k=>_.value=!1),size:"large",class:"w-[100px]"},{default:c(()=>D[5]||(D[5]=[N("取消")])),_:1}),l(V,{type:"primary",onClick:M,size:"large",class:"w-[100px]"},{default:c(()=>D[6]||(D[6]=[N("确定")])),_:1})])]),default:c(()=>[l(R,{class:"mt-[20px]",size:"large"},{default:c(()=>[l(f,{label:"名称"},{default:c(()=>[l(t,{modelValue:y.value,"onUpdate:modelValue":D[1]||(D[1]=k=>y.value=k),placeholder:"请输入名称",maxlength:"20","show-word-limit":"",size:"large"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}}),Zl=P(Ol,[["__scopeId","data-v-c9b6d937"]]),Wl={class:"select-view px-[19px] py-[18px] h-full"},ql={class:"select-view-content flex flex-col mt-[20px] flex-1 h-[calc(100%-24px-20px)]"},$l={class:"select-view-content-item flex justify-center mb-[20px]"},es={class:"select-view-content-list flex-1 overflow-y-auto","element-loading-background":"#F2F6F9"},ts=T({__name:"SelectView",setup(C){const w=g("system"),n=g([]),r=W("formData"),A=W("updateFormData"),o=g(!1),i=g({page:1,perPage:12}),a=g(1),v=b=>{n.value=[],b==="system"?i.value.perPage=12:i.value.perPage=11,p()},x=b=>{n.value.forEach(y=>{y.isSelected=y.id==b,y.isSelected&&(A("image_id",y.id),A("image_url",y.imgurl))})},p=async()=>{try{o.value=!0;const b=await ut({type:w.value==="system"?1:2,...i.value});i.value.page===1&&(n.value=[]),n.value=[...n.value,...b.data],a.value=b.last_page,i.value.page=b.current_page,r.image_id?x(r.image_id):(n.value[0].isSelected=!0,A("image_id",n.value[0].id),A("image_url",n.value[0].imgurl))}catch(b){console.log(b)}finally{o.value=!1}};H(()=>r.image_id,b=>{b&&x(b)},{immediate:!0});const _=()=>{i.value.page>=a.value||(i.value.page++,p())};return re(()=>{p()}),(b,y)=>{const d=Ue,M=Ne,m=Se,D=Te;return u(),h("div",Wl,[y[3]||(y[3]=e("div",{class:"select-view-title"},[e("span",{class:"select-view-title-text text-[#555] text-[16px]"},"选择数字人")],-1)),e("div",ql,[e("div",$l,[l(M,{modelValue:w.value,"onUpdate:modelValue":y[0]||(y[0]=z=>w.value=z),onChange:v,class:"!flex-nowrap"},{default:c(()=>[l(d,{value:"system"},{default:c(()=>y[1]||(y[1]=[N("系统形象")])),_:1}),l(d,{value:"custom"},{default:c(()=>y[2]||(y[2]=[N("我的形象")])),_:1})]),_:1},8,["modelValue"])]),q((u(),h("div",es,[l(Zl,{images:n.value,"is-system":w.value==="system",onRefresh:p},null,8,["images","is-system"])])),[[m,_],[D,o.value]])])])}}}),ls=P(ts,[["__scopeId","data-v-6759b3f8"]]),ss={class:"h-full w-full flex flex-col items-center"},os={class:"w-full h-full overflow-auto"},ns=["src"],is=T({__name:"Image",setup(C){const w=W("formData");return(n,r)=>{var A,o,i,a;return u(),h("div",ss,[e("div",os,[e("div",{class:"image-preview box-content h-full w-full rounded-[10px] m-auto",style:te({background:U(w).isbackground===2?(A=U(w))==null?void 0:A.bg:`url(${(o=U(w))==null?void 0:o.bg}) no-repeat center center/cover`})},[(i=U(w))!=null&&i.image_url?(u(),h("img",{key:0,src:(a=U(w))==null?void 0:a.image_url,alt:"image",class:"h-full w-full object-contain"},null,8,ns)):j("",!0)],4)])])}}}),as=P(is,[["__scopeId","data-v-7de7273a"]]),rs={class:"image-editor"},cs=T({__name:"ImageEditor",setup(C){return(w,n)=>(u(),h("div",rs,[l(as)]))}}),us=P(cs,[["__scopeId","data-v-21de5360"]]),ds={class:"preview-panel h-full"},vs={class:"preview-header flex justify-center items-center"},ms={class:"tab-container flex"},ps=["onClick"],fs={class:"preview-container flex items-center justify-center"},gs={class:"preview-wrapper"},As=["contenteditable","onKeydown"],bs=["contenteditable","onKeydown"],xs=["contenteditable","onKeydown"],ys={class:"absolute w-full h-full left-0 top-0 overflow-hidden z-10"},hs={key:1,class:"video-bg w-full h-full absolute left-0 top-0 object-cover z-1",autoplay:"",muted:"",loop:"",playsinline:""},_s=["src"],ws=T({__name:"PreviewPanel",props:{tab:{type:Number,default:2},form:{type:Object,default:()=>({})}},emits:["update:tab","update:form"],setup(C,{emit:w}){const n=C,r=w,A=g([{label:"封面",value:1},{label:"内容",value:2}]),o=g(2);H(()=>n.tab,t=>{o.value=t},{immediate:!0});const i=t=>{o.value=t.value,r("update:tab",t.value)},a=B({get(){return n.form},set(t){r("update:form",t)}}),v=B(()=>ye(a.value.cover)),x=()=>{a.value.cover=""};g(),B(()=>{const t=v.value;if(console.log("type",t),t=="color")return{backgroundColor:a.value.cover};if(t=="image")return{backgroundImage:`url(${a.value.cover})`,backgroundSize:"cover",backgroundPosition:"center"};if(t=="video")return{backgroundColor:"transparent"}});const p=B(()=>ye(a.value.bg)),_=B(()=>{const t=p.value;if(t=="color")return{backgroundColor:a.value.bg};if(t=="image")return{backgroundImage:`url(${a.value.bg})`,backgroundSize:"cover",backgroundPosition:"center"};if(t=="video")return{backgroundColor:"transparent"}}),b=Ct(),y=fe();re(()=>{b.fetchFontStyles(),a.value.title&&a.value.title.length>0&&(y.setTitleList(a.value.title),a.value.title[0].activeTitleIndex!==void 0&&y.setActiveTitleIndex(a.value.title[0].activeTitleIndex))}),H(()=>a.value.title,t=>{t&&t.length>0&&(y.setTitleList(t),t[0].activeTitleIndex!==void 0&&y.setActiveTitleIndex(t[0].activeTitleIndex))},{deep:!0});const d=B(()=>y.activeTitleWithStyles||{}),M=B(()=>!d.value||!d.value.content?"":d.value.aligning==="left"?"text-left":d.value.aligning==="right"?"text-right":"text-center"),m=g(!1),D=g(null),z=t=>{m.value||(t&&t.stopPropagation(),d.value&&d.value.content&&(m.value=!0,Oe(()=>{var R;const f=D.value;if(f){f.focus();const V=document.createRange(),L=window.getSelection();if(f.childNodes.length>0){const k=f.childNodes[f.childNodes.length-1];k.nodeType===Node.TEXT_NODE&&(V.setStart(k,((R=k.textContent)==null?void 0:R.length)||0),V.collapse(!0),L&&(L.removeAllRanges(),L.addRange(V)))}}})))},I=t=>{if(!m.value)return;t&&(t.preventDefault(),t.stopPropagation());const f=D.value;if(f&&f.textContent){const R=f.textContent.trim();R&&y.updateActiveTitle({content:R})}m.value=!1},X=t=>{if(!m.value)return;t&&(t.preventDefault(),t.stopPropagation());const f=D.value;f&&d.value&&d.value.content&&(f.textContent=d.value.content),m.value=!1},s=t=>{if(m.value)return;t.preventDefault(),t.stopPropagation();const f=t.currentTarget;if(!f)return;console.log("开始拖拽",f.offsetLeft,f.offsetTop);const R=t.clientX-f.offsetLeft,V=t.clientY-f.offsetTop,L=E=>{E.preventDefault(),E.stopPropagation();const Q=E.clientX-R,S=E.clientY-V;console.log("拖拽中",E.clientX,E.clientY,Q,S);const K=document.querySelector(".preview-content");if(K){const O=K.clientWidth-f.clientWidth,ge=K.clientHeight-f.clientHeight,Ae=Math.max(0,Math.min(Q,O)),be=Math.max(0,Math.min(S,ge));console.log("新位置",Ae,be),y.activeTitle&&(f.style.left=`${Ae}px`,f.style.top=`${be}px`,y.updateActiveTitle({x:Ae,y:be}))}},k=E=>{E.preventDefault(),E.stopPropagation(),console.log("拖拽结束"),document.removeEventListener("mousemove",L),document.removeEventListener("mouseup",k)};document.addEventListener("mousemove",L),document.addEventListener("mouseup",k)};return(t,f)=>{const R=ae("Delete"),V=ee,L=xt;return u(),h("div",ds,[e("div",vs,[e("div",ms,[(u(!0),h(G,null,$(A.value,(k,E)=>(u(),h("div",{class:le(["tab-item flex justify-center items-center",{active:o.value==k.value}]),onClick:Q=>i(k)},[e("span",null,Y(k.label),1),k.value==1&&a.value.cover&&o.value==1?(u(),J(V,{key:0,size:16,class:"delete-icon cursor-pointer ml-1",onClick:Z(x,["stop"]),color:"#FFFFFF"},{default:c(()=>[l(R)]),_:1})):j("",!0)],10,ps))),256))])]),e("div",fs,[e("div",gs,[o.value==1?(u(),J(L,{key:0,image:a.value.cover,"onUpdate:image":f[0]||(f[0]=k=>a.value.cover=k)},null,8,["image"])):j("",!0),o.value==2?(u(),h("div",{key:1,class:"preview-content w-full h-full relative overflow-hidden bg-[#000000]",style:te(_.value)},[d.value&&d.value.content?(u(),h("div",{key:0,class:le(["preview-text-overlay !z-20",[M.value,{editing:m.value}]]),style:te({"font-family":d.value.font||"Arial","font-size":`${d.value.size||16}px`,left:`${d.value.x}px`,top:`${d.value.y}px`}),onMousedown:s},[d.value.computedStyles&&d.value.computedStyles.length>1?(u(),h("div",{key:0,class:"text-content multi-style",contenteditable:m.value,onClick:Z(z,["stop"]),onBlur:I,onKeydown:[ie(Z(I,["prevent"]),["enter"]),ie(X,["esc"])],ref_key:"editTextRef",ref:D},[(u(!0),h(G,null,$(d.value.computedStyles,(k,E)=>(u(),h("span",{key:E,class:"style-segment",style:te(k)},Y(d.value.content),5))),128))],40,As)):d.value.computedStyles&&d.value.computedStyles.length===1?(u(),h("div",{key:1,class:"text-content",style:te(d.value.computedStyles[0]),contenteditable:m.value,onClick:Z(z,["stop"]),onBlur:I,onKeydown:[ie(Z(I,["prevent"]),["enter"]),ie(X,["esc"])],ref_key:"editTextRef",ref:D},Y(d.value.content),45,bs)):(u(),h("div",{key:2,class:"text-content",style:te({color:d.value.color||"#000000",fontSize:`${d.value.size||16}px`,fontFamily:d.value.font||"Arial"}),contenteditable:m.value,onClick:Z(z,["stop"]),onBlur:I,onKeydown:[ie(Z(I,["prevent"]),["enter"]),ie(X,["esc"])],ref_key:"editTextRef",ref:D},Y(d.value.content),45,xs))],38)):j("",!0),e("div",ys,[he(t.$slots,"preview-content",{},void 0,!0)]),p.value=="video"?(u(),h("video",hs,[e("source",{src:a.value.bg,type:"video/mp4"},null,8,_s)])):j("",!0)],4)):j("",!0)])])])}}}),ks=P(ws,[["__scopeId","data-v-b59032c2"]]),Cs="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAANCAMAAABIK2QJAAAAAXNSR0IArs4c6QAAAGlQTFRFAAAA/wD/QID/YnbriFX/llr/gF3zjWH2f132mE/5bW30Wn31ZXP3Ynf3jFv4kFf2VIH2l1L1b273UIP3kVf2eWj1jFv2YHf2aHT2gmH2kVf1Wn32kVf2VIH2Wn32YXj2aXL2cWz2emf25QudJAAAAB10Uk5TAAEEDQ8RFh0eKi8zXWVqbHN/l6KnsbzJy+nq8/vCc7WCAAAAUklEQVQI12NggAJGfjDFzAMk+KQ4QWxeCQYGNjEZQRBbXIGLgYFFBMTkllcQAlKsDAxMAtLyCoqiHCBRYVk5IFtJkp0BVRxZPao5KOYj2wtzDwCWbQU1nkkqLAAAAABJRU5ErkJggg==",Is=["data-placeholder"],Ds=T({__name:"DebouncedEditor",props:{modelValue:{},delay:{default:300},placeholder:{default:"请输入内容..."},height:{default:"120px"}},emits:["update:modelValue"],setup(C,{emit:w}){const n=C,r=w,A=g(null),o=g(!1),i=g(!1),a=Ze(d=>{r("update:modelValue",d)},n.delay);H(()=>n.modelValue,d=>{A.value&&d!==v()&&!i.value&&x(d)});const v=()=>{var d,M;return((M=(d=A.value)==null?void 0:d.textContent)==null?void 0:M.trim())||""},x=d=>{A.value&&(A.value.textContent=d)},p=()=>{o.value||(i.value=!0,a(v()))},_=()=>{a.flush(),i.value=!1},b=()=>{o.value=!0},y=()=>{o.value=!1,p()};return re(()=>{x(n.modelValue)}),de(()=>{a.cancel()}),(d,M)=>(u(),h("div",{class:"script-content",style:te({height:d.height})},[e("div",{ref_key:"editorEl",ref:A,class:"edit-content",contenteditable:"","data-placeholder":d.placeholder,onInput:p,onBlur:_,onCompositionstart:b,onCompositionend:y},null,40,Is)],4))}}),zs=P(Ds,[["__scopeId","data-v-f74a1073"]]),Ms={class:"add-voice-dialog"},Es={class:"dialog-content"},Vs={class:"form-item"},Fs={class:"form-item"},Rs={class:"block"},Ls={class:"pr-[43px] w-full"},Ns={class:"block"},Us={class:"upload-inner"},Ss={key:0,class:"flex items-center justify-between flex-1 px-[18px]"},Ts={class:"flex items-center flex-1"},js=["src"],Ps={class:"text-ellipsis overflow-hidden w-[calc(100%-50px)] text-[16px] text-[#555] flex-1"},Bs={class:"upload-icon"},Xs={class:"agreement justify-center"},Qs={class:"dialog-footer"},Ys=T({__name:"AddVoiceDialog",props:{visible:{type:Boolean}},emits:["update:visible","confirm"],setup(C,{emit:w}){const n=C,r=w,A=B({get(){return n.visible},set(s){r("update:visible",s)}}),o=g({name:"",file:null}),i=g(!1),a=()=>{r("update:visible",!1),v()},v=()=>{o.value={name:"",file:null},i.value=!1},x=g(""),p=async()=>{if(!o.value.name){F.warning("请输入音色名称");return}if(!o.value.file){F.warning("请上传音频文件");return}if(!i.value){F.warning("请勾选同意《音色复刻协议》");return}const s=await dt({name:o.value.name,url:x.value});F.success("添加成功"),r("confirm",s),a()},_=g(""),b=s=>{o.value.file=s.raw;const t=new FileReader;t.readAsDataURL(s.raw),t.onload=()=>{_.value=t.result}},y="https://apidev.china9.cn/api/bucket/uploadall",d=g(null),M=g(null),m=s=>{if(!s.type.startsWith("audio/"))return F.error("请上传音频文件"),!1;if(s.size>ce.AUDIO.size)return F.error("文件大小不能超过 "+ce.AUDIO.num+" M"),!1;M.value=null;const f=Me.service({lock:!0,text:"上传中...",background:"rgba(0, 0, 0, 0.7)"});return M.value=f,!0},D=s=>{s.code===200&&(F.success("上传成功"),x.value=s.data),M.value&&(M.value.close(),M.value=null),d.value&&d.value.clearFiles()},z=()=>{o.value.file=null,_.value=""},I=g("");return(async()=>{const s=await je({id:10125});I.value=s.content})(),(s,t)=>{const f=ve,R=ae("Delete"),V=ee,L=ae("Plus"),k=Ie,E=ze,Q=ne,S=ue;return u(),h("div",Ms,[l(S,{modelValue:A.value,"onUpdate:modelValue":t[3]||(t[3]=K=>A.value=K),title:"添加我的音色",width:"62.5vw","close-on-click-modal":!1,onClose:a},{footer:c(()=>[e("div",Qs,[l(Q,{onClick:a,class:"w-[100px]"},{default:c(()=>t[11]||(t[11]=[N("取消")])),_:1}),l(Q,{type:"primary",onClick:p,class:"w-[100px]"},{default:c(()=>t[12]||(t[12]=[N("复刻音色")])),_:1})])]),default:c(()=>[e("div",Es,[e("div",Vs,[t[4]||(t[4]=e("div",{class:"label"},"音色名称：",-1)),l(f,{modelValue:o.value.name,"onUpdate:modelValue":t[0]||(t[0]=K=>o.value.name=K),placeholder:"请输入新的音色克隆名称",size:"large"},null,8,["modelValue"])]),e("div",Fs,[t[9]||(t[9]=e("div",{class:"label"},"上传音频文件：",-1)),e("div",Rs,[e("div",Ls,[e("div",Ns,[l(k,{class:"upload-box",action:y,accept:".wav,.mp3,.ogg,.m4a,.aac,.pcm",ref_key:"uploadRef",ref:d,"on-change":b,limit:1,"show-file-list":!1,"before-upload":m,"on-success":D},{default:c(()=>[e("div",Us,[o.value.file?(u(),h("div",Ss,[e("div",Ts,[t[5]||(t[5]=e("i",{class:"iconfont icon-icon_play text-[18px] mr-[14px] text-[#5C6066]"},null,-1)),e("audio",{src:_.value},null,8,js),t[6]||(t[6]=e("img",{src:It,class:"mr-[9px]"},null,-1)),e("div",Ps,Y(o.value.file.name),1)]),l(V,{size:20,class:"delete-icon cursor-pointer",onClick:Z(z,["stop"]),color:"#999999"},{default:c(()=>[l(R)]),_:1})])):(u(),h(G,{key:1},[e("div",Bs,[l(V,null,{default:c(()=>[l(L)]),_:1})]),t[7]||(t[7]=e("div",{class:"upload-text"},"上传音频文件",-1))],64))])]),_:1},512)])]),t[8]||(t[8]=e("div",{class:"w-full mt-[22px]"},[e("div",{class:"requirements"},[e("div",{class:"requirements-title"},"音频满足以下要求，音色复刻会更好："),e("div",{class:"intro"},[e("p",null," 上传一段音频用于克隆声音。音频的质量会直接影响到最后复刻声音的效果，因此请您尽量保证用于复刻的音频符合以下要求:"),e("p",null,"（1）音频时长:"),e("p",null," 声音复刻的输入只需要10-15s的音频，即可实现音色复刻。所以参考音频不需要情感多变、过长，只需要说话人平稳状态下、带有主要说话特征的10-15s音频即可。"),e("p",null,"（2）音频质量:"),e("p",null,"a.只有一个在说话，避免音频中出现多个人的声音;"),e("p",null,"b.保证低底噪。说话人说话越清晰，最终克隆效果越好，音频底噪会严重影响克隆效果;"),e("p",null,"c.保持音量大小、语速稳定、注意断句、避免口腔噪音(如口水声)、杂音、混响等情况;"),e("p",null,"d.音频中不要有桌椅响声、键盘鼠标敲击声、衣服摩擦声等人为噪声;"),e("p",null,"e.音频中可以存在口误。口误时无需终止录音,可停顿1~2秒后,继续录制即可。"),e("p",null,"（3）音频内容:"),e("p",null," 不建议选取带有噪声、长度过长、多人声、人声不清晰、方言严重、带有一些声音毛刺的音频从而使得最终复刻效果不太理想。"),e("p",null,"（4）音频格式:"),e("p",null,"wav、mp3、ogg、m4a、aac、pcm，其中pcm仅支持24k 单通道 目前限制单文件上传最大10MB")])])],-1))])]),e("div",Xs,[l(E,{modelValue:i.value,"onUpdate:modelValue":t[1]||(t[1]=K=>i.value=K)},{default:c(()=>t[10]||(t[10]=[e("span",{class:"text-[#999999] text-[14px]"},"勾选即表明同意",-1)])),_:1},8,["modelValue"]),l(Xe,{linkText:"《音色复刻协议》",title:"音色复刻协议",linkClass:"text-[14px] ml-[5px]",content:I.value,onAgree:t[2]||(t[2]=K=>i.value=!0)},null,8,["content"])])])]),_:1},8,["modelValue"])])}}}),Js=P(Ys,[["__scopeId","data-v-a85b924a"]]),Gs={class:"voice"},Ks={class:"timbre-settings"},Hs={class:"flex items-center flex-1"},Os={class:"flex-1 pr-[12px]"},Zs={class:"text-[#555] text-[16px] w-[40px]"},Ws={class:"flex items-center flex-1"},qs={class:"flex-1 pr-[12px]"},$s={class:"text-[#555] text-[16px] w-[40px]"},eo={class:"flex items-center flex-1"},to={class:"flex-1 pr-[12px]"},lo={class:"text-[#555] text-[16px] w-[40px]"},so={class:"max-h-[800px] overflow-auto","element-loading-background":"#F2F6F9"},oo={key:0,span:24,class:"w-full"},no=T({__name:"Voice",props:{modelValue:{default:null},label:{default:"口播音色："},color:{default:"#896CF6"}},emits:["update:modelValue","change"],setup(C,{emit:w}){const n=C,r=w,A=B({get:()=>n.modelValue,set:z=>{r("update:modelValue",z)}}),o=g({speed:2,pitch:4,volume:76,voiceId:"",voice:null}),i=g("system"),a=g([]),v=g(!1);function x(){v.value=!0}const p=g({page:1,perPage:12}),_=g(1),b=z=>{i.value=z,p.value.page=1,p.value.perPage=z==="system"?12:11,d()},y=g(!1),d=async()=>{if(i.value==="custom")try{y.value=!0;const z=await vt(p.value);p.value.page===1&&(a.value=[]),a.value=[...a.value,...z.data],_.value=z.last_page,p.value.page=z.current_page}catch(z){console.log(z)}finally{y.value=!1}else a.value=[],o.value.voiceId=""};re(()=>{d()});const M=z=>{o.value.voiceId=z.id,d()},m=()=>{p.value.page>=_.value||(p.value.page++,d())},D=z=>{z&&Le.confirm("确定要删除该音色吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await mt({id:z}),F.success("音色删除成功！"),await d()}catch{F.error("音色删除失败，请重试！")}})};return H(()=>o.value,z=>{r("change",z),A.value=z.voice},{deep:!0,immediate:!0}),(z,I)=>{const X=Ce,s=Re,t=Ue,f=Ne,R=ae("Plus"),V=ee,L=ne,k=Fe,E=Se,Q=Te;return u(),h("div",Gs,[e("div",Ks,[l(k,{model:o.value},{default:c(()=>[l(s,{label:"语速"},{default:c(()=>[e("div",Hs,[e("div",Os,[l(X,{modelValue:o.value.speed,"onUpdate:modelValue":I[0]||(I[0]=S=>o.value.speed=S),min:1,max:5},null,8,["modelValue"])]),e("span",Zs,Y(o.value.speed),1)])]),_:1}),l(s,{label:"音调"},{default:c(()=>[e("div",Ws,[e("div",qs,[l(X,{modelValue:o.value.pitch,"onUpdate:modelValue":I[1]||(I[1]=S=>o.value.pitch=S),min:1,max:5},null,8,["modelValue"])]),e("span",$s,Y(o.value.pitch),1)])]),_:1}),l(s,{label:"音量"},{default:c(()=>[e("div",eo,[e("div",to,[l(X,{modelValue:o.value.volume,"onUpdate:modelValue":I[2]||(I[2]=S=>o.value.volume=S),min:0,max:100},null,8,["modelValue"])]),e("span",lo,Y(o.value.volume),1)])]),_:1}),l(s,null,{default:c(()=>[l(f,{modelValue:i.value,"onUpdate:modelValue":I[3]||(I[3]=S=>i.value=S),onChange:b},{default:c(()=>[l(t,{value:"system",class:"tab"},{default:c(()=>I[7]||(I[7]=[N("系统音色")])),_:1}),l(t,{value:"custom",class:"tab"},{default:c(()=>I[8]||(I[8]=[N("我的音色")])),_:1})]),_:1},8,["modelValue"])]),_:1}),q((u(),h("div",so,[l(s,{style:{overflow:"hidden"}},{default:c(()=>[i.value==="custom"?(u(),h("div",oo,[l(L,{class:"mb-[10px] add-btn",onClick:x},{default:c(()=>[l(V,{size:"20",class:"mr-1"},{default:c(()=>[l(R)]),_:1}),I[9]||(I[9]=N(" 添加音色 "))]),_:1})])):j("",!0),l(Dt,{voices:a.value,showEmpty:i.value==="system",emptyText:i.value==="system"?"暂无自定义音色":"暂无系统音色",value:o.value.voiceId,"onUpdate:value":I[4]||(I[4]=S=>o.value.voiceId=S),voice:o.value.voice,"onUpdate:voice":I[5]||(I[5]=S=>o.value.voice=S),multi:!1,"show-del":!0,onDelVoice:D},null,8,["voices","showEmpty","emptyText","value","voice"])]),_:1})])),[[E,m],[Q,y.value]])]),_:1},8,["model"])]),l(Js,{visible:v.value,"onUpdate:visible":I[6]||(I[6]=S=>v.value=S),onConfirm:M},null,8,["visible"])])}}}),io=P(no,[["__scopeId","data-v-7f3cac86"]]),ao={class:"flex items-center justify-between mb-[10px]"},ro={class:"text-[18px] text-[#111111] font-bold"},co={class:"flex-1 overflow-hidden"},uo=T({__name:"EditDialog",props:{position:{type:String,default:"absolute"},title:{type:String,default:""},showClose:{type:Boolean,default:!0},visible:{type:Boolean,default:!1}},emits:["update:visible","close"],setup(C,{emit:w}){const n=w,r=()=>{n("close"),n("update:visible",!1)};return(A,o)=>{const i=ee;return C.visible?(u(),h("div",{key:0,class:le(["edit-dialog flex flex-col",C.position])},[e("div",ao,[e("span",ro,Y(C.title),1),C.showClose?(u(),J(i,{key:0,class:"close-btn cursor-pointer",onClick:r},{default:c(()=>[l(U(We))]),_:1})):j("",!0)]),e("div",co,[he(A.$slots,"default",{},void 0,!0)])],2)):j("",!0)}}}),Qe=P(uo,[["__scopeId","data-v-08264f7b"]]),vo={class:"document-editor h-full"},mo={class:"content-box h-full flex flex-col"},po={class:"voice-selector flex justify-between items-center flex-wrap"},fo={class:"script-list"},go={class:"script-content"},Ao={class:"script-actions"},bo=["onClick"],xo=["onClick"],yo={key:0,class:"add-script-btn"},ho=T({__name:"Document",emits:["change"],setup(C,{expose:w,emit:n}){const r=W("isMultiple",!1),A={document:"AI文案生成",subtitles:"字幕设置",voice:"选择配音音色"},o=g(""),i=g(""),a=g(!1),v=s=>{s=="voice"&&!D.value||(o.value=s,i.value=A[s],a.value=!0)},x=g(),p=g({}),_=g(null),b=g([""]),y=s=>{const t=b.value[s];yt(t)},d=s=>{b.value.splice(s,1)},M=()=>{b.value.push("")},m=s=>{console.log(s,"AI文案生成"),b.value=s.map(t=>t.content),a.value=!1},D=B(()=>b.value.some(s=>s.trim()));H(b,s=>{I("change",{scripts:s})},{deep:!0});const z=s=>{if(s){if(s.scripts&&!Array.isArray(s.scripts))try{s.scripts=JSON.parse(s.scripts)}catch{s.scripts=[s.scripts]}else s.scripts||(s.scripts=[""]);s.scripts&&(b.value=s.scripts),s.voice&&(_.value=s.voice),s.subtitle&&x.value.setDefaultValue(s.subtitle)}},I=n,X=s=>{p.value=s,I("change",{subtitle:s})};return w({setDocumentData:z}),(s,t)=>{const f=ae("ArrowRight"),R=ee,V=ne,L=qe;return u(),h("div",vo,[e("div",mo,[e("div",po,[l(V,{type:"primary",class:"w-[130px] setting-btn mb-4",onClick:t[0]||(t[0]=k=>v("subtitles")),disabled:!D.value},{default:c(()=>[t[4]||(t[4]=e("i",{class:"iconfont icon-tiquzimu text-[13px] mr-[7px]"},null,-1)),t[5]||(t[5]=N()),t[6]||(t[6]=e("span",null,"字幕设置",-1)),t[7]||(t[7]=N()),l(R,{class:"ml-[7px]"},{default:c(()=>[l(f)]),_:1})]),_:1},8,["disabled"]),e("div",{class:"ai-script-btn cursor-pointer flex items-center justify-center mb-[16px]",onClick:t[1]||(t[1]=k=>v("document"))},t[8]||(t[8]=[e("img",{class:"ai-icon",src:Cs,alt:""},null,-1),e("span",null,"AI文案",-1)]))]),e("div",fo,[(u(!0),h(G,null,$(b.value,(k,E)=>(u(),h("div",{class:"script-item",key:E},[e("div",go,[l(zs,{modelValue:b.value[E],"onUpdate:modelValue":Q=>b.value[E]=Q,height:U(r)?"120px":"360px",placeholder:"请在这里输入口播文案...","debounce-time":3e3},null,8,["modelValue","onUpdate:modelValue","height"])]),e("div",Ao,[k?(u(),J(L,{key:0,content:"复制",placement:"top"},{default:c(()=>[e("div",{class:"action-icon copy-icon",onClick:Q=>y(E)},[l(R,null,{default:c(()=>[l(U($e))]),_:1})],8,bo)]),_:2},1024)):j("",!0),k&&b.value.length>1?(u(),J(L,{key:1,content:"删除",placement:"top"},{default:c(()=>[e("div",{class:"action-icon delete-icon",onClick:Q=>d(E)},[l(R,null,{default:c(()=>[l(U(et))]),_:1})],8,xo)]),_:2},1024)):j("",!0)])]))),128))]),U(r)?(u(),h("div",yo,[l(Be,{onClick:M},{default:c(()=>t[9]||(t[9]=[N("新建口播文案")])),_:1})])):j("",!0)]),l(Qe,{visible:a.value,"onUpdate:visible":t[3]||(t[3]=k=>a.value=k),title:i.value},{default:c(()=>[q(l(Ft,{onGenerate:m},null,512),[[oe,o.value=="document"]]),q(l(Wt,{ref_key:"subtitleSettingRef",ref:x,showClose:!0,onChange:X},null,512),[[oe,o.value=="subtitles"]]),q(l(io,{"model-value":_.value,"onUpdate:modelValue":t[2]||(t[2]=k=>_.value=k)},null,8,["model-value"]),[[oe,o.value=="voice"]])]),_:1},8,["visible","title"])])}}}),_o=P(ho,[["__scopeId","data-v-f8ced339"]]),wo={class:"title-wrapper h-full flex flex-col"},ko=T({__name:"index",props:{showTime:{type:Boolean,default:!0},showTitle:{type:Boolean,default:!1},showClose:{type:Boolean,default:!1},styleMultiple:{type:Boolean,default:!1}},emits:["change"],setup(C,{expose:w,emit:n}){const r=fe(),A=W("isMultiple",!1),o=C,i=g(),a=B({get(){return r.activeTitleIndex},set(b){r.setActiveTitleIndex(b)}}),v=B({get(){return r.titleList},set(b){r.setTitleList(b)}}),x=()=>{r.addTitle()},p=B({get(){return r.activeTitle},set(b){b!==null&&r.updateActiveTitle(b)}});return w({setDefaultValue:b=>{b&&(o.showTitle?(r.setTitleList(b),r.setActiveTitleIndex(0)):r.updateActiveTitle(b))}}),(b,y)=>{const d=Ve;return u(),h("div",wo,[C.showTitle?(u(),h(G,{key:0},[v.value.length?(u(),h(G,{key:0},[l(Rt,{ref_key:"titleTagsRef",ref:i,modelValue:a.value,"onUpdate:modelValue":y[0]||(y[0]=M=>a.value=M),tags:v.value,"onUpdate:tags":y[1]||(y[1]=M=>v.value=M),"max-visible-tags":4,editable:U(A)},null,8,["modelValue","tags","editable"]),l(we,{showTime:C.showTime,showClose:C.showClose,styleMultiple:!1},null,8,["showTime","showClose"])],64)):(u(),J(d,{key:1,class:"flex-1",image:U(Pe),"image-size":207},{description:c(()=>y[3]||(y[3]=[e("div",{class:"empty-title text-[#999999] text-[16px] mt-[-50px]"},"还未添加标题",-1)])),default:c(()=>[l(Be,{width:"130px",class:"mt-[-20px]",onClick:x},{default:c(()=>y[4]||(y[4]=[N("添加标题")])),_:1})]),_:1},8,["image"]))],64)):(u(),J(we,{key:1,"show-time":C.showTime,"show-close":C.showClose,styleMultiple:C.styleMultiple,modelValue:p.value,"onUpdate:modelValue":y[2]||(y[2]=M=>p.value=M)},null,8,["show-time","show-close","styleMultiple","modelValue"]))])}}}),Co={class:"picker-title flex justify-between items-center"},Io={class:"picker flex items-center"},Do={class:"color-list overflow-hidden flex flex-wrap mt-[14px]"},zo=["onClick"],Mo={class:"w-[26px] h-[26px] color-item-inner flex items-center justify-center"},Eo=T({__name:"ColorPicker",props:{color:{default:"transparent"},colorList:{default:()=>["#fff","#000","#FF5C37","#FFEA2F","#FF7B2A","#009A4D","#257BFF","#8296FF"]}},emits:["update:color"],setup(C,{emit:w}){const n=C,r=w,A=B({get(){return n.color},set(o){r("update:color",o)}});return(o,i)=>{const a=ke;return u(),h("div",null,[e("div",Co,[i[2]||(i[2]=e("span",{class:"text-[#555] text-[16px]"},"纯色背景：",-1)),e("div",Io,[l(a,{modelValue:A.value,"onUpdate:modelValue":i[0]||(i[0]=v=>A.value=v)},null,8,["modelValue"]),i[1]||(i[1]=e("span",{class:"text-[#555] text-[16px] ml-[5px]"},"颜色选择器",-1))])]),e("div",Do,[(u(!0),h(G,null,$(o.colorList,v=>(u(),h("div",{class:le(["color-item cursor-pointer mr-[10px]",{"border-primary":v===A.value}]),key:v,onClick:x=>A.value=v},[e("div",Mo,[e("div",{class:"w-[18px] h-[18px] rounded-[2px]",style:te({background:v})},null,4)])],10,zo))),128))])])}}}),Vo=P(Eo,[["__scopeId","data-v-d77a4cae"]]),Fo={class:"video-cover-list-wrap"},Ro={class:"title text-[#555] text-[16px]"},Lo={class:"video-cover-list mt-[15px] overflow-hidden"},No=["onClick"],Uo=["src"],So={class:"select-icon absolute top-[0] right-[0]"},To={class:"absolute top-[-15px] right-[-15px]"},jo=T({__name:"VideoCoverList",props:{list:{},label:{},bg:{}},emits:["update:bg"],setup(C,{emit:w}){const n=C,r=w,A=B({get:()=>n.bg||"",set:o=>{r("update:bg",o)}});return(o,i)=>{const a=ee,v=pe,x=me;return u(),h("div",Fo,[e("div",Ro,Y(o.label),1),e("div",Lo,[l(x,{gutter:16},{default:c(()=>[(u(!0),h(G,null,$(o.list,p=>(u(),J(v,{md:8,sm:12,xs:24,key:p.id},{default:c(()=>[e("div",{class:le(["video-cover-item rounded-[6px] cursor-pointer overflow-hidden relative",{active:o.bg&&o.bg===p.url}]),onClick:_=>A.value=p.url},[e("img",{src:p.url,alt:""},null,8,Uo),e("div",So,[e("div",To,[l(a,{size:16,class:"text-primary",color:"#fff"},{default:c(()=>[l(U(Ee))]),_:1})])])],10,No)]),_:2},1024))),128))]),_:1})])])}}}),Po=P(jo,[["__scopeId","data-v-eadae384"]]),Bo={class:"h-full overflow-y-auto"},Xo=T({__name:"ImageBackground",props:{bg:{default:""},list:{default:()=>[]},showList:{type:Boolean,default:!1},label:{default:"图片背景（素材智能取帧）："}},emits:["update:bg","change"],setup(C,{emit:w}){const n=C,r=w,A=W("imageBackgroundTitle","背景设置"),o=B({get(){return n.bg||""},set(a){r("update:bg",a),r("change",{bg:a,type:ye(a)})}}),i=a=>{a.length&&(o.value=a[0].url)};return(a,v)=>(u(),J(Qe,{position:"static",title:U(A),"show-close":!1,visible:!0},{default:c(()=>[e("div",Bo,[l(it,{label:"素材库添加：",type:"image",theme:"button",multiSelect:!1,onSubmit:i}),l(Vo,{class:"mt-[20px]",color:o.value,"onUpdate:color":v[0]||(v[0]=x=>o.value=x)},null,8,["color"]),a.showList?(u(),J(Po,{key:0,list:a.list,label:a.label,class:"mt-[20px]",bg:o.value,"onUpdate:bg":v[1]||(v[1]=x=>o.value=x)},null,8,["list","label","bg"])):j("",!0)])]),_:1},8,["title"]))}}),Qo="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyVpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDYuMC1jMDAzIDExNi5kZGM3YmM0LCAyMDIxLzA4LzE3LTEzOjE4OjM3ICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgMjEuMiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RDYzODFERTkyOEJDMTFGMEI3RUVFQzI0Q0RCMzRDMjgiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RDYzODFERUEyOEJDMTFGMEI3RUVFQzI0Q0RCMzRDMjgiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpENjM4MURFNzI4QkMxMUYwQjdFRUVDMjRDREIzNEMyOCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpENjM4MURFODI4QkMxMUYwQjdFRUVDMjRDREIzNEMyOCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PsWRaU0AAACQSURBVHjaYvz//z8DuYAFxggLCyPalFWrVjGCaCY08RNA7A7E1kB8Fyq2E4i1gTgAiB9gtRkIngCxGxB/hvJh9HsgvgbF94H4LEwDss1NSBpwgUtQdRiaZxPp5WZsmkkGxGj+R4nmX+RoZqPE2UyUaGaH0jykaI4D4mNArAjlBwHxJqg4CmCkJGNQFM8AAQYAK5IeThhArCQAAAAASUVORK5CYII=",Yo="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAA20lEQVQokY3SIU5DQRSF4a/l5WEhbAGJQhAcbADHjIENkLoaaFAoQICiAoVtJogqEhwagUKyhbKAKgR3wksQr8fMzZ35z8nMnUFK6QQP2NCvBUZNAK+YxUaL+6jHWEY9wR6mw0iYlVLmeMFpHDosv5pjP4B3bA1rbs65xTN2A/iK/g3OcYtrqFAvUEq5qAFNrPUOfUDbTeoFcs7b1bxC4xWAt+peoeUKwEeMQPf1+oDjal6hSR9QSqlDNkgpfWMTn7iL/hqusI5Lf7/iCAcNzjDFDp7812OnXmD0A0GgZ3oxqUWlAAAAAElFTkSuQmCC",Jo={class:"edit-panel"},Go={class:"edit-content bg-secondary mt-2"},Ko=T({__name:"EditPanel",props:{form:{type:Object,default:()=>({})},musicMulti:{type:Boolean,default:!0}},emits:["update:form"],setup(C,{emit:w}){const n=g(),r=g(),A=g(),o=W("showBackground",!1),i=W("isMultiple",!1),a=C,v=w,x=g([{label:"文案",value:"text",icon:Qo},{label:"音乐",value:"music",icon:zt},{label:"标题",value:"title",icon:Mt}]);o&&x.value.push({label:"背景",value:"background",icon:Yo});const p=g("text"),_=B({get(){return a.form},set(d){v("update:form",d)}}),b=d=>{_.value={..._.value,...d}},y=d=>{_.value={..._.value,title:d}};return(d,M)=>(u(),h("div",Jo,[l(Et,{class:"w-full",tabs:x.value,activeTab:p.value,"onUpdate:activeTab":M[0]||(M[0]=m=>p.value=m)},null,8,["tabs","activeTab"]),e("div",Go,[q(l(_o,{ref_key:"editDocumentRef",ref:n,onChange:b},null,512),[[oe,p.value==="text"]]),q(l(Vt,{ref_key:"musicSettingRef",ref:A,multi:a.musicMulti,onChange:b},null,8,["multi"]),[[oe,p.value==="music"]]),q(l(ko,{ref_key:"titleSettingRef",ref:r,styleMultiple:U(i),"show-title":!0,onChange:y},null,8,["styleMultiple"]),[[oe,p.value==="title"]]),q(l(Xo,{bg:_.value.bg,"onUpdate:bg":M[1]||(M[1]=m=>_.value.bg=m)},null,8,["bg"]),[[oe,p.value==="background"]])])]))}}),Ho=P(Ko,[["__scopeId","data-v-caf04cb1"]]),Oo={class:"video-edit-wrapper flex flex-col md:flex-row w-full h-full"},Zo={class:"center-panel bg-secondary md:mr-[20px] mb-4 md:mb-0"},Wo={class:"right-panel"},qo=T({__name:"index",props:{musicMulti:{type:Boolean,default:!0},defaultTab:{type:Number,default:2}},emits:["change"],setup(C,{emit:w}){const n=fe(),r=C,A=w,o=g(2);H(()=>r.defaultTab,a=>{console.log("defaultTab",a),o.value=a,console.log(o.value,"1")},{immediate:!0});const i=g({cover:"",bg:""});return H(i,()=>{console.log("formData",i.value),A("change",i.value)},{deep:!0}),re(()=>{n.reset()}),de(()=>{n.reset()}),(a,v)=>(u(),h("div",Oo,[e("div",Zo,[l(ks,{tab:o.value,"onUpdate:tab":v[0]||(v[0]=x=>o.value=x),form:i.value,"onUpdate:form":v[1]||(v[1]=x=>i.value=x)},{"preview-content":c(()=>[he(a.$slots,"preview-content",{},void 0,!0)]),_:3},8,["tab","form"])]),e("div",Wo,[l(Ho,{form:i.value,"onUpdate:form":v[2]||(v[2]=x=>i.value=x),"music-multi":r.musicMulti},null,8,["form","music-multi"])])]))}}),$o=P(qo,[["__scopeId","data-v-216ae6e9"]]),en={class:"flex flex-col h-full"},tn={class:"title flex flex-wrap justify-between items-center mb-[29px]"},ln={class:"title-center flex-1 px-4 flex items-center justify-center mb-2 sm:mb-0"},sn={key:0,class:"flex items-center justify-center w-full"},on={class:"text-[#111] text-[20px] font-bold line-clamp-1 overflow-hidden"},nn={key:1,class:"flex items-center justify-center flex-1 w-full max-w-[500px]"},an={class:"title-right flex flex-wrap gap-2 w-full sm:w-auto"},rn={class:"user-portrait-view-content-wrapper flex-1 overflow-x-hidden"},cn={class:"user-portrait-view-content-left bg-secondary h-full"},un=T({__name:"Index",setup(C){se("showBackground",!0);const w=fe(),n=tt({id:void 0,name:"",image_id:0,image_url:"",theme_id:0,timbre_id:0,isbackground:2,background:"#000",iscover:2,timbre:{},content:"",cover:"",music:{id:0},bg:"",title:"",subtitle:{content:"",color:"#fff",size:"14"},configuration:"",scripts:""}),r=g(!1),A=()=>{r.value=!0},o=(s,t)=>{s in n&&(n[s]=t)};se("formData",n),se("updateFormData",o);const i=g(!1);se("showCover",i),se("updateShowCover",s=>{i.value=s});const v=g(),x=g(!1),p=async()=>{var R,V,L,k,E;if(!x.value||!n.image_id||!n.name||r.value)return;const t={id:(R=v.value)==null?void 0:R.id,imges_id:n.image_id,theme_id:n.theme_id,isbackground:2,background:"#000",name:n.name,copywriting:n.scripts.length?n.scripts[0]:"",music_id:(V=n.music)==null?void 0:V.id,title:n.title,subtitle:((L=n.subtitle)==null?void 0:L.content)??"",configuration:JSON.stringify({title:w.titleList[0],subtitle:{color:(k=n.subtitle)==null?void 0:k.color,size:(E=n.subtitle)==null?void 0:E.size}}),imgurl:"https://images.china9.cn/attachment/2025-05-15/S151mw50SvWAz9Okyj4DBhN7QPQhVi5fuWd1QrNE.png",iscover:1},f=await gt(t);f&&f.id&&(v.value={id:f.id})},_=s=>{if(x.value){s.stopPropagation(),s.preventDefault();return}x.value=!0,p(),window.removeEventListener("click",_)};window.addEventListener("click",_),de(()=>{window.removeEventListener("click",_)}),H(()=>[n,w],()=>{p()},{deep:!0});const b=()=>{r.value=!1,p()},y=g("content"),d=s=>{y.value=s};se("editorType",y),se("updateEditorType",d);const M=async()=>{try{const s=await pt({});n.name=s.name}catch(s){console.log(s),F.error("初始化标题失败")}},m=s=>{console.log("formDataChange",s),Object.entries(s).forEach(([t,f])=>{o(t,f)})},D=nt(),z=async()=>{if(!v.value||!v.value.id){F.warning("项目信息不完整，请先保存设置");return}try{const s=v.value.id;await At({id:s}),await D.push("/smart-material/user-portrait-preview/"+s)}catch(s){console.error("Create project error:",s),F.error("生成视频失败，请重试")}},I=async()=>{if(!(!v.value||!v.value.id))try{const s=v.value.id,t=await ft({id:s}),{data:f}=t;n.image_id=f.imges_id,n.theme_id=f.theme_id,n.name=f.name,n.music={id:f.music_id},n.title=f.title,n.subtitle=f.subtitle,n.configuration=f.configuration,w.setTitleList([f.configuration.title]),n.cover=f.imgurl,n.content=f.copywriting,n.isbackground=f.isbackground,n.background=f.background,n.iscover=f.iscover,n.timbre=f.timbre,n.cover=f.imgurl,n.timbre_id=f.timbre_id,n.bg=f.bg,f.status===0&&F.warning("视频生成中，请稍后刷新页面")}catch(s){console.error("Get project detail error:",s)}},X=lt();return re(()=>{if(X.query.id){const s=X.query.id;v.value={id:s},I()}else M()}),(s,t)=>{const f=ee,R=ve,V=ne,L=pe,k=me,E=st;return u(),J(E,{class:"user-portrait-view h-full"},{default:c(()=>[e("div",en,[e("div",tn,[t[5]||(t[5]=e("div",{class:"title-left mb-2 sm:mb-0"},[e("div",{class:"title-left-text text-[#111] text-[18px] font-bold"}," AI数字人 ")],-1)),e("div",ln,[r.value?(u(),h("div",nn,[l(R,{modelValue:n.name,"onUpdate:modelValue":t[0]||(t[0]=Q=>n.name=Q),placeholder:"请输入标题",size:"large"},null,8,["modelValue"]),l(V,{type:"primary",link:"",size:"large",class:"ml-4",onClick:b},{default:c(()=>t[2]||(t[2]=[N("保存")])),_:1})])):(u(),h("div",sn,[e("h1",on,Y(n.name),1),l(f,{class:"text-[#6C6C6C] text-[30px] ml-[15px] cursor-pointer",onClick:A},{default:c(()=>[l(U(ot))]),_:1})]))]),e("div",an,[l(V,{type:"primary",onClick:z,class:"flex-1 sm:flex-none"},{default:c(()=>t[3]||(t[3]=[e("i",{class:"iconfont icon-shangchuan mr-1"},null,-1),N(" 生成视频 ")])),_:1}),l(V,{type:"primary",onClick:t[1]||(t[1]=Q=>s.$router.push("/smart-material/user-portrait-history")),class:"flex-1 sm:flex-none"},{default:c(()=>t[4]||(t[4]=[e("i",{class:"iconfont icon-lishijilu mr-1"},null,-1),N(" 历史口播 ")])),_:1})])]),e("div",rn,[l(k,{class:"user-portrait-view-content flex-1 box-border h-full",gutter:20},{default:c(()=>[l(L,{lg:7,md:24,sm:24,class:"h-full"},{default:c(()=>[e("div",cn,[l(ls)])]),_:1}),l(L,{lg:17,md:24,sm:24,class:"h-full"},{default:c(()=>[l($o,{"music-multi":!1,onChange:m},{"preview-content":c(()=>[l(us)]),_:1})]),_:1})]),_:1})])])]),_:1})}}}),Bn=P(un,[["__scopeId","data-v-31c7bb25"]]);export{Bn as default};
