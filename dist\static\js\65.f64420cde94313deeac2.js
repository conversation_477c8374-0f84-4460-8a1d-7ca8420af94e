webpackJsonp([65],{"2a0h":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=a("pI5c"),r={name:"amendPwd",data:function(){return{formData:{},rules:{code:[{required:!0,message:"验证码不能为空",trigger:"blur"}],password:[{required:!0,message:"密码不能为空",trigger:"blur"}],password_confirmation:[{required:!0,message:"确认密码不能为空",trigger:"blur"}]},btn_loading:!1,time:0,uuu:this.$store.state.user.memberInfo}},created:function(){},methods:{sendMesCode:function(){var e=this;0===this.time&&(this.time=60,this.timer(),Object(s._10)({phone:this.uuu.phone,scene:"verify"}).then(function(t){200===t.code&&e.$message.success("验证码发送成功，请注意查收")}))},submit:function(){var e=this;this.$refs.formData.validate(function(t){t&&(e.btn_loading=!0,Object(s._32)(e.formData).then(function(t){e.btn_loading=!1,200===t.code&&(e.$message.success(t.message||"修改成功"),setTimeout(function(){e.$router.replace({path:"/home",query:{type:"logout"}})},1e3))}).catch(function(){e.btn_loading=!1}))})},timer:function(){var e=this;setTimeout(function(){e.time--,0!==e.time&&e.timer()},1e3)}}},o={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("安全设置 > 修改密码")])]),e._v(" "),a("el-form",{ref:"formData",attrs:{"label-position":"left","label-width":"100px",inline:!0,model:e.formData,rules:e.rules,size:"small"}},[a("div",{staticClass:"container"},[a("el-form-item",{attrs:{label:"已验证手机"}},[e._v(e._s(e._f("phoneEncryption")(e.uuu.phone)))]),e._v(" "),a("el-form-item",{attrs:{label:"短信验证码",prop:"code"}},[a("div",{staticStyle:{display:"flex"}},[a("el-input",{attrs:{placeholder:"请输入短信验证码"},model:{value:e.formData.code,callback:function(t){e.$set(e.formData,"code",t)},expression:"formData.code"}}),e._v(" "),a("el-button",{ref:"mesCodeBtn",staticStyle:{"margin-left":"20px"},attrs:{disabled:e.time>0,type:"primary"},on:{click:e.sendMesCode}},[e._v(e._s(0===e.time?"发送短信验证码":"再次发送验证码("+e.time+"s)"))])],1)]),e._v(" "),a("el-form-item",{attrs:{label:"新密码",prop:"password"}},[a("el-input",{attrs:{"show-password":"",placeholder:"请输入新密码"},model:{value:e.formData.password,callback:function(t){e.$set(e.formData,"password",t)},expression:"formData.password"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"确认新密码",prop:"password_confirmation"}},[a("el-input",{attrs:{"show-password":"",placeholder:"再次输入密码"},model:{value:e.formData.password_confirmation,callback:function(t){e.$set(e.formData,"password_confirmation",t)},expression:"formData.password_confirmation"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"margin-top":"50px"},attrs:{label:" "}},[a("el-button",{attrs:{type:"primary",loading:e.btn_loading},on:{click:e.submit}},[e._v("提交")])],1)],1)])],1)],1)},staticRenderFns:[]};var i=a("VU/8")(r,o,!1,function(e){a("9fW/")},"data-v-8b2e1682",null);t.default=i.exports},"9fW/":function(e,t){}});