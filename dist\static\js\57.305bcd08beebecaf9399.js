webpackJsonp([57],{MzgA:function(t,e){},b17q:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=a("pI5c"),r={name:"cancelled",data:function(){return{gcc:"",formData:{},rules:{code:[{required:!0,message:"验证码必填",trigger:"blur"}],reason:[{required:!0,message:"申请原因必填",trigger:"blur"}]},time:0,upload_url:"/api/workorder/upload",uploadParam:{access_token:this.$cookies.get("access_token")},pictureUrl:"",btn_loading:!1}},computed:{uuu:{get:function(){return this.$store.state.user&&"companyInfo"in this.$store.state.user&&"mobile"in this.$store.state.user.companyInfo?this.$store.state.user.companyInfo.mobile:""},set:function(){}}},created:function(){this.userInfo()},methods:{sendMesCode:function(){var t=this;0===this.time&&(this.time=60,this.timer(),Object(s._10)({phone:this.uuu,scene:"verify"}).then(function(e){200===e.code&&t.$message.success("验证码发送成功，请注意查收")}))},submit:function(){var t=this;this.btn_loading=!0,this.$refs.formData.validate(function(e){e&&(t.formData.pictures=[t.pictureUrl],Object(s.r)(t.formData).then(function(e){t.btn_loading=!1,200===e.code&&(t.$message.success("注销成功"),t.$router.replace("/console")),t.userInfo()}).catch(function(){t.btn_loading=!1}))})},timer:function(){var t=this;setTimeout(function(){t.time--,0!==t.time&&t.timer()},1e3)},handleSuccess:function(t,e){200===t.code&&(this.pictureUrl=t.data.url)},userInfo:function(){var t=this;Object(s._31)().then(function(e){200===e.code?(t.gcc=e.data.companyInfo.enterprise_name,t.uuu=e.data.companyInfo.mobile,t.$store.state.user=e.data):t.$message.error(e.data.message)}).catch(function(t){console.log(t)})}}},o={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("申请注销 > 安全设置")])]),t._v(" "),a("el-form",{ref:"formData",attrs:{"label-position":"left","label-width":"100px",inline:!0,model:t.formData,rules:t.rules,size:"small"}},[a("div",{staticClass:"container"},[a("el-form-item",{attrs:{label:"企业名称"}},[t._v(t._s(t.gcc))]),t._v(" "),a("el-form-item",{attrs:{label:"已验证手机"}},[t._v(t._s(t._f("phoneEncryption")(t.uuu)))]),t._v(" "),a("el-form-item",{attrs:{label:"短信验证码"}},[a("div",{staticStyle:{display:"flex"}},[a("el-input",{attrs:{placeholder:"请输入短信验证码"},model:{value:t.formData.code,callback:function(e){t.$set(t.formData,"code",e)},expression:"formData.code"}}),t._v(" "),a("el-button",{ref:"mesCodeBtn",staticStyle:{"margin-left":"20px"},attrs:{disabled:t.time>0,type:"primary"},on:{click:t.sendMesCode}},[t._v(t._s(0===t.time?"发送短信验证码":"再次发送验证码("+t.time+"s)"))])],1)]),t._v(" "),a("el-form-item",{attrs:{label:"注销理由"}},[a("el-input",{staticStyle:{width:"295px"},attrs:{type:"textarea",rows:3,maxlength:"200","show-word-limit":"",placeholder:"请您输入想要注销该企业理由"},model:{value:t.formData.reason,callback:function(e){t.$set(t.formData,"reason",e)},expression:"formData.reason"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"附加"}},[a("el-upload",{staticClass:"avatar-uploader",attrs:{data:t.uploadParam,name:"file",action:t.upload_url,"show-file-list":!1,"on-success":t.handleSuccess}},[t.pictureUrl?a("img",{staticClass:"avatar",attrs:{src:t.pictureUrl}}):a("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-top":"50px"},attrs:{label:" "}},[a("el-button",{attrs:{type:"primary",loading:t.btn_loading},on:{click:t.submit}},[t._v("确定注销")])],1)],1)])],1)],1)},staticRenderFns:[]};var i=a("VU/8")(r,o,!1,function(t){a("MzgA")},"data-v-ea82c0d8",null);e.default=i.exports}});