!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).DouyinOpenJSBridge={})}(this,(function(t){"use strict";"undefined"!=typeof window&&(window.douyin_open=t);var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(t,e){return t(e={exports:{}},e.exports),e.exports}var n=function(t){return t&&t.Math==Math&&t},o=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof e&&e)||function(){return this}()||Function("return this")(),i=function(t){try{return!!t()}catch(t){return!0}},a=!i((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),c={}.propertyIsEnumerable,s=Object.getOwnPropertyDescriptor,u={f:s&&!c.call({1:2},1)?function(t){var e=s(this,t);return!!e&&e.enumerable}:c},l=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},f={}.toString,p=function(t){return f.call(t).slice(8,-1)},h="".split,d=i((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==p(t)?h.call(t,""):Object(t)}:Object,v=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},g=function(t){return d(v(t))},y=function(t){return"object"==typeof t?null!==t:"function"==typeof t},m=function(t,e){if(!y(t))return t;var r,n;if(e&&"function"==typeof(r=t.toString)&&!y(n=r.call(t)))return n;if("function"==typeof(r=t.valueOf)&&!y(n=r.call(t)))return n;if(!e&&"function"==typeof(r=t.toString)&&!y(n=r.call(t)))return n;throw TypeError("Can't convert object to primitive value")},b={}.hasOwnProperty,w=function(t,e){return b.call(t,e)},S=o.document,_=y(S)&&y(S.createElement),E=function(t){return _?S.createElement(t):{}},O=!a&&!i((function(){return 7!=Object.defineProperty(E("div"),"a",{get:function(){return 7}}).a})),x=Object.getOwnPropertyDescriptor,j={f:a?x:function(t,e){if(t=g(t),e=m(e,!0),O)try{return x(t,e)}catch(t){}if(w(t,e))return l(!u.f.call(t,e),t[e])}},I=function(t){if(!y(t))throw TypeError(String(t)+" is not an object");return t},k=Object.defineProperty,P={f:a?k:function(t,e,r){if(I(t),e=m(e,!0),I(r),O)try{return k(t,e,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},A=a?function(t,e,r){return P.f(t,e,l(1,r))}:function(t,e,r){return t[e]=r,t},M=function(t,e){try{A(o,t,e)}catch(r){o[t]=e}return e},N=o["__core-js_shared__"]||M("__core-js_shared__",{}),T=Function.toString;"function"!=typeof N.inspectSource&&(N.inspectSource=function(t){return T.call(t)});var L,R,C,F=N.inspectSource,J=o.WeakMap,B="function"==typeof J&&/native code/.test(F(J)),D=r((function(t){(t.exports=function(t,e){return N[t]||(N[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.10.2",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),U=0,G=Math.random(),z=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++U+G).toString(36)},Q=D("keys"),K=function(t){return Q[t]||(Q[t]=z(t))},V={},W=o.WeakMap;if(B){var H=N.state||(N.state=new W),Y=H.get,$=H.has,q=H.set;L=function(t,e){if($.call(H,t))throw new TypeError("Object already initialized");return e.facade=t,q.call(H,t,e),e},R=function(t){return Y.call(H,t)||{}},C=function(t){return $.call(H,t)}}else{var X=K("state");V[X]=!0,L=function(t,e){if(w(t,X))throw new TypeError("Object already initialized");return e.facade=t,A(t,X,e),e},R=function(t){return w(t,X)?t[X]:{}},C=function(t){return w(t,X)}}var Z,tt,et={set:L,get:R,has:C,enforce:function(t){return C(t)?R(t):L(t,{})},getterFor:function(t){return function(e){var r;if(!y(e)||(r=R(e)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},rt=r((function(t){var e=et.get,r=et.enforce,n=String(String).split("String");(t.exports=function(t,e,i,a){var c,s=!!a&&!!a.unsafe,u=!!a&&!!a.enumerable,l=!!a&&!!a.noTargetGet;"function"==typeof i&&("string"!=typeof e||w(i,"name")||A(i,"name",e),(c=r(i)).source||(c.source=n.join("string"==typeof e?e:""))),t!==o?(s?!l&&t[e]&&(u=!0):delete t[e],u?t[e]=i:A(t,e,i)):u?t[e]=i:M(e,i)})(Function.prototype,"toString",(function(){return"function"==typeof this&&e(this).source||F(this)}))})),nt=o,ot=function(t){return"function"==typeof t?t:void 0},it=function(t,e){return arguments.length<2?ot(nt[t])||ot(o[t]):nt[t]&&nt[t][e]||o[t]&&o[t][e]},at=Math.ceil,ct=Math.floor,st=function(t){return isNaN(t=+t)?0:(t>0?ct:at)(t)},ut=Math.min,lt=function(t){return t>0?ut(st(t),9007199254740991):0},ft=Math.max,pt=Math.min,ht=function(t,e){var r=st(t);return r<0?ft(r+e,0):pt(r,e)},dt=function(t){return function(e,r,n){var o,i=g(e),a=lt(i.length),c=ht(n,a);if(t&&r!=r){for(;a>c;)if((o=i[c++])!=o)return!0}else for(;a>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},vt={includes:dt(!0),indexOf:dt(!1)},gt=vt.indexOf,yt=function(t,e){var r,n=g(t),o=0,i=[];for(r in n)!w(V,r)&&w(n,r)&&i.push(r);for(;e.length>o;)w(n,r=e[o++])&&(~gt(i,r)||i.push(r));return i},mt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],bt=mt.concat("length","prototype"),wt={f:Object.getOwnPropertyNames||function(t){return yt(t,bt)}},St={f:Object.getOwnPropertySymbols},_t=it("Reflect","ownKeys")||function(t){var e=wt.f(I(t)),r=St.f;return r?e.concat(r(t)):e},Et=function(t,e){for(var r=_t(e),n=P.f,o=j.f,i=0;i<r.length;i++){var a=r[i];w(t,a)||n(t,a,o(e,a))}},Ot=/#|\.prototype\./,xt=function(t,e){var r=It[jt(t)];return r==Pt||r!=kt&&("function"==typeof e?i(e):!!e)},jt=xt.normalize=function(t){return String(t).replace(Ot,".").toLowerCase()},It=xt.data={},kt=xt.NATIVE="N",Pt=xt.POLYFILL="P",At=xt,Mt=j.f,Nt=function(t,e){var r,n,i,a,c,s=t.target,u=t.global,l=t.stat;if(r=u?o:l?o[s]||M(s,{}):(o[s]||{}).prototype)for(n in e){if(a=e[n],i=t.noTargetGet?(c=Mt(r,n))&&c.value:r[n],!At(u?n:s+(l?".":"#")+n,t.forced)&&void 0!==i){if(typeof a==typeof i)continue;Et(a,i)}(t.sham||i&&i.sham)&&A(a,"sham",!0),rt(r,n,a,t)}},Tt=function(t){if("function"!=typeof t)throw TypeError(String(t)+" is not a function");return t},Lt=function(t){var e,r;this.promise=new t((function(t,n){if(void 0!==e||void 0!==r)throw TypeError("Bad Promise constructor");e=t,r=n})),this.resolve=Tt(e),this.reject=Tt(r)},Rt={f:function(t){return new Lt(t)}},Ct=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}},Ft="process"==p(o.process),Jt=it("navigator","userAgent")||"",Bt=o.process,Dt=Bt&&Bt.versions,Ut=Dt&&Dt.v8;Ut?tt=(Z=Ut.split("."))[0]+Z[1]:Jt&&(!(Z=Jt.match(/Edge\/(\d+)/))||Z[1]>=74)&&(Z=Jt.match(/Chrome\/(\d+)/))&&(tt=Z[1]);var Gt=tt&&+tt,zt=!!Object.getOwnPropertySymbols&&!i((function(){return!Symbol.sham&&(Ft?38===Gt:Gt>37&&Gt<41)})),Qt=zt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Kt=D("wks"),Vt=o.Symbol,Wt=Qt?Vt:Vt&&Vt.withoutSetter||z,Ht=function(t){return w(Kt,t)&&(zt||"string"==typeof Kt[t])||(zt&&w(Vt,t)?Kt[t]=Vt[t]:Kt[t]=Wt("Symbol."+t)),Kt[t]},Yt={},$t=Ht("iterator"),qt=Array.prototype,Xt=function(t,e,r){if(Tt(t),void 0===e)return t;switch(r){case 0:return function(){return t.call(e)};case 1:return function(r){return t.call(e,r)};case 2:return function(r,n){return t.call(e,r,n)};case 3:return function(r,n,o){return t.call(e,r,n,o)}}return function(){return t.apply(e,arguments)}},Zt={};Zt[Ht("toStringTag")]="z";var te="[object z]"===String(Zt),ee=Ht("toStringTag"),re="Arguments"==p(function(){return arguments}()),ne=te?p:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=Object(t),ee))?r:re?p(e):"Object"==(n=p(e))&&"function"==typeof e.callee?"Arguments":n},oe=Ht("iterator"),ie=function(t){var e=t.return;if(void 0!==e)return I(e.call(t)).value},ae=function(t,e){this.stopped=t,this.result=e},ce=function(t,e,r){var n,o,i,a,c,s,u,l,f=r&&r.that,p=!(!r||!r.AS_ENTRIES),h=!(!r||!r.IS_ITERATOR),d=!(!r||!r.INTERRUPTED),v=Xt(e,f,1+p+d),g=function(t){return n&&ie(n),new ae(!0,t)},y=function(t){return p?(I(t),d?v(t[0],t[1],g):v(t[0],t[1])):d?v(t,g):v(t)};if(h)n=t;else{if("function"!=typeof(o=function(t){if(null!=t)return t[oe]||t["@@iterator"]||Yt[ne(t)]}(t)))throw TypeError("Target is not iterable");if(void 0!==(l=o)&&(Yt.Array===l||qt[$t]===l)){for(i=0,a=lt(t.length);a>i;i++)if((c=y(t[i]))&&c instanceof ae)return c;return new ae(!1)}n=o.call(t)}for(s=n.next;!(u=s.call(n)).done;){try{c=y(u.value)}catch(t){throw ie(n),t}if("object"==typeof c&&c&&c instanceof ae)return c}return new ae(!1)};Nt({target:"Promise",stat:!0},{allSettled:function(t){var e=this,r=Rt.f(e),n=r.resolve,o=r.reject,i=Ct((function(){var r=Tt(e.resolve),o=[],i=0,a=1;ce(t,(function(t){var c=i++,s=!1;o.push(void 0),a++,r.call(e,t).then((function(t){s||(s=!0,o[c]={status:"fulfilled",value:t},--a||n(o))}),(function(t){s||(s=!0,o[c]={status:"rejected",reason:t},--a||n(o))}))})),--a||n(o)}));return i.error&&o(i.value),r.promise}});Nt({target:"Promise",stat:!0},{any:function(t){var e=this,r=Rt.f(e),n=r.resolve,o=r.reject,i=Ct((function(){var r=Tt(e.resolve),i=[],a=0,c=1,s=!1;ce(t,(function(t){var u=a++,l=!1;i.push(void 0),c++,r.call(e,t).then((function(t){l||s||(s=!0,n(t))}),(function(t){l||s||(l=!0,i[u]=t,--c||o(new(it("AggregateError"))(i,"No one promise resolved")))}))})),--c||o(new(it("AggregateError"))(i,"No one promise resolved"))}));return i.error&&o(i.value),r.promise}});var se=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return I(r),function(t){if(!y(t)&&null!==t)throw TypeError("Can't set "+String(t)+" as a prototype")}(n),e?t.call(r,n):r.__proto__=n,r}}():void 0);Nt({target:"Object",stat:!0},{setPrototypeOf:se});var ue,le=Object.keys||function(t){return yt(t,mt)},fe=a?Object.defineProperties:function(t,e){I(t);for(var r,n=le(e),o=n.length,i=0;o>i;)P.f(t,r=n[i++],e[r]);return t},pe=it("document","documentElement"),he=K("IE_PROTO"),de=function(){},ve=function(t){return"<script>"+t+"<\/script>"},ge=function(){try{ue=document.domain&&new ActiveXObject("htmlfile")}catch(t){}var t,e;ge=ue?function(t){t.write(ve("")),t.close();var e=t.parentWindow.Object;return t=null,e}(ue):((e=E("iframe")).style.display="none",pe.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(ve("document.F=Object")),t.close(),t.F);for(var r=mt.length;r--;)delete ge.prototype[mt[r]];return ge()};V[he]=!0;var ye=Object.create||function(t,e){var r;return null!==t?(de.prototype=I(t),r=new de,de.prototype=null,r[he]=t):r=ge(),void 0===e?r:fe(r,e)};Nt({target:"Object",stat:!0,sham:!a},{create:ye});var me=function(t){return Object(v(t))},be=Object.assign,we=Object.defineProperty,Se=!be||i((function(){if(a&&1!==be({b:1},be(we({},"a",{enumerable:!0,get:function(){we(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},r=Symbol();return t[r]=7,"abcdefghijklmnopqrst".split("").forEach((function(t){e[t]=t})),7!=be({},t)[r]||"abcdefghijklmnopqrst"!=le(be({},e)).join("")}))?function(t,e){for(var r=me(t),n=arguments.length,o=1,i=St.f,c=u.f;n>o;)for(var s,l=d(arguments[o++]),f=i?le(l).concat(i(l)):le(l),p=f.length,h=0;p>h;)s=f[h++],a&&!c.call(l,s)||(r[s]=l[s]);return r}:be;Nt({target:"Object",stat:!0,forced:Object.assign!==Se},{assign:Se});var _e=Array.isArray||function(t){return"Array"==p(t)},Ee=Ht("species"),Oe=function(t,e){var r;return _e(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!_e(r.prototype)?y(r)&&null===(r=r[Ee])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===e?0:e)},xe=[].push,je=function(t){var e=1==t,r=2==t,n=3==t,o=4==t,i=6==t,a=7==t,c=5==t||i;return function(s,u,l,f){for(var p,h,v=me(s),g=d(v),y=Xt(u,l,3),m=lt(g.length),b=0,w=f||Oe,S=e?w(s,m):r||a?w(s,0):void 0;m>b;b++)if((c||b in g)&&(h=y(p=g[b],b,v),t))if(e)S[b]=h;else if(h)switch(t){case 3:return!0;case 5:return p;case 6:return b;case 2:xe.call(S,p)}else switch(t){case 4:return!1;case 7:xe.call(S,p)}return i?-1:n||o?o:S}},Ie={forEach:je(0),map:je(1),filter:je(2),some:je(3),every:je(4),find:je(5),findIndex:je(6),filterOut:je(7)},ke=function(t,e){var r=[][t];return!!r&&i((function(){r.call(null,e||function(){throw 1},1)}))},Pe=Ie.forEach,Ae=ke("forEach")?[].forEach:function(t){return Pe(this,t,arguments.length>1?arguments[1]:void 0)};Nt({target:"Array",proto:!0,forced:[].forEach!=Ae},{forEach:Ae});var Me={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0};for(var Ne in Me){var Te=o[Ne],Le=Te&&Te.prototype;if(Le&&Le.forEach!==Ae)try{A(Le,"forEach",Ae)}catch(jc){Le.forEach=Ae}}var Re=i((function(){le(1)}));Nt({target:"Object",stat:!0,forced:Re},{keys:function(t){return le(me(t))}});var Ce=function(t,e,r){var n=m(e);n in t?P.f(t,n,l(0,r)):t[n]=r},Fe=Ht("species"),Je=function(t){return Gt>=51||!i((function(){var e=[];return(e.constructor={})[Fe]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Be=Je("splice"),De=Math.max,Ue=Math.min;Nt({target:"Array",proto:!0,forced:!Be},{splice:function(t,e){var r,n,o,i,a,c,s=me(this),u=lt(s.length),l=ht(t,u),f=arguments.length;if(0===f?r=n=0:1===f?(r=0,n=u-l):(r=f-2,n=Ue(De(st(e),0),u-l)),u+r-n>9007199254740991)throw TypeError("Maximum allowed length exceeded");for(o=Oe(s,n),i=0;i<n;i++)(a=l+i)in s&&Ce(o,i,s[a]);if(o.length=n,r<n){for(i=l;i<u-n;i++)c=i+r,(a=i+n)in s?s[c]=s[a]:delete s[c];for(i=u;i>u-n+r;i--)delete s[i-1]}else if(r>n)for(i=u-n;i>l;i--)c=i+r-1,(a=i+n-1)in s?s[c]=s[a]:delete s[c];for(i=0;i<r;i++)s[i+l]=arguments[i+2];return s.length=u-n+r,o}});var Ge=function(){var t=I(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e};function ze(t,e){return RegExp(t,e)}var Qe={UNSUPPORTED_Y:i((function(){var t=ze("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),BROKEN_CARET:i((function(){var t=ze("^r","gy");return t.lastIndex=2,null!=t.exec("str")}))},Ke=RegExp.prototype.exec,Ve=D("native-string-replace",String.prototype.replace),We=Ke,He=function(){var t=/a/,e=/b*/g;return Ke.call(t,"a"),Ke.call(e,"a"),0!==t.lastIndex||0!==e.lastIndex}(),Ye=Qe.UNSUPPORTED_Y||Qe.BROKEN_CARET,$e=void 0!==/()??/.exec("")[1];(He||$e||Ye)&&(We=function(t){var e,r,n,o,i=this,a=Ye&&i.sticky,c=Ge.call(i),s=i.source,u=0,l=t;return a&&(-1===(c=c.replace("y","")).indexOf("g")&&(c+="g"),l=String(t).slice(i.lastIndex),i.lastIndex>0&&(!i.multiline||i.multiline&&"\n"!==t[i.lastIndex-1])&&(s="(?: "+s+")",l=" "+l,u++),r=new RegExp("^(?:"+s+")",c)),$e&&(r=new RegExp("^"+s+"$(?!\\s)",c)),He&&(e=i.lastIndex),n=Ke.call(a?r:i,l),a?n?(n.input=n.input.slice(u),n[0]=n[0].slice(u),n.index=i.lastIndex,i.lastIndex+=n[0].length):i.lastIndex=0:He&&n&&(i.lastIndex=i.global?n.index+n[0].length:e),$e&&n&&n.length>1&&Ve.call(n[0],r,(function(){for(o=1;o<arguments.length-2;o++)void 0===arguments[o]&&(n[o]=void 0)})),n});var qe=We;Nt({target:"RegExp",proto:!0,forced:/./.exec!==qe},{exec:qe});var Xe=Ht("species"),Ze=!i((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),tr="$0"==="a".replace(/./,"$0"),er=Ht("replace"),rr=!!/./[er]&&""===/./[er]("a","$0"),nr=!i((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]})),or=function(t,e,r,n){var o=Ht(t),a=!i((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),c=a&&!i((function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[Xe]=function(){return r},r.flags="",r[o]=/./[o]),r.exec=function(){return e=!0,null},r[o](""),!e}));if(!a||!c||"replace"===t&&(!Ze||!tr||rr)||"split"===t&&!nr){var s=/./[o],u=r(o,""[t],(function(t,e,r,n,o){return e.exec===RegExp.prototype.exec?a&&!o?{done:!0,value:s.call(e,r,n)}:{done:!0,value:t.call(r,e,n)}:{done:!1}}),{REPLACE_KEEPS_$0:tr,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:rr}),l=u[0],f=u[1];rt(String.prototype,t,l),rt(RegExp.prototype,o,2==e?function(t,e){return f.call(t,this,e)}:function(t){return f.call(t,this)})}n&&A(RegExp.prototype[o],"sham",!0)},ir=function(t){return function(e,r){var n,o,i=String(v(e)),a=st(r),c=i.length;return a<0||a>=c?t?"":void 0:(n=i.charCodeAt(a))<55296||n>56319||a+1===c||(o=i.charCodeAt(a+1))<56320||o>57343?t?i.charAt(a):n:t?i.slice(a,a+2):o-56320+(n-55296<<10)+65536}},ar={codeAt:ir(!1),charAt:ir(!0)},cr=ar.charAt,sr=function(t,e,r){return e+(r?cr(t,e).length:1)},ur=function(t,e){var r=t.exec;if("function"==typeof r){var n=r.call(t,e);if("object"!=typeof n)throw TypeError("RegExp exec method returned something other than an Object or null");return n}if("RegExp"!==p(t))throw TypeError("RegExp#exec called on incompatible receiver");return qe.call(t,e)};or("match",1,(function(t,e,r){return[function(e){var r=v(this),n=null==e?void 0:e[t];return void 0!==n?n.call(e,r):new RegExp(e)[t](String(r))},function(t){var n=r(e,t,this);if(n.done)return n.value;var o=I(t),i=String(this);if(!o.global)return ur(o,i);var a=o.unicode;o.lastIndex=0;for(var c,s=[],u=0;null!==(c=ur(o,i));){var l=String(c[0]);s[u]=l,""===l&&(o.lastIndex=sr(i,lt(o.lastIndex),a)),u++}return 0===u?null:s}]}));var lr=[].slice,fr={},pr=function(t,e,r){if(!(e in fr)){for(var n=[],o=0;o<e;o++)n[o]="a["+o+"]";fr[e]=Function("C,a","return new C("+n.join(",")+")")}return fr[e](t,r)},hr=Function.bind||function(t){var e=Tt(this),r=lr.call(arguments,1),n=function(){var o=r.concat(lr.call(arguments));return this instanceof n?pr(e,o.length,o):e.apply(t,o)};return y(e.prototype)&&(n.prototype=e.prototype),n};Nt({target:"Function",proto:!0},{bind:hr}),Nt({target:"Object",stat:!0,forced:!a,sham:!a},{defineProperties:fe});var dr=!i((function(){return Object.isExtensible(Object.preventExtensions({}))})),vr=r((function(t){var e=P.f,r=z("meta"),n=0,o=Object.isExtensible||function(){return!0},i=function(t){e(t,r,{value:{objectID:"O"+ ++n,weakData:{}}})},a=t.exports={REQUIRED:!1,fastKey:function(t,e){if(!y(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!w(t,r)){if(!o(t))return"F";if(!e)return"E";i(t)}return t[r].objectID},getWeakData:function(t,e){if(!w(t,r)){if(!o(t))return!0;if(!e)return!1;i(t)}return t[r].weakData},onFreeze:function(t){return dr&&a.REQUIRED&&o(t)&&!w(t,r)&&i(t),t}};V[r]=!0})),gr=(vr.REQUIRED,vr.fastKey,vr.getWeakData,vr.onFreeze,vr.onFreeze),yr=Object.freeze,mr=i((function(){yr(1)}));Nt({target:"Object",stat:!0,forced:mr,sham:!dr},{freeze:function(t){return yr&&y(t)?yr(gr(t)):t}});var br=vt.indexOf,wr=[].indexOf,Sr=!!wr&&1/[1].indexOf(1,-0)<0,_r=ke("indexOf");Nt({target:"Array",proto:!0,forced:Sr||!_r},{indexOf:function(t){return Sr?wr.apply(this,arguments)||0:br(this,t,arguments.length>1?arguments[1]:void 0)}});var Er=j.f,Or=i((function(){Er(1)}));Nt({target:"Object",stat:!0,forced:!a||Or,sham:!a},{getOwnPropertyDescriptor:function(t,e){return Er(g(t),e)}}),Nt({target:"Object",stat:!0,forced:!a,sham:!a},{defineProperty:P.f});
/*!
	* @bridge/bytedance v2.3.0
	* (c) 2022 
	*/
/*! *****************************************************************************
	Copyright (c) Microsoft Corporation.

	Permission to use, copy, modify, and/or distribute this software for any
	purpose with or without fee is hereby granted.

	THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
	REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
	AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
	INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
	LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
	OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
	PERFORMANCE OF THIS SOFTWARE.
	***************************************************************************** */
var xr=function(t,e){return(xr=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)};function jr(t,e){function r(){this.constructor=t}xr(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}var Ir,kr=function(){return(kr=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};
/*!
	* @bridge/base v2.1.1
	* (c) 2022 
	*/!function(t){t[t.Failure=0]="Failure",t[t.Success=1]="Success",t[t.Unauthorized=-1]="Unauthorized",t[t.NotExist=-2]="NotExist"}(Ir||(Ir={}));var Pr="undefined"==typeof __PIA_WORKER__?1023:1023e5,Ar=function(){function t(t){this.version=t.version||"2.1.1",this.nativeMethodInvoker=t.nativeMethodInvoker,this.nativeEventListener=t.nativeEventListener,this.scheme=t.scheme||"nativeapp://",this.dispatchMessagePath=t.dispatchMessagePath||"dispatch_message/",this.setResultPath=t.setResultPath||"private/setresult/SCENE_FETCHQUEUE",this.dispatchMessageIFrameId=t.dispatchMessageIFrameId||"__JSBridgeIframe__",this.setResultIFrameId=t.setResultIFrameId||"__JSBridgeIframe_SetResult__",this.listenNativeEvent=!0===t.listenNativeEvent,this.callbackId=Pr,this.callbackMap={},this.eventMap={},this.javascriptMessageQueue=[],this.callbackProcessor=t.callbackProcessor}return t.prototype.setAppID=function(t){this.appID=t},t.prototype.call=function(t,e,r,n){void 0===n&&(n=this.version);var o,i=this.version,a=this.appID;if(t&&"string"==typeof t){"object"!=typeof e&&(e={}),"string"==typeof n?i=n||this.version:"object"==typeof n&&(o=n.namespace,i=n.sdkVersion||this.version,a=n.appID||this.appID);var c={func:t,params:e,JSSDK:i,__msg_type:"call",namespace:o,appID:a};if("function"==typeof r||void 0===r){var s=this.registerCallback(t,r);c.__callback_id=s}"undefined"==typeof __PIA_WORKER__&&window.parent!==window&&(c.__iframe_url=window.location.href),this.sendMessageToNative(c)}},t.prototype.on=function(t,e,r){if(void 0===r&&(r=!1),t&&"string"==typeof t&&"function"==typeof e){var n=this.registerCallback(t,e);return this.eventMap[t]=this.eventMap[t]||{},this.eventMap[t][n]={once:r},this.listenNativeEvent&&(this.nativeEventListener?this.nativeEventListener(t):this.call("addEventListener",{name:t},null,{sdkVersion:1})),n}},t.prototype.once=function(t,e){return this.on(t,e,!0)},t.prototype.off=function(t,e){if(!t||"string"!=typeof t)return!0;var r=this.eventMap[t];return!r||"object"!=typeof r||!r.hasOwnProperty(e)||(this.deregisterCallback(e),delete r[e],!0)},t.prototype.trigger=function(t,e){return this.handleMessageFromNative({__msg_type:"event",__params:e,__event_id:t})},t.prototype.handleMessageFromNative=function(t){var e=this,r=t,n=String(r.__callback_id);if(this.callbackProcessor&&"function"==typeof this.callbackProcessor){var o=(this.callbackMap&&this.callbackMap[n]||{}).method;this.callbackProcessor(r,o)}var i=r.__params,a=String(r.__event_id||""),c=r.__msg_type;this.callbackMap[n]?c="callback":this.eventMap[n]&&(c="event",a=a||n);var s={__err_code:"cb404"};switch(c){case"callback":var u=(this.callbackMap&&this.callbackMap[n]||{}).callback;"function"==typeof u&&(s=u(i)),this.deregisterCallback(n);break;case"event":var l=this.eventMap[a];l&&"object"==typeof l&&Object.keys(l).forEach((function(t){var r=(e.callbackMap&&e.callbackMap[t]||{}).callback,n=l[t];"function"==typeof r&&(s=r(i)),n&&n.once&&(e.deregisterCallback(t),delete l[t])}))}return s},t.prototype.fetchJavaScriptMessageQueue=function(){var t=JSON.stringify(this.javascriptMessageQueue),e=btoa(unescape(encodeURIComponent(t)));return this.setResultIFrame&&this.javascriptMessageQueue.length>0&&(this.setResultIFrame.src=""+this.scheme+this.setResultPath+"&"+e),this.javascriptMessageQueue.splice(0,this.javascriptMessageQueue.length),t},t.prototype.sendMessageToNative=function(t){if("1"!==String(t.JSSDK)&&this.nativeMethodInvoker){var e=this.nativeMethodInvoker(t);if(e){var r=JSON.parse(e);this.handleMessageFromNative(r)}}else this.javascriptMessageQueue.push(t),this.dispatchMessageIFrame||this.tryCreateIFrames(),this.dispatchMessageIFrame.src=""+this.scheme+this.dispatchMessagePath},t.prototype.registerCallback=function(t,e){var r=String(this.callbackId++);return this.callbackMap[r]={method:t,callback:e},r},t.prototype.deregisterCallback=function(t){delete this.callbackMap[t]},t.prototype.tryCreateIFrames=function(){this.dispatchMessageIFrame=this.createIFrame(this.dispatchMessageIFrameId),this.setResultIFrame=this.createIFrame(this.setResultIFrameId)},t.prototype.createIFrame=function(t){var e=document.getElementById(t);return e&&"IFRAME"===e.tagName||((e=document.createElement("iframe")).style.display="none",e.id=t,document.documentElement.appendChild(e)),e},t}(),Mr="undefined"!=typeof __PIA_WORKER__?new Function("return this")():"undefined"!=typeof window?window:{},Nr=void 0!==Mr&&Mr.navigator?Mr.navigator.userAgent:"",Tr=(!!Nr.match(/(newsarticle|videoarticle|lv|faceu|ulike|beauty_me_|faceu-os|ulike-os|beauty_me_oversea_|retouch)\/([\d.]+)/i)||/super|automobile/gi.test(Nr))&&!/webcast/gi.test(Nr)&&!/luckycatversion/gi.test(Nr),Lr=!!Nr.match(/(faceu)\/([\d.]+)/i)||/gsdk/gi.test(Nr)||/PIANativeWorker/gi.test(Nr),Rr=!!Nr.match(/ttad\/0/i),Cr=!!Nr.match(/aweme|trill|musical_ly|phoenix_\d+|TikTokNow_\d+|alligatortiktok/i),Fr=!!Nr.match(/live_stream/i),Jr=!!Nr.match(/Webcast/i),Br=!!Nr.match(/super/i),Dr=!!Nr.match(/life_service_merchant/i),Ur=/super/gi.test(Nr);function Gr(){var t;if(Tr)return Mr.JSBridge&&Mr.JSBridge.on?t=Mr.JSBridge.on:Mr.JS2NativeBridge&&Mr.JS2NativeBridge.on?t=function(t){var e={JSSDK:"2.3.0",__msg_type:"event",__callback_id:t,func:t};Mr.JS2NativeBridge.on(t,JSON.stringify(e))}:Mr.webkit&&Mr.webkit.messageHandlers&&Mr.webkit.messageHandlers.onMethodParams?t=function(t){var e={JSSDK:"2.3.0",__msg_type:"event",__callback_id:t,func:t};Mr.webkit.messageHandlers.onMethodParams.postMessage(e)}:Mr.onMethodParams&&(t=function(t){var e={JSSDK:"2.3.0",__msg_type:"event",__callback_id:t,func:t};return Mr.onMethodParams(t,e)}),t}function zr(t,e){if(("string"!=typeof e||!0!==/^(x|tc)\./.test(e))&&(Cr||Fr||Jr||Dr)){var r=t.__params;Mr.JS2NativeBridge&&Mr.JS2NativeBridge._invokeMethod&&(t.__params=kr({code:r.code},r.data))}}var Qr=function(t){function e(){var e=t.call(this,{version:"2.3.0",scheme:"bytedance://",listenNativeEvent:!0,dispatchMessageIFrameId:"__JSBridgeIframe_1.0__",setResultIFrameId:"__JSBridgeIframe_SetResult_1.0__",nativeEventListener:Gr(),callbackProcessor:zr})||this;return e.publicApi={call:e.call.bind(e),on:e.on.bind(e),once:e.once.bind(e),off:e.off.bind(e),trigger:e.trigger.bind(e),setAppID:e.setAppID.bind(e),_fetchQueue:e.fetchJavaScriptMessageQueue.bind(e),_handleMessageFromToutiao:e.handleMessageFromNative.bind(e)},e}return jr(e,t),e.prototype.exposePublicApiToGlobal=function(){Mr.ToutiaoJSBridge=Object.assign(Mr.ToutiaoJSBridge||{},this.publicApi)},e}(Ar),Kr=function(t){function e(e){var r,n=t.call(this,{version:"2.3.0",nativeMethodInvoker:(Mr.JS2NativeBridge&&Mr.JS2NativeBridge._invokeMethod?r=function(t){return Mr.JS2NativeBridge._invokeMethod(JSON.stringify(t))}:Mr.ToutiaoJSBridge&&Mr.ToutiaoJSBridge.invokeMethod?r=function(t){return Mr.ToutiaoJSBridge.invokeMethod(JSON.stringify(t))}:Mr.JS2NativeBridge&&Mr.JS2NativeBridge.call?r=function(t){return Mr.JS2NativeBridge.call(t.func,JSON.stringify(t))}:Mr.webkit&&Mr.webkit.messageHandlers&&Mr.webkit.messageHandlers.callMethodParams?r=function(t){Mr.webkit.messageHandlers.callMethodParams.postMessage(t)}:Mr.callMethodParams&&(r=function(t){return Mr.callMethodParams(t.func,t)}),r),nativeEventListener:Gr(),scheme:Br?"bds://":Ur?"bytedance://":Tr||Mr.JSBridge&&Mr.JSBridge._invokeMethod?"nativeapp://":"bytedance://",listenNativeEvent:Tr,callbackProcessor:zr})||this;return n.toutiaoLegacyJSB=e,n.publicApi={call:n.call.bind(n),on:n.on.bind(n),once:n.once.bind(n),off:n.off.bind(n),trigger:n.trigger.bind(n),setAppID:n.setAppID.bind(n),_fetchQueue:n.fetchJavaScriptMessageQueue.bind(n),_handleMessageFromApp:n.handleMessageFromNative.bind(n),_handleMessageFromToutiao:n.handleMessageFromNative.bind(n)},n}return jr(e,t),e.prototype.call=function(e,r,n,o){void 0===o&&(o="2.3.0"),this.isLegacyCall(e)?this.toutiaoLegacyJSB.call(e,r,n,o):t.prototype.call.call(this,e,r,n,o)},e.prototype.on=function(e,r,n,o){return void 0===n&&(n=!1),(o||{}).useLegacy||this.isLegacyCall(e)?this.toutiaoLegacyJSB.on(e,r,n):t.prototype.on.call(this,e,r,n)},e.prototype.once=function(e,r){return this.isLegacyCall(e)?this.toutiaoLegacyJSB.once(e,r):t.prototype.once.call(this,e,r)},e.prototype.off=function(e,r){return this.isLegacyCall(e)?this.toutiaoLegacyJSB.off(e,r):t.prototype.off.call(this,e,r)},e.prototype.trigger=function(e,r){return this.isLegacyCall(e)?this.toutiaoLegacyJSB.trigger(e,r):t.prototype.trigger.call(this,e,r)},e.prototype.setAppID=function(e){this.toutiaoLegacyJSB&&this.toutiaoLegacyJSB.setAppID(e),t.prototype.setAppID.call(this,e)},e.prototype.exposePublicApiToGlobal=function(){var t=this;Mr.JSBridge=Object.assign(Mr.JSBridge||{},this.publicApi),Mr.__DISABLE_JSB_PROTOCAL2__||(Mr.Native2JSBridge=Object.assign(Mr.Native2JSBridge||{},this.publicApi)),Ur?Mr.ToutiaoJSBridge=Object.assign(Mr.ToutiaoJSBridge||{},this.publicApi):(Tr||Rr)&&this.toutiaoLegacyJSB?this.toutiaoLegacyJSB.exposePublicApiToGlobal():Mr.ToutiaoJSBridge=Object.assign(Mr.ToutiaoJSBridge||{},this.publicApi),Mr.parent!==Mr&&Mr.addEventListener&&Mr.addEventListener("message",(function(e){e&&e.data&&e.data.__callback_id&&t.handleMessageFromNative(e.data)}),!1),Object.defineProperties(Mr,{JSBridge:{writable:!1},Native2JSBridge:{writable:!1},ToutiaoJSBridge:{writable:!1}}),Object.freeze(Mr.JSBridge),Object.freeze(Mr.Native2JSBridge),Object.freeze(Mr.ToutiaoJSBridge)},e.prototype.isLegacyCall=function(t){return!(!t||"string"!=typeof t||!this.toutiaoLegacyJSB)&&(!!Rr||!Lr&&!Ur&&(Tr&&t.indexOf(".")<0))},e}(Ar);function Vr(t,e){if(null==t)throw new TypeError("Cannot convert first argument to object");for(var r=Object(t),n=1;n<arguments.length;n++){var o=arguments[n];if(null!=o)for(var i=Object.keys(Object(o)),a=0,c=i.length;a<c;a++){var s=i[a],u=Object.getOwnPropertyDescriptor(o,s);void 0!==u&&u.enumerable&&(r[s]=o[s])}}return r}({assign:Vr,polyfill:function(){Object.assign||Object.defineProperty(Object,"assign",{enumerable:!1,configurable:!0,writable:!0,value:Vr})}}).polyfill();var Wr=new Kr(new Qr);try{console.warn("[Bridge] This version has Break Change, please pay attention to compatibility, more details: https://zjsms.com/JXKorkC/"),Wr.exposePublicApiToGlobal()}catch(jc){console.warn(jc.message)}var Hr=function(t,e,r){var n,o;return se&&"function"==typeof(n=e.constructor)&&n!==r&&y(o=n.prototype)&&o!==r.prototype&&se(t,o),t},Yr=Ht("match"),$r=function(t){var e;return y(t)&&(void 0!==(e=t[Yr])?!!e:"RegExp"==p(t))},qr=Ht("species"),Xr=function(t){var e=it(t),r=P.f;a&&e&&!e[qr]&&r(e,qr,{configurable:!0,get:function(){return this}})},Zr=P.f,tn=wt.f,en=et.set,rn=Ht("match"),nn=o.RegExp,on=nn.prototype,an=/a/g,cn=/a/g,sn=new nn(an)!==an,un=Qe.UNSUPPORTED_Y;if(a&&At("RegExp",!sn||un||i((function(){return cn[rn]=!1,nn(an)!=an||nn(cn)==cn||"/a/i"!=nn(an,"i")})))){for(var ln=function(t,e){var r,n=this instanceof ln,o=$r(t),i=void 0===e;if(!n&&o&&t.constructor===ln&&i)return t;sn?o&&!i&&(t=t.source):t instanceof ln&&(i&&(e=Ge.call(t)),t=t.source),un&&(r=!!e&&e.indexOf("y")>-1)&&(e=e.replace(/y/g,""));var a=Hr(sn?new nn(t,e):nn(t,e),n?this:on,ln);return un&&r&&en(a,{sticky:r}),a},fn=function(t){t in ln||Zr(ln,t,{configurable:!0,get:function(){return nn[t]},set:function(e){nn[t]=e}})},pn=tn(nn),hn=0;pn.length>hn;)fn(pn[hn++]);on.constructor=ln,ln.prototype=on,rt(o,"RegExp",ln)}Xr("RegExp");var dn=RegExp.prototype,vn=dn.toString,gn=i((function(){return"/a/b"!=vn.call({source:"a",flags:"b"})})),yn="toString"!=vn.name;(gn||yn)&&rt(RegExp.prototype,"toString",(function(){var t=I(this),e=String(t.source),r=t.flags;return"/"+e+"/"+String(void 0===r&&t instanceof RegExp&&!("flags"in dn)?Ge.call(t):r)}),{unsafe:!0});var mn=Ht("species"),bn=function(t,e){var r,n=I(t).constructor;return void 0===n||null==(r=I(n)[mn])?e:Tt(r)},wn=Qe.UNSUPPORTED_Y,Sn=[].push,_n=Math.min;or("split",2,(function(t,e,r){var n;return n="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,r){var n=String(v(this)),o=void 0===r?4294967295:r>>>0;if(0===o)return[];if(void 0===t)return[n];if(!$r(t))return e.call(n,t,o);for(var i,a,c,s=[],u=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),l=0,f=new RegExp(t.source,u+"g");(i=qe.call(f,n))&&!((a=f.lastIndex)>l&&(s.push(n.slice(l,i.index)),i.length>1&&i.index<n.length&&Sn.apply(s,i.slice(1)),c=i[0].length,l=a,s.length>=o));)f.lastIndex===i.index&&f.lastIndex++;return l===n.length?!c&&f.test("")||s.push(""):s.push(n.slice(l)),s.length>o?s.slice(0,o):s}:"0".split(void 0,0).length?function(t,r){return void 0===t&&0===r?[]:e.call(this,t,r)}:e,[function(e,r){var o=v(this),i=null==e?void 0:e[t];return void 0!==i?i.call(e,o,r):n.call(String(o),e,r)},function(t,o){var i=r(n,t,this,o,n!==e);if(i.done)return i.value;var a=I(t),c=String(this),s=bn(a,RegExp),u=a.unicode,l=(a.ignoreCase?"i":"")+(a.multiline?"m":"")+(a.unicode?"u":"")+(wn?"g":"y"),f=new s(wn?"^(?:"+a.source+")":a,l),p=void 0===o?4294967295:o>>>0;if(0===p)return[];if(0===c.length)return null===ur(f,c)?[c]:[];for(var h=0,d=0,v=[];d<c.length;){f.lastIndex=wn?0:d;var g,y=ur(f,wn?c.slice(d):c);if(null===y||(g=_n(lt(f.lastIndex+(wn?d:0)),c.length))===h)d=sr(c,d,u);else{if(v.push(c.slice(h,d)),v.length===p)return v;for(var m=1;m<=y.length-1;m++)if(v.push(y[m]),v.length===p)return v;d=h=g}}return v.push(c.slice(h)),v}]}),wn);var En=te?{}.toString:function(){return"[object "+ne(this)+"]"};te||rt(Object.prototype,"toString",En,{unsafe:!0});var On=o.Promise,xn=P.f,jn=Ht("toStringTag"),In=function(t,e,r){t&&!w(t=r?t:t.prototype,jn)&&xn(t,jn,{configurable:!0,value:e})},kn=Ht("iterator"),Pn=!1;try{var An=0,Mn={next:function(){return{done:!!An++}},return:function(){Pn=!0}};Mn[kn]=function(){return this},Array.from(Mn,(function(){throw 2}))}catch(jc){}var Nn,Tn,Ln,Rn=/(?:iphone|ipod|ipad).*applewebkit/i.test(Jt),Cn=o.location,Fn=o.setImmediate,Jn=o.clearImmediate,Bn=o.process,Dn=o.MessageChannel,Un=o.Dispatch,Gn=0,zn={},Qn=function(t){if(zn.hasOwnProperty(t)){var e=zn[t];delete zn[t],e()}},Kn=function(t){return function(){Qn(t)}},Vn=function(t){Qn(t.data)},Wn=function(t){o.postMessage(t+"",Cn.protocol+"//"+Cn.host)};Fn&&Jn||(Fn=function(t){for(var e=[],r=1;arguments.length>r;)e.push(arguments[r++]);return zn[++Gn]=function(){("function"==typeof t?t:Function(t)).apply(void 0,e)},Nn(Gn),Gn},Jn=function(t){delete zn[t]},Ft?Nn=function(t){Bn.nextTick(Kn(t))}:Un&&Un.now?Nn=function(t){Un.now(Kn(t))}:Dn&&!Rn?(Ln=(Tn=new Dn).port2,Tn.port1.onmessage=Vn,Nn=Xt(Ln.postMessage,Ln,1)):o.addEventListener&&"function"==typeof postMessage&&!o.importScripts&&Cn&&"file:"!==Cn.protocol&&!i(Wn)?(Nn=Wn,o.addEventListener("message",Vn,!1)):Nn="onreadystatechange"in E("script")?function(t){pe.appendChild(E("script")).onreadystatechange=function(){pe.removeChild(this),Qn(t)}}:function(t){setTimeout(Kn(t),0)});var Hn,Yn,$n,qn,Xn,Zn,to,eo,ro={set:Fn,clear:Jn},no=/web0s(?!.*chrome)/i.test(Jt),oo=j.f,io=ro.set,ao=o.MutationObserver||o.WebKitMutationObserver,co=o.document,so=o.process,uo=o.Promise,lo=oo(o,"queueMicrotask"),fo=lo&&lo.value;fo||(Hn=function(){var t,e;for(Ft&&(t=so.domain)&&t.exit();Yn;){e=Yn.fn,Yn=Yn.next;try{e()}catch(t){throw Yn?qn():$n=void 0,t}}$n=void 0,t&&t.enter()},Rn||Ft||no||!ao||!co?uo&&uo.resolve?(to=uo.resolve(void 0),eo=to.then,qn=function(){eo.call(to,Hn)}):qn=Ft?function(){so.nextTick(Hn)}:function(){io.call(o,Hn)}:(Xn=!0,Zn=co.createTextNode(""),new ao(Hn).observe(Zn,{characterData:!0}),qn=function(){Zn.data=Xn=!Xn}));var po,ho,vo,go,yo=fo||function(t){var e={fn:t,next:void 0};$n&&($n.next=e),Yn||(Yn=e,qn()),$n=e},mo=function(t,e){if(I(t),y(e)&&e.constructor===t)return e;var r=Rt.f(t);return(0,r.resolve)(e),r.promise},bo=ro.set,wo=Ht("species"),So="Promise",_o=et.get,Eo=et.set,Oo=et.getterFor(So),xo=On,jo=o.TypeError,Io=o.document,ko=o.process,Po=it("fetch"),Ao=Rt.f,Mo=Ao,No=!!(Io&&Io.createEvent&&o.dispatchEvent),To="function"==typeof PromiseRejectionEvent,Lo=At(So,(function(){if(!(F(xo)!==String(xo))){if(66===Gt)return!0;if(!Ft&&!To)return!0}if(Gt>=51&&/native code/.test(xo))return!1;var t=xo.resolve(1),e=function(t){t((function(){}),(function(){}))};return(t.constructor={})[wo]=e,!(t.then((function(){}))instanceof e)})),Ro=Lo||!function(t,e){if(!e&&!Pn)return!1;var r=!1;try{var n={};n[kn]=function(){return{next:function(){return{done:r=!0}}}},t(n)}catch(t){}return r}((function(t){xo.all(t).catch((function(){}))})),Co=function(t){var e;return!(!y(t)||"function"!=typeof(e=t.then))&&e},Fo=function(t,e){if(!t.notified){t.notified=!0;var r=t.reactions;yo((function(){for(var n=t.value,o=1==t.state,i=0;r.length>i;){var a,c,s,u=r[i++],l=o?u.ok:u.fail,f=u.resolve,p=u.reject,h=u.domain;try{l?(o||(2===t.rejection&&Uo(t),t.rejection=1),!0===l?a=n:(h&&h.enter(),a=l(n),h&&(h.exit(),s=!0)),a===u.promise?p(jo("Promise-chain cycle")):(c=Co(a))?c.call(a,f,p):f(a)):p(n)}catch(t){h&&!s&&h.exit(),p(t)}}t.reactions=[],t.notified=!1,e&&!t.rejection&&Bo(t)}))}},Jo=function(t,e,r){var n,i;No?((n=Io.createEvent("Event")).promise=e,n.reason=r,n.initEvent(t,!1,!0),o.dispatchEvent(n)):n={promise:e,reason:r},!To&&(i=o["on"+t])?i(n):"unhandledrejection"===t&&function(t,e){var r=o.console;r&&r.error&&(1===arguments.length?r.error(t):r.error(t,e))}("Unhandled promise rejection",r)},Bo=function(t){bo.call(o,(function(){var e,r=t.facade,n=t.value;if(Do(t)&&(e=Ct((function(){Ft?ko.emit("unhandledRejection",n,r):Jo("unhandledrejection",r,n)})),t.rejection=Ft||Do(t)?2:1,e.error))throw e.value}))},Do=function(t){return 1!==t.rejection&&!t.parent},Uo=function(t){bo.call(o,(function(){var e=t.facade;Ft?ko.emit("rejectionHandled",e):Jo("rejectionhandled",e,t.value)}))},Go=function(t,e,r){return function(n){t(e,n,r)}},zo=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,Fo(t,!0))},Qo=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw jo("Promise can't be resolved itself");var n=Co(e);n?yo((function(){var r={done:!1};try{n.call(e,Go(Qo,r,t),Go(zo,r,t))}catch(e){zo(r,e,t)}})):(t.value=e,t.state=1,Fo(t,!1))}catch(e){zo({done:!1},e,t)}}};Lo&&(xo=function(t){!function(t,e,r){if(!(t instanceof e))throw TypeError("Incorrect "+(r?r+" ":"")+"invocation")}(this,xo,So),Tt(t),po.call(this);var e=_o(this);try{t(Go(Qo,e),Go(zo,e))}catch(t){zo(e,t)}},(po=function(t){Eo(this,{type:So,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=function(t,e,r){for(var n in e)rt(t,n,e[n],r);return t}(xo.prototype,{then:function(t,e){var r=Oo(this),n=Ao(bn(this,xo));return n.ok="function"!=typeof t||t,n.fail="function"==typeof e&&e,n.domain=Ft?ko.domain:void 0,r.parent=!0,r.reactions.push(n),0!=r.state&&Fo(r,!1),n.promise},catch:function(t){return this.then(void 0,t)}}),ho=function(){var t=new po,e=_o(t);this.promise=t,this.resolve=Go(Qo,e),this.reject=Go(zo,e)},Rt.f=Ao=function(t){return t===xo||t===vo?new ho(t):Mo(t)},"function"==typeof On&&(go=On.prototype.then,rt(On.prototype,"then",(function(t,e){var r=this;return new xo((function(t,e){go.call(r,t,e)})).then(t,e)}),{unsafe:!0}),"function"==typeof Po&&Nt({global:!0,enumerable:!0,forced:!0},{fetch:function(t){return mo(xo,Po.apply(o,arguments))}}))),Nt({global:!0,wrap:!0,forced:Lo},{Promise:xo}),In(xo,So,!1),Xr(So),vo=it(So),Nt({target:So,stat:!0,forced:Lo},{reject:function(t){var e=Ao(this);return e.reject.call(void 0,t),e.promise}}),Nt({target:So,stat:!0,forced:Lo},{resolve:function(t){return mo(this,t)}}),Nt({target:So,stat:!0,forced:Ro},{all:function(t){var e=this,r=Ao(e),n=r.resolve,o=r.reject,i=Ct((function(){var r=Tt(e.resolve),i=[],a=0,c=1;ce(t,(function(t){var s=a++,u=!1;i.push(void 0),c++,r.call(e,t).then((function(t){u||(u=!0,i[s]=t,--c||n(i))}),o)})),--c||n(i)}));return i.error&&o(i.value),r.promise},race:function(t){var e=this,r=Ao(e),n=r.reject,o=Ct((function(){var o=Tt(e.resolve);ce(t,(function(t){o.call(e,t).then(r.resolve,n)}))}));return o.error&&n(o.value),r.promise}});var Ko,Vo=r((function(t){function e(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){return void r(t)}c.done?e(s):Promise.resolve(s).then(n,o)}t.exports=function(t){return function(){var r=this,n=arguments;return new Promise((function(o,i){var a=t.apply(r,n);function c(t){e(a,o,i,c,s,"next",t)}function s(t){e(a,o,i,c,s,"throw",t)}c(void 0)}))}},t.exports.default=t.exports,t.exports.__esModule=!0})),Wo=(Ko=Vo)&&Ko.__esModule&&Object.prototype.hasOwnProperty.call(Ko,"default")?Ko.default:Ko,Ho=wt.f,Yo={}.toString,$o="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],qo={f:function(t){return $o&&"[object Window]"==Yo.call(t)?function(t){try{return Ho(t)}catch(t){return $o.slice()}}(t):Ho(g(t))}},Xo={f:Ht},Zo=P.f,ti=function(t){var e=nt.Symbol||(nt.Symbol={});w(e,t)||Zo(e,t,{value:Xo.f(t)})},ei=Ie.forEach,ri=K("hidden"),ni=Ht("toPrimitive"),oi=et.set,ii=et.getterFor("Symbol"),ai=Object.prototype,ci=o.Symbol,si=it("JSON","stringify"),ui=j.f,li=P.f,fi=qo.f,pi=u.f,hi=D("symbols"),di=D("op-symbols"),vi=D("string-to-symbol-registry"),gi=D("symbol-to-string-registry"),yi=D("wks"),mi=o.QObject,bi=!mi||!mi.prototype||!mi.prototype.findChild,wi=a&&i((function(){return 7!=ye(li({},"a",{get:function(){return li(this,"a",{value:7}).a}})).a}))?function(t,e,r){var n=ui(ai,e);n&&delete ai[e],li(t,e,r),n&&t!==ai&&li(ai,e,n)}:li,Si=function(t,e){var r=hi[t]=ye(ci.prototype);return oi(r,{type:"Symbol",tag:t,description:e}),a||(r.description=e),r},_i=Qt?function(t){return"symbol"==typeof t}:function(t){return Object(t)instanceof ci},Ei=function(t,e,r){t===ai&&Ei(di,e,r),I(t);var n=m(e,!0);return I(r),w(hi,n)?(r.enumerable?(w(t,ri)&&t[ri][n]&&(t[ri][n]=!1),r=ye(r,{enumerable:l(0,!1)})):(w(t,ri)||li(t,ri,l(1,{})),t[ri][n]=!0),wi(t,n,r)):li(t,n,r)},Oi=function(t,e){I(t);var r=g(e),n=le(r).concat(ki(r));return ei(n,(function(e){a&&!xi.call(r,e)||Ei(t,e,r[e])})),t},xi=function(t){var e=m(t,!0),r=pi.call(this,e);return!(this===ai&&w(hi,e)&&!w(di,e))&&(!(r||!w(this,e)||!w(hi,e)||w(this,ri)&&this[ri][e])||r)},ji=function(t,e){var r=g(t),n=m(e,!0);if(r!==ai||!w(hi,n)||w(di,n)){var o=ui(r,n);return!o||!w(hi,n)||w(r,ri)&&r[ri][n]||(o.enumerable=!0),o}},Ii=function(t){var e=fi(g(t)),r=[];return ei(e,(function(t){w(hi,t)||w(V,t)||r.push(t)})),r},ki=function(t){var e=t===ai,r=fi(e?di:g(t)),n=[];return ei(r,(function(t){!w(hi,t)||e&&!w(ai,t)||n.push(hi[t])})),n};if(zt||(rt((ci=function(){if(this instanceof ci)throw TypeError("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?String(arguments[0]):void 0,e=z(t),r=function(t){this===ai&&r.call(di,t),w(this,ri)&&w(this[ri],e)&&(this[ri][e]=!1),wi(this,e,l(1,t))};return a&&bi&&wi(ai,e,{configurable:!0,set:r}),Si(e,t)}).prototype,"toString",(function(){return ii(this).tag})),rt(ci,"withoutSetter",(function(t){return Si(z(t),t)})),u.f=xi,P.f=Ei,j.f=ji,wt.f=qo.f=Ii,St.f=ki,Xo.f=function(t){return Si(Ht(t),t)},a&&(li(ci.prototype,"description",{configurable:!0,get:function(){return ii(this).description}}),rt(ai,"propertyIsEnumerable",xi,{unsafe:!0}))),Nt({global:!0,wrap:!0,forced:!zt,sham:!zt},{Symbol:ci}),ei(le(yi),(function(t){ti(t)})),Nt({target:"Symbol",stat:!0,forced:!zt},{for:function(t){var e=String(t);if(w(vi,e))return vi[e];var r=ci(e);return vi[e]=r,gi[r]=e,r},keyFor:function(t){if(!_i(t))throw TypeError(t+" is not a symbol");if(w(gi,t))return gi[t]},useSetter:function(){bi=!0},useSimple:function(){bi=!1}}),Nt({target:"Object",stat:!0,forced:!zt,sham:!a},{create:function(t,e){return void 0===e?ye(t):Oi(ye(t),e)},defineProperty:Ei,defineProperties:Oi,getOwnPropertyDescriptor:ji}),Nt({target:"Object",stat:!0,forced:!zt},{getOwnPropertyNames:Ii,getOwnPropertySymbols:ki}),Nt({target:"Object",stat:!0,forced:i((function(){St.f(1)}))},{getOwnPropertySymbols:function(t){return St.f(me(t))}}),si){var Pi=!zt||i((function(){var t=ci();return"[null]"!=si([t])||"{}"!=si({a:t})||"{}"!=si(Object(t))}));Nt({target:"JSON",stat:!0,forced:Pi},{stringify:function(t,e,r){for(var n,o=[t],i=1;arguments.length>i;)o.push(arguments[i++]);if(n=e,(y(e)||void 0!==t)&&!_i(t))return _e(e)||(e=function(t,e){if("function"==typeof n&&(e=n.call(this,t,e)),!_i(e))return e}),o[1]=e,si.apply(null,o)}})}ci.prototype[ni]||A(ci.prototype,ni,ci.prototype.valueOf),In(ci,"Symbol"),V[ri]=!0;var Ai=P.f,Mi=o.Symbol;if(a&&"function"==typeof Mi&&(!("description"in Mi.prototype)||void 0!==Mi().description)){var Ni={},Ti=function(){var t=arguments.length<1||void 0===arguments[0]?void 0:String(arguments[0]),e=this instanceof Ti?new Mi(t):void 0===t?Mi():Mi(t);return""===t&&(Ni[e]=!0),e};Et(Ti,Mi);var Li=Ti.prototype=Mi.prototype;Li.constructor=Ti;var Ri=Li.toString,Ci="Symbol(test)"==String(Mi("test")),Fi=/^Symbol\((.*)\)[^)]+$/;Ai(Li,"description",{configurable:!0,get:function(){var t=y(this)?this.valueOf():this,e=Ri.call(t);if(w(Ni,t))return"";var r=Ci?e.slice(7,-1):e.replace(Fi,"$1");return""===r?void 0:r}}),Nt({global:!0,forced:!0},{Symbol:Ti})}ti("iterator");var Ji=Ht("unscopables"),Bi=Array.prototype;null==Bi[Ji]&&P.f(Bi,Ji,{configurable:!0,value:ye(null)});var Di,Ui,Gi,zi=function(t){Bi[Ji][t]=!0},Qi=!i((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),Ki=K("IE_PROTO"),Vi=Object.prototype,Wi=Qi?Object.getPrototypeOf:function(t){return t=me(t),w(t,Ki)?t[Ki]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?Vi:null},Hi=Ht("iterator"),Yi=!1;[].keys&&("next"in(Gi=[].keys())?(Ui=Wi(Wi(Gi)))!==Object.prototype&&(Di=Ui):Yi=!0),(null==Di||i((function(){var t={};return Di[Hi].call(t)!==t})))&&(Di={}),w(Di,Hi)||A(Di,Hi,(function(){return this}));var $i={IteratorPrototype:Di,BUGGY_SAFARI_ITERATORS:Yi},qi=$i.IteratorPrototype,Xi=function(){return this},Zi=$i.IteratorPrototype,ta=$i.BUGGY_SAFARI_ITERATORS,ea=Ht("iterator"),ra=function(){return this},na=function(t,e,r,n,o,i,a){!function(t,e,r){var n=e+" Iterator";t.prototype=ye(qi,{next:l(1,r)}),In(t,n,!1),Yt[n]=Xi}(r,e,n);var c,s,u,f=function(t){if(t===o&&g)return g;if(!ta&&t in d)return d[t];switch(t){case"keys":case"values":case"entries":return function(){return new r(this,t)}}return function(){return new r(this)}},p=e+" Iterator",h=!1,d=t.prototype,v=d[ea]||d["@@iterator"]||o&&d[o],g=!ta&&v||f(o),y="Array"==e&&d.entries||v;if(y&&(c=Wi(y.call(new t)),Zi!==Object.prototype&&c.next&&(Wi(c)!==Zi&&(se?se(c,Zi):"function"!=typeof c[ea]&&A(c,ea,ra)),In(c,p,!0))),"values"==o&&v&&"values"!==v.name&&(h=!0,g=function(){return v.call(this)}),d[ea]!==g&&A(d,ea,g),Yt[e]=g,o)if(s={values:f("values"),keys:i?g:f("keys"),entries:f("entries")},a)for(u in s)(ta||h||!(u in d))&&rt(d,u,s[u]);else Nt({target:e,proto:!0,forced:ta||h},s);return s},oa=et.set,ia=et.getterFor("Array Iterator"),aa=na(Array,"Array",(function(t,e){oa(this,{type:"Array Iterator",target:g(t),index:0,kind:e})}),(function(){var t=ia(this),e=t.target,r=t.kind,n=t.index++;return!e||n>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==r?{value:n,done:!1}:"values"==r?{value:e[n],done:!1}:{value:[n,e[n]],done:!1}}),"values");Yt.Arguments=Yt.Array,zi("keys"),zi("values"),zi("entries");var ca=ar.charAt,sa=et.set,ua=et.getterFor("String Iterator");na(String,"String",(function(t){sa(this,{type:"String Iterator",string:String(t),index:0})}),(function(){var t,e=ua(this),r=e.string,n=e.index;return n>=r.length?{value:void 0,done:!0}:(t=ca(r,n),e.index+=t.length,{value:t,done:!1})}));var la=Ht("iterator"),fa=Ht("toStringTag"),pa=aa.values;for(var ha in Me){var da=o[ha],va=da&&da.prototype;if(va){if(va[la]!==pa)try{A(va,la,pa)}catch(jc){va[la]=pa}if(va[fa]||A(va,fa,ha),Me[ha])for(var ga in aa)if(va[ga]!==aa[ga])try{A(va,ga,aa[ga])}catch(jc){va[ga]=aa[ga]}}}ti("asyncIterator"),ti("toStringTag"),In(o.JSON,"JSON",!0),In(Math,"Math",!0);var ya=i((function(){Wi(1)}));Nt({target:"Object",stat:!0,forced:ya,sham:!Qi},{getPrototypeOf:function(t){return Wi(me(t))}});var ma=P.f,ba=Function.prototype,wa=ba.toString,Sa=/^\s*function ([^ (]*)/;a&&!("name"in ba)&&ma(ba,"name",{configurable:!0,get:function(){try{return wa.call(this).match(Sa)[1]}catch(t){return""}}});var _a=[].reverse,Ea=[1,2];Nt({target:"Array",proto:!0,forced:String(Ea)===String(Ea.reverse())},{reverse:function(){return _e(this)&&(this.length=this.length),_a.call(this)}});var Oa=Je("slice"),xa=Ht("species"),ja=[].slice,Ia=Math.max;Nt({target:"Array",proto:!0,forced:!Oa},{slice:function(t,e){var r,n,o,i=g(this),a=lt(i.length),c=ht(t,a),s=ht(void 0===e?a:e,a);if(_e(i)&&("function"!=typeof(r=i.constructor)||r!==Array&&!_e(r.prototype)?y(r)&&null===(r=r[xa])&&(r=void 0):r=void 0,r===Array||void 0===r))return ja.call(i,c,s);for(n=new(void 0===r?Array:r)(Ia(s-c,0)),o=0;c<s;c++,o++)c in i&&Ce(n,o,i[c]);return n.length=o,n}}),Nt({global:!0},{globalThis:o});var ka=r((function(t){var e=function(t){var e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i=e&&e.prototype instanceof p?e:p,a=Object.create(i.prototype),c=new x(o||[]);return n(a,"_invoke",{value:S(t,r,c)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var f={};function p(){}function h(){}function d(){}var v={};s(v,i,(function(){return this}));var g=Object.getPrototypeOf,y=g&&g(g(j([])));y&&y!==e&&r.call(y,i)&&(v=y);var m=d.prototype=p.prototype=Object.create(v);function b(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e((function(o,a){!function n(o,i,a,c){var s=l(t[o],t,i);if("throw"!==s.type){var u=s.arg,f=u.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(f).then((function(t){u.value=t,a(u)}),(function(t){return n("throw",t,a,c)}))}c(s.arg)}(n,i,o,a)}))}return o=o?o.then(a,a):a()}})}function S(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return I()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var c=_(a,r);if(c){if(c===f)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=l(t,e,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===f)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(void 0===n)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=void 0,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;var o=l(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,f;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,f):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function x(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function j(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:I}}function I(){return{value:void 0,done:!0}}return h.prototype=d,n(m,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object.create(m),t},t.awrap=function(t){return{__await:t}},b(w.prototype),s(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(m),s(m,c,"Generator"),s(m,i,(function(){return this})),s(m,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=j,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(O),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),O(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;O(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:j(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},t}(t.exports);try{regeneratorRuntime=e}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=e:Function("r","regeneratorRuntime = r")(e)}})),Pa=!!On&&i((function(){On.prototype.finally.call({then:function(){}},(function(){}))}));Nt({target:"Promise",proto:!0,real:!0,forced:Pa},{finally:function(t){var e=bn(this,it("Promise")),r="function"==typeof t;return this.then(r?function(r){return mo(e,t()).then((function(){return r}))}:t,r?function(r){return mo(e,t()).then((function(){throw r}))}:t)}}),"function"!=typeof On||On.prototype.finally||rt(On.prototype,"finally",it("Promise").prototype.finally);var Aa=Ie.find,Ma=!0;"find"in[]&&Array(1).find((function(){Ma=!1})),Nt({target:"Array",proto:!0,forced:Ma},{find:function(t){return Aa(this,t,arguments.length>1?arguments[1]:void 0)}}),zi("find");var Na=[],Ta=Na.sort,La=i((function(){Na.sort(void 0)})),Ra=i((function(){Na.sort(null)})),Ca=ke("sort");Nt({target:"Array",proto:!0,forced:La||!Ra||!Ca},{sort:function(t){return void 0===t?Ta.call(me(this)):Ta.call(me(this),Tt(t))}});var Fa=Ie.map,Ja=Je("map");Nt({target:"Array",proto:!0,forced:!Ja},{map:function(t){return Fa(this,t,arguments.length>1?arguments[1]:void 0)}});var Ba=Date.prototype,Da=Ba.toString,Ua=Ba.getTime;function Ga(t,e){return t}new Date(NaN)+""!="Invalid Date"&&rt(Ba,"toString",(function(){var t=Ua.call(this);return t==t?Da.call(this):"Invalid Date"}));var za="[\t\n\v\f\r                　\u2028\u2029\ufeff]",Qa=RegExp("^"+za+za+"*"),Ka=RegExp(za+za+"*$"),Va=function(t){return function(e){var r=String(v(e));return 1&t&&(r=r.replace(Qa,"")),2&t&&(r=r.replace(Ka,"")),r}},Wa={start:Va(1),end:Va(2),trim:Va(3)},Ha=wt.f,Ya=j.f,$a=P.f,qa=Wa.trim,Xa=o.Number,Za=Xa.prototype,tc="Number"==p(ye(Za)),ec=function(t){var e,r,n,o,i,a,c,s,u=m(t,!1);if("string"==typeof u&&u.length>2)if(43===(e=(u=qa(u)).charCodeAt(0))||45===e){if(88===(r=u.charCodeAt(2))||120===r)return NaN}else if(48===e){switch(u.charCodeAt(1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+u}for(a=(i=u.slice(2)).length,c=0;c<a;c++)if((s=i.charCodeAt(c))<48||s>o)return NaN;return parseInt(i,n)}return+u};if(At("Number",!Xa(" 0o1")||!Xa("0b1")||Xa("+0x1"))){for(var rc,nc=function(t){var e=arguments.length<1?0:t,r=this;return r instanceof nc&&(tc?i((function(){Za.valueOf.call(r)})):"Number"!=p(r))?Hr(new Xa(ec(e)),r,nc):ec(e)},oc=a?Ha(Xa):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger,fromString,range".split(","),ic=0;oc.length>ic;ic++)w(Xa,rc=oc[ic])&&!w(nc,rc)&&$a(nc,rc,Ya(Xa,rc));nc.prototype=Za,Za.constructor=nc,rt(o,"Number",nc)}var ac=Ie.filter,cc=Je("filter");Nt({target:"Array",proto:!0,forced:!cc},{filter:function(t){return ac(this,t,arguments.length>1?arguments[1]:void 0)}});var sc=Ie.some,uc=ke("some");Nt({target:"Array",proto:!0,forced:!uc},{some:function(t){return sc(this,t,arguments.length>1?arguments[1]:void 0)}}),Nt({target:"Array",stat:!0},{isArray:_e});var lc={info:function(t){console.info(t)},warn:function(t){console.warn(t)},log:function(t){console.log(t)}},fc=Object.prototype,pc=fc.hasOwnProperty,hc=fc.toString,dc={hasOwnProp:function(t,e){return!!this.isObject(t)&&pc.call(t,e)},isEmpty:function(t){return null==t||0===this.getProLen(t)},getType:function(t){return hc.call(t).match(/\[object (\w+)\]/)[1]},getProLen:function(t){var e=0;return this.isObject(t)&&(e=Object.keys(t).length),e},verCompare:function(t,e){if(t===e)return 0;for(var r=t.toString().split("."),n=e.toString().split(".");r.length<n.length;)r.push("0");for(;n.length<r.length;)n.push("0");for(var o=0;o<r.length;){var i=Number(r[o]),a=Number(n[o]);if(a>i)return-1;if(a<i)return 1;o+=1}return 0},warn:function(t){lc.warn("[via]: "+t)},error:function(t){lc.warn("[via]: "+t)}};function vc(t,e){return void 0===e||"*"===e||String(e).split("|").map((function(t){var e=t.split("-"),r="";switch(e.length){case 1:r=e[0];break;case 2:r={min:e[0],max:e[1]}}return r})).filter((function(t){return t})).some((function(e){if(dc.isString(e))return e===t;if(dc.isObject(e)){var r=e.min,n=e.max,o=!0;return r&&(o=o&&-1!==dc.verCompare(t,r)),n&&(o=o&&1!==dc.verCompare(t,n)),o}return!1}))}function gc(t,e,r){if(Array.isArray(e))return e.some((function(e){return gc(t,e,r)}));var n=function(t){var e=t.appid,r=t.appId,n=t.container,o=t.os,i=t.version,a=t.sdkId,c=t.sdkVersion;return{s_aid:Number(e)||Number(r),s_sid:a,s_container:n,s_os:o,s_version:i,s_sversion:c}}(t),o=n.s_aid,i=n.s_sid,a=n.s_os,c=n.s_container,s=n.s_version,u=n.s_sversion,l=function(t){var e=t.appid,r=t.container,n=t.os,o=t.version,i=t.hostId;return{t_hid:e||i,t_container:r,t_os:n,t_version:o}}(e),f=l.t_hid,p=l.t_os,h=l.t_container,d=l.t_version;if("sdk"===r&&!i)return!1;if("sdk"===r){if(f&&"*"!==f&&f!==i)return!1}else if(f&&"*"!==f&&f!==o)return!1;if("*"!==h&&h!==c)return!1;if("*"!==p&&p!==a)return!1;if("sdk"===r){if(d&&"*"!==d&&!vc(u,d))return!1}else if(d&&"*"!==d&&!vc(s,d))return!1;return!0}["Number","Date","Object","String","Function","Boolean","Null","Undefined","Array"].forEach((function(t){dc["is"+t]=function(e){return hc.call(e)==="[object "+t+"]"}}));var yc="undefined"!=typeof window,mc=function(){function t(t){this.bridge=t.bridge,this.container=t.container||"web",this.os=t.os,this.options={},this.env=null,this.context=null,this.monitor=t.monitor,this.onInvokeStart=null,this.onInvokeEnd=null,this.logDisabled=!1}var e=t.prototype;return e.init=function(t){var e=this,r=t.getRuntimeEnv;this.getEnv=new Promise((function(t,n){r({jsb:e.bridge,bridge:e.bridge,container:e.container,os:e.os},(function(r,o){r?n(r):t(Object.assign({container:e.container,os:e.os},o))}))})).finally((function(){}))},e.on=function(t,e){var r=this;"lynx"===this.container&&(this.bridge.on=this.bridge.on.bind(this.context));var n=this.bridge.on(t,(function(t){e.length<2?e(t):e(null,t)}));return{remove:function(){return r.bridge.off(t,n)}}},e.getRuleForMethod=function(t,e){return void 0===e&&(e=[]),e.sort((function(t,e){return"sdk"===t.type?-1:1})).find((function(e){return gc(t,e.target,e.type)}))},e.transformConfig=function(){var t=Wo(ka.mark((function t(e,r,n,o){var i,a,c,s,u,l;return ka.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===o&&(o="call"),t.t0=this.env,t.t0){t.next=6;break}return t.next=5,this.getEnv;case 5:t.t0=t.sent;case 6:return i=t.t0,a=this.getRuleForMethod(i,n)||{},c=a.map&&a.map.method||e,a.map&&a.map.module&&"on"!==o?c=a.map:a.map&&a.map.method&&(c=a.map.method),s=a.preprocess?a.preprocess(r,{env:i,bridge:this.bridge,logger:lc}):r,!1===this.logDisabled&&(u="[Queqiao Call Log: "+e+"]",l={hitRule:a.map||a.interceptor?a:'Rule not found, please check value of "appId, appVersion" in the parameter "env" or hook "getRuntimeEnv" in the Queqiao platform; ',rules:n,env:i,sdkInfo:{method:e,params:r},jsbInfo:a.map?{method:c,params:s}:null,type:o},a.map||a.interceptor?console.log(u,l):console.warn(u,l)),t.abrupt("return",{realMethod:c,realParams:s,rule:a,env:i});case 13:case"end":return t.stop()}}),t,this)})));return function(e,r,n,o){return t.apply(this,arguments)}}(),e.addInternalBridge=function(t){for(var e in t)this[e]=t[e]},e.globalConfig=function(t){var e=this;void 0===t&&(t={});var r=t,n=r.env,o=r.context,i=r.monitor,a=r.onInvokeStart,c=r.onInvokeEnd,s=r.disableLog,u=r.options;this.logDisabled=!!s;var l={container:this.container,os:this.os};"lynx"===this.container&&o&&(this.bridge.on=this.bridge.on.bind(o),this.bridge.off=this.bridge.off.bind(o)),n&&(this.env=Object.assign({},l,n));var f={context:o,monitor:i,onInvokeStart:a,onInvokeEnd:c,options:u};Object.keys(f).forEach((function(r){void 0!==t[r]&&(e[r]=t[r])}))},e.flattenResponse=function(t,e){return"ios"===e.os&&"web"===e.container&&window.JS2NativeBridge&&window.JS2NativeBridge._invokeMethod?Object.assign({code:t.code},t.data):t},e.caniuse=function(t,e){return!!this.getRuleForMethod(Object.assign({container:this.container,os:this.os},e),t.rules)},e.pipeCall=function(){var t=Wo(ka.mark((function t(e,r){var n,o,i,a,c,s,u,l,f,p,h,d,v,g=this;return ka.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e.method,o=e.params,i=e.callback,a=e.rules,c=e.options,void 0===r&&(r=!0),yc||"web"!==this.container){t.next=4;break}return t.abrupt("return",Promise.resolve());case 4:return s={method:n,params:o},this.onInvokeStart&&(s=this.onInvokeStart(s)),t.next=8,this.transformConfig(n,o,a);case 8:if(u=t.sent,l=u.realMethod,f=u.realParams,p=u.rule,h=u.env,d=p.interceptor,v=(new Date).getTime(),!d||"function"!=typeof d){t.next=17;break}return t.abrupt("return",new Promise((function(t){d(Object.assign({params:o,bridge:g.bridge},h),(function(e){t(e),"function"==typeof i&&i(e)}))})));case 17:if(!r){t.next=21;break}return t.abrupt("return",new Promise((function(t,e){g.bridge.call(l,f,(function(r){var c=r;try{Ga&&(c=Ga(c)),p.postprocess&&"function"==typeof p.postprocess&&(c=p.postprocess(c,{method:n,params:o,env:h,logger:lc,type:"call"}))}catch(t){e(t)}"function"==typeof i&&(i.length<2?i(c):i(null,c)),t(c),g.onInvokeEnd&&g.onInvokeEnd({response:c,config:s}),"function"==typeof g.monitor&&g.monitor(Object.assign({bridge:g.bridge},h),{method:n,realMethod:l,params:o,realParams:f,res:c,realRes:r,rules:a,rule:p,startTimestamp:v})}),Object.assign(g.options,c))})));case 21:return t.abrupt("return",this.bridge.call(l,f,(function(t){p.postprocess&&"function"==typeof p.postprocess&&p.postprocess(t,{params:o,env:h,logger:lc,type:"call"})}),Object.assign(this.options,c)));case 22:case"end":return t.stop()}}),t,this)})));return function(e,r){return t.apply(this,arguments)}}(),e.pipeEvent=function(t){var e=this,r=t.event,n=t.callback,o=t.rules,i=t.once;if(!yc&&"web"===this.container)return{remove:function(){},listener:n};var a=this.transformConfig(r,null,o,"on").then((function(t){var o=t.realMethod,a=t.rule,c=t.env,s=a.interceptor;if(s&&"function"==typeof s)return s(Object.assign({bridge:e.bridge},c),(function(t){"function"==typeof n&&n(t)}));var u=e.bridge.on(o,(function(t){var e=t;Ga&&(e=Ga(e)),a.postprocess&&"function"==typeof a.postprocess&&(e=a.postprocess(e,{event:r,realMethod:o,env:c,logger:lc,type:"on"})),a.postprocess?null!==e&&n(e):n(e)}),i);return[o,u]}));return{remove:function(){a.then((function(t){var r=t[0],n=t[1];e.bridge.off(r,n)}))},listener:n}},t}();var bc="undefined"!=typeof window,wc=bc?window.navigator.userAgent:"",Sc=/(Android);?\s+([\d.]+)?/i.test(wc),_c=new mc({bridge:bc?window.JSBridge:{},container:"web",os:Sc?"android":"ios",monitor:function(t,e){console.log("[鹊桥 SDK Monitor]: ",e)}});_c.init({getRuntimeEnv:bc?function(t,e){if(console.log("getRuntimeEnv"),console.log("window",window),"undefined"==typeof window)return{};var r=navigator.userAgent.toLowerCase(),n=t.os,o={android:new RegExp("app_version/","i"),ios:new RegExp("aweme_","i")};try{var i=r.split(o[n])[1];if(console.log("version",i),i&&"string"==typeof i){console.log("UA识别成功"),i=i.split(" ")[0];i=i.match(/[0-9\.]+/)[0],console.log("version is",i),e(null,{appid:990003,version:i})}else console.log("UA识别失败"),t.bridge.call("x.getAppInfo",{},(function(t){1===t.code?(console.log("data",t),e(null,{appid:990003,version:t.data.appVersion})):e(new Error("JSB调用失败 x.getAppInfo"))}))}catch(t){console.log("未获取到抖音客户端信息")}}:function(t,e){e(null,{})}});var Ec=[{target:[{hostId:990003,os:"*",version:"10.4.0-",container:"web"}],map:{method:"openConfig"},preprocess:function(t,e){var r=e||{},n=r.bridge;if(function(t,e){if(t===e)return 0;for(var r=t.toString().split("."),n=e.toString().split(".");r.length<n.length;)r.push("0");for(;n.length<r.length;)n.push("0");for(var o=0;o<r.length;){var i=Number(r[o]),a=Number(n[o]);if(a>i)return-1;if(a<i)return 1;if(a<i)return 0;o+=1}return 0}(r.env.version,"10.4.0")<0)return n.trigger("openConfigError",{status_code:-1,status_msg:"不支持的抖音客户端版本"}),{};var o="{}";try{o=JSON.stringify(t.params)}catch(t){}return{config_json:o}}}];function Oc(t,e,r){return _c.pipeCall({method:"config",params:t,callback:e,rules:Ec,options:r},!1)}Oc.rules=Ec;var xc=[{target:[{hostId:990003,os:"*",version:"0.0.0-",container:"web"}],map:{method:"openConfigError"}}];function jc(t,e){return _c.pipeEvent({event:"error",callback:t,rules:xc,once:e})}jc.rules=xc;var Ic=[{target:[{hostId:990003,os:"*",version:"0.0.0-",container:"web"}],map:{method:"openConfigSuccess"}}];function kc(t,e){return _c.pipeEvent({event:"ready",callback:t,rules:Ic,once:e})}kc.rules=Ic;var Pc=[{target:[{hostId:990003,os:"*",version:"10.8.0-",container:"web"}],map:{method:"showOpenAuth"},preprocess:function(t,e){return t.params},postprocess:function(t,e){t=t.data||t;var r=e.params,n=r.success,o=r.error,i=t,a=i.response,c=i.errorCode,s=i.errorMsg,u=(a||{}).ticket;u?"function"==typeof n&&n({code:0,msg:"success",ticket:u,response:a}):"function"==typeof o&&o({code:c,msg:s,res:t})}}];function Ac(t,e,r){return _c.pipeCall({method:"showOpenAuth",params:t,callback:e,rules:Pc,options:r},!1)}Ac.rules=Pc;var Mc,Nc=[{target:[{hostId:990003,os:"*",version:"0.0.0-",container:"web"},{hostId:990003,os:"*",version:"0.0.0-",container:"lynx"}],map:{method:"chooseAvatar"}}];function Tc(t,e,r){return _c.pipeCall({method:"chooseAvatar",params:t,callback:e,rules:Nc,options:r},!1)}!function(t){t[t.Success=1]="Success",t[t.Failed=0]="Failed"}(Mc||(Mc={})),Tc.rules=Nc;var Lc=[{target:[{hostId:990003,os:"ios",version:"21.4.0-",container:"web"},{hostId:990003,os:"android",version:"21.1.0-",container:"web"},{hostId:990003,os:"ios",version:"21.4.0-",container:"lynx"},{hostId:990003,os:"android",version:"21.1.0-",container:"lynx"}],map:{method:"uploadAvatar"}}];function Rc(t,e,r){return _c.pipeCall({method:"uploadAvatar",params:t,callback:e,rules:Lc,options:r},!1)}Rc.rules=Lc;var Cc=[{target:[{hostId:990003,os:"*",version:"0.0.0-",container:"web"}],map:{method:"relationshipAuthCallback"}}];function Fc(t,e,r){return _c.pipeCall({method:"relationshipAuthCallback",params:t,callback:e,rules:Cc,options:r},!1)}Fc.rules=Cc;var Jc=_c.bridge,Bc=_c.on.bind(_c),Dc={caniuse:_c.caniuse.bind(_c)},Uc=_c.globalConfig.bind(_c),Gc={config:Oc,error:jc,ready:kc,showOpenAuth:Ac,chooseAvatar:Tc,uploadAvatar:Rc,relationshipAuthCallback:Fc,bridge:Jc,on:Bc,util:Dc,globalConfig:Uc},zc=function(t,e){var r=String(t).match(/^(\w+)\.(\w+)$/);if(!r)throw new Error("[bridge]: invalid method id '"+t+"'");var n=r[1],o=r[2];this[n]=this[n]||{};var i=e.rules,a=void 0===i?[]:i,c=e.type,s=void 0===c?"call":c;this[n][o]="call"===s?function(t,e){return _c.pipeCall({method:o,params:t,callback:e,rules:a})}:function(){var t=Wo(ka.mark((function t(e,r){return ka.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",_c.pipeEvent({event:o,callback:e,rules:a,once:r}));case 1:case"end":return t.stop()}}),t)})));return function(e,r){return t.apply(this,arguments)}}()}.bind(Gc);t.bridge=Jc,t.chooseAvatar=Tc,t.config=Oc,t.default=Gc,t.error=jc,t.globalConfig=Uc,t.on=Bc,t.ready=kc,t.register=zc,t.relationshipAuthCallback=Fc,t.showOpenAuth=Ac,t.uploadAvatar=Rc,t.util=Dc,Object.defineProperty(t,"__esModule",{value:!0})}));
