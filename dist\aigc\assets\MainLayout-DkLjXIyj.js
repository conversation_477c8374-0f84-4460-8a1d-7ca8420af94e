import{k as x,l as J,u as N,c as d,m as _,o as s,F as I,f as A,b as p,a as M,n as E,t as H,p as l,w as C,q as T,v as Y,s as U,x as q,R as j,y as b,_ as O,z as D,A as B,B as $,C as F,D as G,T as K,G as L,H as P}from"./index-BBeD0eDz.js";import{a as W}from"./request-Ciyrqj7N.js";import{g as z}from"./index-CbzCmic8.js";const Q="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAAPBJREFUSEvtlTEKwjAUhv9MUsQj5B5u4iJUwcXBqWN7qYwdg2Mt6iBOHiQH6CASnJ6kVAiiNJFqB5M1/3tf3pdAGHparCcuAvhn5oPqoPprBsLjeqs2TdMjY+xSVdVaSqntYBzHA865JKKREGLqcj/OqrMs2wGYAdgrpZZlWd4MoIFuACyI6CCEMJnW5QxOkmQYRVEBYAKgUEqtTHfOeQ0FcNJaL/I8v7ZSAb9PwoYT0dYAGGNzX2hd53I6O/M0udnymvTRyxtsChv42Rxcaz121WsP8BHY19KrfAB3YdGpR1DtpKmLUFDdhUWnHv+n+g7nN00f4skt7AAAAABJRU5ErkJggg==",Z={key:0,class:"menu-group"},X=["src"],ee={class:"flex-1 truncate"},te=["src"],ne={class:"nav-subitem"},ae=["src"],se=x({__name:"MenuItem",props:{item:{},expandedMenus:{}},setup(S){const c=N(),f=b(),o=S,u=e=>!(e.meta&&e.meta.hidden),y=e=>{var t;return((t=e.children)==null?void 0:t.filter(n=>u(n)))||[]},h=e=>!!e.children&&y(e).length>0,g=e=>y(e).some(t=>{var n;return!!(t.path===c.path||((n=c.meta)==null?void 0:n.parent)===t.path||c.path.startsWith(t.path+"/")||h(t)&&g(t))}),v=J(()=>{var a,m;const e=c.path===o.item.path,t=((a=o.item.meta)==null?void 0:a.parent)===c.path,n=((m=c.meta)==null?void 0:m.parent)===o.item.path;return h(o.item)&&g(o.item)?!1:e||t||n}),i=J(()=>{const e=o.expandedMenus.includes(o.item.path),t=h(o.item)&&g(o.item);return e||t}),w=e=>{if(h(e)){const t=o.expandedMenus.indexOf(e.path),n=[...o.expandedMenus];t===-1?n.push(e.path):n.splice(t,1),localStorage.setItem("expandedMenus",JSON.stringify(n)),window.dispatchEvent(new CustomEvent("menuToggled",{detail:{expandedMenus:n}}))}else f.push(e.path)};return(e,t)=>{var a,m;const n=q;return u(e.item)?(s(),d(I,{key:0},[h(e.item)?(s(),d("div",Z,[p("div",{class:E(["nav-item",{active:v.value}]),onClick:t[0]||(t[0]=r=>w(e.item))},[e.item.meta&&e.item.meta.icon?(s(),d("img",{key:0,src:v.value?(a=e.item.meta)==null?void 0:a.activeIcon:(m=e.item.meta)==null?void 0:m.icon,class:"icon",alt:"menu icon"},null,8,X)):_("",!0),p("span",ee,H(e.item.meta&&e.item.meta.title),1),p("img",{src:l(Q),class:E(["arrow-icon",{"arrow-up":i.value}]),alt:"arrow"},null,10,te)],2),M(n,null,{default:C(()=>[T(p("div",ne,[(s(!0),d(I,null,U(y(e.item),r=>(s(),A(V,{key:r.path,item:r,"expanded-menus":e.expandedMenus},null,8,["item","expanded-menus"]))),128))],512),[[Y,i.value]])]),_:1})])):(s(),A(l(j),{key:1,to:e.item.path,class:E(["nav-item",{active:v.value}])},{default:C(()=>{var r,k,R;return[e.item.meta&&e.item.meta.icon?(s(),d("img",{key:0,src:v.value?(r=e.item.meta)==null?void 0:r.activeIcon:(k=e.item.meta)==null?void 0:k.icon,class:"icon",alt:"menu icon"},null,8,ae)):_("",!0),p("span",null,H((R=e.item.meta)==null?void 0:R.title),1)]}),_:1},8,["to","class"]))],64)):_("",!0)}}}),V=O(se,[["__scopeId","data-v-c739c0e2"]]),oe="https://images.china9.cn/static/",ie="202408271812",re=x({name:"ZhyConsoleHeader",render(){return D("script",{name:"ZhyConsoleHeaderJs",type:"text/javascript",src:oe+"consoleHeader.js?v="+ie})}}),le=""+new URL("avatar-CmJC3Xrk.png",import.meta.url).href,de={class:"layout-container"},ce={key:0,id:"zhyConsoleHeader"},ue={class:"main-container"},me={key:0,class:"sidebar"},pe={class:"sidebar-nav"},he={class:"content flex-1 p-[20px]"},ve=x({__name:"MainLayout",setup(S){const c=b(),f=N(),o=B({avatar:le,name:"用户名称",role:"--"}),u=c.getRoutes().find(e=>e.name==="Layout"),y=u?u.children.filter(e=>e.meta&&e.meta.title).map(e=>({...e,meta:e.meta})):[],h=!1,g=B(),v=()=>{const e=[];return u&&u.children&&u.children.forEach(t=>{t.children&&t.children.length>0&&t.children.filter(a=>a.meta&&a.meta.title&&!a.meta.hidden).length>0&&e.push(t.path)}),e},i=B(v()),w=async()=>{try{const e=await z({});o.value=e}catch(e){console.error("获取用户信息失败",e)}};return $(()=>{w(),g.value=W.get("jzt_20_manage_login");try{const e=localStorage.getItem("expandedMenus");if(e){const t=JSON.parse(e),n=v(),a=[...new Set([...n,...t])];i.value=a}else localStorage.setItem("expandedMenus",JSON.stringify(i.value));window.addEventListener("menuToggled",t=>{i.value=t.detail.expandedMenus})}catch(e){console.error("Error loading expanded menus:",e),localStorage.setItem("expandedMenus",JSON.stringify(i.value))}}),F(()=>{window.removeEventListener("menuToggled",e=>{i.value=e.detail.expandedMenus})}),(e,t)=>{const n=P;return s(),d("div",de,[!l(h)&&g.value!="close"?(s(),d("div",ce)):_("",!0),_("",!0),p("div",ue,[e.$route.meta.asideHidden?_("",!0):(s(),d("aside",me,[p("nav",pe,[(s(!0),d(I,null,U(l(y),a=>(s(),A(V,{key:a.path,item:a,"expanded-menus":i.value},null,8,["item","expanded-menus"]))),128))])])),p("main",he,[M(l(G),null,{default:C(({Component:a})=>[M(K,{name:"el-fade-in"},{default:C(()=>{var m,r;return[(m=l(f).meta)!=null&&m.hidden&&!((r=l(f).meta)!=null&&r.asideHidden)?(s(),A(n,{key:0,class:"h-full",title:l(f).meta.title,"show-back":"",onBack:t[1]||(t[1]=k=>e.$router.back()),titleBottom:l(f).meta.bottom},{default:C(()=>[(s(),A(L(a)))]),_:2},1032,["title","titleBottom"])):(s(),A(L(a),{key:1}))]}),_:2},1024)]),_:1})])]),M(re)])}}}),_e=O(ve,[["__scopeId","data-v-255f28c3"]]);export{_e as default};
