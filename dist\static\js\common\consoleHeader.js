/* eslint-disable */

//token
var access_token = Cookies.get("access_token") || Cookies.get("token");

// 判断access_token最后一位是否以u结尾忽略大小写
var isUser = false;
if (access_token && access_token.slice(-1).toLowerCase() === "u") {
  isUser = true;
}

var unique_id = "";

// 跳转链接域名
var zhyConsoleHeaderLinkDomain = "https://dev.china9.cn/#/";
var zhyConsoleHeaderApiDomain = "https://apidev.china9.cn/";
var zhyImageDomain = "https://zcloud.obs.cn-north-4.myhuaweicloud.com/";
var zhyHomeHeaderLinkDomain = "https://dev.china9.cn/#/";
var employeeUrl = "https://employee.china9.cn/new-router/main";

// cookie的域名
var cookieDomain = "china9.cn";
if ("undefined" != typeof environment) {
  if (environment === "pro") {
    zhyConsoleHeaderLinkDomain = "https://www.china9.cn/#/";
    zhyConsoleHeaderApiDomain = "https://api.china9.cn/";
    zhyHomeHeaderLinkDomain = "https://www.china9.cn/#/";
    cookieDomain = "china9.cn";
    employeeUrl = "https://employee.china9.cn/new-router/main";
  } else if (environment === "local") {
    zhyHomeHeaderLinkDomain = "http://" + window.location.host + "/#/";
    zhyConsoleHeaderLinkDomain = "http://" + window.location.host + "/#/";
    cookieDomain = document.location.hostname;
    employeeUrl = "http://localhost:8000/new-router/main";
  } else {
    zhyFooterLinkDomain = "https://dev.china9.cn/#/";
    zhyFooterApiDomain = "https://apidev.china9.cn/";
    cookieDomain = "china9.cn";
    employeeUrl = "https://employee.china9.cn/new-router/main";
  }
} else {
  zhyFooterLinkDomain = "https://dev.china9.cn/#/";
  zhyFooterApiDomain = "https://apidev.china9.cn/";
  cookieDomain = "china9.cn";
  employeeUrl = "https://employee.china9.cn/new-router/main";
}

var zhyHeaderload = false;

var version = "202504101407";

var selectedCompany = {};
var nowCompany = {};

function getCssPromise(href, idName) {
  return new Promise((resolve) => {
    $.get(href, function (res) {
      var style = document.createElement("style");
      style.id = idName;
      style.innerHTML = res;
      if (idName === "elementCss") {
        // 获取head中的第一个元素
        var firstElement = document.head.firstChild;
        // 将新的style元素插入到第一个元素之前
        document.head.insertBefore(style, firstElement);
      } else {
        document.head.appendChild(style);
      }
      resolve({ code: 200, id: idName });
    });
  });
}

// 过期清cookies
var minitesConsole = 60;
var timeConsole = new Date(new Date().getTime() + minitesConsole * 1000 * 60);

var companyListArr = [];

var consoleHeaderLoadNum = 0;

//  先加载样式，防止先出现没有样式的内容
if (!document.getElementById("ziHaiConsoleHeaderCloud")) {
  let elementCssUrl =
    "https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/index.css?v=" +
    version;
  let commonCssUrl =
    "https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/ziHaiConsoleHeaderCloud.css?v=" +
    version;
  if (environment && (environment === "local" || environment === "dev")) {
    commonCssUrl = "/static/js/common/ziHaiConsoleHeaderCloud.css";
    elementCssUrl = "/static/js/common/index.css";
  }
  var cssList = [
    getCssPromise(commonCssUrl, "ziHaiConsoleHeaderCloud"),
    getCssPromise(elementCssUrl, "elementCss"),
  ];
  Promise.all(cssList).then((res) => {
    var success = res.filter((item) => item.code === 200);
    if (success.length === 2) {
      zhyHeaderload = true;
      checkHasConsoleEle();
    }
  });
} else {
  zhyHeaderload = true;
  checkHasConsoleEle();
}

var authList = [getBuyGoods(), getIsSuper()];

function checkHasConsoleEle() {
  setTimeout(function () {
    if (consoleHeaderLoadNum < 2) {
      if (!document.getElementById("zhyConsoleHeader")) {
        checkHasConsoleEle();
      } else {
        getZhConsoleUserInfo();
        getCompanyList(unique_id);
      }
    }
    consoleHeaderLoadNum++;
  }, 500);
}

// 获取购买产品列表
function getBuyGoods() {
  return new Promise((resolve) => {
    $.post(zhyConsoleHeaderApiDomain + "api/role/getUserPowerConsole", {
      token: access_token,
      access_token: access_token,
      enterpriseSide: "pc",
    }).then((res) => {
      if (res.code === 200) {
        resolve(res.data.myInfo);
      } else {
        resolve([]);
      }
    });
  });
}

function getIsSuper() {
  return new Promise((resolve) => {
    $.post(zhyConsoleHeaderApiDomain + "api/user/home_card", {
      token: access_token,
      access_token: access_token,
      enterpriseSide: "pc",
    }).then((res) => {
      if (res.code === 200) {
        resolve(res.data.user.super);
      } else {
        resolve(false);
      }
    });
  });
}

// 跳转管理智能体
function toWork() {
  window.location.href =
    zhyConsoleHeaderLinkDomain + "console/workContentSettings";
}

// 控制台头部
function addControlHeader(
  hasData,
  avatar,
  companyName,
  unique_id,
  phone,
  certificate,
  showCompanyInfo,
  companyNav
) {
  if (!avatar) {
    avatar = zhyImageDomain + "avatar_bitmap.png";
  }
  if (!companyName) {
    companyName = "";
  }
  if (!unique_id) {
    unique_id = "";
  }
  if (typeof certificate == "undefined" || certificate == null) {
    certificate = false;
  }
  let str =
    '<div class="header">' +
    '    <div class="zhyHomeHeader-title">' +
    '        <a href="' +
    zhyConsoleHeaderLinkDomain +
    'home">' +
    '            <img class="logo" src="' +
    zhyImageDomain +
    'static/logo.f983bd4.png">' +
    '            <img class="zihaiyun" src="' +
    zhyImageDomain +
    'static/zihaiyunR.png">' +
    "            <span>|</span>" +
    "        </a>";
  if (hasData) {
    str +=
      '<div id="swit" class="title-company">' +
      '            <a id="companyName">' +
      companyName;
    if (certificate) {
      str +=
        '<img src="' +
        zhyImageDomain +
        'static/Certified.png" style="height: 20px;margin-left: 8px">';
    } else {
      str +=
        '<img src="' +
        zhyImageDomain +
        'static/notCertified.png" style="height: 20px;margin-left: 8px">';
    }
    str +=
      "</a>" +
      '            <div class="btn-change" data-id="' +
      unique_id +
      '" onclick="changeCompany()">' +
      '               <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAALCAYAAACksgdhAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyFpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDpDNzNBNEQxRDkyQ0MxMUVCODA3QTg1M0NGNTg1NUZBOSIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDpDNzNBNEQxRTkyQ0MxMUVCODA3QTg1M0NGNTg1NUZBOSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOkM3M0E0RDFCOTJDQzExRUI4MDdBODUzQ0Y1ODU1RkE5IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOkM3M0E0RDFDOTJDQzExRUI4MDdBODUzQ0Y1ODU1RkE5Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+UwKBVgAAALpJREFUeNpi/P//PwMh4HPux/R/Xxg9/39hstjuxfqCiYE4cA+I5YF4vcfKv+ws7U++5/xgYFABYobvDP+BmIHhJxT//sPA8OcHI5DFCCK+ArEFEE9lARItQMzPQDwIBWnSAmIxEjS9ZiQmINAByE/SQP+I4vPTv29A/IWRARh6DP8/M70GOe8qiX76CNK0BoiTgfgbEM8BYkLuvQvSlA0NDEsgfjZbhquTkFVMlTKcIOcHIUUgQQAQYACi5EaBg6DXsgAAAABJRU5ErkJggg=="> ' +
      "               <span>切换企业</span>" +
      "           </div>" +
      "        </div>";
  }

  str +=
    "    </div>" +
    '    <div id="text-div">' +
    "        <ul>" +
    "            <li>" +
    '                <a href="' +
    zhyConsoleHeaderLinkDomain +
    'console/display/console">控制台</a>' +
    "            </li>";
  if (showCompanyInfo) {
    str +=
      '      <li id="order">' +
      '                <a href="' +
      zhyConsoleHeaderLinkDomain +
      'console/order">费用</a>' +
      '                <div id="order-pop" class="posiabox">' +
      '                    <a href="' +
      zhyConsoleHeaderLinkDomain +
      'console/order">订单</a>' +
      '                    <a href="' +
      zhyConsoleHeaderLinkDomain +
      'console/orderRenew">续费</a>' +
      '                    <a href="' +
      zhyConsoleHeaderLinkDomain +
      'console/wallet">充值</a>' +
      '                    <a href="' +
      zhyConsoleHeaderLinkDomain +
      'console/invoice">发票</a>' +
      // '                    <a href="'+zhyConsoleHeaderLinkDomain+'console/bills/waterBills">流水</a>' +
      "                </div>" +
      "            </li>";
  }
  str += '       <li id="workorder">';
  if (!isUser) {
    str +=
      '                <a href="' +
      zhyConsoleHeaderLinkDomain +
      'workorder">工单</a>' +
      '                <div id="workorder-pop" class="posiabox">' +
      '                    <a href="' +
      zhyConsoleHeaderLinkDomain +
      'workorder">提交工单</a>' +
      '                    <a href="' +
      zhyConsoleHeaderLinkDomain +
      'workorder/list">我的工单</a>' +
      "                </div>" +
      "            </li>";
  }
  let companyUrl = zhyConsoleHeaderLinkDomain + "company/detail";
  if (isUser) {
    companyUrl = zhyConsoleHeaderLinkDomain + "company/information/list";
  }
  if (companyNav.length) {
    let showCompanyNavList = companyNav.filter((v) => v.isshow === "1");
    if (showCompanyNavList.length) {
      str +=
        '       <li id="company">' +
        '                <a href="' +
        companyUrl +
        '">企业</a>';
      str += '<div id="company-pop" class="posiabox">';
      showCompanyNavList.forEach((item) => {
        let style = "";
        let id = "";
        // 如果item.url以/开头，去掉/
        if (item.url.startsWith("/")) {
          // 如果支持subarray方法用item.url.subarray(1)，否则用item.url.slice(1)
          if (item.url.subarray) {
            item.url = item.url.subarray(1);
          } else {
            item.url = item.url.slice(1);
          }
        }
        str +=
          '<a href="' +
          zhyConsoleHeaderLinkDomain +
          item.url +
          '" style="' +
          style +
          '" id="' +
          id +
          '">' +
          item.label +
          "</a>";
      });
      str += "</div>";
      str += "</li>";
    }
  }

  str +=
    "         <li>" +
    '                <a href="' +
    zhyConsoleHeaderLinkDomain +
    'home">首页</a>' +
    "            </li>" +
    "        </ul>" +
    "    </div>" +
    '    <div class="icon-div">' +
    '        <a href="' +
    zhyConsoleHeaderLinkDomain +
    'message">' +
    '            <img src="https://zhjzt.china9.cn/statics/zihai/images/ld.png">' +
    "        </a>";
  if (!isUser) {
    str +=
      '        <a href="' +
      zhyConsoleHeaderLinkDomain +
      'home/document">' +
      '            <img src="https://zhjzt.china9.cn/statics/zihai/images/wd_1.png">' +
      "        </a>";
  }
  str +=
    "    </div>" +
    '    <div id="avatar" style="display: flex;align-items: center">' +
    '        <img id="avatar-header" src="' +
    avatar +
    '">' +
    '        <div id="seledbox" class="posiabox">' +
    '            <a href="' +
    zhyConsoleHeaderLinkDomain +
    'personal">个人信息</a>' +
    '            <a href="javascript:;" onclick="consoleLogout()">退出登录</a>' +
    "        </div>" +
    '      <div class="zhyHomeHeaderPhone" style="margin-right: 20px;color: rgba(255,255,255,0.6)">' +
    phone +
    "</div>" +
    "    </div>";
  /* if (companyNav.length && companyNav.findIndex((v) => v.id === 3) > -1) {
    str += '<div class="primary-button" onclick="toWork()">管理智能体</div>';
  } */
  str +=
    "</div>" +
    '<div class="jzt-open-box" style="display: none">' +
    '    <div class="layui-layer-shade" onclick="closeSelectCompany()"></div>' +
    '    <div class="jzt-open-main">' +
    '       <i class="close" onclick="closeSelectCompany()">×</i>' +
    '       <div class="el-dialog__header">' +
    '          <span class="el-dialog__title">选择企业</span>' +
    "       </div>" +
    '       <div class="select-company-form">' +
    '             <div class="input-box">' +
    '                 <input id="search-name" placeholder="请输入企业名称"">' +
    '                 <button type="button" class="el-button" onclick="searchByName()">' +
    "                   <span>搜索</span>" +
    "                 </button>" +
    "             </div>" +
    '             <div class="company-list">' +
    '                 <ul>' +
    '                   <span class="loading">正在加载...</span>' +
    '                 </ul>' +
  '               </div>'+
    "       </div>" +
    '       <div style="display: flex; margin-top: 50px; justify-content: center">' +
    '           <button type="button" class="el-button btn-size btn-create el-button--text" onclick="gotoCreateCompany()">' +
    "               <span>新建企业</span>" +
    "           </button>" +
    '           <button type="button" class="el-button btn-size el-button--primary" onclick="toggleCompany()">' +
    "               <span>确认切换</span>" +
    "           </button>" +
    "       </div>" +
    "    </div>" +
    "</div>" +
    '<div tabindex="-1" role="dialog" aria-modal="true" id="overdue-alert" aria-label="标题名称" class="el-message-box__wrapper" style="z-index: *********9;display: none;background: rgba(0,0,0,0.3);">' +
    '     <div class="el-message-box">' +
    '         <div class="el-message-box__header">' +
    '              <div class="el-message-box__title">' +
    "                   <span>登录过期</span>" +
    "              </div>" +
    "         </div>" +
    '         <div class="el-message-box__content">' +
    '             <div class="el-message-box__container">' +
    '                 <div class="el-message-box__message">' +
    "                     <p>您已登录超时，请重新登录</p>" +
    "                 </div>" +
    "             </div>" +
    '         <div class="el-message-box__input" style="display: none;">' +
    '             <div class="el-input">' +
    '                     <input type="text" autocomplete="off" placeholder="" class="el-input__inner">' +
    "                 </div>" +
    '                 <div class="el-message-box__errormsg" style="visibility: hidden;"></div>' +
    "             </div>" +
    "         </div>" +
    '<div class="el-message-box__btns">' +
    '     <button type="button" class="el-button el-button--default el-button--small el-button--danger" onclick="consoleLogout();return false;" style="height: auto">' +
    "       <span>\n" +
    "          确定\n" +
    "        </span>" +
    "     </button>" +
    "</div>" +
    "</div>" +
    "</div>";
  $("#zhyConsoleHeader").html(str);

  if (Cookies.get("employee_token")) {
    $(".btn-to-user").show();
  }

  Promise.all(authList).then((res) => {
    if (res[0].length > 0 && res[1]) {
      $("#taskSettings").show();
    }
  });
}

var getNavList = [
  new Promise((resolve) => {
    try {
      $.post(zhyConsoleHeaderApiDomain + "api/user/info", {
        access_token: access_token,
        type: "pc",
        enterpriseSide: "pc",
      })
        .done((res) => {
          resolve(res);
        })
        .fail((err) => {
          resolve({ code: 400, msg: err });
        });
    } catch (e) {
      $.post(zhyConsoleHeaderApiDomain + "api/user/info", {
        access_token: access_token,
        type: "pc",
        enterpriseSide: "pc",
      })
        .success((res) => {
          resolve(res);
        })
        .error((err) => {
          resolve({ code: 400, msg: err });
        });
    }
  }),
  new Promise((resolve) => {
    try {
      $.post(zhyConsoleHeaderApiDomain + "api/role/getCompanyPowerConsole", {
        access_token: access_token,
        type: "pc",
        enterpriseSide: "pc",
      })
        .done((res) => {
          resolve(res);
        })
        .fail((err) => {
          resolve({ code: 400, msg: err });
        });
    } catch (e) {
      $.post(zhyConsoleHeaderApiDomain + "api/role/getCompanyPowerConsole", {
        access_token: access_token,
        type: "pc",
        enterpriseSide: "pc",
      })
        .success((res) => {
          resolve(res);
        })
        .error((err) => {
          resolve({ code: 400, msg: err });
        });
    }
  }),
];

// 获取个人和公司信息
function getZhConsoleUserInfo() {
  unique_id = "";
  Promise.all(getNavList).then((res) => {
    let companyNav = [];
    if (res[1].code === 200) {
      companyNav = res[1].data;
    }
    if (res[0].code === 200) {
      loginFlag = true;
      if (
        res[0].data &&
        Object.prototype.toString.call(res[0].data) === "[object Object]"
      ) {
        let avatar = res[0].data.avatar,
          companyName = "",
          phone = res[0].data.phone || "";

        phone = phone.toString().substr(0, 3) + "****" + phone.slice(-4);

        let showCompanyInfo = false;
        let certificate = false;

        if (
          res[0].data.company &&
          Object.prototype.toString.call(res[0].data) === "[object Object]"
        ) {
          companyName = res[0].data.company.name;
          unique_id = res[0].data.company.unique_id;
          nowCompany = res[0].data.company;
          if (res[0].data.company.is_owner == 1) {
            showCompanyInfo = true;
          } else {
            showCompanyInfo = false;
          }
          if (res[0].data.company.is_certified == 1) {
            certificate = true;
          } else {
            certificate = false;
          }
        } else {
          showCompanyInfo = false;
          certificate = false;
        }
        addControlHeader(
          true,
          avatar,
          companyName,
          unique_id,
          phone,
          certificate,
          showCompanyInfo,
          companyNav
        );
        checkPass();

        getCompanyList(unique_id);
      }
    } else {
      loginFlag = false;
      if (document.getElementById("zhyConsoleHeader")) {
        if (environment && environment !== "local") {
          if (!window.location.href.includes("jzt2")) {
            window.location.href = zhyConsoleHeaderLinkDomain + "home";
          }
        }
      }
    }
  });
}

function getCompanyList(unique_id) {
  let token = access_token || Cookies.get("employee_token");
  $.post(
    zhyConsoleHeaderApiDomain + "api/user/switchCompanyData",
    { access_token: token, origin: "pc", enterpriseSide: "pc" },
    (res) => {
      if (res.code === 200) {
        if (res.data) {
          companyListArr = res.data;
          setCompanyList(res.data, unique_id);
        }
        if (document.getElementById("search-name")) {
          document
            .getElementById("search-name")
            .addEventListener("input", debounce(searchByName, 1000));
        }
      }
    }
  );
}

// 监听hash路由，解决路由变化后返回加不上头部的问题
window.addEventListener("hashchange", function () {
  setTimeout(function () {
    getZhConsoleUserInfo();
  }, 1000);
});

//  点击切换切换企业
function changeCompany() {
  $("#zhyConsoleHeader .jzt-open-box").show();
}

//  关闭切换企业弹窗
function closeSelectCompany() {
  $("#zhyConsoleHeader .jzt-open-box").hide();
}

//  选择公司
function selectCompanyItem(even, unique_id, name, om_id, company_id) {
console.log("🚀 ~ selectCompanyItem ~ even: ", even);
  even.stopPropagation();
  $(even.target).toggleClass("active");
  $(even.target)
    .siblings()
    .removeClass("active");
  selectedCompany = {
    unique_id,
    name,
    om_id,
    company_id,
  };
}

//  新建公司
function gotoCreateCompany() {
  $("#zhyConsoleHeader .jzt-open-box").hide();
  window.location.href = zhyConsoleHeaderLinkDomain + "company/create";
}

//  切换公司
function toggleCompany() {
  unique_id = selectedCompany.unique_id;

  const data = {
    unique_id,
    token: Cookies.get("employee_token"),
    enterpriseSide: "pc",
  };

  let toggleUrl = zhyConsoleHeaderApiDomain + "api/user/cutNew";

  $.post(toggleUrl, data, (res) => {
    if (res.code === 200) {
      try {
        Cookies.remove("access_token", { path: "/", domain: cookieDomain });
        Cookies.remove("token", { path: "/", domain: cookieDomain });
        Cookies.remove("gcc", { path: "/", domain: cookieDomain });

        sessionStorage.clear();

        if (res.data.token) {
          Cookies.set("token", res.data.token, {
            path: "/",
            domain: cookieDomain,
            expires: new Date(res.data.expires_in * 1000),
          });
          Cookies.set("access_token", res.data.token, {
            path: "/",
            domain: cookieDomain,
            expires: new Date(res.data.expires_in * 1000),
          });
        }

        if (res.data.gcc) {
          Cookies.set("gcc", res.data.gcc, {
            path: "/",
            domain: cookieDomain,
            expires: new Date(res.data.expires_in * 1000),
          });
        } else {
          Cookies.set("gcc", "", {
            path: "/",
            domain: cookieDomain,
            expires: new Date(res.data.expires_in * 1000),
          });
        }
      } catch (e) {
        console.log(e);
      } finally {
        setTimeout(function () {
          window.location.reload();
        }, 1000);
      }
    } else {
      try {
        showMsg(res.msg || res.message || "切换失败");
      } catch (e) {
        alert(res.msg || res.message || "切换失败");
      }
      selectedCompany = {};

      if (res.code === 401) {
        window.location.href = zhyConsoleHeaderLinkDomain + "home";
      }
    }
  }).fail(function (err) {
    try {
      showMsg("切换失败");
    } catch (e) {
      alert("切换失败");
    }
    selectedCompany = {};
  });
}

function showMsg(tips) {
  const id = Math.random().toString(36).substr(2);
  $("body").append(
    `<div role="alert" class="el-message el-message--error" id="${id}" style="top: 20px; z-index: *********;"><i class="el-message__icon el-icon-error"></i><p class="el-message__content">${tips}</p><!----></div>`
  );
  setTimeout(function () {
    $("#" + id).remove();
  }, 3000);
}

function clearAllCookie() {
  var keys = document.cookie.match(/[^ =;]+(?=\=)/g);
  if (keys) {
    for (var i = keys.length; i--; )
      document.cookie = keys[i] + "=0;expires=" + new Date(0).toUTCString();
  }
}

function consoleLogout() {
  $.post(
    zhyConsoleHeaderApiDomain + "api/logout",
    { access_token, enterpriseSide: "pc" },
    function () {
      Cookies.remove("access_token", { path: "/", domain: cookieDomain });
      Cookies.remove("token", { path: "/", domain: cookieDomain });
      Cookies.remove("gcc", { path: "/", domain: cookieDomain });
      Cookies.remove("employee_token", { path: "/", domain: cookieDomain });
      Cookies.remove("site_id", { path: "/", domain: cookieDomain });
      Cookies.remove("china_site_id", { path: "/", domain: cookieDomain });
      Cookies.remove("jzt_20_site_id", { path: "/", domain: cookieDomain });
      Cookies.remove("company_unique_id", { path: "/", domain: cookieDomain });

      clearAllCookie();
      sessionStorage.clear();
      localStorage.clear();

      if (
        location.hostname === "www.china9.cn" ||
        location.hostname === "china9.cn" ||
        location.hostname === "localhost"
      ) {
        localStorage.clear();
      }

      try {
        $.post("https://zhjzt.china9.cn/newclient/Login/newoutLogin", {
          enterpriseSide: "pc",
        }).done(function () {
          window.location.href = zhyHomeHeaderLinkDomain + "home?type=logout";
        });
      } catch (e) {
        $.post("https://zhjzt.china9.cn/newclient/Login/newoutLogin", {
          enterpriseSide: "pc",
        }).complete(function () {
          window.location.href = zhyHomeHeaderLinkDomain + "home?type=logout";
        });
      }
    }
  );
}

var loginFlag = false;
window.addEventListener("click", function () {
  if (!Cookies.get("token") || !Cookies.get("access_token")) {
    if (loginFlag) {
      return $("#overdue-alert").show();
    } else {
      return false;
    }
  }
  timeConsole = new Date(new Date().getTime() + minitesConsole * 1000 * 60);
  Cookies.set("access_token", Cookies.get("access_token"), {
    path: "/",
    domain: cookieDomain,
    expires: timeConsole,
  });
  Cookies.set("token", Cookies.get("token"), {
    path: "/",
    domain: cookieDomain,
    expires: timeConsole,
  });
  Cookies.set("gcc", Cookies.get("gcc"), {
    path: "/",
    domain: cookieDomain,
    expires: timeConsole,
  });
  Cookies.set("employee_token", Cookies.get("employee_token"), {
    path: "/",
    domain: cookieDomain,
    expires: timeConsole,
  });
});

// 百度统计
var _hmt = _hmt || [];
(function () {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?0dbb4f63a579d2d7a009fa1df830affd";
  var s = document.getElementsByTagName("script")[0];
  s.parentNode.insertBefore(hm, s);
})();

// 弱密码
function checkPass() {
  $.get(zhyConsoleHeaderApiDomain + "api/user/weakPassword", {
    token: access_token,
    enterpriseSide: "pc",
  }).then(function (res) {
    if (res.code === 200) {
      if (parseInt(res.data) === 1) {
        $("#zhyConsoleHeader .zhyHomeHeaderPhone").addClass("weak-tips");
      }
    }
  });
}

function setCompanyList(data, unique_id) {
  try {
    let companyList = document.querySelector(
      "#zhyConsoleHeader .company-list ul"
    );
    let str = "";

    data.forEach(function (v, i) {
      if (isUser) {
        if (v.unique_id === unique_id) {
          $("#zhyConsoleHeader #search-name")
            .attr("data-om_id", v.om_id)
            .attr("data-company_id", v.company_id)
            .attr("data-id", v.unique_id)
            .html(v.name);
        }
      }
      var liActive = v.unique_id === nowCompany.unique_id ? "active" : "";
      str += '<li style="display: flex;align-items: center;justify-content: space-between" onclick="selectCompanyItem(event,\'' +
        v.unique_id +
        "','" +
        v.name +
        "','" +
        v.om_id +
        "', '" +
        v.company_id +
        "')\" title="+v.name+" class=\""+liActive+"\">" +
        '<div style="overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1;width:' +
        (v.account_mold ? "80%" : "100%") +
        ';" title="' +
        v.name +
        '">' +
        v.name +
        "</div>";
      if (v.account_mold) {
        str +=
          "<div>" +
          '<p style="background-color: #fdf6ec;border: 1px solid #faecd8;color: #e6a23c;font-size: 12px;height: 24px;line-height: 24px;padding: 0 3px">' +
          v.account_mold +
          "</p></div>";
      }
      str += "</li>";
    });

    if (companyList) {
      if(str === "") {
        companyList.innerHTML = '<span class="loading">暂无企业</span>';
        return;
      }
      companyList.innerHTML = str;
    }

    if (!document.getElementById("zhyConsoleHeader")) return;
  } catch (e) {
    console.log(e);
  }
}

// 搜索企业
function searchByName() {
  let name = $("#zhyConsoleHeader #search-name").val();
  console.log("🚀 ~ searchByName ~ name: ", name);
  if (name) {
    let result = companyListArr.filter((item) => item.name.includes(name));
    setCompanyList(result);
  } else {
    setCompanyList(companyListArr);
  }
}

// 防抖
function debounce(fn, wait) {
  var timer = null;
  return function () {
    if (timer !== null) {
      clearTimeout(timer);
    }
    timer = setTimeout(fn, wait);
  };
}
