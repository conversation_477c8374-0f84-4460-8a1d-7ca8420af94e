<template>
  <el-form :model="formData" label-position="top">
    <el-form-item :label="`${item.label}服务器域名`" :prop="item.key + item.type">
      <div class="flex flex-align-center">
        <el-input style="width: 40%; margin-right: 10px;" v-model="formData[item.key]"
          :placeholder="`请输入${item.label}服务器域名`" :disabled="loading" />
        <el-button class="save-btn" type="primary" @click="handleSubmit" :loading="loading" :disabled="loading">
          保存配置
        </el-button>
      </div>
    </el-form-item>
  </el-form>
</template>

<script>

export default {
  name: "Form",
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    form: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    form: {
      handler(newVal, oldVal) {
        // console.log('form', newVal)
        this.formData = JSON.parse(JSON.stringify(newVal))
      },
      immediate: true
    }
  },
  data() {
    return {
      formData: {}
    }
  },
  methods: {
    handleSubmit() {
      // 验证key 必填
      if (!this.formData[this.item.key]) {
        this.$message.warning(`请输入${this.item.label}服务器域名`)
        return
      }
      // 验证url格式
      if (!/^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/.test(this.formData[this.item.key])) {
        this.$message.warning('请输入正确的服务器域名')
        return
      }
      this.$emit('submit', { ...this.formData, type: this.item.type })
    }
  }
}
</script>

<style scoped lang="scss">
.save-btn {
  background: #4D80FF;
  border-color: #4D80FF;
}

/deep/.el-form {
  .el-form-item__label {
    font-size: 15px;
    line-height: 30px;
    margin-bottom: 6px;
    padding: 0px;
  }

  .el-loading-mask {
    border-radius: 4px;
  }
}
</style>
