<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>视频发布 - 资海云AIGC</title>
  <!-- <script src="./js/eruda.js"></script>
  <script>eruda.init();</script> -->
  <!-- 引入脚本 -->
  <script src="./js/jquery-3.7.1.min.js"></script>
  <script src="./js/<EMAIL>"></script>
  <script src="./js/w_rem.js"></script>
  <script src="./js/common.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html {
      /* 基础字体大小，将通过JS动态计算 */
      font-size: 50px; /* 750px设计图下的基准值 */
      background-color: #FFFFFF; /* 白色背景 */;
    }
    [v-cloak] {
      display: none;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      position: relative;
    }
    /* 背景颜色 */
    body::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 80%;
      background: linear-gradient(180deg, #cfe1ff 0%, #FFFFFF 100%);
      z-index: -1;
    }

    .video-container {
      width: 100%;
      height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
    }
    .video-wrapper {
      width: 100%;
    }

    .user-info {
      width: 100%;
      height: 0.8rem;
      display: flex;
      align-items: center;
      padding: 0 0.24rem;
    }
    .user-info img {
      width: 0.5rem;
      height: 0.5rem;
      border-radius: 50%;
      margin-right: 0.12rem;
    }
    .user-info span {
      font-size: 0.3rem;
      color: #222222;
    }

    .video-content {
      aspect-ratio: 9 / 16;
      width: 5.3rem;
      background: #F5f5f5;
      border-radius: 0.2rem;
      overflow: hidden;
      position: relative;
      margin: 0.6rem auto;
    }

    .video-overlay {
      width: 100%;
      height: 100%;
    }
    .video-overlay img, .video-overlay video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .play-button {
      width: 1rem;
      height: 1rem;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .play-button img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .button-wrapper {
      width: 100%;
      height: 1.2rem;
      border-top: 0.02rem solid #F1F1F1;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .button-wrapper .publish-button {
      width: 6.4rem;
      height: 0.9rem;
      background: #186DF5;
      border-radius: 0.45rem;
      font-size: 0.3rem;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  </style>
</head>

<body>
  <div id="app" v-cloak class="video-container">
    <div class="video-wrapper">
      <!-- 用户头像昵称 -->
      <div v-if="userInfo" class="user-info">
        <img :src="userInfo.avatar" alt="用户头像">
        <span>{{ userInfo.nickname }}</span>
      </div>
      <div class="video-content">
        <div class="video-overlay">
          <video v-if="videoUrl" :src="videoUrl" controls></video>
          <!-- <img v-if="coverUrl && !isPlaying" src="./images/video.png" alt="视频封面">
          <video v-if="videoUrl && isPlaying" controls> -->
        </div>
        <!-- <div v-if="!isPlaying && videoUrl" class="play-button" @click="playVideo">
          <img src="./images/video-play.png" alt="">
        </div> -->
      </div>
    </div>
    <div class="button-wrapper">
      <div class="publish-button" @click="publishVideo">立即发布</div>
    </div>
  </div>

  <script>
    var pushApp = new Vue({
      el: '#app',
      data: {
        objectId: '',
        isPlaying: false,
        objectInfo: null,
        coverUrl: '',
        videoUrl: '',
        userInfo: null,
      },
      mounted() {
        this.userInfo = window.localStorage.getItem('userInfo');
        console.log(this.userInfo, '用户信息');
        if (!this.userInfo) {
          window.location.href = './login.html';
        } else {
          this.userInfo = JSON.parse(this.userInfo);
          // 获取视频信息
          this.getVideoInfo();
        }
      },
      methods: {
        // 获取视频信息
        getVideoInfo() {
          const that = this
          let id = getUrlParam('id');
          if (!id) {
            showErrorToastWithShake('视频ID不存在', 'error', 3000);
            return;
          }
          this.objectId = id;
          // 调用接口获取视频信息
          $.ajax({
            url: '/newapi/share/sharevideo?id=' + id,
            type: 'GET',
            success: (res) => {
              console.log(res, '获取到的视频信息');
              if (res.code == 200) {
                that.objectInfo = JSON.parse(JSON.stringify(res.data));
                let { video } = that.objectInfo
                let { cover, url } = video;
                if (cover) that.coverUrl = cover;
                if (url) that.videoUrl = url;
              } else {
                showErrorToastWithShake(res.message || res.msg || '获取视频信息失败', 'error', 3000);
              }
            },
            error: (err) => {
              console.log(err, '获取视频信息失败');
              showErrorToastWithShake('获取视频信息失败', 'error', 3000);
            }
          })
        },
        // 播放视频
        playVideo() {
          const that = this
          if (this.videoUrl) {
            this.isPlaying = true;
            // 加载视频
            const video = document.querySelector('video');
            video.src = this.videoUrl;
            video.load();
            video.play();
          }
        },
        // 发布视频
        publishVideo() {
          const that = this
          showLoading('发布中...');
          const { id, title, video } = that.objectInfo;
          const { cover, url } = video;
          const { avatar, nickname, open_id } = that.userInfo;
          let data = { id, title, cover, video: url, avatar, nickname, openid: open_id  }
          $.ajax({
            url: '/newapi/share/share',
            type: 'POST',
            data: JSON.stringify(data),
            contentType: 'application/json',
            success: (res) => {
              console.log(res, '发布视频');
              if (res.code == 200) {
                hideLoading();
                window.location.href = res.data;
              } else {
                hideLoading();
                showErrorToastWithShake(res.message, 'error', 3000);
              }
            },
            error: (err) => {
              hideLoading();
              showErrorToastWithShake('发布失败，请重试', 'error', 3000);
            }
          })
        }
      }
    })
  </script>
</body>

</html>