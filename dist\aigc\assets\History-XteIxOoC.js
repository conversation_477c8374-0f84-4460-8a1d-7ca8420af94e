import{H as e}from"./history-D-4DMQfo.js";import{l as a,m as i,n as o}from"./index-vH7ypFZe.js";import{k as r,c as p,a as s,p as t,o as m,_ as l}from"./index-BBeD0eDz.js";/* empty css                *//* empty css              */import"./index-BUwi-MV-.js";import"./empty-CSpEo1eL.js";/* empty css               *//* empty css                    *//* empty css                     */import"./request-Ciyrqj7N.js";const c={class:"pt-[10px] h-full"},d=r({__name:"History",setup(n){return(_,u)=>(m(),p("div",c,[s(e,{"list-api":t(o),"del-api":t(i),"show-copy":!0,"copy-api":t(a),field:{title:"name",lastEditTime:"created_at",thumbnail:"imgurl"},"edit-path":"/smart-material/user-portrait?id=","video-path":"/smart-material/user-portrait-preview/"},null,8,["list-api","del-api","copy-api"])]))}}),E=l(d,[["__scopeId","data-v-d185fa0d"]]);export{E as default};
