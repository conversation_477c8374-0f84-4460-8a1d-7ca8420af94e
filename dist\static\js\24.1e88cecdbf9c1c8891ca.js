webpackJsonp([24],{"2Nj6":function(t,e){},"31z4":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=n("Xxa5"),r=n.n(a),i=n("Dd8w"),s=n.n(i),o=n("exGp"),c=n.n(o),l={name:"WorkContent",inject:["workContentListFn","activeIndexFn","workContentIdFn"],props:{scrolling:{type:Boolean,default:!1}},data:function(){return{activeWorkContentIndex:0}},computed:{workContentList:function(){return this.workContentListFn()},activeWorkContent:function(){return this.workContentListFn()[this.activeWorkContentIndex]},showWorkContentList:function(){return this.workContentListFn().filter(function(t){return t.id})},workContentId:function(){return this.workContentIdFn()}},methods:{changeWorkContent:function(t){this.$emit("setScrolling",!1),this.activeWorkContentIndex=t,this.$emit("changeWorkContent",this.activeWorkContent),this.scrollToIndex(t)},scrollToIndex:function(t){this.activeWorkContentIndex=t>-1?t:0;var e=document.querySelector("#work-content"),n=document.querySelectorAll("#work-content .card")[t];if(n){var a=e.scrollTop,r=n.offsetTop-e.offsetTop,i=Date.now();requestAnimationFrame(function t(){var n=Date.now()-i,s=Math.min(n/300,1);e.scrollTop=a+(r-a)*s,n<300&&requestAnimationFrame(t)})}},addWorkContent:function(){this.$emit("addWorkContent")}},watch:{workContentId:function(t){t&&(this.activeWorkContentIndex=this.workContentList.findIndex(function(e){return e.id===t}))}}},u={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"work-content-list"},[t.showWorkContentList.length?n("ul",t._l(t.showWorkContentList,function(e,a){return n("li",{key:e.id,class:["ellipsis",{active:t.activeWorkContentIndex===a}],attrs:{title:e.name,"data-id":e.id},on:{click:function(e){return t.changeWorkContent(a)}}},[t._v(t._s(e.name))])}),0):t._e(),t._v(" "),n("el-button",{staticClass:"add-btn",on:{click:t.addWorkContent}},[n("i",{staticClass:"el-icon-circle-plus-outline"}),t._v(" "),n("span",[t._v("添加工作内容")])])],1)},staticRenderFns:[]};var d=n("VU/8")(l,u,!1,function(t){n("y35g")},"data-v-47225c4e",null).exports,p=n("//Fk"),m=n.n(p),f=n("mvHQ"),v=n.n(f);function h(t){return t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate()}var g={name:"RemindSettings",props:{data:{type:Object,default:function(){return{}}}},data:function(){return{reminderCycle:[{label:"日",value:1},{label:"周",value:4},{label:"月",value:3}],weekOptions:[{label:"周一",value:1},{label:"周二",value:2},{label:"周三",value:3},{label:"周四",value:4},{label:"周五",value:5},{label:"周六",value:6},{label:"周日",value:7}],pickerOptions:{shortcuts:[{text:"第一天",onClick:function(t){var e=new Date,n=h(new Date(e.getFullYear(),e.getMonth(),1));t.$emit("pick",n)}},{text:"最后一天",onClick:function(t){var e=new Date,n=h(new Date(e.getFullYear(),e.getMonth()+1,0));t.$emit("pick",n)}}]},units:[{label:"天",value:1},{label:"周",value:4},{label:"月",value:3}]}},computed:{monthOptions:function(){for(var t=[{label:"最后一天",value:-1}],e=1;e<=31;e++)t.push({label:e+"日",value:e});return t},row:{get:function(){return this.data},set:function(t){this.$emit("update:data",t)}}}},b={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-radio-group",{staticClass:"flex flex-align-center justify-between radio-group flex-wrap",staticStyle:{width:"100%"},model:{value:t.row.type,callback:function(e){t.$set(t.row,"type",e)},expression:"row.type"}},[n("el-radio",{staticClass:"flex-1",attrs:{label:1,tabindex:1}},[n("span",{staticStyle:{"margin-right":"10px","font-size":"14px",color:"#3C3C3C"}},[t._v("每")]),t._v(" "),n("el-select",{staticClass:"mr-10",staticStyle:{width:"100px"},model:{value:t.row.daytype,callback:function(e){t.$set(t.row,"daytype",e)},expression:"row.daytype"}},t._l(t.reminderCycle,function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1),t._v(" "),4===t.row.daytype?n("el-select",{staticClass:"mr-10 date",attrs:{multiple:"",placeholder:"请选择"},model:{value:t.row.dayshow,callback:function(e){t.$set(t.row,"dayshow",e)},expression:"row.dayshow"}},t._l(t.weekOptions,function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1):t._e(),t._v(" "),3===t.row.daytype?n("el-select",{staticClass:"mr-10 date",attrs:{clearable:"",multiple:"",placeholder:"请选择"},model:{value:t.row.dayshow,callback:function(e){t.$set(t.row,"dayshow",e)},expression:"row.dayshow"}},t._l(t.monthOptions,function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1):t._e(),t._v(" "),n("el-time-picker",{staticStyle:{width:"200px"},attrs:{format:"HH:mm","value-format":"HH:mm","is-range":"","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间",placeholder:"选择时间范围"},model:{value:t.row.timecon,callback:function(e){t.$set(t.row,"timecon",e)},expression:"row.timecon"}})],1),t._v(" "),n("el-radio",{staticClass:"flex-1",attrs:{label:2,tabindex:2}},[n("span",{staticStyle:{"margin-right":"10px"}},[t._v("每隔")]),t._v(" "),n("el-input-number",{staticClass:"mr-10",attrs:{step:1,min:1,"controls-position":"right"},model:{value:t.row.daynum,callback:function(e){t.$set(t.row,"daynum",e)},expression:"row.daynum"}}),t._v(" "),n("el-select",{staticClass:"unit mr-10",staticStyle:{width:"100px"},model:{value:t.row.daytype2,callback:function(e){t.$set(t.row,"daytype2",e)},expression:"row.daytype2"}},t._l(t.units,function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1),t._v(" "),n("el-time-picker",{staticStyle:{width:"200px"},attrs:{format:"HH:mm","value-format":"HH:mm","is-range":"","range-separator":"-","start-placeholder":"开始时间","end-placeholder":"结束时间",placeholder:"选择时间范围"},model:{value:t.row.timecon2,callback:function(e){t.$set(t.row,"timecon2",e)},expression:"row.timecon2"}})],1)],1)],1)},staticRenderFns:[]};var k=n("VU/8")(g,b,!1,function(t){n("2Nj6")},"data-v-b82acede",null).exports,C=n("mW/y"),x={name:"",tips:"",type:1,daytype:1,dayshow:[],timecon:["",""],timecon2:["",""],daynum:"",is_use:1,edit:!0},w={name:"WorkContentCard",components:{RemindSettings:k},inject:["activeIndexFn","getWorkContentList","setWorkContentId"],props:{card:{type:Object,default:function(){return{}}}},data:function(){return{unitsMap:{1:{unit1:"日",unit2:"天"},4:{unit1:"周",unit2:"周"},3:{unit1:"月",unit2:"月"}},tableData:[],oldData:[],loading:!1,localCard:null}},created:function(){this.localCard=JSON.parse(v()(this.card))},methods:{addWorkItem:function(){if(this.tableData.length&&!this.tableData[this.tableData.length-1].name)return void this.$message({message:"请先填写工作内容名称",type:"warning"});this.$set(this.tableData,this.tableData.length,JSON.parse(v()(x)))},updateCardName:function(t){this.$set(this.localCard,"name",t)},getUnit:function(t,e){return t&&e&&this.unitsMap[t]&&this.unitsMap[t]["unit"+e]||""},getDate:function(t,e){if(!t||!e||!e.length)return"";e=e.split(",");var n={1:"周一",2:"周二",3:"周三",4:"周四",5:"周五",6:"周六",7:"周日"};return 1===t?"":4===t?e.map(function(t){return n[t]}).join("、"):3===t?e.map(function(t){return""+t=="-1"?"最后一天":t+"日"}).join("、"):""},getTips:function(t){return 1===t.type?"每"+this.getUnit(t.daytype,1)+" &nbsp; "+this.getDate(t.daytype,t.dayshow)+" &nbsp; "+t.timecon:"每隔 "+t.daynum+" "+this.getUnit(t.daytype,2)+" &nbsp; "+t.timecon},initTable:function(){var t=this;if(!this.localCard||!this.localCard.list)return[];var e=JSON.parse(v()(this.localCard.list));return e&&e.length?e.map(function(e){var n=JSON.parse(v()(e)),a=n.timecon?n.timecon.split("-"):["",""],r=n.daytype,i=void 0;return n.dayshow&&(i=n.dayshow.split(",")).length&&(i=i.map(function(t){return parseInt(t)})),s()({},n,{tips:t.getTips(n),timecon:1===n.type?a:"",timecon2:2===n.type?a:"",daytype:1===n.type?r:"",daytype2:2===n.type?r:"",dayshow:i,edit:n.edit||!1})}):[]},saveWorkContent:function(){var t=this;return c()(r.a.mark(function e(){var n,a;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n={NO_TITLE:"请输入工作内容名称",NO_WORK_CONTENT:"请添加工作内容",NO_FIRST_CONTENT_TITLE:"请输入操作内容"},t.localCard.name){e.next=3;break}return e.abrupt("return",t.$message({message:n.NO_TITLE,type:"warning"}));case 3:if(0!==!t.tableData.length){e.next=5;break}return e.abrupt("return",t.$message({message:n.NO_WORK_CONTENT,type:"warning"}));case 5:if(t.checkItem(t.tableData[0])){e.next=8;break}return e.abrupt("return");case 8:return e.prev=8,t.$emit("update:card",JSON.parse(v()(t.localCard))),e.next=12,Object(C.d)({name:t.localCard.name,gw_id:t.activeIndexFn()});case 12:if(200!==(a=e.sent).code){e.next=23;break}if(t.$message.success("保存成功"),!t.tableData.length){e.next=18;break}return e.next=18,m.a.all(t.tableData.map(function(){var e=c()(r.a.mark(function e(n,i){return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",Object(C.b)(t.getSaveItem(s()({},n,{jobcontype:a.data.id}))));case 1:case"end":return e.stop()}},e,t)}));return function(t,n){return e.apply(this,arguments)}}()));case 18:return e.next=20,t.getWorkContentList();case 20:t.setWorkContentId(a.data.id),e.next=24;break;case 23:t.$message.error(a.msg);case 24:return e.prev=24,t.localCard.list.forEach(function(t){t.edit=!1}),t.localCard.edit&&(t.localCard.edit=!1),e.finish(24);case 28:case"end":return e.stop()}},e,t,[[8,,24,28]])}))()},delWorkContent:function(){var t=this;return c()(r.a.mark(function e(){return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t.localCard.id){e.next=2;break}return e.abrupt("return",t.$listeners.delWorkContent());case 2:return e.prev=2,t.loading=!0,e.next=6,Object(C.i)({id:t.localCard.id});case 6:if(200!==e.sent.code){e.next=11;break}return t.$message.success("删除成功"),e.next=11,t.getWorkContentList();case 11:e.next=16;break;case 13:e.prev=13,e.t0=e.catch(2),console.log(e.t0,"e");case 16:return e.prev=16,t.loading=!1,e.finish(16);case 19:case"end":return e.stop()}},e,t,[[2,13,16,19]])}))()},getRowStyle:function(t){var e=t.row;return this.localCard.edit||e.edit?{background:"#F6F7FB"}:{background:"#FFFFFF"}},resetItem:function(t){var e=s()({},JSON.parse(v()(x)));this.$set(this.tableData,t,e),this.oldData[t]||this.$set(this.oldData,t,JSON.parse(v()(e)))},resetItemToCardListItem:function(t){if(this.oldData&&this.oldData[t]){var e=s()({},JSON.parse(v()(this.oldData[t])),{edit:!0});this.$set(this.tableData,t,e)}else this.resetItem(t)},checkItem:function(t){var e="请输入操作内容",n="请选择提醒类型",a="请选择提醒周期",r="请选择提醒日期",i="请选择提醒时间",s="请输入间隔天数",o=t.name,c=t.type,l=t.daytype,u=t.dayshow,d=t.timecon,p=t.daynum,m=t.daytype2,f=t.timecon2;if(!o)return this.$message({message:e,type:"warning"}),!1;if(!c)return this.$message({message:n,type:"warning"}),!1;if(1===c){if(!l)return this.$message({message:a,type:"warning"}),!1}else if(!m)return this.$message({message:a,type:"warning"}),!1;if(1===c){if(!(1==+l||u&&u.length))return this.$message({message:r,type:"warning"}),!1}else if(!p)return this.$message({message:s,type:"warning"}),!1;if(1===c){if(!(d&&d.length&&d[0]&&d[1]))return this.$message({message:i,type:"warning"}),!1}else if(!(f&&f.length&&f[0]&&f[1]))return this.$message({message:i,type:"warning"}),!1;return!0},getSaveItem:function(t){var e=t.name,n=t.type,a=t.daytype,r=t.dayshow,i=t.timecon,s=t.daynum,o=t.daytype2,c=t.timecon2;return{qltype:3,gw_id:this.activeIndexFn(),jobcontype:this.localCard.id||t.jobcontype,name:e,type:n,daytype:1===n?a:o,dayshow:1===n&&r?r.join(","):"1",timecon:1===n?i.join("-"):c.join("-"),daynum:1===n?1:s}},updateWorkItem:function(t){var e=this;return c()(r.a.mark(function n(){var a,i;return r.a.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(e.checkItem(t)){n.next=3;break}return n.abrupt("return");case 3:return a=e.getSaveItem(t),n.prev=4,t.loading=!0,i=void 0,t.id?(a.id=t.id,i=C.k):i=C.b,n.next=10,i(a);case 10:if(200!==n.sent.code){n.next=15;break}return e.$message.success("保存成功"),n.next=15,e.getWorkContentList();case 15:n.next=20;break;case 17:n.prev=17,n.t0=n.catch(4),console.log(n.t0);case 20:return n.prev=20,t.loading=!1,t.edit&&(t.edit=!1),n.finish(20);case 24:case"end":return n.stop()}},n,e,[[4,17,20,24]])}))()},saveWorkItem:function(t){t.edit?this.updateWorkItem(t):t.edit=!0},cancelWorkItem:function(t){var e=JSON.parse(v()(this.localCard.list[t]));this.$set(this.localCard.list,t,e),this.tableData=this.initTable()},delWorkItem:function(t,e){var n=this;return c()(r.a.mark(function a(){var i;return r.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(console.log("🚀 ~ delWorkItem ~ row:",t),i=t.id){a.next=7;break}return n.localCard.list.splice(e,1),n.tableData=n.initTable(),n.$emit("update:card",JSON.parse(v()(n.localCard))),a.abrupt("return");case 7:if(!t.delLoading){a.next=9;break}return a.abrupt("return");case 9:return a.prev=9,t.delLoading=!0,a.next=13,Object(C.g)({id:i});case 13:if(200!==a.sent.code){a.next=18;break}return n.$message.success("删除成功"),a.next=18,n.getWorkContentList();case 18:a.next=23;break;case 20:a.prev=20,a.t0=a.catch(9),console.log(a.t0);case 23:return a.prev=23,t.delLoading=!1,a.finish(23);case 26:case"end":return a.stop()}},a,n,[[9,20,23,26]])}))()},changeStatus:function(t,e){var n=this;return c()(r.a.mark(function a(){var i;return r.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(t.id){a.next=2;break}return a.abrupt("return");case 2:return a.prev=2,t.loading=!0,a.next=6,Object(C.e)({id:t.id,status:e});case 6:if(200!==(i=a.sent).code){a.next=13;break}return n.$message.success("操作成功"),a.next=11,n.getWorkContentList();case 11:a.next=15;break;case 13:n.$message.error(i.msg),t.is_use=!e;case 15:a.next=21;break;case 17:a.prev=17,a.t0=a.catch(2),console.log(a.t0),t.is_use=!e;case 21:return a.prev=21,t.loading=!1,a.finish(21);case 24:case"end":return a.stop()}},a,n,[[2,17,21,24]])}))()},changeWorkContentStatus:function(t,e){var n=this;return c()(r.a.mark(function a(){var i;return r.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(t.id){a.next=2;break}return a.abrupt("return");case 2:return console.log("🚀 ~ changeWorkContentStatus ~ value: ",e),a.prev=3,t.loading=!0,a.next=7,Object(C.G)({id:t.id,status:e});case 7:if(200!==(i=a.sent).code){a.next=14;break}return n.$message.success("操作成功"),a.next=12,n.getWorkContentList();case 12:a.next=16;break;case 14:n.$message.error(i.msg),t.is_use=!e;case 16:a.next=22;break;case 18:a.prev=18,a.t0=a.catch(3),console.log(a.t0),t.is_use=!e;case 22:return a.prev=22,t.loading=!1,a.finish(22);case 25:case"end":return a.stop()}},a,n,[[3,18,22,25]])}))()},updateWorkContentName:function(){var t=this;return c()(r.a.mark(function e(){var n,a,i;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t.card.name!==t.localCard.name){e.next=4;break}return t.$message.warning("工作内容名称未改变"),t.$set(t.localCard,"nameEdit",!1),e.abrupt("return");case 4:if(!t.localCard.nameEditLoading){e.next=6;break}return e.abrupt("return");case 6:if(t.$set(t.localCard,"nameEdit",!1),e.prev=7,t.localCard.name){e.next=11;break}return t.$message.error("请输入工作内容名称"),e.abrupt("return");case 11:return t.$set(t.localCard,"nameEditLoading",!0),e.next=14,Object(C.l)({id:t.localCard.id,name:t.localCard.name});case 14:if(n=e.sent,a=n.code,i=n.msg,200!==a){e.next=23;break}return t.$message.success("编辑成功"),e.next=21,t.getWorkContentList();case 21:e.next=24;break;case 23:t.$message.error(i);case 24:e.next=30;break;case 26:e.prev=26,e.t0=e.catch(7),console.log(e.t0),t.$message.error("编辑失败");case 30:return e.prev=30,t.$set(t.localCard,"nameEditLoading",!1),e.finish(30);case 33:case"end":return e.stop()}},e,t,[[7,26,30,33]])}))()}},watch:{card:{handler:function(t){this.localCard=JSON.parse(v()(t)),this.tableData=this.initTable(),this.oldData=JSON.parse(v()(this.tableData))},immediate:!0,deep:!0}}},y={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{class:["card",{edit:t.localCard.edit}],attrs:{"data-id":t.localCard.id}},[n("div",{staticClass:"title flex-between flex-align-center"},[n("div",[t.localCard.edit?n("el-input",{staticClass:"work-title",attrs:{placeholder:"请输入工作内容名称",size:"small"},model:{value:t.localCard.name,callback:function(e){t.$set(t.localCard,"name",e)},expression:"localCard.name"}}):n("div",{staticClass:"title-box flex justify-start flex-align-center"},[t.localCard.nameEdit?[n("el-input",{staticClass:"work-title",attrs:{placeholder:"请输入工作内容名称",size:"small"},on:{blur:t.updateWorkContentName},model:{value:t.localCard.name,callback:function(e){t.$set(t.localCard,"name",e)},expression:"localCard.name"}}),t._v(" "),n("el-button",{staticClass:"save-btn",attrs:{type:"text"},on:{click:t.updateWorkContentName}},[n("i",{staticClass:"el-icon-circle-check"})])]:[n("h3",[t._v(t._s(t.localCard.name))]),t._v(" "),n("el-button",{staticClass:"edit-btn",attrs:{type:"text"},on:{click:function(e){t.localCard.nameEdit=!0}}},[n("i",{staticClass:"el-icon-edit-outline"})])],t._v(" "),n("el-switch",{staticStyle:{"margin-left":"10px"},attrs:{"active-value":1,"inactive-value":0,"active-color":"#4D80FF","inactive-color":"#C0C4CC"},on:{change:function(e){return t.changeWorkContentStatus(t.localCard,e)}},model:{value:t.localCard.status,callback:function(e){t.$set(t.localCard,"status",e)},expression:"localCard.status"}}),t._v(" "),n("span",{staticClass:"switch-tips"},[t._v("\n          开启后此项工作内容方可生效\n        ")])],2)],1),t._v(" "),n("div",[t.localCard.edit?n("el-button",{staticClass:"save",attrs:{type:"primary"},on:{click:t.saveWorkContent}},[t._v("保存")]):t._e(),t._v(" "),n("el-popconfirm",{staticClass:"del-popconfirm",attrs:{title:"确定删除此工作内容吗？","confirm-button-text":"确定","cancel-button-text":"取消"},on:{confirm:t.delWorkContent}},[n("el-button",{staticClass:"del",attrs:{slot:"reference"},slot:"reference"},[t._v("删除此工作内容")])],1)],1)]),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:t.localCard.status,expression:"localCard.status"}],staticClass:"table-wrap"},[n("el-table",{attrs:{data:t.tableData,"header-cell-class-name":"header-cell-class","cell-class-name":"cell-class","row-style":t.getRowStyle}},[n("el-table-column",{attrs:{prop:"name",label:"操作内容","scoped-slot":""},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("div",{staticStyle:{width:"90%"}},[t.localCard.edit||a.edit?n("el-input",{staticClass:"work-title",attrs:{placeholder:"请输入操作内容"},on:{input:function(e){return t.$forceUpdate()}},model:{value:a.name,callback:function(e){t.$set(a,"name",e)},expression:"row.name"}}):[t._v("\n              "+t._s(a.name)+"\n            ")]],2)]}}])},[n("template",{slot:"header"},[n("div",{staticClass:"flex-align-center"},[n("span",[t._v("操作内容")]),t._v(" "),n("el-button",{staticClass:"add-work-content",attrs:{type:"primary"},on:{click:t.addWorkItem}},[n("i",{staticClass:"el-icon-circle-plus-outline"}),t._v(" "),n("span",[t._v("添加")])])],1)])],2),t._v(" "),n("el-table-column",{attrs:{prop:"time",label:"时间安排",width:"600px"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[t.localCard.edit||a.edit?n("remind-settings",{attrs:{data:a}}):n("span",{domProps:{innerHTML:t._s(a.tips)}})]}}])}),t._v(" "),n("el-table-column",{attrs:{prop:"is_use",label:"状态",width:"100px"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[n("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-color":"#4D80FF","inactive-color":"#C0C4CC"},on:{change:function(e){return t.changeStatus(a,e)}},model:{value:a.is_use,callback:function(e){t.$set(a,"is_use",e)},expression:"row.is_use"}})]}}])}),t._v(" "),n("el-table-column",{attrs:{prop:"action",label:"操作",width:"140",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row,r=e.$index;return[t.localCard.id||a.id?t.localCard.id&&!a.id?[n("el-button",{staticClass:"option-btn",attrs:{type:"text",loading:a.loading},on:{click:function(e){return t.saveWorkItem(a)}}},[t._v("\n              "+t._s(a.edit?"保存":"编辑")+"\n            ")]),t._v(" "),a.edit?n("el-button",{staticClass:"option-btn",attrs:{type:"text"},on:{click:function(e){return t.cancelWorkItem(r)}}},[t._v("取消")]):t._e()]:[n("el-button",{staticClass:"option-btn",attrs:{type:"text",loading:a.loading},on:{click:function(e){return t.saveWorkItem(a)}}},[t._v("\n              "+t._s(a.edit?"保存":"编辑")+"\n            ")]),t._v(" "),a.edit?[n("el-button",{staticClass:"option-btn",attrs:{type:"text"},on:{click:function(e){return t.resetItemToCardListItem(r)}}},[t._v("重置")]),t._v(" "),n("el-button",{staticClass:"option-btn",attrs:{type:"text"},on:{click:function(e){return t.cancelWorkItem(r)}}},[t._v("取消")])]:[n("el-popconfirm",{attrs:{title:"确定删除此操作内容吗？","confirm-button-text":"确定","cancel-button-text":"取消"},on:{confirm:function(e){return t.delWorkItem(a,r)}}},[n("el-button",{staticClass:"option-btn",attrs:{slot:"reference",type:"text"},slot:"reference"},[t._v("删除")])],1)]]:[0===r?n("el-button",{staticClass:"option-btn",attrs:{type:"text"},on:{click:function(e){return t.resetItem(r)}}},[t._v("重置")]):n("el-popconfirm",{attrs:{title:"确定删除此操作内容吗？","confirm-button-text":"确定","cancel-button-text":"取消"},on:{confirm:function(e){return t.delWorkItem(a,r)}}},[n("el-button",{staticClass:"option-btn",attrs:{slot:"reference",type:"text"},slot:"reference"},[t._v("删除")])],1)]]}}])})],1)],1)])},staticRenderFns:[]};var _={name:"workContentDetail",components:{WorkContentCard:n("VU/8")(w,y,!1,function(t){n("rnPC")},"data-v-5b56382e",null).exports},inject:["workContentListFn","workContentIdFn"],props:{scrolling:{type:Boolean,default:!1}},data:function(){return{currentVisibleIndex:0,debouncedScrollHandler:null}},computed:{workContentId:function(){return this.workContentIdFn()}},methods:{delWorkContent:function(t){this.workContentListFn().splice(t,1)},handleScroll:function(){for(var t=document.querySelector("#work-content"),e=document.querySelectorAll(".card"),n=t.getBoundingClientRect(),a=0;a<e.length;a++){var r=e[a].getBoundingClientRect(),i=r.top-n.top,s=r.bottom-n.top;if(i>=0&&s<=n.height){this.currentVisibleIndex=a;break}}this.$listeners.changeWorkContent(this.workContentListFn()[this.currentVisibleIndex])},debounce:function(t,e){var n=void 0;return function(){for(var a=arguments.length,r=Array(a),i=0;i<a;i++)r[i]=arguments[i];var s=this;clearTimeout(n),n=setTimeout(function(){return t.apply(s,r)},e)}}},mounted:function(){var t=this;this.debouncedScrollHandler=this.debounce(this.handleScroll,300),this.$nextTick(function(){document.querySelector("#work-content").addEventListener("scroll",t.debouncedScrollHandler)})},beforeDestroy:function(){var t=document.querySelector("#work-content");t&&t.removeEventListener("scroll",this.debouncedScrollHandler)},watch:{workContentId:{handler:function(t,e){var n=document.querySelector('[data-id="'+t+'"]');n&&n.scrollIntoView({behavior:"smooth",block:"nearest",inline:"nearest"})}}}},D={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"work-content-detail"},[t.workContentListFn().length?t._l(t.workContentListFn(),function(e,n){return a("work-content-card",{key:e.id,staticClass:"card-wrap",attrs:{"card-index":n,card:e},on:{"update:card":function(t){e=t},delWorkContent:t.delWorkContent}})}):a("el-empty",{attrs:{image:n("X7GI"),"image-size":400}})],2)},staticRenderFns:[]};var I=n("VU/8")(_,D,!1,function(t){n("HURl")},"data-v-48f15324",null).exports,S=n("8JQp"),$=n("M+KZ"),W=n("pI5c"),L={name:"UpdateMain",props:{dialogVisible:{type:Boolean,default:!1},operating:{type:String,default:""},mainData:{type:Object,default:function(){return{operating:"",industry:[],company_size:"",business_model:[],development:"",attendance_method:[],objectives:""}}}},data:function(){return{formData:{operating:"",industry:[],company_size:""},formDataDesc:{business_model:[],development:"",attendance_method:[],objectives:""},rules:{operating:[{required:!0,message:"请输入企业主营项目",trigger:"blur"},{min:15,message:"最少输入 15 个字符",trigger:"blur"}],industry:[{required:!0,message:"请选择所属行业",trigger:"change"}],company_size:[{required:!0,message:"请输入企业员工数量",trigger:"blur"}]},loading:!1,options:{ORGANIZATION_TRADE:[]},business_models:[{value:"1",label:"B2B"},{value:"2",label:"B2C"},{value:"3",label:"平台型"}],developments:[{value:"1",label:"初创期"},{value:"2",label:"扩张期"},{value:"3",label:"成熟期"}],attendance_methods:[{value:"1",label:"固定时间"},{value:"2",label:"排班制"},{value:"3",label:"自由时间"}]}},computed:{_dialogVisible:{get:function(){return this.dialogVisible},set:function(t){this.$emit("update:dialogVisible",t)}},visibleValue:function(){return{visible:this.dialogVisible,value:this.operating}}},watch:{visibleValue:{immediate:!0,handler:function(t){this.formData=s()({},this.formData,this.mainData),this.formDataDesc=s()({},this.formDataDesc,this.mainData),this.formData.operating=t.value}}},methods:{cancel:function(){this.$refs.form.resetFields(),this.$emit("update:dialogVisible",!1)},submitForm:function(t){var e,n=this;this.$refs[t].validate((e=c()(r.a.mark(function t(e){var a,i,s,o,c;return r.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!e){t.next=20;break}return t.prev=1,a=n.formDataDesc.business_model.join(","),i=n.formDataDesc.attendance_method.join(","),s=n.formDataDesc.industry.join(","),o={operating:n.formData.operating,industry:s,company_size:n.formData.company_size,business_model:a,development:n.formDataDesc.development,attendance_method:i,objectives:n.formDataDesc.objectives},console.log("🚀 ~  ~ postData: ",o),t.next=9,Object(C.o)(o);case 9:200===(c=t.sent).code?(n.$message.success("保存成功"),n.$emit("update:dialogVisible",!1),n.$emit("update:operating",n.formData.operating),n.$emit("refresh",n.formData)):n.$message.error(c.msg),t.next=17;break;case 13:t.prev=13,t.t0=t.catch(1),console.log(t.t0),n.$message.error("保存失败");case 17:return t.prev=17,n.loading=!1,t.finish(17);case 20:case"end":return t.stop()}},t,n,[[1,13,17,20]])})),function(t){return e.apply(this,arguments)}))},getTreeData:function(t){for(var e=0;e<t.length;e++)t[e].child.length<1?t[e].child=void 0:this.getTreeData(t[e].child);return t},getCompanyIndustry:function(){var t=this;return c()(r.a.mark(function e(){var n,a,i;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.options.ORGANIZATION_TRADE=[],e.prev=1,e.next=4,Object(W.s)();case 4:n=e.sent,a=n.code,i=n.data,t.options.ORGANIZATION_TRADE=200===a?t.getTreeData(i):[],e.next=12;break;case 9:e.prev=9,e.t0=e.catch(1),console.log(e.t0);case 12:case"end":return e.stop()}},e,t,[[1,9]])}))()},toggleSelection:function(t){this.formDataDesc.development===t?this.formDataDesc.development="":this.formDataDesc.development=t}},mounted:function(){this.getCompanyIndustry()}},O={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-dialog",{staticClass:"new-task",attrs:{visible:t._dialogVisible,width:"68%"},on:{"update:visible":function(e){t._dialogVisible=e}}},[n("div",{staticClass:"dialog-header",attrs:{slot:"title"},slot:"title"},[n("span",{staticClass:"title"},[t._v("企业描述")]),t._v(" "),n("span",{staticClass:"title-tips"},[t._v("请务必确保信息真实准确，以便于AI更好的助力企业运营，实现企业智能化管理")])]),t._v(" "),n("el-form",{ref:"form",attrs:{model:t.formData,rules:t.rules,"label-position":"left"}},[n("el-form-item",{attrs:{label:"所属行业",prop:"industry"}},[n("el-cascader",{attrs:{options:t.options.ORGANIZATION_TRADE,props:{label:"title",children:"child",value:"id"},clearable:""},model:{value:t.formData.industry,callback:function(e){t.$set(t.formData,"industry",e)},expression:"formData.industry"}})],1),t._v(" "),n("el-form-item",{attrs:{label:"企业规模",prop:"company_size"}},[n("el-input",{staticStyle:{width:"217px","margin-right":"10px"},attrs:{placeholder:"请输入企业规模",clearable:""},model:{value:t.formData.company_size,callback:function(e){t.$set(t.formData,"company_size",e)},expression:"formData.company_size"}}),t._v("人\n    ")],1),t._v(" "),n("el-form-item",{attrs:{label:"主营项目或范围",prop:"operating"}},[n("el-input",{staticStyle:{"margin-top":"10px"},attrs:{type:"textarea",autosize:{minRows:3,maxRows:6},placeholder:"请输入企业主营项目",clearable:"",min:15},model:{value:t.formData.operating,callback:function(e){t.$set(t.formData,"operating",e)},expression:"formData.operating"}})],1)],1),t._v(" "),n("div",{staticClass:"dialog-header",staticStyle:{"margin-top":"12px"}},[n("span",{staticClass:"title"},[t._v("补充描述")]),t._v(" "),n("span",{staticClass:"title-tips"},[t._v("填选后系统推荐将会更加精准")])]),t._v(" "),n("el-form",{ref:"formDesc",staticClass:"form-desc",staticStyle:{"margin-top":"25px"},attrs:{model:t.formDataDesc,"label-position":"left"}},[n("el-form-item",{attrs:{label:"商业模式（多选）",prop:"business_model","label-width":"180px"}},[n("el-checkbox-group",{model:{value:t.formDataDesc.business_model,callback:function(e){t.$set(t.formDataDesc,"business_model",e)},expression:"formDataDesc.business_model"}},t._l(t.business_models,function(e){return n("el-checkbox-button",{key:e.value,attrs:{label:e.value}},[t._v(t._s(e.label)+"\n        ")])}),1)],1),t._v(" "),n("el-form-item",{attrs:{label:"发展阶段",prop:"development","label-width":"180px"}},[n("div",{staticClass:"custom-checkbox-group"},t._l(t.developments,function(e){return n("div",{key:e.value,staticClass:"el-checkbox-button",class:{"is-checked":t.formDataDesc.development===e.value},on:{click:function(n){return t.toggleSelection(e.value)}}},[n("span",{staticClass:"el-checkbox-button__inner"},[t._v(t._s(e.label))])])}),0)]),t._v(" "),n("el-form-item",{attrs:{label:"企业考勤方式（多选）",prop:"attendance_method","label-width":"180px"}},[n("el-checkbox-group",{model:{value:t.formDataDesc.attendance_method,callback:function(e){t.$set(t.formDataDesc,"attendance_method",e)},expression:"formDataDesc.attendance_method"}},t._l(t.attendance_methods,function(e){return n("el-checkbox-button",{key:e.value,attrs:{label:e.value}},[t._v(t._s(e.label)+"\n        ")])}),1)],1),t._v(" "),n("el-form-item",{attrs:{label:"年度战略目标",prop:"objectives"}},[n("el-input",{staticStyle:{"margin-top":"10px"},attrs:{type:"textarea",autosize:{minRows:3,maxRows:6},placeholder:"填写您企业的市场拓展计划以及预期发展规模或企业发展需求(如:订单量实现20%增长率，企业制度整理更加完善，合同管理水平要进一步加强）",clearable:"",maxlength:1e3,"show-word-limit":""},model:{value:t.formDataDesc.objectives,callback:function(e){t.$set(t.formDataDesc,"objectives",e)},expression:"formDataDesc.objectives"}})],1)],1),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{staticClass:"footer-btn cancel-btn",on:{click:function(e){return t.cancel(!1)}}},[t._v("取消")]),t._v(" "),n("el-button",{staticClass:"footer-btn save-btn",attrs:{type:"primary",loading:t.loading},on:{click:function(e){return t.submitForm("form")}}},[t._v("保存\n    ")])],1)],1)},staticRenderFns:[]};var F={name:"workContentSettingsIndex",components:{UpdateMain:n("VU/8")(L,O,!1,function(t){n("894J")},"data-v-2fe47dd5",null).exports,Header:S.a,WorkContentDetail:I,WorkContent:d,Layout:$.a},data:function(){return{activeIndex:"0",menuList:[],workContentList:[],loading:!1,workContentId:"",timer:null,progress:0,createLoading:!1,progressInfo:"",isShowTips:!1,isShowMain:!1,operating:"",isRecreate:!0,isClickPosition:!1,loading1:!1,isOperating:!0,mainData:{operating:"",industry:[],company_size:"",business_model:[],development:"",attendance_method:[],objectives:""}}},methods:{toPositionSettings:function(){this.$router.push({path:"/console/positionSettings"})},getWorkContentList:function(){var t=this,e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return c()(r.a.mark(function n(){var a;return r.a.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,e&&(t.loading=!0),n.next=4,Object(C.x)({gw_id:t.activeIndex});case 4:a=n.sent,console.log(a,"res1"),200===a.code&&(t.workContentList=a.data.map(function(t){return s()({},t,{edit:!1,nameEdit:!1,nameEditLoading:!1,list:t.list.map(function(t){return s()({},t,{edit:!1})})})})),n.next=12;break;case 9:n.prev=9,n.t0=n.catch(0),console.log(n.t0);case 12:return n.prev=12,t.loading=!1,n.finish(12);case 15:case"end":return n.stop()}},n,t,[[0,9,12,15]])}))()},fn:function(){return this.aiCreateProgress(),this.fn},getMenuList:function(){var t=this,e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return c()(r.a.mark(function a(){var i;return r.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.prev=0,e&&(t.loading=!0),a.next=4,Object(C.t)();case 4:if(200!==(i=a.sent).code){a.next=17;break}if(t.menuList=i.data,!(i.data.length>0)){a.next=14;break}return t.activeIndex=""+i.data[0].id,t.isOperating=!0,a.next=12,t.getWorkContentList(e);case 12:a.next=16;break;case 14:return a.next=16,t.checkOperating();case 16:n&&(t.timer=setInterval(t.fn(),3e4));case 17:a.next=22;break;case 19:a.prev=19,a.t0=a.catch(0),console.log(a.t0);case 22:return a.prev=22,t.loading=!1,a.finish(22);case 25:case"end":return a.stop()}},a,t,[[0,19,22,25]])}))()},handleSelect:function(t,e){this.activeIndex=t,this.getWorkContentList()},activeIndexFn:function(){return this.activeIndex},workContentListFn:function(){return this.workContentList.map(function(t){return s()({},t,{status:"status"in t?t.status:1})})},changeWorkContent:function(t){t&&(this.workContentId=t.id)},addWorkContent:function(){if(this.workContentList.length&&!this.workContentList[0].name)return;this.workContentList.unshift({name:"",list:[{name:"",tips:"",type:1,daytype:1,dayshow:"",timecon:"-",timecon2:"-",daynum:"",is_use:1,edit:!0}],edit:!0,nameEdit:!0,nameEditLoading:!1}),setTimeout(function(){var t=document.querySelector("#work-content"),e=document.querySelectorAll("#work-content .card")[0],n=t.scrollTop,a=e.offsetTop-t.offsetTop-(t.clientHeight/2-e.clientHeight/2),r=Date.now();requestAnimationFrame(function e(){var i=Date.now()-r,s=Math.min(i/300,1);t.scrollTop=n+(a-n)*s,i<300&&requestAnimationFrame(e)})},100)},workContentIdFn:function(){return this.workContentId},setWorkContentId:function(t){this.workContentId=t},aiCreate:function(){var t=this;this.isShowTips?this.$confirm("是否修改企业主营项目？","提示",{confirmButtonText:"去修改",cancelButtonText:"直接生成",confirmButtonClass:"edit-btn",distinguishCancelAndClose:!0,type:"warning"}).then(function(){t.isRecreate=!0,setTimeout(function(){t.isShowMain=!0},200)}).catch(function(e){console.log("🚀 ~ aiCreate ~ action:",e),"cancel"===e&&t.aiCreatePosition()}):this.isShowMain=!0},aiCreatePosition:function(){var t=this;return c()(r.a.mark(function e(){return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.isClickPosition=!0,e.prev=1,t.createLoading=!0,e.next=5,Object(C.m)();case 5:200===e.sent.code?t.timer=setInterval(t.fn(),3e4):(t.progress=0,t.createLoading=!1),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),console.log(e.t0),t.createLoading=!1;case 13:return e.prev=13,t.isRecreate=!0,e.finish(13);case 16:case"end":return e.stop()}},e,t,[[1,9,13,16]])}))()},aiCreateProgress:function(){var t=this;return c()(r.a.mark(function e(){var n;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t.progress>=100)){e.next=4;break}return t.isOperating=!0,clearInterval(t.timer),e.abrupt("return");case 4:return e.prev=4,e.next=7,Object(C.n)({ack:+t.isClickPosition});case 7:if(200!==(n=e.sent).code){e.next=27;break}if(t.progress=n.data.percent,t.progressInfo=n.data.title,t.isClickPosition=!!n.data.ack,!(n.data.percent>=100)){e.next=23;break}if(t.isOperating=!0,t.progress=100,clearInterval(t.timer),t.createLoading=!1,100!==n.data.percent){e.next=21;break}if(!n.data.ack){e.next=21;break}return e.next=21,t.$confirm(n.data.title||"生成成功","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"success"});case 21:e.next=24;break;case 23:n.data.ack&&(t.createLoading=!0);case 24:t.setMainData(n),e.next=31;break;case 27:t.isOperating=!1,t.progress=0,clearInterval(t.timer),t.createLoading=!1;case 31:e.next=38;break;case 33:e.prev=33,e.t0=e.catch(4),console.log(e.t0),clearInterval(t.timer),t.createLoading=!1;case 38:case"end":return e.stop()}},e,t,[[4,33]])}))()},setMainData:function(t){this.isShowTips=!!t.data.operating,this.isOperating=!!this.menuList.length||!!t.data.operating,this.operating=t.data.operating;var e=t.data.industry.split(",");e.length>0&&(e=e.map(function(t){return+t})),this.mainData={operating:t.data.operating,industry:e,company_size:t.data.company_size,business_model:t.data.business_model,development:t.data.development[0],attendance_method:t.data.attendance_method,objectives:t.data.objectives},this.operating||(this.loading=!1)},refreshMain:function(){this.checkOperating(!1),this.isRecreate&&this.aiCreatePosition()},checkOperating:function(){var t=this,e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return c()(r.a.mark(function n(){var a;return r.a.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,e&&(t.loading=!0),n.next=4,Object(C.n)({ack:0});case 4:200===(a=n.sent).code?t.setMainData(a):t.isOperating=!1,n.next=13;break;case 8:n.prev=8,n.t0=n.catch(0),console.log(n.t0),t.isOperating=!1,t.loading=!1;case 13:case"end":return n.stop()}},n,t,[[0,8]])}))()}},provide:function(){return{workContentListFn:this.workContentListFn,activeIndexFn:this.activeIndexFn,getWorkContentList:this.getWorkContentList,workContentIdFn:this.workContentIdFn,setWorkContentId:this.setWorkContentId}},watch:{progress:{handler:function(t,e){t!==e&&this.getMenuList(!1,!1)}}},created:function(){var t=this;return c()(r.a.mark(function e(){return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.getMenuList(!0,!0);case 2:case"end":return e.stop()}},e,t)}))()},beforeDestroy:function(){clearInterval(this.timer)}},T={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("Layout",{staticStyle:{position:"relative"}},[a("template",{slot:"header"},[a("div",{staticClass:"flex flex-align-center"},[t.menuList.length?t._e():a("el-button",{staticClass:"self-primary-btn",attrs:{type:"primary"},on:{click:function(e){t.isRecreate=!1,t.isShowMain=!0}}},[t._v("\n        完善企业主营项目\n      ")]),t._v(" "),a("el-button",{staticClass:"self-primary-btn",attrs:{type:"primary"},on:{click:t.toPositionSettings}},[t._v("岗位分配与编辑")])],1)]),t._v(" "),a("template",{slot:"body"},[t.isOperating?a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[a("div",{staticClass:"nav"},[t.menuList.length?a("el-menu",{staticClass:"el-menu-demo",attrs:{"default-active":t.activeIndex,mode:"horizontal","text-color":"#000000","active-text-color":"#000000"},on:{select:t.handleSelect}},t._l(t.menuList,function(e){return a("el-menu-item",{key:e.id,attrs:{index:""+e.id}},[t._v(t._s(e.name))])}),1):a("div",{staticClass:"flex-justify-end flex-align-center",staticStyle:{height:"56px"}},[a("el-button",{staticClass:"self-primary-btn",attrs:{type:"primary"},on:{click:t.aiCreate}},[t._v("AI生成岗位")])],1)],1),t._v(" "),a("div",{staticClass:"work-content flex"},[a("div",{staticClass:"sidebar flex-column"},[a("h2",[t._v("工作内容")]),t._v(" "),a("div",{staticClass:"sidebar-menu flex-1"},[a("work-content",{on:{addWorkContent:t.addWorkContent,changeWorkContent:t.changeWorkContent}})],1)]),t._v(" "),a("div",{staticClass:"content",attrs:{id:"work-content"}},[a("work-content-detail",{on:{changeWorkContent:t.changeWorkContent}})],1)])]):a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"empty"},[a("el-empty",{staticStyle:{height:"100%"},attrs:{image:n("2Hfj"),"image-size":257}},[a("div",{attrs:{slot:"description"},slot:"description"},[a("p",{staticClass:"desc"},[t._v("为了使系统推荐的岗位操作与工作内容更贴近您企业的业务，"),a("span",{on:{click:function(e){t.isShowMain=!0}}},[t._v("请完善企业描述")])]),t._v(" "),a("p",{staticClass:"tips"},[t._v("\n            请务必确保信息真实准确，AI系统将基于您的描述进行智能分析，将更好的助力企业运营，实现企业智能化管理")])]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.isShowMain=!0}}},[t._v("立即完善")])],1)],1),t._v(" "),t.createLoading?a("div",{staticClass:"self-loading"},[a("div",{staticClass:"flex-column flex-align-center flex-justify-center w100 h100",staticStyle:{height:"100%"}},[a("div",{staticClass:"animbox"},[a("div"),t._v(" "),a("div"),t._v(" "),a("div"),t._v(" "),a("div"),t._v(" "),a("div")]),t._v(" "),a("el-progress",{staticClass:"flex flex-align-center",staticStyle:{width:"300px"},attrs:{percentage:t.progress}}),t._v(" "),a("div",{staticStyle:{"margin-top":"30px"}},[a("div",{staticStyle:{"text-align":"center",color:"#fff"}},[t._v(t._s(t.progressInfo))])])],1)]):t._e(),t._v(" "),a("update-main",{attrs:{"dialog-visible":t.isShowMain,operating:t.operating,"main-data":t.mainData},on:{"update:dialogVisible":function(e){t.isShowMain=e},"update:dialog-visible":function(e){t.isShowMain=e},"update:operating":function(e){t.operating=e},refresh:t.refreshMain}})],1)],2)},staticRenderFns:[]};var j=n("VU/8")(F,T,!1,function(t){n("OISS"),n("jURD")},"data-v-b75c1870",null);e.default=j.exports},"894J":function(t,e){},"8JQp":function(t,e,n){"use strict";var a={render:function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"title flex flex-align-center flex-between"},[e("h1",[this._v("管理智能体-工作内容设置")]),this._v(" "),this._t("default")],2)},staticRenderFns:[]};var r=n("VU/8")({name:"Header"},a,!1,function(t){n("avPf")},"data-v-985ae7f8",null);e.a=r.exports},HURl:function(t,e){},"M+KZ":function(t,e,n){"use strict";var a={name:"layout",components:{Header:n("8JQp").a},methods:{}},r={render:function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"work-content-settings-index"},[e("Header",[this._t("header")],2),this._v(" "),this.$slots.breadcrumb?e("div",{staticClass:"breadcrumb-wrap"},[this._t("breadcrumb")],2):this._e(),this._v(" "),e("div",{staticClass:"work-content-wrap"},[e("div",{staticClass:"work-card"},[this._t("body")],2)])],1)},staticRenderFns:[]};var i=n("VU/8")(a,r,!1,function(t){n("zUSS"),n("rT6D")},"data-v-52a05615",null);e.a=i.exports},OISS:function(t,e){},avPf:function(t,e){},jURD:function(t,e){},rT6D:function(t,e){},rnPC:function(t,e){},y35g:function(t,e){},zUSS:function(t,e){}});