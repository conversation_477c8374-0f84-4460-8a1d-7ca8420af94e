import{k as $,l as v,A as B,c as r,o as i,b as c,m as p,a as u,K as D,w as f,p as _,aJ as I,a6 as A,F as E,s as J,n as P,t as z,i as F,O as L,a7 as j,_ as G}from"./index-BBeD0eDz.js";import{_ as H}from"./input-delete-BxT9zbwn.js";const K={class:"title-tags"},R={class:"tags-container"},W={class:"tags-wrapper"},X=["onClick"],q={class:"tag-text"},Q=["onClick"],t=108,U=$({__name:"TitleTags",props:{tags:{type:Array,default:()=>[]},maxVisibleTags:{type:Number,default:4},modelValue:{type:Number,default:0},editable:{type:Boolean,default:!0},tagText:{type:String,default:"标题"},defaultData:{type:[String,Object],default:""}},emits:["update:modelValue","add","delete","update:tags"],setup(h,{emit:b}){const s=h,g=b,l=v({get:()=>s.tags,set:e=>{g("update:tags",e)}}),T=v({get:()=>s.modelValue,set:e=>{g("update:modelValue",e)}}),a=B(0),V=v(()=>s.editable),x=v(()=>a.value>0),y=v(()=>{const e=Math.max(0,l.value.length*t-s.maxVisibleTags*t);return a.value<e}),m=e=>{const o=e*t,n=a.value,k=n+s.maxVisibleTags*t;o<n?a.value=o:o+t>k&&(a.value=o-(s.maxVisibleTags-1)*t),a.value=Math.max(0,a.value)},w=()=>{x.value&&(a.value=Math.max(0,a.value-t))},C=()=>{if(y.value){const e=Math.max(0,l.value.length*t-s.maxVisibleTags*t);a.value=Math.min(e,a.value+t)}},S=e=>{T.value=e,g("update:modelValue",e),m(e)},M=()=>{s.editable&&(l.value.push(JSON.parse(JSON.stringify(s.defaultData))),m(l.value.length-1))},N=e=>{l.value.splice(e,1),m(l.value.length-1)};return(e,o)=>{const n=D;return i(),r("div",K,[c("div",R,[x.value?(i(),r("div",{key:0,class:"nav-arrow left-arrow",onClick:w},[u(n,null,{default:f(()=>[u(_(I))]),_:1})])):p("",!0),c("div",W,[c("div",{class:"tags-scroll-container",style:A({transform:`translateX(${-a.value}px)`})},[(i(!0),r(E,null,J(l.value,(k,d)=>(i(),r("div",{key:d,class:P(["tag-item",{active:T.value===d}]),onClick:O=>S(d)},[c("span",q,z(`${h.tagText}${d+1}`),1),c("span",{class:"tag-delete",onClick:F(O=>N(d),["stop"])},o[0]||(o[0]=[c("img",{src:H,alt:""},null,-1)]),8,Q)],10,X))),128))],4)]),y.value?(i(),r("div",{key:1,class:"nav-arrow right-arrow",onClick:C},[u(n,null,{default:f(()=>[u(_(L))]),_:1})])):p("",!0),V.value&&l.value.length<20?(i(),r("div",{key:2,class:"add-tag",onClick:M},[u(n,null,{default:f(()=>[u(_(j))]),_:1})])):p("",!0)])])}}}),ee=G(U,[["__scopeId","data-v-79663e49"]]);export{ee as T};
