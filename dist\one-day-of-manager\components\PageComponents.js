// 主界面
var mainPage = {
  props: [],
  components: {
    'pointer-view': pointerView
  },
  template: `
    <div class="flex-yx-center">
      <div class="main-title mb103 mt118">“<span style="color: #5183F6">总经理</span>的一天”</div>
      <div class="width640 mb102">
        <img src="./images/manager.png" alt="hr">
      </div>
      <pointer-view left="354" top="56" :is-btn="true" @click="$emit('click')">马上体验</pointer-view>
    </div>
  `
}

// 流程主页
var sectionPage = {
  props: ['data'],
  components: {
    'pointer-view': pointerView,
    'tip-view': tipView
  },
  template: `
    <div class="flex-yx-center">
      <template v-if="data.type == 'clock'">
        <div class="width150 mt526 mb42">
          <img :src="data.icon" alt="clock">
        </div>
        <pointer-view left="354" top="56" :is-btn="true" @click="$emit('click')">{{data.btnText}}</pointer-view>
        <tip-view>{{data.desc}}</tip-view>
      </template>
      <template v-else>
        <div class="icon-view width150 mt146 mb423">
          <img :src="data.icon" alt="clock">
          <div v-if="data.num" class="num-view">{{data.num}}</div>
        </div>
        <tip-view top="473">{{data.desc}}</tip-view>
        <pointer-view left="354" top="56" :is-btn="true" @click="$emit('click')">{{data.btnText}}</pointer-view>
      </template>
    </div>
  `
}

// 流程
var flowPage = {
  props: ['data'],
  components: {
    'pointer-view': pointerView,
    'tip-view': tipView
  },
  template: `
    <div class="flex-yx-center">
      <pointer-view :left="data.left" :top="data.top" :is-img="true" :img-width="526" :data="data" @click="$emit('click')">
      </pointer-view>
      <tip-view>{{data.desc}}</tip-view>
    </div>
  `
}

// 结束页
var endPage = {
  template: `
  <div class="end-view flex-yx-center">
    <div class="main-title mb90 mt209" style="color: #FFFFFF;">“总经理的一天”</div>
    <div :class="['tips-box', 'width570']" style="position: relative;">
      <div class="text-box px25 py28">资海云移动办公，企业管理者随时随地安排日程、掌控运营状态，有效提升企业管理效率，赋能决策。
      </div>
    </div>
    <div class="btn btn-plain mt120" @click="$emit('replay')">重新体验</div>
    <p class="end-tip mt32">您已完成“总经理的一天”体验</p>
    <div class="btn width600 mt114" @click="$emit('to-form')">立即咨询</div>
  </div>
  `
}

// 表单
var formPage = {
  data: function() {
    return {
      // 防止连点
      isSubmit: false,
      formData: {
        company: '',
        name: '',
        telephone: '',
        remarks: '“总经理的一天”'
      }
    }
  },
  methods: {
    checkVal (name, formValue) {
      let reg1 = /^\s*|\s*$/g
      if (reg1.test(formValue)) {
        this.formData[name] = formValue.replace(reg1, '')
      }
      // 表单中对应的值中除中文、英文、数字、空格、，、、《、》、【】、{}、[]以外的字符替换为空字符，把html标签和数据库语句也替换成空字符
      // 正则表达式：/[^\u4e00-\u9fa5a-zA-Z0-9]/g
      let reg = /[^\u4e00-\u9fa5a-zA-Z0-9\s,，.、。；《》【】{}[\]?？]/g
      if (reg.test(formValue)) {
        // 把html标签和数据库语句也替换成空字符
        // 正则表达式：/<[^>]+>/g
        let reg2 = /<[^>]+>/g
        if (reg2.test(formValue)) {
          this.formData[name] = formValue.replace(reg2, '')
        }
        this.formData[name] = formValue.replace(reg, '')
      }
    },
    submitForm() {
      var phoneReg = /^1[3456789]\d{9}$/
      if (this.isSubmit) return alert('请勿重复提交')
      if (!this.formData.company) return alert('请输入公司名称')
      if (!this.formData.name) return alert('请输入负责人姓名')
      if (!this.formData.telephone) return alert('请输入联系方式')
      if (!phoneReg.test(this.formData.telephone)) return alert('请输入正确的联系电话')
      this.isSubmit = true
      // 提交表单
      var formData = this.formData
      axios.post('https://hr.china9.cn/human/user_action/leave_message1', formData).then(res => {
        console.log(res, '111111')
        var result = res.data
        if (result.code == 200) {
          alert('提交成功')
          this.isSubmit = false
          this.formData = {
            company: '',
            name: '',
            telephone: '',
            remarks: '“HR的一天”'
          }
          this.$emit('click')
        } else {
          alert(result.message || '提交失败')
          this.isSubmit = false
        }
      })
    }
  },
  template: `
    <div class="form-view flex-yx-center">
      <div class="top-info mt125 flex-yx-center">
        <h3>资海云移动办公体验</h3>
        <p class="mt20">咨询热线：************</p>
      </div>
      <div class="form-box flex-yx-center mt80">
        <input type="text" name="company" v-model="formData.company" placeholder="请输入公司名称" @change="checkVal('company', formData.company)">
        <input type="text" name="name" v-model="formData.name" placeholder="请输入负责人姓名" @change="checkVal('name', formData.name)">
        <input type="text" name="telephone" v-model="formData.telephone" placeholder="请输入联系方式" @change="checkVal('telephone', formData.telephone)">
        <div class="btn width600 mt120" @click="submitForm">立即提交</div>
      </div>
    </div>
  `
}
