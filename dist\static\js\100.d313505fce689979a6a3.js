webpackJsonp([100],{"+9ay":function(t,e){},z5KT:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=a("Xxa5"),n=a.n(r),i=a("exGp"),o=a.n(i),s=a("c/Tr"),c=a.n(s),d=(a("8PcR"),a("/sSb")),u=a("pI5c"),l=a("MJLE"),p=a.n(l),m={name:"Buy",data:function(){return{form:{category:void 0,product:void 0,supportingProducts:void 0,time:12,payType:1},rules:{category:[{required:!0,message:"请输入产品名称",trigger:"change"}],product:[{required:!0,message:"请选择产品配置",trigger:"change"}],supportingProducts:[{required:!0,message:"请选择附加产品",trigger:"change"}],time:[{required:!0,message:"请选择购买时长",trigger:"change"}]},category:[],products:[],supportingProducts:[],totalPrice:0,payWays:[{id:1,name:"支付宝支付",img:a("7//0")},{id:2,name:"微信支付",img:a("EMaV")},{id:4,name:"余额支付",img:a("INvr")}],accountBalance:0,loading:!1,loading2:!1,loading3:!1,orderNo:"",payed:!1,dialogVisible:!1,loading_weixin:!1}},computed:{timeOptions:function(){return c()({length:10},function(t,e){return{id:12*(e+1),name:e+1+"年"}})},productInfo:function(){var t=this;return this.products.length?this.products.find(function(e){return e.id===t.form.product}):[]},productTime:function(){return{product:this.form.product,time:this.form.time}}},watch:{"form.category":{handler:function(t){t&&this.getProductByCate(t)},immediate:!0},"form.product":{handler:function(t){t&&this.getSupportingProductsByProduct(t)},immediate:!0},productTime:{handler:function(t){t.product&&t.time&&this.getPrice()},immediate:!0,deep:!0}},methods:{getProductCate:function(){var t=this;return o()(n.a.mark(function e(){var a;return n.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.loading=!0,e.next=3,Object(d.g)();case 3:if(a=e.sent,t.loading=!1,200!==a.code||!a.data){e.next=10;break}return t.category=a.data,t.form.category=+t.$route.query.cateId||t.category[0].id,e.next=10,t.getProductByCate(t.form.category);case 10:case"end":return e.stop()}},e,t)}))()},getProductByCate:function(t){var e=this;return o()(n.a.mark(function a(){var r;return n.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(t){a.next=2;break}return a.abrupt("return");case 2:return e.loading2=!0,a.next=5,Object(d.e)({category_id:t});case 5:r=a.sent,e.loading2=!1,200===r.code&&r.data&&(e.products=r.data,e.form.product=e.products[0].id);case 8:case"end":return a.stop()}},a,e)}))()},getSupportingProductsByProduct:function(t){var e=this;return o()(n.a.mark(function a(){var r,i;return n.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(t){a.next=2;break}return a.abrupt("return");case 2:if(r=e.productInfo){a.next=5;break}return a.abrupt("return");case 5:return e.loading3=!0,a.next=8,Object(d.f)({app_id:r.app_id,package_id:r.package_id});case 8:i=a.sent,e.loading3=!1,200===i.code&&i.data&&(e.supportingProducts=i.data,e.form.supportingProducts=e.supportingProducts.map(function(t){return t.id}));case 11:case"end":return a.stop()}},a,e)}))()},getPrice:function(){var t=this;return o()(n.a.mark(function e(){var a,r,i,o,s;return n.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a=t.productInfo,r=a.app_id,i=a.package_id,o={app_id:r,package_id:i,month:t.form.time,id:i,type:1,activity_id:0},e.next=4,Object(d.h)(o);case 4:200===(s=e.sent).code&&s.data&&(t.totalPrice=s.data.total_amount);case 6:case"end":return e.stop()}},e,t)}))()},obtainAccountBalance:function(){var t=this;return o()(n.a.mark(function e(){var a;return n.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(u._34)();case 2:200===(a=e.sent).code&&a.data&&(t.accountBalance=a.data.amount);case 4:case"end":return e.stop()}},e,t)}))()},toCharge:function(){this.$router.push("/console/wallet")},submitForm:function(){var t,e=this;this.$refs.form.validate((t=o()(n.a.mark(function t(a){var r,i,o,s,c;return n.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!a){t.next=43;break}return r=e.supportingProducts.filter(function(t){return e.form.supportingProducts.includes(t.id)}),t.next=4,Object(d.a)({source:"pc",order_type:1,app_id:e.productInfo.app_id,package_id:e.form.product,month:e.form.time,supplementList:r,remark:"资海云新版购买页",activity_id:0});case 4:if(200!==(i=t.sent).code||!i.data){t.next=41;break}if(e.orderNo=i.data.order_no,!e.orderNo){t.next=41;break}return t.next=10,Object(d.d)({no:e.orderNo,pay_type:e.form.payType});case 10:if(o=t.sent,e.payed=!0,4!==e.form.payType){t.next=18;break}return e.$message.success("订单支付成功"),t.next=16,e.$router.replace("/console/order");case 16:t.next=41;break;case 18:if(1!==o.data.status){t.next=40;break}e.order_no=o.data.order_no,t.t0=e.form.payType,t.next=1===t.t0?23:2===t.t0?29:3===t.t0?35:38;break;case 23:return(s=document.createElement("div")).innerHTML=o.data.payload,document.body.appendChild(s),document.forms.alipay_submit.target="_blank",document.forms.alipay_submit.submit(),t.abrupt("break",38);case 29:return e.dialogVisible=!0,e.loading_weixin=!0,c=e,setTimeout(function(){document.getElementById("qrcode").innerHTML="",new p.a("qrcode",{width:150,height:150,text:o.data.payload}),c.loading_weixin=!1},1e3),e.payCirculation(),t.abrupt("break",38);case 35:return document.getElementById("innerHtml").innerHTML=o.data.payload,document.forms.pay_form.submit(),t.abrupt("break",38);case 38:t.next=41;break;case 40:2===o.data.status&&(e.$message.success("订单支付成功"),e.$router.replace("/console/order"));case 41:t.next=44;break;case 43:return t.abrupt("return",!1);case 44:case"end":return t.stop()}},t,e)})),function(e){return t.apply(this,arguments)}))},payCirculation:function(){var t=this;setTimeout(function(){Object(u._18)({no:t.order_no}).then(function(e){e&&(2===e.data.status&&(t.dialogVisible=!1,t.$message.success("订单支付成功"),t.$router.replace({path:"/console/order"})),1===e.data.status&&!0===t.dialogVisible&&t.payCirculation())}).catch(function(){})},4e3)},payload:function(){var t=this;2===this.form.payType&&(this.dialogVisible=!0),Object(u._18)({no:this.order_no}).then(function(e){2!==t.form.payType&&(t.dialogVisible=!1),e&&(2===e.data.status?(t.payed=!1,t.$message.success("订单支付成功"),t.$router.push({path:"/buySuccess"})):2!==t.form.payType&&t.$message.error("订单未支付"))}).catch(function(){t.dialogVisible=!1})}},mounted:function(){var t=this;this.getProductCate(),this.obtainAccountBalance(),window.addEventListener("visibilitychange",function(){document.hidden||t.payed&&t.payload()})}},f={render:function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"buy"},[r("div",{staticClass:"title flex-align-center"},[t._v("产品购买")]),t._v(" "),r("div",{staticClass:"box"},[r("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"100px","label-position":"left"}},[r("div",{staticClass:"product-info card"},[r("el-form-item",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{label:"产品名称",prop:"name"}},[r("el-radio-group",{model:{value:t.form.category,callback:function(e){t.$set(t.form,"category",e)},expression:"form.category"}},t._l(t.category,function(e){return r("el-radio-button",{key:e.id,attrs:{label:e.id}},[t._v(t._s(e.name)+"\n            ")])}),1)],1),t._v(" "),r("el-form-item",{directives:[{name:"loading",rawName:"v-loading",value:t.loading2,expression:"loading2"}],attrs:{label:"产品配置",prop:"product"}},[r("el-radio-group",{model:{value:t.form.product,callback:function(e){t.$set(t.form,"product",e)},expression:"form.product"}},t._l(t.products,function(e){return r("el-radio-button",{key:e.id,attrs:{label:e.id}},[t._v("\n              "+t._s(e.name)+"\n            ")])}),1)],1),t._v(" "),t.supportingProducts.length?r("el-form-item",{directives:[{name:"loading",rawName:"v-loading",value:t.loading3,expression:"loading3"}],attrs:{label:"配套产品",prop:"supportingProducts"}},[r("div",{staticClass:"flex"},[r("el-checkbox-group",{model:{value:t.form.supportingProducts,callback:function(e){t.$set(t.form,"supportingProducts",e)},expression:"form.supportingProducts"}},t._l(t.supportingProducts,function(e){return r("el-checkbox-button",{key:e.id,attrs:{label:e.id}},[t._v(t._s(e.name))])}),1),t._v(" "),r("div",{staticClass:"tips"},[t._v("注：配套产品为套餐内包含，不收取额外费用")])],1)]):t._e(),t._v(" "),r("el-form-item",{staticClass:"time",attrs:{label:"购买时长",prop:"time"}},[r("el-radio-group",{model:{value:t.form.time,callback:function(e){t.$set(t.form,"time",e)},expression:"form.time"}},t._l(t.timeOptions,function(e){return r("el-radio-button",{key:e.id,attrs:{label:e.id}},[t._v(t._s(e.name))])}),1)],1)],1),t._v(" "),r("div",{staticClass:"price-info card"},[r("el-form-item",{directives:[{name:"loading",rawName:"v-loading",value:t.loading2,expression:"loading2"}],attrs:{label:"应付费用："}},[r("span",{staticClass:"total-price"},[t._v("￥"+t._s(t.totalPrice))])]),t._v(" "),r("el-form-item",{attrs:{label:"支付方式："}},[r("el-radio-group",{model:{value:t.form.payType,callback:function(e){t.$set(t.form,"payType",e)},expression:"form.payType"}},t._l(t.payWays,function(e){return r("el-radio-button",{key:e.id,staticClass:"pay-item",attrs:{label:e.id}},[r("img",{staticClass:"logo",attrs:{src:e.img,alt:""}}),t._v(" "),e.id===t.form.payType?r("img",{staticClass:"selected",attrs:{src:a("Iqnx"),alt:""}}):t._e(),t._v(" "),4===e.id?r("div",{staticClass:"balance flex-align-center flex-justify-between"},[r("span",[t._v("账户余额"+t._s(t.accountBalance)+"元")]),t._v(" "),r("el-button",{attrs:{type:"text",size:"small"},on:{click:t.toCharge}},[t._v("立即充值")])],1):t._e()])}),1)],1)],1),t._v(" "),r("div",{staticClass:"button-group flex-column flex-align-center"},[r("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm()}}},[t._v("立即支付")]),t._v(" "),r("div",{staticClass:"tips",staticStyle:{"margin-left":"0"}},[t._v("注：支付成功后产品立即生效")])],1)])],1),t._v(" "),r("div",[r("el-dialog",{attrs:{title:'打开微信"扫一扫"支付',visible:t.dialogVisible,width:"30%",center:""},on:{"update:visible":function(e){t.dialogVisible=e}}},[r("div",{staticClass:"weixin"},[r("div",{ref:"qrcode",staticStyle:{width:"150px",height:"150px"},attrs:{id:"qrcode"}}),t._v(" "),r("span",{staticClass:"dialog-footer",staticStyle:{"margin-top":"30px"},attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),t._v(" "),r("el-button",{attrs:{loading:t.loading_weixin,type:"primary"},on:{click:t.payload}},[t._v("我已支付成功")])],1)])]),t._v(" "),r("div",{ref:"innerHtml",attrs:{id:"innerHtml"}})],1)])},staticRenderFns:[]};var g=a("VU/8")(m,f,!1,function(t){a("+9ay")},"data-v-02c9b499",null);e.default=g.exports}});