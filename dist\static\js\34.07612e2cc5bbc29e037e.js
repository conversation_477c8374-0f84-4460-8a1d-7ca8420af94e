webpackJsonp([34],{Jc08:function(e,a){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAADK0lEQVRYR71X3XnaQBCcFe+BqyC4gtgVBCowriBQQXBE8mr8mqDYqSC4gkAFtiuwqSC4ghN5F5tv7/RzIAnJCl/uEU57c7O7s3OE16zvf87BPABzF0AHhFPzOeMZQAiiNYgW+PRmWTcsVW680V1svSuAB+bQeisEaAFve4lLFR765DCAYCMHj4sP5sfdwPS+4CABcgu/fV0GohjAje4govuUYkMzv4AwR4QFviihPL++6lO0MABjCKK36QZJUYv7RWzkAZggdJ/emnkDwhi+mtdjP94V6DEYUxC1419CRNzfB78LQG6+pd8Z5byCh15VHkuBmXh4AOhdvGcNj8/ceBmAfdqZ7zBRw1fdumzzTM9B9CHtGCcdGYBATwG6sjF4BV/ZFjvWCvRzxgRfw1dTCW0BmFajJ0O95LyFbiXtNl3yDfZpLcRsGIbohNRECI9P5AwLwKUIPKpVcN90D54pVmDLfXxWD5VkSWGCbizJNsUWQBDq+PYvmChRuerVBIC9rLAgLRrC7yjCTA9A9CvOfZqbSgRNAbi1xnwhALIKjfisVGT2ETUFYHXG1g7zHSHQ0qdWRv1O9WxIgDQFYFPOMeOPhFn4ZCWXH+GrXiX1RwEQX5rxTC6a/w7A6IBDR2MAzBeYqEVt9py0Sw3ECvUPKTAFRWNM2j9qgUgB8Kp5EeYEzDijBVo8qlTR3SJs2IbJVQM9BOPWGbtrRHxx2DO4bdhUiFyurRGZO2NXery4LnJCZKkUQylDYg2/c1Irj/ub7LARJuzYBS/hK/GRuysIxW90zdCbqFh4mgyjMpSGUQwRYZpLQ+kwKhmVjZgo+8h1W87ILzYkYiInnbOjAkgV16Rnz5BkFe24FprDb4+OAiLY/AQ4tne7PjNvSjPXYl88JXa6FrC8z3xBC6fFpjSJKC3l4WHHTjNNa6tcEme2+Qhi8X32NSV536J32JYnH+fttPyzBmiOaLs8LDLeeUy346zK7X3F00xPwRg7bGTMs/h9ZxHyo9w+am4TB1yUtmoDkggMINYteeUcLgE5GDIXMK6aC9UA3KNEZAwQ2Od5+uLhlTGZLGnC4jWj+S9wUMED7DYkGgAAAABJRU5ErkJggg=="},SnCo:function(e,a){},jOBD:function(e,a){},u3Y8:function(e,a,t){"use strict";var r={name:"company-auth",props:{formData:{required:!1,type:Object,default:function(){return{}}}},data:function(){return{loading:!1}},created:function(){},methods:{commit:function(){this.$emit("commit")}}},s={render:function(){var e=this,a=e.$createElement,r=e._self._c||a;return r("div",[1===e.formData.status?r("div",{staticClass:"top-tag"},[r("span",[e._v("如果您需要变更企业认证信息，可以")]),e._v(" "),r("a",{on:{click:e.commit}},[e._v("重新实名认证")])]):e._e(),e._v(" "),r("el-card",{staticClass:"box-card",attrs:{"v-loading":e.loading}},[1===e.formData.status?r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("img",{staticStyle:{width:"20px","margin-right":"10px"},attrs:{src:t("fD/T")}}),e._v(" "),r("span",[e._v("您已通过资海云实名认证")])]):e._e(),e._v(" "),0===e.formData.status?r("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[r("img",{staticStyle:{width:"20px","margin-right":"10px"},attrs:{src:t("Jc08")}}),e._v(" "),r("span",[e._v("资海云实名认证审核中")])]):e._e(),e._v(" "),r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.formData}},[r("el-form-item",{attrs:{label:"单位名称"}},[r("span",[e._v(e._s(e.formData.name))])]),e._v(" "),r("el-form-item",{attrs:{label:"单位证件号码"}},[r("span",[e._v(e._s(e.formData.license_number))])]),e._v(" "),r("el-form-item",{attrs:{label:"法人真实姓名"}},[r("span",[e._v(e._s(e.formData.legal_person_name))])]),e._v(" "),r("el-form-item",{attrs:{label:"法人身份证号"}},[r("span",[e._v(e._s(e.formData.legal_person_number))])]),e._v(" "),r("el-form-item",{attrs:{label:"法人联系电话"}},[r("span",[e._v(e._s(e.formData.legal_person_phone))])])],1)],1),e._v(" "),r("el-col",{attrs:{span:12}},[r("h1",{staticStyle:{"font-size":"17px","margin-bottom":"10px"}},[e._v("企业联系人信息")]),e._v(" "),r("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.formData}},[r("el-form-item",{attrs:{label:"真实姓名"}},[r("span",[e._v(e._s(e.formData.principal_name))])]),e._v(" "),r("el-form-item",{attrs:{label:"联系电话"}},[r("span",[e._v(e._s(e.formData.principal_phone))])])],1)],1)],1)],1)],1)},staticRenderFns:[]};var i=t("VU/8")(r,s,!1,function(e){t("SnCo")},"data-v-e18bcff4",null);a.a=i.exports},wk7D:function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var r=t("pFYg"),s=t.n(r),i=t("u3Y8"),n=t("pI5c"),o={name:"index",components:{"company-auth":i.a},data:function(){return{active:0,statement:!1,companyName:"",hasCardA:!0,hasCardB:!0,hasLicenseImage:!0,date_of_incorporation:"",legal_person:"",rules:{date_of_incorporation:[{required:!0,message:"请选择成立日期",trigger:"change"}],legal_person:[{required:!0,message:"请输入法人姓名",trigger:"blur"}],legal_person_number:[{required:!0,message:"请输入法人证件号码",trigger:"blur"}],registration:[{required:!0,message:"请输入登记机关",trigger:"blur"}],registered_capital:[{required:!0,message:"请输入注册资本",trigger:"blur"}],cods:[{required:!0,message:"请输入统一社会信息代码",trigger:"blur"}],business:[{required:!0,message:"请输入经营范围",trigger:"blur"}],nature:[{required:!0,message:"请选择企业类型",trigger:"blur"}],legalPersonIdCardFront:[{required:!0,message:"请上传法人证件A面",trigger:"blur"}],legalPersonIdCardBack:[{required:!0,message:"请上传法人证件B面",trigger:"blur"}],license:[{required:!0,message:"请上传营业执照",trigger:"change"}],linkman:[{required:!0,message:"请输入联系人姓名",trigger:"blur"}],linkmanphone:[{required:!0,message:"请输入联系人联系方式",trigger:"blur"}],linkmanemail:[{required:!0,message:"请输入联系人邮箱地址",trigger:"blur"}]},step1Rules:{date_of_incorporation:[{required:!0,message:"请选择成立日期",trigger:"change"}],legal_person:[{required:!0,message:"请输入法人姓名",trigger:"blur"}],legal_person_number:[{required:!0,message:"请输入法人证件号码",trigger:"blur"},{validator:function(e,a,t){18!==a.length?t(new Error("请输入18位身份证号码")):/^[0-9xX]*$/.test(a)?t():t(new Error("请输入正确的身份证号码"))},trigger:"blur"}],nature:[{required:!0,message:"请选择企业类型",trigger:"blur"}],registration:[{required:!0,message:"请输入登记机关",trigger:"blur"}],registered_capital:[{required:!0,message:"请输入注册资本",trigger:"blur"}],cods:[{required:!0,message:"请输入统一社会信息代码",trigger:"blur"},{validator:function(e,a,t){18!==a.length?t(new Error("请输入18位统一社会信用代码")):/^[0-9a-zA-Z]*$/.test(a)?t():t(new Error("请输入正确的统一社会信用代码"))}}],business:[{required:!0,message:"请输入经营范围",trigger:"blur"}]},step3Rules:{linkman:[{required:!0,message:"请输入联系人姓名",trigger:"blur"}],linkmanphone:[{required:!0,message:"请输入联系人联系方式",trigger:"blur"},{validator:function(e,a,t){/^1[3456789]\d{9}$/.test(a)?t():t(new Error("请输入正确的手机号码"))},trigger:"blur"}],linkmanemail:[{required:!0,message:"请输入联系人邮箱地址",trigger:"blur"},{validator:function(e,a,t){/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(a)?t():t(new Error("请输入正确的邮箱地址"))},trigger:"blur"}]},formLoading:!1,status:-2,province_ids:[],trade:[],trade_ids:[],business_types:[],scales:[],upload_url:"/api/workorder/upload",logo:null,licenseImage:null,cardA:null,cardB:null,formData:{agree:!1,date_of_incorporation:""},props:{label:"title",children:"child",value:"id"},options:{}}},computed:{lastIsDisabled:function(){return this.active<=0},token:function(){return this.$cookies.get("token")||this.$cookies.get("access_token")||this.$store.getters.token}},created:function(){this.getOptions(),this.getCompanyinfo(),this.$store.state.user&&(this.user=this.$store.state.user),this.user&&this.user.company&&(this.companyName=this.user.company.name)},methods:{last:function(){this.active>=1&&this.active--,3!==this.active&&(this.statement=!1)},next:function(){var e=this;if(1!==this.active)this.$refs["step"+(this.active+1)+"Form"].validate(function(a){if(!a)return e.$message.warning("请将表单填写完整"),!1;e.active++>2&&(e.active=0),3===e.active&&(e.statement=!0)});else{if(this.checkCardA(),this.checkCardB(),this.checkLicenseImage(),!(this.hasCardA&&this.hasCardB&&this.hasLicenseImage))return this.$message.warning("请将表单填写完整"),!1;this.active++>2&&(this.active=0),3===this.active&&(this.statement=!0)}},getCompanyinfo:function(){var e=this;this.formLoading=!0,Object(n.C)({}).then(function(a){e.formData.status=a.data.status,e.companyName=a.data.enterprise_name,e.date_of_incorporation=e.$formatDate(parseInt(a.data.create_time.padEnd(13,"0")),"yyyy-MM-dd"),e.legal_person=a.data.legal_person,e.formLoading=!1,e.companyCertificateGet(a.data.business)})},getOptions:function(){var e=this;Object(n.A)().then(function(a){e.options.ORGANIZATION_TYPE=e.getTreeData(a.data)})},getTreeData:function(e){for(var a=0;a<e.length;a++)e[a].child.length<1?e[a].child=void 0:this.getTreeData(e[a].child);return e},handleLogoSuccess:function(e,a){this.logo=URL.createObjectURL(a.raw),this.formData.logo=e.data[0]},handleLicenseSuccess:function(e,a){this.licenseImage=e.data.url,e.data.path_url&&(this.formData.license=e.data.path_url),this.checkLicenseImage()},handleCardASuccess:function(e,a){this.cardA=e.data.url,e.data.path_url&&(this.formData.legalPersonIdCardFront=e.data.path_url),this.checkCardA()},handleCardBSuccess:function(e,a){this.cardB=e.data.url,e.data.path_url&&(this.formData.legalPersonIdCardBack=e.data.path_url),this.checkCardB()},beforeAvatarUpload:function(e){var a="image/jpeg"===e.type,t="image/png"===e.type,r=e.size/1024/1024<2;return a||t||this.$message.error("上传的图标只能是 JPG 或 PNG 格式!"),r||this.$message.error("上传的图标大小不能超过 2MB!"),(a||t)&&r},companyCertificateGet:function(e){var a=this;Object(n.o)().then(function(t){if(a.formLoading=!1,200===t.code){var r=a.formData.status,s=[];t.data.nature.split(",").forEach(function(e){s.push(parseInt(e))}),a.formData={status:r,reason:t.data.reason,cods:t.data.cods,date_of_incorporation:t.data.date_of_incorporation,legal_person:t.data.legalPerson,legal_person_number:t.data.legalPersonNumber,nature:s,registration:t.data.registration,registered_capital:t.data.registered_capital,company_profile:t.data.company_profile,legalPersonIdCardFront:"https://images.china9.cn/"+t.data.legalPersonIdCardFront,legalPersonIdCardBack:"https://images.china9.cn/"+t.data.legalPersonIdCardBack,license:"https://images.china9.cn/"+t.data.license,linkman:t.data.linkman,linkmanphone:t.data.linkmanphone,linkmanemail:t.data.linkmanemail,agree:!1,business:e},a.licenseImage=a.formData.license,a.cardA=a.formData.legalPersonIdCardFront,a.cardB=a.formData.legalPersonIdCardBack,a.formData.cods?a.active=4:a.active=0}}).catch(function(){a.formLoading=!1})},companyCertificatePut:function(){var e=this;this.$refs.form.validate(function(a){if(!a)return e.formLoading=!1,console.log("error submit!!"),!1;"object"===s()(e.formData.nature)&&(e.formData.nature=e.formData.nature.join(",")),e.formData.legalPersonIdCardBack.includes("https://images.china9.cn/")&&(e.formData.legalPersonIdCardBack=e.formData.legalPersonIdCardBack.replace("https://images.china9.cn/","")),e.formData.legalPersonIdCardFront.includes("https://images.china9.cn/")&&(e.formData.legalPersonIdCardFront=e.formData.legalPersonIdCardFront.replace("https://images.china9.cn/","")),e.formData.license.includes("https://images.china9.cn/")&&(e.formData.license=e.formData.license.replace("https://images.china9.cn/","")),e.formLoading=!0,Object(n.p)(e.formData).then(function(a){e.formLoading=!1,200===a.code&&(e.active=4,e.formData.status=0,e.statement=!1)}).catch(function(){e.formLoading=!1})})},anew:function(){this.active=0},reset:function(){this.active=0},restCommit:function(){this.status=-2},checkCardA:function(){this.hasCardA=!!this.cardA},checkCardB:function(){this.hasCardB=!!this.cardB},checkLicenseImage:function(){this.hasLicenseImage=!!this.licenseImage}}},l={render:function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"app-container"},[-1===e.status?t("el-alert",{staticStyle:{"margin-bottom":"20px"},attrs:{title:"审核被拒绝，请重新提交认证信息",type:"error"}}):e._e(),e._v(" "),t("el-card",{directives:[{name:"loading",rawName:"v-loading",value:e.formLoading,expression:"formLoading"}],staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("企业认证")])]),e._v(" "),t("el-row",[t("el-col",{staticClass:"container"},[t("el-steps",{attrs:{active:e.active,"finish-status":"success"}},[t("el-step",{attrs:{title:"填写工商信息"}}),e._v(" "),t("el-step",{attrs:{title:"上传证件"}}),e._v(" "),t("el-step",{attrs:{title:"填写联系人信息"}}),e._v(" "),t("el-step",{attrs:{title:"预览并提交"}}),e._v(" "),t("el-step",{attrs:{title:"认证结果"}})],1),e._v(" "),t("el-form",{ref:"form",staticStyle:{margin:"20px 0"},attrs:{model:e.formData}},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.statement||0===e.active,expression:"statement || active === 0"}]},[t("el-form",{ref:"step1Form",attrs:{model:e.formData,rules:e.step1Rules}},[t("el-form-item",{attrs:{label:"成立日期",prop:"date_of_incorporation"}},[t("el-date-picker",{attrs:{type:"date",placeholder:"选择日期","value-format":"yyyy-MM-dd",disabled:e.statement},model:{value:e.formData.date_of_incorporation,callback:function(a){e.$set(e.formData,"date_of_incorporation",a)},expression:"formData.date_of_incorporation"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"法人姓名",prop:"legal_person"}},[t("el-input",{directives:[{name:"filter-special-char",rawName:"v-filter-special-char"}],attrs:{placeholder:"请输入法人姓名",disabled:e.statement,clearable:""},model:{value:e.formData.legal_person,callback:function(a){e.$set(e.formData,"legal_person",a)},expression:"formData.legal_person"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"法人身份证号",prop:"legal_person_number"}},[t("el-input",{attrs:{placeholder:"请输入法人身份证号",disabled:e.statement,clearable:""},model:{value:e.formData.legal_person_number,callback:function(a){e.$set(e.formData,"legal_person_number",a)},expression:"formData.legal_person_number"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"企业类型",prop:"nature"}},[t("el-cascader",{attrs:{options:e.options.ORGANIZATION_TYPE,disabled:e.statement,props:e.props},model:{value:e.formData.nature,callback:function(a){e.$set(e.formData,"nature",a)},expression:"formData.nature"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"登记机关",prop:"registration"}},[t("el-input",{directives:[{name:"filter-special-char",rawName:"v-filter-special-char"}],attrs:{placeholder:"请输入登记机关",disabled:e.statement,clearable:""},model:{value:e.formData.registration,callback:function(a){e.$set(e.formData,"registration",a)},expression:"formData.registration"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"注册资本",prop:"registered_capital"}},[t("el-input",{attrs:{placeholder:"请输入注册资本",type:"number",disabled:e.statement,clearable:""},model:{value:e.formData.registered_capital,callback:function(a){e.$set(e.formData,"registered_capital",a)},expression:"formData.registered_capital"}}),e._v("\n                万\n              ")],1),e._v(" "),t("el-form-item",{attrs:{label:"统一社会信息代码",prop:"cods"}},[t("el-input",{attrs:{disabled:e.statement,placeholder:"请输入统一社会信息代码",clearable:""},model:{value:e.formData.cods,callback:function(a){e.$set(e.formData,"cods",a)},expression:"formData.cods"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"经营范围",prop:"business"}},[t("el-input",{directives:[{name:"filter-special-char",rawName:"v-filter-special-char"}],attrs:{type:"textarea",rows:4,disabled:e.statement,placeholder:"请输入经营范围",clearable:""},model:{value:e.formData.business,callback:function(a){e.$set(e.formData,"business",a)},expression:"formData.business"}})],1)],1)],1),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:e.statement||1===e.active,expression:"statement || active === 1"}]},[t("el-form",{ref:"step2Form",attrs:{model:e.formData}},[t("el-form-item",{staticClass:"upload-img",attrs:{label:"法人身份证A面"}},[t("el-upload",{class:["avatar-uploader",e.statement?"upload-disabled":""],attrs:{name:"file",accept:"image/*",data:{access_token:e.token},action:e.upload_url,"show-file-list":!1,"on-success":e.handleCardASuccess,"before-upload":e.beforeAvatarUpload,disabled:e.statement}},[e.cardA?t("el-image",{staticClass:"card",attrs:{fit:"contain",src:e.cardA}}):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),e._v(" "),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("单位法定代表人身份证国徽面照片")])],1),e._v(" "),e.hasCardA?e._e():t("span",{staticClass:"el-form-item__error"},[e._v("请上传法人身份证A面")])],1),e._v(" "),t("el-form-item",{staticClass:"upload-img",attrs:{label:"法人身份证B面"}},[t("el-upload",{class:["avatar-uploader",e.statement?"upload-disabled":""],attrs:{name:"file",accept:"image/*",data:{access_token:e.token},action:e.upload_url,"show-file-list":!1,"on-success":e.handleCardBSuccess,"before-upload":e.beforeAvatarUpload,disabled:e.statement}},[e.cardB?t("el-image",{staticClass:"card",attrs:{fit:"contain",src:e.cardB}}):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),e._v(" "),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("单位法定代表人身份证国徽面照片")])],1),e._v(" "),e.hasCardB?e._e():t("span",{staticClass:"el-form-item__error"},[e._v("请上传法人身份证B面")])],1),e._v(" "),t("el-form-item",{staticClass:"upload-img",attrs:{label:"营业执照"}},[t("el-upload",{class:["avatar-uploader",e.statement?"upload-disabled":""],attrs:{name:"file",data:{access_token:e.token},action:e.upload_url,"show-file-list":!1,"on-success":e.handleLicenseSuccess,"before-upload":e.beforeAvatarUpload,disabled:e.statement}},[e.licenseImage?t("el-image",{staticClass:"license",attrs:{fit:"contain",src:e.licenseImage}}):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"}),e._v(" "),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("请上传清晰、端正无水印的图片")]),e._v(" "),t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("加速您的审核")])],1),e._v(" "),e.hasLicenseImage?e._e():t("span",{staticClass:"el-form-item__error"},[e._v("请上传营业执照")])],1)],1)],1),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:e.statement||2===e.active,expression:"statement || active === 2"}]},[t("el-form",{ref:"step3Form",attrs:{model:e.formData,rules:e.step3Rules}},[t("el-form-item",{directives:[{name:"filter-special-char",rawName:"v-filter-special-char"}],attrs:{label:"姓名",prop:"linkman"}},[t("el-input",{attrs:{placeholder:"请输入负责人的真实姓名",disabled:e.statement,clearable:""},model:{value:e.formData.linkman,callback:function(a){e.$set(e.formData,"linkman",a)},expression:"formData.linkman"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"手机号码",prop:"linkmanphone"}},[t("el-input",{attrs:{placeholder:"请输入负责人的手机号码",disabled:e.statement,clearable:""},model:{value:e.formData.linkmanphone,callback:function(a){e.$set(e.formData,"linkmanphone",a)},expression:"formData.linkmanphone"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"邮箱地址",prop:"linkmanemail"}},[t("el-input",{attrs:{placeholder:"请输入负责人邮箱地址",disabled:e.statement,clearable:""},model:{value:e.formData.linkmanemail,callback:function(a){e.$set(e.formData,"linkmanemail",a)},expression:"formData.linkmanemail"}})],1)],1)],1),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:3===e.active,expression:"active === 3"}]},[t("el-form-item",{attrs:{label:""}},[t("el-checkbox",{model:{value:e.formData.agree,callback:function(a){e.$set(e.formData,"agree",a)},expression:"formData.agree"}},[e._v("同意")]),e._v(" "),t("a",{attrs:{href:"https://account.china9.cn/资海云网站服务条款.html",target:"_blank"}},[e._v("《资海云网站服务条款》")])],1)],1),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:4===e.active,expression:"active === 4"}],staticStyle:{width:"1000px"}},[t("div",{directives:[{name:"show",rawName:"v-show",value:void 0===e.formData.status||0===e.formData.status,expression:"formData.status === undefined || formData.status === 0"}],staticClass:"waiting"},[t("h3",[t("i",{staticClass:"el-icon-success"}),e._v(" 提交成功，等待审核中")]),e._v(" "),t("p",[e._v("预计审核将在48小时内完成，请通过站内信或邮箱查看审核结果。")]),e._v(" "),t("p",[e._v("如果没有完成审核，请致电400-650-5024")])]),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:void 0!==e.formData.status&&1===e.formData.status,expression:"formData.status !== undefined && formData.status === 1"}],staticClass:"success"},[t("el-image",{attrs:{src:"../../../../static/image/console/center/审核已通过.png"}}),e._v(" "),t("h3",[e._v("审核已通过")]),e._v(" "),t("p",{staticStyle:{"white-space":"normal"}},[e._v("如果您的企业信息已在工商局变更，可以点击\n                "),t("el-button",{staticStyle:{width:"auto",border:"0","vertical-align":"1px",padding:"0"},attrs:{type:"text"},on:{click:e.anew}},[e._v("重新认证")]),e._v("\n                进行重新认证\n              ")],1)],1),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:void 0!==e.formData.status&&2===e.formData.status,expression:"formData.status !== undefined && formData.status === 2"}],staticClass:"danger"},[t("el-image",{attrs:{src:"../../../../static/image/console/center/审核未通过.png"}}),e._v(" "),t("h3",[e._v("审核被驳回")]),e._v(" "),t("p",[e._v("驳回原因："+e._s(e.formData.reason))]),e._v(" "),t("p",{staticStyle:{"white-space":"normal"}},[e._v("可以点击\n                "),t("el-button",{staticStyle:{width:"auto",border:"0"},attrs:{type:"text"},on:{click:e.reset}},[e._v("修改资料")]),e._v("\n                进行重认证，如果有疑问，请致电400-650-5024\n              ")],1)],1),e._v(" "),t("div",{staticClass:"company_mes"},[t("ul",[t("li",{staticStyle:{"background-color":"#f2f2f2"}},[t("span",[e._v("企业名称")]),e._v(" "),t("span",[e._v("成立日期")]),e._v(" "),t("span",[e._v("法人姓名")])]),e._v(" "),t("li",[t("span",[e._v(e._s(e.companyName))]),e._v(" "),t("span",[e._v(e._s(e.formData.date_of_incorporation||e.date_of_incorporation))]),e._v(" "),t("span",[e._v(e._s(e.formData.legal_person||e.legal_person))])])])])])]),e._v(" "),t("div",{directives:[{name:"show",rawName:"v-show",value:e.active<=3,expression:"active <= 3"}]},[t("el-button",{attrs:{disabled:e.lastIsDisabled},on:{click:e.last}},[e._v("上一步")]),e._v(" "),e.active<3?t("el-button",{on:{click:e.next}},[e._v("下一步")]):e._e(),e._v(" "),t("el-button",{directives:[{name:"show",rawName:"v-show",value:3===e.active,expression:"active === 3"}],attrs:{disabled:!1===e.formData.agree},on:{click:e.companyCertificatePut}},[e._v("提交\n          ")])],1)],1)],1)],1)],1)},staticRenderFns:[]};var c=t("VU/8")(o,l,!1,function(e){t("jOBD"),t("x1ez")},"data-v-60a193be",null);a.default=c.exports},x1ez:function(e,a){}});