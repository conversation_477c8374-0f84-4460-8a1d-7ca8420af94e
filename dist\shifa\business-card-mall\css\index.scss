@import url(../font/BAHNSCHRIFT/BAHNSCHRIFT.css);
@import url(../font/MiSans-Heavy/MiSans-Heavy.css);
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}
body {
	width: 100%;
	overflow-x: hidden;
  background: #E93832;
  font-size: 0.26rem;
  // font-family: SourceHanSansCN;
  // font-family: 微软雅黑;
  font-weight: 400;
}
.flex {
	display: flex;
	align-items: center;
}
.flex-x {
  display: flex;
}
.flex-wrap {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}
.flex-column {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
.flex-x-center {
  @extend .flex;
  justify-content: center;
}
.flex-y-center {
  @extend .flex;
  flex-direction: column;
  justify-content: center;
}
.animate__animated {
  animation-duration: 0.5s;
}

input:focus {
	outline: none;
}
/* 通用 */
::-webkit-input-placeholder {
	color: rgba(255, 255, 255, 0.8);
}
::-moz-placeholder {
	color: rgba(255, 255, 255, 0.8);
} /* firefox 19+ */
:-ms-input-placeholder {
	color: rgba(255, 255, 255, 0.8);
} /* ie */
input:-moz-placeholder {
	color: rgba(255, 255, 255, 0.8);
}
@font-face {
  font-family: "SourceHanSansCN";
  src: url("../font/SourceHanSansCN-Regular.otf");
}

@font-face {
  font-family: "YouSheBiaoTiHei";
  src: url("../font/YouSheBiaoTiHei-2.ttf");
}
img {
  display: block;
}

.bg-img {
  position: absolute;
  object-fit: scale-down;
}
.container {
  position: relative;
  z-index: 10;
  padding: 0 0.26rem;
}

/* 表单样式 */
.form-wrap {
  background: #FFFFFF;
  padding: 0.45rem;
  @extend .flex-y-center;
  .f-title {
    @extend .flex;
    font-size: 0.36rem;
    font-weight: bold;
    color: #222222;
    margin-bottom: 0.5rem;
    .icon {
      width: 0.34rem;
      height: 0.34rem;
      margin-right: 0.17rem;
    }
  }
  .input-wrap {
    @extend .flex-x-center;
    width: 100%;
    height: 0.9rem;
    background: #FFFFFF;
    border: 1px solid #E1E1E1;
    margin-bottom: 0.3rem;
    padding: 0.2rem 0.38rem;
    position: relative;
    .input-item {
      border: none;
      outline: none;
      background: none;
      width: 100%;
      height: 100%;
      font-size: 0.28rem;
      color: #333;
      // placeholder 样式
      &::placeholder {
        color: #999999;
      }
    }
  }
  .form-btn {
    @extend .flex-x-center;
    width: 100%;
    height: 0.9rem;
    background: #E93832;
    font-size: 0.3rem;
    color: #FFFFFF;
    margin-top: 0.26rem;
    margin-bottom: 0.35rem;
    cursor: pointer;
  }
  .form-desc {
    font-size: 0.28rem;
    color: #999999;
  }
}
.header-wrap {
  position: relative;
  z-index: 10;
  padding-top: 1.1rem;
  .bg-img {
    width: 100%;
    height: 13.89rem;
    position: absolute;
    top: 0;
    left: 0;
  }
  .title {
    font-family: MiSans-Heavy;
    font-weight: 800;
    font-size: 0.9rem;
    color: #FFFFFF;
    text-shadow: 0rem 0.04rem 0.2rem rgba(203,24,18,0.35);
    text-align: center;
  }
  .desc {
    font-size: 0.42rem;
    color: #E93832;
    text-align: center;
    margin: 0.2rem auto 1.02rem;
    width: 5.6rem;
    height: 0.7rem;
    background: linear-gradient(90deg, #FEFEFC, #FFFBBE);
    border-radius: 0.35rem;
    @extend .flex-x-center;
  }
  .img1 {
    width: 7.38rem;
    height: 4.32rem;
    margin: 0 auto 0.63rem -0.14rem;
  }
  
  .ecommerce-intro {
    margin-bottom: 0.63rem;
    
    .intro-title {
      height: 0.7rem;
      line-height: 0.7rem;
      background: transparent;
      color: #FFFFFF;
      font-size: 0.3rem;
      font-weight: bold;
      @extend .flex-x-center;
      position: relative;
      margin: 0 0 0.05rem 0.1rem;
      width: calc(100% - 0.05rem);
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #E93832;
        transform: skewX(-15deg);
        z-index: -1;
        border: 1px solid #FFFFFF;
      }
    }
    
    .intro-content {
      background: linear-gradient(90deg, #FDFFFE, #FFFBBE);
      padding: 0.42rem 0.38rem;
      font-size: 0.28rem;
      color: #380300;
      line-height: 1.6;
      position: relative;
      // 右、下 平行四边形
      &::before {
        content: '';
        position: absolute;
        top: 0.09rem;
        right: -0.34rem;
        width: 0.34rem;
        height: 100%;
        background: #D0201A;
        transform: skewY(35deg);
      }
      &::after {
        content: '';
        position: absolute;
        bottom: -0.3rem;
        left: 0.1rem;
        width: calc(100% + 0.5rem);
        height: 0.3rem;
        background: #D0201A;
        transform: skewX(35deg);
      }
    }
  }
}

.pain-points {
  margin-top: 0.82rem;
  padding: 0 0.04rem;
  
  .pain-title {
    font-family: MiSans-Heavy;
    font-size: 0.5rem;
    color: #FFF2D8;
    font-weight: bold;
    text-align: center;
    margin-bottom: 0.5rem;
    position: relative;
    
    &::before {
      content: '3';
      position: absolute;
      bottom: -0.5rem;
      left: calc(50% - 0.25rem);
      transform: translate(-50%, -50%);
      font-size: 1rem;
      color: rgba(255, 243, 225, 0.1);
      font-weight: bold;
      z-index: -1;
      line-height: 1rem;
      font-family: MiSans-Heavy;
    }
  }
  
  .pain-items {
    .pain-item {
      display: flex;
      margin-bottom: 0.4rem;
      background: linear-gradient(90deg, #FDFFFE, #FFFBBE);
      border-radius: 0.2rem;
      padding: 0.12rem 0.07rem;
      width: 6rem;
      
      &.right-item {
        flex-direction: row-reverse;
        margin-left: 0.9rem;
        
        .item-right {
          margin-left: 0;
          margin-right: 0.12rem;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          .item-title {
            justify-content: flex-end;
            flex-direction: row-reverse;
            .arrow-box {
              transform: rotate(180deg);
              margin-left: 0.1rem;
              margin-right: 0;
            }
          }
        }
      }
      
      .item-left {
        width: 1.08rem;
        height: 1.08rem;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #E93832;
        border-radius: 50%;
        
        img {
          margin-bottom: 0.3rem;
        }
      }
      
      .item-right {
        flex: 1;
        margin-left: 0.12rem;
        
        .item-title {
          display: flex;
          align-items: center;
          font-size: 0.32rem;
          font-weight: bold;
          color: #E93832;
          margin-top: 0.12rem;
          margin-bottom: 0.18rem;
          
          .arrow-box {
            // 三角
            width: 0;
            height: 0;
            border-top: 0.1rem solid transparent;
            border-bottom: 0.1rem solid transparent;
            border-left: 0.1rem solid #E93832;
            margin-right: 0.1rem;
          }
        }
        
        .item-desc {
          font-size: 0.26rem;
          color: #380300;
          line-height: 1.5;
          margin-bottom: 0.22rem;
        }
      }
    }
  }
}

.solution-section {
  margin-top: 0.75rem;
  margin-bottom: 0.73rem;
  padding: 0 0.04rem;
  
  .solution-title {
    font-family: MiSans-Heavy;
    font-size: 0.5rem;
    color: #FFFFFF;
    font-weight: bold;
    text-align: center;
    margin-bottom: 0.5rem;
  }
  
  .solution-items {
    .solution-item {
      display: flex;
      align-items: center;
      margin-bottom: 0.3rem;
      
      .item-number {
        font-family: MiSans-Heavy;
        width: 0.8rem;
        height: 0.8rem;
        background: linear-gradient(45deg, #F48A5A, #FFB694);
        border-radius: 0.2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.5rem;
        color: #FFF3E1;
        font-weight: bold;
        position: relative;
        z-index: 5;
      }
      
      .item-content {
        height: 0.8rem;
        line-height: 0.8rem;
        padding: 0 0.4rem;
        background: linear-gradient(90deg, #FFF9E6, #FFFBBE);
        border-radius: 0 0.2rem 0.2rem 0;
        font-size: 0.32rem;
        color: #380300;
        font-weight: bold;
        margin-left: -0.2rem;
      }
      &:nth-child(2) {
        margin-left: 2.9rem;
      }
      &:nth-child(3) {
        margin-left: 0.61rem;
      }
    }
  }
}
.part-wrap {
  background: #FFFFFF;
  border-radius: 0.2rem;
  margin-bottom: 0.3rem;
  position: relative;
  padding: 1rem 0 0.36rem;
  .part-title {
    width: 4.08rem;
    height: 0.79rem;
    background: url(../images/title-bg.png) no-repeat center center;
    background-size: 100% 100%;
    position: absolute;
    top: -0.12rem;
    left: 50%;
    transform: translateX(-50%);
    @extend .flex-x-center;
    .title-text {
      font-size: 0.32rem;
      color: #380300;
      font-weight: bold;
    }
  }
  .content-item {
    .content-item-title {
      width: 3.6rem;
      height: 0.6rem;
      background: linear-gradient(90deg, rgba(233, 56, 50, 0.5) 10%, rgba(233, 56, 50, 0));
      background-size: 100% 100%;
      padding: 0 0.36rem;
      line-height: 0.6rem;
      font-size: 0.32rem;
      color: #380300;
      font-weight: bold;
    }
    .content-item-desc {
      font-size: 0.28rem;
      color: #666666;
      line-height: 1.5;
      margin: 0.2rem 0.4rem 0.55rem 0.38rem;
      position: relative;
      padding-left: 0.24rem;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0.14rem;
        width: 0.1rem;
        height: 0.1rem;
        background: #E93832;
      }
    }
  }
  .part-img {
    margin: 0 auto;
  }
  .advantage-content {
    padding: 0 0.3rem;
    
    .advantage-item {
      display: flex;
      align-items: center;
      background: #FFF5EB;
      border-radius: 0.2rem;
      border: 1px solid #FFC4AE;
      padding: 0.32rem 0.24rem;
      margin-bottom: 0.3rem;
      
      .item-icon {
        width: 0.78rem;
        height: 0.78rem;
        background: #FFDDBA;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.23rem;
        flex-shrink: 0;
      }
      
      .item-text {
        flex: 1;
        font-size: 0.28rem;
        color: #380300;
        line-height: 1.5;
      }
    }
  }
}
.tips-img {
  margin: 0.7rem auto 0.5rem;
}
.line-img {
  margin: 0 auto 0.56rem;
}
.tips-text {
  font-weight: bold;
  font-size: 0.36rem;
  color: #FFFFFF;
  text-align: center;
  margin-bottom: 0.8rem;
}
