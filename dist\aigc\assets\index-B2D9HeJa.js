import{h as e}from"./request-Ciyrqj7N.js";const r=()=>e.post("/knowledge/getCompanyOperating"),n=t=>e.post("/knowledge/editCompanyOperating",t),p=()=>e.post("/knowledge/getprotype"),s=t=>e.post("/knowledge/proindex",t),d=t=>e.post("/knowledge/procreate",t),a=t=>e.post("/knowledge/proupdate",t),i=t=>e.post("/knowledge/prodelete",t),g=t=>e.post("/copywriting/product",t);export{g as a,d as b,r as c,i as d,a as e,p as f,s as g,n as s};
