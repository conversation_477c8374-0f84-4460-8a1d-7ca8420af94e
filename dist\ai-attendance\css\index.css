* {
  margin: 0;
  padding: 0;
}

li {
  list-style: none;
}

.container {
  width: 69%;
  margin: 0 auto;
}

.flex {
  display: flex;
}

.flex-align-center, .section-2 .container .idx1_con .con .con_r .til + div p {
  display: flex;
  align-items: center;
}

.flex-center, .btn, .concat-us .btn-wrap .consulting-button {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-align-stretch, .section-3 .intro-wrap {
  display: flex;
  align-items: stretch;
}

.flex-align-start {
  display: flex;
  align-items: flex-start;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-col {
  flex-direction: column;
}

.flex-space-between, .concat-us .form {
  display: flex;
  justify-content: space-between;
}

.title h2 {
  font-weight: 800;
  font-size: 2.29vw;
  color: #222222;
}
.title p {
  font-size: 0.94vw;
  color: #222222;
  margin-top: 0.89vw;
  text-transform: uppercase;
}

.btn, .concat-us .btn-wrap .consulting-button {
  width: 10.42vw;
  height: 3.13vw;
  background: #2d68ff;
  font-size: 1.25vw;
  color: #ffffff;
  cursor: pointer;
}

#zhyHomeHeader {
  height: 70px;
}

.pc {
  display: block;
}

.phone {
  display: none;
}

.section-1 {
  background: url("../img/bg.png") no-repeat #fff;
  background-size: 100% auto;
}
.section-1 .banner {
  padding-top: 11.77vw;
}
.section-1 .banner .banner-title {
  font-weight: 800;
  font-size: 3.13vw;
  color: #0b1c72;
  line-height: 4.69vw;
}
.section-1 .banner .btn, .section-1 .banner .concat-us .btn-wrap .consulting-button, .concat-us .btn-wrap .section-1 .banner .consulting-button {
  margin-top: 3.28rem;
}
.section-1 .pain-point {
  margin-top: 9.43vw;
  padding-bottom: 4.48vw;
}
.section-1 .pain-point .pain-point-list {
  gap: 1.56vw;
  margin-top: 3.07vw;
}
.section-1 .pain-point .pain-point-list li {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  padding: 2.55vw 2.4vw;
}
.section-1 .pain-point .pain-point-list li:nth-child(1) {
  background-image: url("../img/pain-point-bg1.png");
}
.section-1 .pain-point .pain-point-list li:nth-child(2) {
  background-image: url("../img/pain-point-bg2.png");
}
.section-1 .pain-point .pain-point-list li:nth-child(3) {
  background-image: url("../img/pain-point-bg3.png");
}
.section-1 .pain-point .pain-point-list li img {
  width: 13%;
  height: auto;
  margin-top: 12.86vw;
}
.section-1 .pain-point .pain-point-list li .pain-title {
  margin-top: 1.82vw;
  font-size: 1.56vw;
  color: #ffffff;
  line-height: 2.19vw;
}
.section-1 .pain-point .pain-point-list li .pain-desc {
  margin-top: 1.61vw;
  font-size: 0.73vw;
  color: #b2c7ff;
  line-height: 1.15vw;
}

.section-2 {
  background: #f1f6ff;
}
.section-2 .container {
  padding: 3.65vw 0 2.81vw;
}
.section-2 .container .idx1_con {
  background: #fff;
  margin-top: 1.77vw;
  padding-bottom: 1.04vw;
}
.section-2 .container .idx1_con > ul {
  display: flex;
  align-items: center;
}
.section-2 .container .idx1_con > ul > li {
  cursor: pointer;
  padding: 2.667vw 0 1.333vw 0;
  text-align: center;
  flex: 1 0 80px;
}
.section-2 .container .idx1_con > ul > li .img {
  overflow: hidden;
  height: 2.6vw;
  width: 100%;
}
.section-2 .container .idx1_con > ul > li .img img {
  transform: translateY(-20vw);
  filter: drop-shadow(0 20vw #707070);
}
.section-2 .container .idx1_con > ul > li p {
  font-size: 0.83vw;
  color: #707070;
}
.section-2 .container .idx1_con > ul > li:hover .img img, .section-2 .container .idx1_con > ul > li.active .img img {
  filter: drop-shadow(0 20vw #306ae4);
}
.section-2 .container .idx1_con > ul > li:hover p, .section-2 .container .idx1_con > ul > li.active p {
  color: #306ae4;
}
.section-2 .container .idx1_con .con {
  margin-top: 4.167vw;
  padding: 0 2vw;
  display: none;
}
.section-2 .container .idx1_con .con.active {
  display: flex;
}
.section-2 .container .idx1_con .con .img {
  margin-right: 2.083vw;
}
.section-2 .container .idx1_con .con .img img {
  width: 28.438vw;
}
.section-2 .container .idx1_con .con .con_r .title {
  font-size: 1.667vw;
  color: #565656;
}
.section-2 .container .idx1_con .con .con_r .title + span {
  display: block;
  width: 6.5625vw;
  height: 1px;
  background: #306ae4;
  margin: 1.302vw 0;
  position: relative;
}
.section-2 .container .idx1_con .con .con_r .title + span::before {
  content: "";
  position: absolute;
  width: 1.6vw;
  height: 3px;
  background: #306ae4;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
.section-2 .container .idx1_con .con .con_r .til {
  font-size: 1.042vw;
  font-weight: bold;
  white-space: nowrap;
  color: #565656;
}
.section-2 .container .idx1_con .con .con_r .til + div {
  margin-top: 2.083vw;
}
.section-2 .container .idx1_con .con .con_r .til + div p {
  line-height: 2;
  font-size: 0.729vw;
  color: #565656;
  margin-bottom: 1.82vw;
  position: relative;
}
.section-2 .container .idx1_con .con .con_r .til + div p img {
  width: 1.46vw;
  height: 1.46vw;
  -o-object-fit: contain;
     object-fit: contain;
  display: block;
  margin-right: 0.5rem;
}
.section-2 .container .btn, .section-2 .container .concat-us .btn-wrap .consulting-button, .concat-us .btn-wrap .section-2 .container .consulting-button {
  margin-top: 1.67vw;
}

.section-3 {
  background-color: #fff;
  padding: 3.854vw 0 6.25vw;
}
.section-3 .intro-wrap {
  margin-top: 2.92vw;
  background: #2d68ff;
}
.section-3 .intro-wrap img {
  display: block;
  -o-object-fit: cover;
     object-fit: cover;
  width: 30.26vw;
}
.section-3 .intro-wrap .intro {
  padding: 4.58vw 3.33vw;
}
.section-3 .intro-wrap .intro p {
  font-size: 16px;
  color: #d1deff;
  line-height: 34px;
}
.section-3 .intro-wrap .intro p + p {
  margin-top: 2.08vw;
}

.section-4 {
  background-color: #d7e3f6;
  padding: 3.49vw 0;
}
.section-4 .advantage-wrap {
  margin-top: 3.07vw;
}
.section-4 .advantage-wrap .advantage-item {
  width: calc(33.3333333333% - 2.24vw + 0.7466666667vw);
  background: linear-gradient(266deg, #dfe9f8, #eef2fc);
  box-shadow: 0vw 0.26vw 0vw 0vw rgba(255, 255, 255, 0.83), 0vw 0.052vw 0.104vw 0vw #ffffff;
  padding: 3.96vw 2.24vw;
}
.section-4 .advantage-wrap .advantage-item + .advantage-item {
  margin-left: 2.24vw;
}
.section-4 .advantage-wrap .advantage-item img {
  width: 2.97vw;
  height: 2.97vw;
}
.section-4 .advantage-wrap .advantage-item h3.title {
  font-weight: bold;
  font-size: 1.458vw;
  color: #2a2f4d;
  margin-top: 2.14vw;
}
.section-4 .advantage-wrap .advantage-item .content {
  margin-top: 3.23vw;
  font-size: 0.938vw;
  color: #2a2f4d;
  line-height: 1.875vw;
}
.section-4 .btn-wrap {
  margin-top: 3.65vw;
}

.concat-us {
  background: url("../img/bg2.png") no-repeat;
  background-size: cover;
  padding: 4.01vw 0 2.66vw;
  text-align: center;
}
.concat-us .form-title {
  font-size: 1.15vw;
  font-weight: 500;
  color: #ffffff;
}
.concat-us .form-en-title {
  font-size: 0.63vw;
  font-weight: 500;
  color: #ffffff;
  margin-top: 0.63vw;
}
.concat-us .form {
  width: 42.7vw;
  margin: 2.08vw auto 0;
}
.concat-us .form .input-wrap {
  width: 29.3%;
  position: relative;
}
.concat-us .form .input-wrap input {
  width: 100%;
  height: 2.08vw;
  background: transparent;
  outline: transparent;
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 2.08vw;
  padding: 0 1vw;
  box-sizing: border-box;
  color: #fff;
  font-size: 0.73vw;
}
.concat-us .btn-wrap {
  margin-top: 1.77vw;
}
.concat-us .btn-wrap .consulting-button {
  width: 15.625vw;
  height: 2.708vw;
  border-radius: 1.354vw;
  font-size: 1.042vw;
}

@media screen and (max-width: 1024px) {
  .container {
    width: 91.33%;
  }
  .title h2 {
    font-size: 5.867vw;
  }
  .title p {
    font-size: 2.4vw;
    margin-top: 1vw;
  }
  .btn, .concat-us .btn-wrap .consulting-button {
    width: 26.667vw;
    height: 9.333vw;
    font-size: 3.733vw;
    letter-spacing: 1px;
  }
  .pc {
    display: none;
  }
  .phone {
    display: block;
  }
  .section-1 {
    background: url("../img/phone/bg.png") no-repeat #fff;
    background-size: 100% auto;
  }
  .section-1 .banner {
    padding-top: 21.6vw;
  }
  .section-1 .banner h2.banner-title {
    font-size: 4vw;
    line-height: 1;
  }
  .section-1 .banner h1.banner-title {
    font-size: 7.8vw;
    line-height: 11.467vw;
    margin-top: 4.8vw;
  }
  .section-1 .banner .btn, .section-1 .banner .concat-us .btn-wrap .consulting-button, .concat-us .btn-wrap .section-1 .banner .consulting-button {
    margin-top: 4.933vw;
  }
  .section-1 .pain-point {
    margin-top: 14.467vw;
    padding-bottom: 10.667vw;
  }
  .section-1 .pain-point .pain-point-list {
    display: block;
  }
  .section-1 .pain-point .pain-point-list::after {
    content: "";
    display: block;
    clear: both;
    overflow: hidden;
  }
  .section-1 .pain-point .pain-point-list li {
    width: 78.267vw;
    float: left;
    padding: 5.733vw 8.133vw;
    box-sizing: border-box;
  }
  .section-1 .pain-point .pain-point-list li + li {
    margin-top: 6.533vw;
  }
  .section-1 .pain-point .pain-point-list li:nth-child(even) {
    float: right;
  }
  .section-1 .pain-point .pain-point-list li:nth-child(1) {
    background-image: url("../img/phone/pain-point-bg1.png");
  }
  .section-1 .pain-point .pain-point-list li:nth-child(2) {
    background-image: url("../img/phone/pain-point-bg2.png");
  }
  .section-1 .pain-point .pain-point-list li:nth-child(3) {
    background-image: url("../img/phone/pain-point-bg3.png");
  }
  .section-1 .pain-point .pain-point-list li img {
    margin-top: 36.8vw;
    width: 9.333vw;
  }
  .section-1 .pain-point .pain-point-list li .pain-title {
    margin-top: 5.6vw;
    font-size: 5.333vw;
    line-height: 6.4vw;
  }
  .section-1 .pain-point .pain-point-list li .pain-desc {
    margin-top: 4.533vw;
    font-size: 3.467vw;
    line-height: 4.533vw;
  }
  .section-2 {
    padding-top: 11.733vw;
    padding-bottom: 11.733vw;
  }
  .section-2 .container .idx_type {
    overflow: hidden;
  }
  .section-2 .container .idx_type .swiper-slide {
    overflow: hidden;
  }
  .section-2 .container .idx_type p.title {
    font-size: 4.8vw;
    text-align: center;
    margin: 7.2vw 0;
  }
  .section-2 .container .idx_type .img {
    width: 87.467vw;
    margin: auto;
  }
  .section-2 .container .idx_type .img img {
    width: 100%;
    height: auto;
    max-width: 100%;
    display: block;
  }
  .section-2 .container .idx_type div.con {
    width: 95%;
    margin-top: 3.2vw;
  }
  .section-2 .container .idx_type div.con .til {
    font-size: 2.933vw;
    font-weight: bold;
    text-align: center;
  }
  .section-2 .container .idx_type div.con .til + div {
    margin-top: 4.8vw;
  }
  .section-2 .container .idx_type div.con .til + div p {
    line-height: 2;
    margin-top: 0.3rem;
    font-size: 2.933vw;
    display: flex;
    align-items: center;
    padding-left: 2.667vw;
  }
  .section-2 .container .idx_type div.con .til + div p::before {
    content: "";
    width: 0.8vw;
    height: 0.8vw;
    background: #306ae4;
    border-radius: 50%;
    margin-right: 0.5rem;
  }
  .section-2 .container .idx_type .swiper-pagination {
    display: flex;
    overflow: auto;
    position: static;
    justify-content: center;
    margin-top: 6.667vw;
  }
  .section-2 .container .idx_type .swiper-pagination .swiper-pagination-bullet {
    display: inline-block;
    width: 2.667vw;
    height: 1.6vw;
    background: #306ae4;
    border-radius: 0.8vw;
  }
  .section-2 .container .idx_type .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
    width: 4vw;
  }
  .section-2 .container .btn, .section-2 .container .concat-us .btn-wrap .consulting-button, .concat-us .btn-wrap .section-2 .container .consulting-button {
    margin-top: 10.667vw;
  }
  .section-3 {
    padding-top: 14.667vw;
    padding-bottom: 10.667vw;
  }
  .section-3 .intro-wrap {
    display: block;
    margin-top: 9.733vw;
  }
  .section-3 .intro-wrap img {
    width: 100%;
  }
  .section-3 .intro-wrap .intro {
    padding: 12vw 6.4vw;
  }
  .section-3 .intro-wrap .intro p {
    font-size: 3.733vw;
    line-height: 5.6vw;
  }
  .section-3 .intro-wrap .intro p + p {
    margin-top: 5.6vw;
  }
  .section-4 {
    padding-top: 11.733vw;
    padding-bottom: 11.733vw;
  }
  .section-4 .advantage-wrap {
    margin-top: 12vw;
    display: block;
  }
  .section-4 .advantage-wrap .advantage-item {
    width: 100%;
    box-sizing: border-box;
    padding: 10.267vw 5.733vw;
  }
  .section-4 .advantage-wrap .advantage-item + .advantage-item {
    margin-left: 0;
    margin-top: 4.667vw;
  }
  .section-4 .advantage-wrap .advantage-item img {
    width: 10.667vw;
    height: 10.667vw;
  }
  .section-4 .advantage-wrap .advantage-item h3.title {
    font-size: 5.333vw;
    margin-top: 7.067vw;
  }
  .section-4 .advantage-wrap .advantage-item .content {
    margin-top: 4.4vw;
    font-size: 3.467vw;
    line-height: 4.8vw;
  }
  .section-4 .btn-wrap {
    margin-top: 10.667vw;
  }
  .concat-us {
    background: url("../img/phone/footer-bg.png") no-repeat;
    background-size: 100% 100%;
    padding: 12.533vw 2.4vw 13.333vw;
  }
  .concat-us .form-title {
    font-weight: bold;
    font-size: 5.067vw;
  }
  .concat-us .form-en-title {
    font-size: 2.133vw;
    margin-top: 2.533vw;
  }
  .concat-us .form {
    display: flex;
    flex-direction: column;
    width: 92vw;
    margin-top: 7.733vw;
  }
  .concat-us .form .input-wrap {
    width: 100%;
  }
  .concat-us .form .input-wrap input {
    height: 10.667vw;
    display: block;
    border-radius: 10.667vw;
    padding: 0 6vw;
    font-size: 3.733vw;
  }
  .concat-us .form .input-wrap + .input-wrap {
    margin-top: 6.667vw;
  }
  .concat-us .btn-wrap {
    margin-top: 9.867vw;
  }
  .concat-us .btn-wrap .btn, .concat-us .btn-wrap .consulting-button {
    border-radius: 10.667vw;
    width: 92vw;
    height: 10.667vw;
  }
}
@media screen and (max-width: 700px) {
  #zhyHomeHeader {
    height: 50px;
  }
}/*# sourceMappingURL=index.css.map */