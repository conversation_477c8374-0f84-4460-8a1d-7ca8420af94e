webpackJsonp([53],{PgLt:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a("+PoZ"),o={name:"index",data:function(){return{loading:!1,upload_url:"//api/workorder/upload",formData:{attrs:[],phone:this.$store.state.user.phone},fileList:[],cateList:[],dialogImageUrl:"",dialogVisible:!1,rules:{cate_id:[{required:!0,message:"请选择产品问题",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"}],question:[{required:!0,message:"请输入问题描述",trigger:"blur"}]}}},computed:{uploadParam:function(){return{token:this.$cookies.get("token")||this.$cookies.get("access_token")||this.$store.getters.token}}},created:function(){this.getWorkorderCate()},methods:{handleSuccess:function(e,t,a){200===e.code&&(this.fileList=a)},handleRemove:function(e,t){this.fileList=t},handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0},getWorkorderCate:function(){var e=this;Object(r.d)().then(function(t){200===t.code&&(e.cateList=t.data)})},workorderStore:function(){var e=this;this.$refs.dataForm.validate(function(t){if(t){e.formData.attrs=[];var a=e;e.fileList.forEach(function(e){a.formData.attrs.push(e.response.data.url)}),Object(r.j)(e.formData).then(function(t){200===t.code?(e.$message.success("提交成功"),e.$router.push("/workorder/list")):e.$message.error(t.messages)})}})}}},i={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card",attrs:{"v-loading":e.loading}},[a("el-row",[a("el-col",[a("div",{staticClass:"header_title"},[a("h1",[e._v("提交工单")])]),e._v(" "),a("el-form",{ref:"dataForm",staticClass:"demo-form-inline",attrs:{rules:e.rules,inline:!0,model:e.formData,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"产品名称：",prop:"cate_id"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.formData.cate_id,callback:function(t){e.$set(e.formData,"cate_id",t)},expression:"formData.cate_id"}},e._l(e.cateList,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.id}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"问题描述：",prop:"question"}},[a("el-input",{staticStyle:{width:"300px"},attrs:{type:"textarea",rows:5,placeholder:"请输入问题描述"},model:{value:e.formData.question,callback:function(t){e.$set(e.formData,"question",t)},expression:"formData.question"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"手机号：",prop:"phone"}},[a("el-input",{attrs:{placeholder:"请输入手机号"},model:{value:e.formData.phone,callback:function(t){e.$set(e.formData,"phone",t)},expression:"formData.phone"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"上传附件："}},[a("el-upload",{attrs:{data:e.uploadParam,name:"file",action:e.upload_url,"list-type":"picture-card","on-preview":e.handlePictureCardPreview,"on-remove":e.handleRemove,"on-success":e.handleSuccess}},[a("i",{staticClass:"el-icon-plus"}),e._v(" "),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e._v("可上传5个附件")]),e._v(" "),a("div",{staticClass:"el-upload__tip",staticStyle:{"margin-top":"-20px"},attrs:{slot:"tip"},slot:"tip"},[e._v("每个附件大小不超过2M。附件支持格式有'jpg','png'")])]),e._v(" "),a("el-dialog",{attrs:{visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})])],1)],1),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.workorderStore}},[e._v("提交")])],1)],1)],1)],1)},staticRenderFns:[]};var s=a("VU/8")(o,i,!1,function(e){a("cBnz"),a("oNJ7")},"data-v-3164c452",null);t.default=s.exports},cBnz:function(e,t){},oNJ7:function(e,t){}});