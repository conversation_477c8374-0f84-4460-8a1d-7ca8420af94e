<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8">
	<title>资海云-智能印章</title>
	<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
	<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
	<link rel="stylesheet" type="text/css" href="css/index.css" />
	<script src="js/jquery.min.js" type="text/javascript" charset="utf-8"></script>
	<!-- 资海云头部 -->
	<script>
		var environment = "pro";
	</script>
	<script type="text/javascript"
		src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/js-cookie.js?v=1227"></script>
	<script src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/homeHeader.js?v=03"></script>
	<script src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/footer.js?v=1228"></script>
	<script src="js/jweixin-1.2.0.js" type="text/javascript"></script>
	<script src="js/weixinapi.min.js" type="text/javascript"></script>
</head>

<body>
	<!-- 头部 -->
	<div id="zhyHomeHeader" style="background-color: #ffffff"></div>
	<div style="clear: both"></div>
	<div class="pc-box">
		<!-- banner -->
		<section class="section01 flex">
			<div class="container flex">
				<div class="right-part ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0s">
					<div class="tag">智能印章-集团版</div>
					<h1 class="ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.8s">
						资海云智能印章-集团版 <br>
						可跨分公司管理的智慧印章
					</h1>
					<a class="section-btn ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="1.6s"
						href="#consult">立即咨询</a>
				</div>
			</div>
		</section>
		<!-- 集团版 功能升级 -->
		<section class="section08 flex" data-show="1">
			<div class="container">
				<h3 class="ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0s">集团版 功能升级</h3>
				<div class="tab-wrap">
					<div class="tab-panel active ani" animate-effect="fadeIn" animate-duration="0.8s" animate-delay="0s">
						<div class="card ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.5s">
							<h3>印章跨分公司 <br>
								集中管理</h3>
							<ul>
								<li class="flex">
									<img src="./newImg/icon1.png" alt="">
									<p>
										智能印章集团版是针对各类中、大型企业集团的升级版本，实现了印章跨分公司集中管理的功能；
									</p>
								</li>
								<li class="flex">
									<img src="./newImg/icon1.png" alt="">
									<p>
										让集团总部能够轻松掌控各分公司的智能印章设备使用情况；
									</p>
								</li>
							</ul>
						</div>
					</div>
					<div class="tab-panel ani" animate-effect="fadeIn" animate-duration="0.8s" animate-delay="0s">
						<div class="card ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.5s">
							<h3>灵活的 <br>
								权限设置功能</h3>
							<ul>
								<li class="flex">
									<img src="./newImg/icon1.png" alt="">
									<p>
										提供了灵活的权限设置功能，能够根据不同分公司的需求，为其分配相应的管理权限，实现分级管理；
									</p>
								</li>
								<li class="flex">
									<img src="./newImg/icon1.png" alt="">
									<p>
										让集团企业印章管理更加高效、安全、便捷；
									</p>
								</li>
							</ul>
						</div>
					</div>
					<div class="tab flex">
						<div class="tab-item flex active">跨司管理</div>
						<div class="tab-item flex">灵活权限</div>
					</div>
				</div>
			</div>
		</section>
		<!-- 传统公章现状 -->
		<section class="section02 flex">
			<div class="container">
				<h3 class=" ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0s">传统公章 使用现状</h3>
				<div class="item-box flex-wrap">
					<div class="item flex ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.4s">
						<img src="images/u003.png" class="number" />
						<p class="text-box flex">
							<span class="large">印章多</span>
							<span class="text">印章种类多、数量多，刻制、销毁单纯靠人工管理容易混乱</span>
						</p>
					</div>
					<div class="item flex ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.8s">
						<img src="images/u004.png" class="number" />
						<p class="text-box flex">
							<span class="large">用印分散</span>
							<span class="text">分子公司、办事处遍布全国或全球，异地用章情况无从知晓</span>
						</p>
					</div>
					<div class="item flex ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="1.2s">
						<img src="images/u005.png" class="number" />
						<p class="text-box flex">
							<span class="large">印章外带</span>
							<span class="text">工商、银行、 税务、甲方要求等需要外带印章，印章处于无监管状态，风险巨大</span>
						</p>
					</div>
					<div class="item flex ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="1.6s">
						<img src="images/u006.png" class="number" />
						<p class="text-box flex">
							<span class="large">违规用印</span>
							<span class="text">印章使用者无意疏忽或徇私舞弊，对未经授权文件盖章</span>
						</p>
					</div>
					<div class="item flex ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="2s">
						<img src="images/u007.png" class="number" />
						<p class="text-box flex">
							<span class="large">合同混乱</span>
							<span class="text">文件众多，查询文件合同难以迅速找出</span>
						</p>
					</div>
				</div>
			</div>
		</section>
		<!-- 智能用印 核心功能 -->
		<section class="section03 flex">
			<div class="container">
				<h3 class="ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.4s">智能用印 核心功能</h3>
				<div class="item-box flex">
					<div class="item ani" animate-effect="fadeInUp" animate-duration="0.4s" animate-delay="0.6s">
						<div class="top-box">
							<img src="./images/u008.png" class="b" alt="">
							<img src="./images/u009.png" class="red" alt="">
							<img src="./images/u010.png" class="icon" alt="">
						</div>
						<p>集中管控</p>
					</div>
					<div class="item ani" animate-effect="fadeInUp" animate-duration="0.4s" animate-delay="0.8s">
						<div class="top-box">
							<img src="./images/u008.png" class="b" alt="">
							<img src="./images/u009.png" class="red" alt="">
							<img src="./images/u011.png" class="icon" alt="">
						</div>
						<p>人印分离</p>
					</div>
					<div class="item ani" animate-effect="fadeInUp" animate-duration="0.4s" animate-delay="1.0s">
						<div class="top-box">
							<img src="./images/u008.png" class="b" alt="">
							<img src="./images/u009.png" class="red" alt="">
							<img src="./images/u012.png" class="icon" alt="">
						</div>
						<p>多种模式</p>
					</div>
					<div class="item ani" animate-effect="fadeInUp" animate-duration="0.4s" animate-delay="1.2s">
						<div class="top-box">
							<img src="./images/u008.png" class="b" alt="">
							<img src="./images/u009.png" class="red" alt="">
							<img src="./images/u013.png" class="icon" alt="">
						</div>
						<p>人脸验证</p>
					</div>
					<div class="item ani" animate-effect="fadeInUp" animate-duration="0.4s" animate-delay="1.4s">
						<div class="top-box">
							<img src="./images/u008.png" class="b" alt="">
							<img src="./images/u009.png" class="red" alt="">
							<img src="./images/u014.png" class="icon" alt="">
						</div>
						<p>数据归档</p>
					</div>
					<div class="item ani" animate-effect="fadeInUp" animate-duration="0.4s" animate-delay="1.6s">
						<div class="top-box">
							<img src="./images/u008.png" class="b" alt="">
							<img src="./images/u009.png" class="red" alt="">
							<img src="./images/u015.png" class="icon" alt="">
						</div>
						<p>记录溯源</p>
					</div>
				</div>
			</div>
		</section>
		<!-- 智能一体化 管理更轻松 -->
		<section class="section04 flex">
			<div class="container">
				<h3 class="ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.4s">智能一体化 管理更轻松</h3>
				<div class="content-box flex-column ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.6s">
					<div class="item-box flex">
						<div class="item">
							<div class="top-box">
								<div class="number">
									<span class="large" data-number="90">90</span>
									<span class="small">%</span>
								</div>
								<div class="icon">
									<img src="images/u016.png" alt="" />
									<img src="images/u017.png" alt="">
								</div>
							</div>
							<p>降低印章管理风险</p>
						</div>
						<div class="item">
							<div class="top-box">
								<div class="number">
									<span class="large" data-number="60">60</span>
									<span class="small">%</span>
								</div>
								<div class="icon">
									<img src="images/u016.png" alt="" />
									<img src="images/u017.png" alt="">
								</div>
							</div>
							<p>控制印章管理成本</p>
						</div>
						<div class="item">
							<div class="top-box">
								<div class="number">
									<span class="large" data-number="80">80</span>
									<span class="small">%</span>
								</div>
								<div class="icon">
									<img src="images/u016.png" alt="" />
									<img src="images/u017.png" alt="">
								</div>
							</div>
							<p>提升用印效率</p>
						</div>
					</div>
					<p class="bot-title">融入物联网技术，智能硬件同软件相结合。使传统物理印章升级为电子签印章，一体化解决企业智能用印诉求</p>
				</div>
			</div>
		</section>
		<!-- 用印快捷 安全防控 -->
		<section class="section05 flex">
			<div class="container">
				<h3 class=" ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.4s">用印快捷 安全防控</h3>
				<div class="item-box flex">
					<div class="item ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.6s">
						<div class="icon">
							<img src="images/u019.png">
						</div>
						<p class="title">一键用印</p>
						<p class="desc">打开云经理App，一键用印，带您开启全新用印体验。</p>
					</div>
					<div class="line ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.8s">
						<img src="images/u018.png" alt="">
					</div>
					<div class="item ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="1s">
						<div class="icon">
							<img src="images/u020.png">
						</div>
						<p class="title">人脸识别 生物防假</p>
						<p class="desc">用印前确认本人操作，严防他人盗用，快速启动。用印，无需等待。</p>
					</div>
					<div class="line ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="1.2s">
						<img src="images/u018.png" alt="">
					</div>
					<div class="item ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="1.4s">
						<div class="icon">
							<img src="images/u021.png">
						</div>
						<p class="title">智能用印 简单高效</p>
						<p class="desc">员工自主扫码盖章，实时同步用印时间、地点、内容，事后追溯。手持模式、支架模式一键切换。盖章再也不用跑上跑下，流程不再繁锁。</p>
					</div>
					<div class="line ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="1.6s">
						<img src="images/u018.png" alt="">
					</div>
					<div class="item ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="1.8s">
						<div class="icon">
							<img src="images/u022.png">
						</div>
						<p class="title">用印记录 实时查看</p>
						<p class="desc">打破时间、空间的束缚，印章记录 随时可查看，所有印章操作均有详细的操作日志，能够追根溯源。</p>
					</div>
				</div>
			</div>
		</section>
		<!-- 降本增效 防范风险 -->
		<section class="section06 flex">
			<div class="container">
				<h3 class="ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.4s">降本增效 防范风险</h3>
				<div class="content-box ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="1s">
					<div class="tab-box flex">
						<div class="item active" data-id="0">
							<p>降低风险</p>
						</div>
						<div class="item" data-id="1">
							<p>节约成本</p>
						</div>
						<div class="item" data-id="2">
							<p>提高效率</p>
						</div>
						<div class="item" data-id="3">
							<p>数据分析</p>
						</div>
						<div class="item" data-id="4">
							<p>规范管理</p>
						</div>
						<div class="item" data-id="5">
							<p>品牌提升</p>
						</div>
					</div>
					<div class="content" id="content0" style="display: block;">
						<p>智能印章可实现人章隔离，授权后才可盖章，用印全过程被记录</p>
					</div>
					<div class="content" id="content1">
						<p>可把印章下沉到分支机构，减少快递和差旅成本；外带由经办人带出，不需要跟章人出差，减少差旅成本</p>
					</div>
					<div class="content" id="content2">
						<p>智能印章放置到分支机构，远程授权后 随时可以使用</p>
					</div>
					<div class="content" id="content3">
						<p>所有盖章文件及用印数据自动保存到服务器，依托海量数据进行企业风控分析</p>
					</div>
					<div class="content" id="content4">
						<p>利用前沿的物联网技术，规范印章使用，进行创新管理</p>
					</div>
					<div class="content" id="content5">
						<p>让社会/合作伙伴感受到先进的印章管理办法， 提升企业品牌，产生潜在价值</p>
					</div>
				</div>
			</div>
		</section>
		<!-- 底部 -->
		<section class="section07 flex" id="consult">
			<div class="container">
				<h3 class=" ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0s">资海云智能印章 </h3>
				<p class="title ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.8s">
					让印章的每一次使用做到：<span>事前认证、事中监控、事后有据</span></p>
				<div class="form-box ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="1.4s">
					<div class="form-item">
						<input type="text" placeholder="公司名称" name="company" id="company" value="">
					</div>
					<div class="form-item">
						<input type="text" placeholder="联系人（必填）" name="name" id="name" value="">
						<div class="el-form-item__error" style="display: none"></div>
					</div>
					<div class="form-item">
						<input type="number" placeholder="电话（必填）" name="telephone" id="telephone" value="">
						<div class="el-form-item__error" style="display: none"></div>
					</div>
				</div>
				<a onclick="submitForm()" class="section-btn ani" animate-effect="fadeInUp" animate-duration="0.8s"
					animate-delay="1.8s">立即咨询</a>
			</div>
		</section>
	</div>
	<div class="mobile-box">
		<div class="header clear">
			<h3>资海云智能印章-集团版 <br>
				可跨分公司管理的智慧印章</h3>
			<a class="btn ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="1.6s" href="#footer">立即咨询</a>
			<div class="display-img">
				<img src="./newImg/seals.png" alt="">
			</div>
		</div>
		<div class="upgrade" data-show="0">
			<div class="container">
				<div class="title">
					<img src="./img/content-title.png" alt="">
					<h2>集团版 功能升级</h2>
					<img src="./img/content-title.png" alt="">
				</div>

				<div class="tab-wrap ani animated">
					<div class="tab-panel active ani animated fadeInLeft">
						<div class="card ani animated fadeInUp">
							<h3>印章跨分公司 <br>
								集中管理</h3>
							<ul>
								<li class="flex">
									<img src="./newImg/icon1.png" alt="">
									<p>
										智能印章集团版是针对各类中、大型企业集团的升级版本，实现了印章跨分公司集中管理的功能；
									</p>
								</li>
								<li class="flex">
									<img src="./newImg/icon1.png" alt="">
									<p>
										让集团总部能够轻松掌控各分公司的智能印章设备使用情况；
									</p>
								</li>
							</ul>
						</div>
					</div>
					<div class="tab-panel ani animated fadeInRight">
						<div class="card ani animated fadeInUp">
							<h3>灵活的 <br>
								权限设置功能</h3>
							<ul>
								<li class="flex">
									<img src="./newImg/icon1.png" alt="">
									<p>
										提供了灵活的权限设置功能，能够根据不同分公司的需求，为其分配相应的管理权限，实现分级管理；
									</p>
								</li>
								<li class="flex">
									<img src="./newImg/icon1.png" alt="">
									<p>
										让集团企业印章管理更加高效、安全、便捷；
									</p>
								</li>
							</ul>
						</div>
					</div>
					<div class="tab flex">
						<div class="tab-item flex active">跨司管理</div>
						<div class="tab-item flex">灵活权限</div>
					</div>
				</div>
			</div>
		</div>
		<div class="traditional container">
			<div class="title">
				<img src="./img/content-title.png" alt="">
				<h2>传统公章 使用现状</h2>
				<img src="./img/content-title.png" alt="">
			</div>
			<ul class="content ani animated">
				<li class="ani animated fadeInLeftBig">
					<div class="traditional-content-title">
						<span class="index">1.</span>
						<span>印章多</span>
					</div>
					<div class="traditional-content">
						印章种类多、数量多，刻制、销毁单纯靠人工管理容易混乱
					</div>
				</li>
				<li class="ani animated fadeInRightBig">
					<div class="traditional-content-title">
						<span class="index">2.</span>
						<span>用印分散</span>
					</div>
					<div class="traditional-content">
						分子公司、办事处遍布全国或全球，异地用章情况无从知晓
					</div>
				</li>
				<li class="ani animated fadeInLeftBig">
					<div class="traditional-content-title">
						<span class="index">3.</span>
						<span>印章外带</span>
					</div>
					<div class="traditional-content">
						工商、银行、 税务、甲方要求等需要外带印章，印章处于无监管状态，风险巨大
					</div>
				</li>
				<li class="ani animated fadeInRightBig">
					<div class="traditional-content-title">
						<span class="index">4.</span>
						<span>违规用印</span>
					</div>
					<div class="traditional-content">
						印章使用者无意疏忽或徇私舞弊，对未经授权文件盖章
					</div>
				</li>
				<li class="ani animated fadeInLeftBig">
					<div class="traditional-content-title">
						<span class="index">5.</span>
						<span>合同混乱</span>
					</div>
					<div class="traditional-content">
						文件众多，查询文件合同难以迅速找出
					</div>
				</li>
			</ul>
		</div>
		<div class="function container">
			<div class="title">
				<img src="./img/content-title.png" alt="">
				<h2>智能用印 核心功能</h2>
				<img src="./img/content-title.png" alt="">
			</div>
			<div class="content">
				<ul class="function-list ani animated">
					<li class="ani animated">
						<img src="./img/function1.png" alt="">
						<p>集中管控</p>
					</li>
					<li class="ani animated">
						<img src="./img/function2.png" alt="">
						<p>人印分离</p>
					</li>
					<li class="ani animated">
						<img src="./img/function3.png" alt="">
						<p>多种模式</p>
					</li>
					<li class="ani animated">
						<img src="./img/function4.png" alt="">
						<p>文件比对</p>
					</li>
					<li class="ani animated">
						<img src="./img/function5.png" alt="">
						<p>数据归档</p>
					</li>
					<li class="ani animated">
						<img src="./img/function6.png" alt="">
						<p>记录溯源</p>
					</li>
				</ul>
				<div class="intelligent-integration">
					<p class="intelligent-integration-title">智能一体化 管理更轻松</p>
					<div class="intelligent-integration-content clear">
						<div class="fl">
							<div class="percent"><span class="num">90</span><span>%</span></div>
							<img src="./img/arrow.png" alt="" class="arrow">
							<p>降低印章管理风险</p>
						</div>
						<div class="fl fr">
							<div class="percent"><span class="num">60</span><span>%</span></div>
							<img src="./img/arrow.png" alt="" class="arrow">
							<p>控制印章管理成本</p>
						</div>
						<div class="fl" style="margin-left: 50%;transform: translateX(-50%);">
							<div class="percent"><span class="num">80</span><span>%</span></div>
							<img src="./img/arrow1.png" alt="" class="arrow">
							<p>提升用印效率</p>
						</div>
					</div>
					<div class="intelligent-integration-content-bottom">
						融入物联网技术，智能硬件同软件相结合。使传统物理印章升级为电子签印章，一体化解决企业智能用印诉求
					</div>
				</div>
			</div>
		</div>
		<div class="safe container">
			<div class="title">
				<img src="./img/content-title.png" alt="">
				<h2>用印快捷 安全防控</h2>
				<img src="./img/content-title.png" alt="">
			</div>
			<ul class="safe-content quick ani animated">
				<li class="ani animated">
					<img class="fl" src="./img/safe1.png">
					<div class="safe-content-box">
						<p class="safe-content-title">一键用印</p>
						<div>打开云经理App，一键用印，带您开启全新用印体验。</div>
					</div>
				</li>
				<li class="ani animated">
					<img class="fl" src="./img/safe2.png">
					<div class="safe-content-box">
						<p class="safe-content-title">人脸识别 生物防假</p>
						<div>用印前确认本人操作，严防他人盗用，快速启动。用印，无需等待。</div>
					</div>
				</li>
				<li class="ani animated">
					<img class="fl" src="./img/safe3.png">
					<div class="safe-content-box">
						<p class="safe-content-title">智能用印 简单高效</p>
						<div>员工自主扫码盖章，实时同步用印时间、地点、内容，事后追溯。手持模式、支架模式一键切换。</div>
					</div>
				</li>
				<li class="ani animated">
					<img class="fl" src="./img/safe4.png">
					<div class="safe-content-box">
						<p class="safe-content-title">用印记录 实时查看</p>
						<div>
							打破时间、空间的束缚，印章记录 随时可查看，所有印章操作均有详细的操作日志，能够追根溯源。
						</div>
					</div>
				</li>
			</ul>
			<div class="against-risk">
				<p class="against-title">降本增效 防范风险</p>
				<ul class="safe-content low-cost ani animated">
					<li class="ani animated">
						<div class="img">1</div>
						<div class="safe-content-box">
							<p class="safe-content-title">降低风险</p>
							<div>
								智能印章可实现⼈章隔离，授权后才可盖章，用印全过程被记录
							</div>
						</div>
					</li>
					<li class="ani animated">
						<div class="img">2</div>
						<div class="safe-content-box">
							<p class="safe-content-title">节约成本</p>
							<div>
								可把印章下沉到分支机构，减少快递和差旅成本；外带由经办人带出，不需要跟章人出差，减少差旅成本
							</div>
						</div>
					</li>
					<li class="ani animated">
						<div class="img">3</div>
						<div class="safe-content-box">
							<p class="safe-content-title">提高效率</p>
							<div>
								智能印章放置到分支机构，远程授权后随时可以使⽤
							</div>
						</div>
					</li>
					<li class="ani animated">
						<div class="img">4</div>
						<div class="safe-content-box">
							<p class="safe-content-title">数据分析</p>
							<div>
								所有盖章⽂件及用印数据自动保存到服务器，依托海量数据进⾏企业风控分析
							</div>
						</div>
					</li>
					<li class="ani animated">
						<div class="img">5</div>
						<div class="safe-content-box">
							<p class="safe-content-title">规范管理</p>
							<div>
								利用前沿的物联⽹网技术，规范印章使⽤，进行创新管理
							</div>
						</div>
					</li>
					<li class="ani animated">
						<div class="img">6</div>
						<div class="safe-content-box">
							<p class="safe-content-title">品牌提升</p>
							<div>
								让社会/合作伙伴感受到先进的印章管理办法，提升企业品牌，产生潜在价值
							</div>
						</div>
					</li>
				</ul>
			</div>
		</div>
		<div class="footer" id="footer">
			<p class="footer-title">资海云智能印章</p>
			<p class="footer-content">让印章的每一次使用做到：<span>事前认证、事中监控、事后有据</span></p>
			<div class="form">
				<div class="form-item">
					<input type="text" placeholder="公司名称" name="company_m" id="company_m" value="">
				</div>
				<div class="form-item">
					<input type="text" placeholder="联系人（必填）" name="name_m" id="name_m" value="">
					<div class="el-form-item__error" style="display: none"></div>
				</div>
				<div class="form-item">
					<input type="number" placeholder="电话（必填）" name="telephone_m" id="telephone_m" value="">
					<div class="el-form-item__error" style="display: none"></div>
				</div>
				<div class="form-item">
					<button class="btn" onclick="submitForm()">立即咨询</button>
				</div>
			</div>
		</div>
		<script src="./js/slowNumber.js"></script>
		<script src="./js/smartSeals.js"></script>
	</div>
	<!-- 底部 -->
	<footer id="zhyFooter"></footer>
	<!--提示弹窗-->
	<div class="message-box" style="z-index: 2019; display: none" id="message">
		<span id="message-text"></span>
	</div>
	<script type="text/javascript">
		$(function () {
			let isChangeNumber = false;
			animateEl('.section01', 'add')
			animateEl('.section02', 'add')
			$(window).scroll(function () {
				let scorllTop = $(window).scrollTop();
				// console.log(scorllTop);
				initSection('.section01', -1, 450)
				initSection('.section08', 2, 575)
				initSection('.section02', 575, 1150)
				initSection('.section03', 1150, 1610)
				initSection('.section04', 1610, 2200)
				initSection('.section05', 2200, 2880)
				initSection('.section06', 2880, 3480)
				initSection('.section07', 3480, 4080)
				// 数字动画滚动位置
				console.log('[ scorllTop ] >', scorllTop)
				if (scorllTop > 1613 && scorllTop < 2913) {
					if (!isChangeNumber) {
						numberChange();
						isChangeNumber = true;
					}
				} else {
					isChangeNumber = false;
				}
			});
			function initSection(dom, top, bottom) {
				let scorllTop = $(window).scrollTop();
				if (scorllTop > top && scorllTop < bottom) {
					animateEl(dom, 'add')
				} else {
					animateEl(dom, 'remove')
				}
			}
			// 元素动画
			function animateEl(dom, type) {
				$(dom + ' .ani').each(function () {
					let that = $(this),
						effect = $(this).attr('animate-effect'),
						duration = $(this).attr('animate-duration'),
						delay = $(this).attr('animate-delay');
					switch (type) {
						case 'add':
							that.addClass('animated');
							that.addClass(effect);
							that.css('animation-delay', delay);
							that.css('animation-duration', duration);
							break;
						case 'remove':
							that.removeClass('animated');
							that.removeClass(effect);
							that.attr('style', '');
							break;
					}
				})
			}
			// 数字动画
			function numberChange() {
				$('.section04 .number .large').each(function () {
					let number = $(this).attr('data-number'),
						count = 0,
						that = $(this);
					let int = setInterval(function () {
						if (count < number) {
							count++;
							that.html(count)
						} else {
							clearInterval(int)
						}
					}, 2000 / number)
				})
			}
			// 防范风险切换
			$('.section06 .tab-box .item').mouseover(function () {
				$(this).addClass('active').siblings().removeClass('active');
				let id = $(this).attr('data-id');
				$('.content').hide()
				$('#content' + id).show()
			})
		})

		//提交咨询信息 20211018
		function submitForm() {
			let company = $('#company').val() || $('#company_m').val(),
				name = $('#name').val() || $('#name_m').val(),
				telephone = $('#telephone').val() || $('#telephone_m').val();
			$(".form input, .form-box input").trigger("blur");
			if (window.innerWidth > 768) {
				if ($(".form-box .el-form-item__error[style='display:block;']").length || $(".form-box .el-form-item__error[style='']").length) {
					return
				}
			} else {
				if ($(".form .el-form-item__error[style='display:block;']").length || $(".form .el-form-item__error[style='']").length) {
					return
				}
			}

			var url = 'https://hr.china9.cn/human/user_action/leave_message1';
			$.ajax({
				url: url, //请求地址
				data: {
					company: company,
					name: name,
					telephone: telephone,
					remarks: '智能印章咨询'
				}, //发送的数据
				dataType: 'json', //请求的数据类型
				type: 'post', //发送的请求类型
				success: function (request) {
					if (request.code == 200) {
						// layer.msg(request.message, {
						// 	icon: 1
						// });
						$("#message").fadeIn().find("#message-text").html(request.message);
						setTimeout(function () {
							$("#message").fadeOut();
						}, 3000)
						$('input').val('')
					} else {
						// layer.msg(request.message, {
						// 	icon: 2
						// });

						$("#message").fadeIn().find("#message-text").html(request.message);
						setTimeout(function () {
							$("#message").fadeOut();
						}, 3000)
					}
				}
			});
		}

		// 表单验证
		$(".form-box input, .form input").unbind().on("blur", function () {
			const that = this;
			checkVal(that)
		})

		function checkVal(ele) {
			if (!$(ele).val()) {
				$(ele).siblings(".el-form-item__error").show().text("请输入" + $(ele).attr("placeholder").replace("（必填）", "")).parent().addClass("input-error").parent().addClass("input-error");
			} else {
				if ($(ele).attr("name") === "telephone") {
					if (!/^1[3456789]\d{9}$/.test($(ele).val())) {
						$(ele).addClass("input-error").siblings(".el-form-item__error").show().text("请输入正确的手机号码").addClass("input-error");
					} else {
						$(ele).removeClass("input-error").siblings(".el-form-item__error").hide().text("").parent().removeClass("input-error");
					}
				} else {
					$(ele).removeClass("input-error").siblings(".el-form-item__error").hide().text("").parent().removeClass("input-error");
				}
			}
		}
		function shareInit() {
			var imgUrl = 'https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/logo.f983bd4.png';
			var lineLink = window.location.href;
			var shareTitle = '智能印章';
			var descContent = '开启智能考勤新时代';
			var linkMy = encodeURIComponent(location.href.split('#')[0])
			$.post('https://api.china9.cn/api/wechat_Share', {
				url: lineLink
			}, function (response) {
				const { data } = response
				var appId = data.appId;
				var timestamp = data.timestamp;
				var nonceStr = data.nonceStr;
				var signature = data.signature;
				var lineLink = data.url;
				wx.config({
					debug: false,
					appId: appId,
					timestamp: timestamp,
					nonceStr: nonceStr,
					signature: signature,
					jsApiList: ['checkJsApi', 'onMenuShareTimeline', 'onMenuShareAppMessage']
				});
				wx.ready(function () {
					console.log(imgUrl)
					wx.onMenuShareTimeline({
						title: shareTitle, // 分享标题
						link: lineLink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
						imgUrl: imgUrl//, // 分享图标
					}),
						wx.onMenuShareAppMessage({
							title: shareTitle, // 分享标题
							desc: descContent, // 分享描述
							link: lineLink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
							imgUrl: imgUrl, // 分享图标
							type: '', // 分享类型,music、video或link，不填默认为link
							dataUrl: ''//, // 如果type是music或video，则要提供数据链接，默认为空
							, success: function () {
								// 用户确认分享后执行的回调函数
								// alert('分享成功')
							},
							cancel: function () {
								// 用户取消分享后执行的回调函数
								// alert('分享取消')
							}
						})
				});
			}, "json")
		}
		shareInit()

		// 点击切换选项卡
		function toggleTab(e, parent) {
			if ($(e.target).hasClass('tab-item') && !$(e.target).hasClass('active')) {
				const index = $(e.target).index()
				$(e.target).addClass('active').siblings().removeClass('active');
				$('.' + parent + ' .tab-panel').eq(index).addClass('active').addClass('ani').siblings().removeClass('ani').removeClass('active');
				$(`.${parent}`).data('show', index);
			}
		}

		$('.section08 .tab').on('click', (e) => {
			toggleTab(e, 'section08')
		});
		$('.upgrade .tab').on('click', (e) => {
			toggleTab(e, 'upgrade')
		});
	</script>
</body>

</html>
