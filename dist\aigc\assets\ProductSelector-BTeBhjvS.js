import{k as R,A as l,l as k,B as A,ag as F,q as U,c,o as d,a6 as J,m as S,b as r,a as u,K,w as T,p as L,a7 as $,F as j,s as G,n as b,t as B,aI as H,J as O,a3 as h,_ as Q}from"./index-BBeD0eDz.js";/* empty css                */import{P as X}from"./ProductDrawer-1eJ7VyQy.js";import{a as Y}from"./index-B2D9HeJa.js";const Z={key:0,class:"section-title"},ee={class:"product-tags-container"},te={class:"product-tags"},ae=["onClick"],se=R({__name:"ProductSelector",props:{selectId:{type:[Number,String],default:""},showLabel:{type:Boolean,default:!0}},emits:["update:selectId","change"],setup(i,{expose:C,emit:E}){const w=i,_=E,p=l(!1),a=l([]),s=l(!1),g=l(5),x=k(()=>{const e=a.value.findIndex(t=>t.name==="测试产品");return e!==-1?a.value.length>e+1:a.value.length>g.value-1}),V=k(()=>{if(x.value){if(s.value)return a.value;{const e=a.value.findIndex(t=>t.name==="测试产品");return e!==-1?a.value.slice(0,e+1):a.value.slice(0,g.value-2)}}else return a.value}),W=()=>{s.value=!s.value},v=()=>{h(()=>{const e=document.querySelector(".product-tags"),t=document.querySelector(".product-tags .tag"),n=document.querySelector(".product-tags .add-tag");if(e&&t){const f=e.clientWidth,o=t.clientWidth+8,I=n?n.clientWidth+8:0,D=f-I,M=Math.floor(D/o)+1;g.value=Math.max(2,M),s.value&&(s.value=!1,h(()=>{s.value=!0}))}})};A(()=>{v(),window.addEventListener("resize",v)}),F(()=>{window.removeEventListener("resize",v)});const y=async()=>{p.value=!0;try{const e=await Y({});console.log(e,"产品列表全部"),a.value=e,h(()=>{v()})}catch(e){console.error("获取产品列表失败",e)}finally{p.value=!1}};y();const N=e=>{let t=w.selectId;e.id===w.selectId?t="":t=e.id,P(t)},m=l(!1),q=()=>{m.value=!0},z=e=>{console.log(e,"保存产品信息"),y()},P=e=>{_("update:selectId",e),_("change",e)};return C({resetTags:()=>{P("")}}),(e,t)=>{const n=K,f=O;return U((d(),c("div",{class:"product-selector",style:J({marginBottom:i.showLabel?"15px":"0"})},[i.showLabel?(d(),c("div",Z,"选择产品：")):S("",!0),r("div",ee,[r("div",te,[r("div",{class:"tag add-tag",onClick:q},[u(n,null,{default:T(()=>[u(L($))]),_:1}),t[1]||(t[1]=r("span",null,"添加",-1))]),(d(!0),c(j,null,G(V.value,o=>(d(),c("div",{key:o.id,class:b(["tag",{active:i.selectId==o.id}]),onClick:I=>N(o)},B(o.name),11,ae))),128))]),x.value?(d(),c("div",{key:0,class:"expand-button",onClick:W},[r("span",null,B(s.value?"收起":"展开"),1),u(n,{class:b(["expand-icon",{"is-expanded":s.value}])},{default:T(()=>[u(L(H))]),_:1},8,["class"])])):S("",!0)]),u(X,{ref:"productDrawerRef",modelValue:m.value,"onUpdate:modelValue":t[0]||(t[0]=o=>m.value=o),onSave:z},null,8,["modelValue"])],4)),[[f,p.value]])}}}),re=Q(se,[["__scopeId","data-v-36cc0caa"]]);export{re as P};
