var indexApp = new Vue({
  el: '#index-app',
  components: {
    'pointer-view': pointerView,  // 手指组件
    'main-page': mainPage,  // 主页面
    'section-page': sectionPage,  // 模块页面
    'flow-page': flowPage,  // 流程页面
    'end-page': endPage,  // 结束页面
    'form-page': formPage,  // 表单页面
  },
  data: {
    // type clock:考勤 approve:审批 report:日报 job:招聘
    pageFlow: pageFlow,
    // 当前显示的页面类型
    currentPage: 'main',
    // 当前显示的数据
    // currentData: null,
    currentData: null,
    
    // 当前流程、模块index
    currentFlowIndex: 0,
    // 当前显示的模块
    currentSectionIndex: 0,
  },
  computed: {
    // 当前显示的模块
    currentSection: function () {
      return this.pageFlow.find(item => item.type === this.currentDataType)
    },
    // 当前显示的流程列表
    currentFlow: function () {
      return this.currentSection.children
    },
    // 当前所有模块类型
    sectionTypes: function () {
      return this.pageFlow.map(item => item.type)
    },
    // 当前数据类型
    currentDataType: function () {
      return this.sectionTypes[this.currentSectionIndex]
    },
  },
  watch: {
    currentData: {
      // 深度监听
      handler: function (val, oldVal) {
        console.log(val)
        if (val && val.title) window.document.title = val.title
      },
      deep: true
    }
  },
  methods: {
    init: function () {
      this.currentPage = 'main'
      this.currentSectionIndex = 0
      this.currentFlowIndex = 0
      this.currentData = null
    },
    // 主页跳转 section
    jumpToSection: function (index) {
      this.currentPage = 'section'
      this.currentData = this.currentSection
      this.currentFlowIndex = 0
      if (!this.currentSection) this.jumpToEnd()
    },
    // 跳转flow
    jumpToFlow: function (data) {
      this.currentPage = 'flow'
      this.currentData = this.currentFlow[this.currentFlowIndex]
      if (this.currentFlowIndex <= this.currentFlow.length - 1) this.currentFlowIndex ++
      else this.currentSectionIndex++, this.jumpToSection()
    },
    // 跳转结束页
    jumpToEnd: function (data) {
      this.currentPage = 'end'
      this.currentData = {
        bg: './images/end-bg.png',
        logo: './images/logo-white.png',
      }
    },
    // 跳转form表单
    jumpToForm: function (data) {
      this.currentPage = 'form'
      this.currentData = {
        bg: './images/form-bg.png',
        logo: './images/logo-white.png',
      }
    },
    // 表单提交成功
    formSubmit: function () {
      
    },
    // 初始化分享
    shareInit() {
      var that = this
      var imgUrl = 'https://www.china9.cn/one-day-of-hr/images/logo-f.jpg';
      var lineLink = window.location.href;
      var shareTitle = 'hr的一天';
      var descContent = 'HR的日常：忙碌而充实，与人才共舞，确保企业脉搏强健有力。';
      var linkMy = location.href.split('#')[0]
      lineLink = linkMy
      axios.post('https://api.china9.cn/api/wechat_Share', {
        url: lineLink
      }).then(function (response) {
        console.log(response.data)
        var result = response.data.data
        var appId = result.appId;
        var timestamp = result.timestamp;
        var nonceStr = result.nonceStr;
        var signature = result.signature;
        var lineLink = result.url;
        //  var signature = 'aaaaa';

        wx.config({
          debug: false,
          appId: appId,
          timestamp: timestamp,
          nonceStr: nonceStr,
          signature: signature,
          jsApiList: ['checkJsApi', 'onMenuShareTimeline', 'onMenuShareAppMessage']
        });
        wx.ready(function () {
          console.log(imgUrl)
          wx.onMenuShareTimeline({
            title: shareTitle, // 分享标题
            link: lineLink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
            imgUrl: imgUrl//, // 分享图标
            // success: function() {
            // 	// 用户确认分享后执行的回调函数
            // },
            // cancel: function() {
            // 	// 用户取消分享后执行的回调函数
            // }
          }),
          wx.onMenuShareAppMessage({
            title: shareTitle, // 分享标题
            desc: descContent, // 分享描述
            link: lineLink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
            imgUrl: imgUrl, // 分享图标
            type: '', // 分享类型,music、video或link，不填默认为link
            dataUrl: ''//, // 如果type是music或video，则要提供数据链接，默认为空
            , success: function () {
              // 用户确认分享后执行的回调函数
              // alert('分享成功')
            },
            cancel: function () {
              // 用户取消分享后执行的回调函数
              // alert('分享取消')
            }
          })
        });
      })
    },
  },
  mounted: function () {
    this.shareInit()
  },
  created: function () {
    //
  }
})