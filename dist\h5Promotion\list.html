<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="./element-ui/index.css">
  <script src="./js/<EMAIL>"></script>
  <script src="./element-ui/index.js"></script>
  <title>资海云产品分享</title>
</head>

<body>
  <div id="list-app">
    <el-divider content-position="left">产品选择</el-divider>
    <template v-for="(item, index) in productList">
      <el-button :type="productKey == item.key ? 'primary' : ''" size="small" round @click="productKey = item.key" style="margin-bottom: 10px;">资海云{{ item.name }}</el-button>
    </template>
    <el-divider content-position="left">公司选择</el-divider>
    <el-select v-model="unique_id" filterable placeholder="请选择公司" style="width: 100%;">
      <el-option
        v-for="(item, index) in companyList"
        :key="index"
        :label="item.name"
        :value="item.unique_id">
      </el-option>
    </el-select>
    <el-button v-if="unique_id" type="primary" style="margin-top: 40px;" @click="toLink">跳转链接</el-button>
  </div>
  <script>
    var listApp = new Vue({
      el: '#list-app',
      data() {
        return {
          // 产品列表
          productList: [
            { name: '云名片', key: 'ymp' },
            { name: '智能印章', key: 'znyz' },
            { name: '全域网站建设', key: 'qywzjs' },
            { name: '视频管家', key: 'spgj' },
            { name: '智感考勤', key: 'zgkq' }
          ],
          productKey: 'ymp',
          unique_id: '',
          // 公司列表
          companyList: [
            {
              "name": "太原龙采",
              "unique_id": "cccac214aee211eab127fa163ea50a57"
            },
            {
              "name": "哈龙采",
              "unique_id": "e772f32ecea011ea8b3ffa163ea50a57"
            },
            {
              "name": "运城龙采",
              "unique_id": "24ececacaee711ea8c54fa163ea50a57"
            },
            {
              "name": "大连龙采",
              "unique_id": "26819040aee711ea997afa163ea50a57"
            },
            {
              "name": "齐齐哈尔龙采",
              "unique_id": "66a4969cce8911eabcf7fa163ea50a57"
            },
            {
              "name": "晋城龙采",
              "unique_id": "24080db2aee711ea9581fa163ea50a57"
            },
            {
              "name": "吕梁龙采",
              "unique_id": "2435365caee711eaa931fa163ea50a57"
            },
            {
              "name": "长春龙采",
              "unique_id": "660d8392ce8911ea8874fa163ea50a57"
            },
            {
              "name": "大同龙采",
              "unique_id": "254e26caaee711ea8032fa163ea50a57"
            },
            {
              "name": "沈阳龙采",
              "unique_id": "2606cf90aee711ea869dfa163ea50a57"
            },
            {
              "name": "青岛龙采",
              "unique_id": "668dd470ce8911ea9dd0fa163ea50a57"
            },
            {
              "name": "鞍山龙采",
              "unique_id": "2c6888baaee711ea9a85fa163ea50a57"
            },
            {
              "name": "临汾龙采",
              "unique_id": "25aa860eaee711ea89a2fa163ea50a57"
            },
            {
              "name": "西安龙采",
              "unique_id": "28ba6224aee711eab0f1fa163ea50a57"
            },
            {
              "name": "武汉龙采",
              "unique_id": "2cc979aeaee711ea8cc8fa163ea50a57"
            },
            {
              "name": "晋中龙采",
              "unique_id": "248f62bcaee711eabfaffa163ea50a57"
            },
            {
              "name": "佳木斯龙采",
              "unique_id": "2bcb6936aee711eab846fa163ea50a57"
            },
            {
              "name": "大庆分公司",
              "unique_id": "273a04e0aee711ea9140fa163ea50a57"
            },
            {
              "name": "牡丹江龙采",
              "unique_id": "6602a954ce8911eabfe3fa163ea50a57"
            },
            {
              "name": "南昌龙采",
              "unique_id": "4228cf362e2911eb8edafa163e5582a5"
            },
            {
              "name": "阳泉龙采",
              "unique_id": "24be6738aee711ea8b14fa163ea50a57"
            },
            {
              "name": "山东龙采",
              "unique_id": "2a360324aee711eab9a6fa163ea50a57"
            },
            {
              "name": "天津龙采",
              "unique_id": "29a1ada0aee711ea84dafa163ea50a57"
            },
            {
              "name": "福州龙采",
              "unique_id": "7b3ad464bf5b11ea82eefa163ea50a57"
            },
            {
              "name": "南京龙采",
              "unique_id": "a497618074e911ebbe3dfa163ed39cbd"
            },
          ]
        }
      },
      methods: {
        toLink() {
          window.location.href = './?type=' + this.productKey + '&unique_id=' + this.unique_id
        }
      }
    })
  </script>
</body>

</html>