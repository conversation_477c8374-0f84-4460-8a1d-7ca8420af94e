import{k as z,c as n,F as C,s as U,o as s,n as W,b as a,t as V,_ as j,l as G,A as b,m as I,a6 as T,a as u,w as f,p as B,ax as O,K as q,V as L,aq as xe,f as N,a5 as ke,al as ae,am as ie,i as D,a1 as $,X as be,aG as Ie,Z as Se,Q as we,B as Ce,E as Ee,ar as ne,d as Me,q as le,h as Ve,aH as Ue,aI as Ne,v as Be,ae as Re,j as X,at as ue,e as Ye,J as De,U as re,ap as se,W as Ge,as as Je,Y as Fe}from"./index-BBeD0eDz.js";/* empty css                *//* empty css                  *//* empty css                    */import"./AddMaterial-C0wyZ-zd.js";/* empty css                     *//* empty css                *//* empty css               */import"./input-delete-BxT9zbwn.js";import{I as Te}from"./ImportFromMaterial-lo8a_mzn.js";import"./List-CZfQXqH2.js";/* empty css                 *//* empty css              */import{E as oe}from"./empty-CSpEo1eL.js";import{j as Qe,k as Ze}from"./index-vH7ypFZe.js";import{h as ee}from"./request-Ciyrqj7N.js";const ce=()=>ee.post("/companyimage/getconfige"),Tl=y=>ee.post("/precisevideo/narratorupdate",y),Ql=y=>ee.post("/precisevideo/structureUpdate",y),Le={class:"edit-tabs flex"},ze=["onClick"],je=["src"],Pe=z({__name:"EditTabs",props:{tabs:{},activeTab:{}},emits:["update:activeTab"],setup(y,{emit:M}){const S=M,m=o=>{S("update:activeTab",o)};return(o,l)=>(s(),n("div",Le,[(s(!0),n(C,null,U(o.tabs,(k,A)=>(s(),n("div",{key:A,class:W(["tab-item flex items-center justify-center",{active:o.activeTab===k.value}]),onClick:g=>m(k.value)},[a("img",{src:k.icon,alt:""},null,8,je),a("span",null,V(k.label),1)],10,ze))),128))]))}}),Zl=j(Pe,[["__scopeId","data-v-24c3cfd2"]]),Ll="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyVpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDYuMC1jMDAzIDExNi5kZGM3YmM0LCAyMDIxLzA4LzE3LTEzOjE4OjM3ICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgMjEuMiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RDU4N0RBMDEyOEJDMTFGMEE2RjVCNjI5NTYxNUI3NjMiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RDU4N0RBMDIyOEJDMTFGMEE2RjVCNjI5NTYxNUI3NjMiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpENTg3RDlGRjI4QkMxMUYwQTZGNUI2Mjk1NjE1Qjc2MyIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpENTg3REEwMDI4QkMxMUYwQTZGNUI2Mjk1NjE1Qjc2MyIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PnQCW7UAAADkSURBVHjaYvz//z8DuYAFRISFheGS5wViTSDWBmItKA3CbKtWrZJkwaKBE4iXAbEREMsCMSMQfwfiG0B8FYiPA3E43GY0IA7EAUA8BYh3A/E1IL4HxP+g8gH4NMPAUiA+gc/PTAwUgIHTzEKkOlCI60FjQo0UzSCNO4DYDZezPaHx+B6IL6Kp0YVqBEWRJBCnAPF/ZJsXQKNlAhALA3ELkuZnQPwNiIug8W0OxJeRbWYF4s9A/AGIOaAmv4TKvQFiLyB+DsQCUEuCkG3OhKaoaKgtjUB8H8n2g1CMGhiU5CqAAAMAq3Au9fEiLlIAAAAASUVORK5CYII=",zl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyVpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDYuMC1jMDAzIDExNi5kZGM3YmM0LCAyMDIxLzA4LzE3LTEzOjE4OjM3ICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgMjEuMiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RDQ2MDQxODkyOEJDMTFGMEFFNUZCQjUzQTA0MUY4Q0EiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RDQ2MDQxOEEyOEJDMTFGMEFFNUZCQjUzQTA0MUY4Q0EiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpENDYwNDE4NzI4QkMxMUYwQUU1RkJCNTNBMDQxRjhDQSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpENDYwNDE4ODI4QkMxMUYwQUU1RkJCNTNBMDQxRjhDQSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PnFlc2IAAAB0SURBVHjaYvz//z8DuYAFmRMWFvYMSEUB8QE0dQ5AvHTVqlXSyIJMaIokgVgCiyUgMSl0QSYGCsDAaWbBIqYFDSB0MaI010IxWTaDNO5BE3MB4mZiNN8B4hNoYgqDK7SpqvkxED/Hou45VA4FMFKSqwACDADL7RVqS6156AAAAABJRU5ErkJggg==",We="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAOBAMAAADUAYG5AAAAAXNSR0IArs4c6QAAACFQTFRFAAAAgICAiYmJiYmJiYmJiYmJiIiIioqKiYmJiYmJiYmJmrW7NwAAAAp0Uk5TAAjCxsfJyszNz0MU40kAAAAzSURBVAjXY4haBQaLGaogjKUMjIJgIMCACRhh4iDFy0AMkPYlDPgB3MBZECtWMFhBGIsAzL4bd3PJPJ0AAAAASUVORK5CYII=",Oe="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAOBAMAAADUAYG5AAAAAXNSR0IArs4c6QAAAB5QTFRFAAAAgICAiYmJiYmJiYmJiIiIioqKiYmJiYmJiYmJ0enGeQAAAAl0Uk5TAAjCxsnKzM3PLH1u0QAAADZJREFUCNdj8JwJBpMZMiGMKQyMgmAgwIANgGRANBtIrQOQwQpiGDDgA3ADOyFWTGewhDAmAQAJIBiSP4RP0wAAAABJRU5ErkJggg==",$e="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAOBAMAAADUAYG5AAAAAXNSR0IArs4c6QAAACFQTFRFAAAAgICAiYmJiYmJiYmJiYmJiIiIioqKiYmJiYmJiYmJmrW7NwAAAAp0Uk5TAAjCxsfJyszNz0MU40kAAAAySURBVAjXY7BaBQZLGaIgjOUMjIJgIMCAFTDCpLLAioHAC6wdD4Ab2AWxYiWDFoSxGACUTBr/E4vgRwAAAABJRU5ErkJggg==",He={class:"font-style-list"},Ke={class:"setting-item"},Xe={class:"setting-content"},qe={key:0,class:"font-style-group"},et=["onClick"],tt={class:"improve-svg-text-stroke text h-full w-full"},lt={key:0,class:"selected-mark"},st={class:"check-icon"},ot={key:1,class:"font-style-group"},at=["onClick"],it={class:"improve-svg-text-stroke text h-full w-full"},nt={key:0,class:"selected-mark"},ut={class:"check-icon"},rt=z({__name:"ArtisticFont",props:{multi:{type:Boolean},modelValue:{},fontColor:{}},emits:["update:modelValue"],setup(y,{emit:M}){const S=y,m=M,o=G({get(){return S.modelValue},set(t){m("update:modelValue",t)}}),l=b([]),k=b(!1);(async()=>{k.value=!0;try{const t=await ce();l.value=t.titleMap}catch(t){console.log(t)}finally{k.value=!1}})();const g=t=>({backgroundColor:t.backgroundColor,fontWeight:t.fontWeight,stroke:t.strokeColor,strokeWidth:t.strokeWidth,color:t.textColor}),E=t=>{if(!t)return;Array.isArray(o.value)||(o.value=[]);const r=o.value.findIndex(p=>p.id===t.id);if(r===-1){const p=JSON.parse(JSON.stringify(t));S.fontColor&&(p.textColor=S.fontColor),o.value.push(p)}else o.value.splice(r,1)},w=t=>{if(t){const r=JSON.parse(JSON.stringify(t));S.fontColor&&(r.textColor=S.fontColor),o.value=r}};return(t,r)=>{const p=q;return s(),n("div",He,[a("div",Ke,[r[0]||(r[0]=a("div",{class:"setting-label"},"样式：",-1)),a("div",Xe,[S.multi?(s(),n("div",ot,[(s(!0),n(C,null,U(l.value,e=>{var c,h;return s(),n("div",{key:e.id,class:"font-style-item-wrap lg:w-1/6 md:w-1/4 sm:w-1/4 px-[6px] min-w-[50px] px-[6px] mb-[12px]",onClick:i=>E(e)},[a("div",{class:W(["font-style-item",{"is-selected":(c=o.value)==null?void 0:c.some(i=>i.id===e.id)}])},[a("div",it,[(s(),n("svg",{height:"50",width:"100%",style:T({backgroundColor:e.backgroundColor})},[a("use",{"xlink:href":"#text",style:T(g(e))},null,4),a("text",{x:"50%",y:"53%",id:"text",style:T(g(e))},"花字",4)],4))]),(h=o.value)!=null&&h.some(i=>i.id===e.id)?(s(),n("div",nt,[a("div",ut,[u(p,{color:"#fff"},{default:f(()=>[u(B(O))]),_:1})])])):I("",!0)],2)],8,at)}),128))])):(s(),n("div",qe,[(s(!0),n(C,null,U(l.value,e=>{var c,h;return s(),n("div",{key:e.id,class:"font-style-item-wrap lg:w-1/6 md:w-1/4 sm:w-1/4 px-[6px] min-w-[50px] mb-[12px]",onClick:i=>w(e)},[a("div",{class:W(["font-style-item",{"is-selected":((c=o.value)==null?void 0:c.id)===e.id}])},[a("div",tt,[(s(),n("svg",{height:"50",width:"100%",style:T({backgroundColor:e.backgroundColor})},[a("use",{"xlink:href":"#text",style:T(g(e))},null,4),a("text",{x:"50%",y:"53%",id:"text",style:T(g(e))},"花字",4)],4))]),((h=o.value)==null?void 0:h.id)===e.id?(s(),n("div",lt,[a("div",st,[u(p,{color:"#fff"},{default:f(()=>[u(B(O))]),_:1})])])):I("",!0)],2)],8,et)}),128))]))])])])}}}),ct=j(rt,[["__scopeId","data-v-08ca8f60"]]),de="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAMAAAAM7l6QAAABNVBMVEUAAADQ3f7Q3f7R3v/Q3v7Q3f7R3v/R3f/Q3f7Q3f/L2v/P3f/Q3f7P3v7R3f/Q3f7Q3f/Q3f/R3f7D2//R3v7Q3v7R3v/Q3P3Q3P3P3P7Q3P3Q3f3P3f3O2//Q3f7///8hbP//tETM2/7T3/7/zH/9/v8/f/8xdv9sm//P3f6ow/7/5b7/zoTC1f+50P9pmf/i6v7I2P6Grf5Xj/7/7dNCgf/1+P7z9v6/0v61zP6txv6Ttv6CrP51of9wnv/4+v7q8P7m7f7k6/7b5f6yyf6gvv6Xuf5mmP5dk/5bkf5HhP48ff7+/Pr//Pf/+O7/9ef44L/x277/1Zb/tUZ9pf9Ihf83ev8rcv8kbv/W4f6LsP6Jr/55pf5Piv5Jhv43ev7/8d3/58X/58T33rr/2J7/ulT/uE902fzNAAAAHnRSTlMA6uXD6JaRSfVSEg757K6Of1xXB+7JmoR87Kidhk4QvntKAAABvElEQVQoz32Sd1viQBCHNwlVOEGu6LVhk5AEoiBNur1377xe7d//IzgzG4l58PH9Y3fn97I8w7BizMuPCUPLZmNGIikmmJp9AWO0/FTUzmgQIVZ4bN/ABPHQTsMTTD/Y90HQb+62Wou/a9H7M6raPpABf32VFLjnt3Rs7En5bWXH94dlT8qyxf1lUH8A4p/0hhVgrOZ32eJTHqfxiiPvoIebvblp41Y79Pi6JkQaiNEeXq2vFpHVOn56v8lxUsRpq7o9sLvFgK4NPbdKeUK8pq35B6DDiukALP+i3BA86tYWrClzcUnrGgz2KdcFt+tWbEfp0jmtjm0tUZ4SWVzxXGd5XCyZfKiDy5q/vOJuj6qU3n1WujbyWeuqtSUcJfojc6Fkni6c1qT88ZVbUz9sF/XKUfuY9Lx5tohlmfKcGsuQ6jPzSulPpHd4LGqoPulrNKEeqKGKWdwHWH85uTm5xdba5+1lLLcwnqM/VANo/JfezyLRnscFPHloYd+Zh+dg9RuwEUzG2YBGHy2kow/RXu84TnfdDsrEs0/xnRgTn5B0N6QQi0o9LSJk8looY3MZMUEyZ+iplG7kkmF2D0i/ZClUYvIwAAAAAElFTkSuQmCC",dt={class:"w-full"},mt={class:"flex items-center flex-1 h-full"},vt=["onClick"],pt=["onClick"],ft={key:2,src:de,class:"mr-[9px]"},At={class:"w-0 text-[16px] flex-1 h-full flex flex-col"},gt={class:"text-ellipsis overflow-hidden text-[#555] leading-none"},yt={key:0,class:"text-[#999] text-[14px] leading-none mt-[9px]"},_t={class:"select-icon absolute top-0 right-0"},ht={class:"absolute top-[-18px] right-[-15px]"},xt={key:1,class:"flex-1"},kt={class:"w-full"},bt={class:"flex items-center flex-1"},It=["onClick"],St=["onClick"],wt={key:2,src:de,class:"mr-[9px]"},Ct={class:"w-0 text-[16px] flex-1 h-full flex flex-col"},Et={class:"text-ellipsis overflow-hidden text-[#555]"},Mt={key:0,class:"text-[#999] text-[14px] mt-[9px]"},Vt={class:"select-icon absolute top-0 right-0"},Ut={class:"absolute top-[-15px] right-[-15px]"},Nt={key:1,class:"flex-1"},Bt=z({__name:"VoiceList",props:{voices:{default:()=>[]},showEmpty:{type:Boolean,default:!0},emptyText:{default:"暂无系统音色"},value:{default:""},multi:{type:Boolean,default:!1},voice:{default:null},showDuration:{type:Boolean,default:!1},showIcon:{type:Boolean,default:!0},showDel:{type:Boolean,default:!1}},emits:["update:value","update:voice","del-voice","load"],setup(y,{expose:M,emit:S}){const m=y,o=S,l=G({get(){return m.value},set(e){o("update:value",e)}});L(()=>l.value,e=>{if(e)if(m.multi){if(!Array.isArray(e))return;const c=m.voices.filter(h=>e.includes(h.id));o("update:voice",c)}else{const c=m.voices.find(h=>h.id===e);o("update:voice",c)}else o("update:voice",null)},{immediate:!0});const k=e=>{o("del-voice",e)},A=b(null),g=b(null),E=e=>{if(A.value&&g.value&&g.value!==e&&(A.value.pause(),g.value.playing=!1),e.playing)A.value&&(A.value.pause(),A.value=null),e.playing=!1,g.value=null;else{const c=new Audio(e.url);c.addEventListener("ended",()=>{e.playing=!1,A.value=null,g.value=null}),c.addEventListener("error",()=>{e.playing=!1,A.value=null,g.value=null,we.error("音频播放失败")}),c.play().then(()=>{e.playing=!0,A.value=c,g.value=e}).catch(h=>{console.error("播放音频失败:",h),e.playing=!1})}},w=xe({loading:!1,_scrollTop:0}),t=b(null),r=e=>{if(!t.value)return;let c=t.value.wrapRef;t.value.moveY=c.scrollTop*100/c.clientHeight,t.value.moveX=c.scrollLeft*100/c.clientWidth;let h=c.scrollHeight-c.clientHeight;e.scrollTop+2>=h&&(w._scrollTop=e.scrollTop,o("load"))};return M({resetScroll:()=>{t.value&&(w._scrollTop=0,t.value.setScrollTop(w._scrollTop))}}),(e,c)=>{const h=q,i=ie,Q=be,Z=ae,J=Se,H=Ie,P=ke;return s(),N(P,{"max-height":"200px",class:"w-full",onScroll:r,ref_key:"scrollbarRef",ref:t},{default:f(()=>[e.multi?(s(),N(H,{key:1,modelValue:l.value,"onUpdate:modelValue":c[1]||(c[1]=_=>l.value=_),class:"overflow-hidden w-full"},{default:f(()=>[a("div",kt,[e.voices.length?(s(!0),n(C,{key:0},U(e.voices,_=>(s(),n("div",{class:"w-full",key:_.id},[u(J,{border:"",value:_.id,class:"mb-[10px] voice-item relative items-center"},{default:f(()=>[a("div",bt,[_.playing?(s(),n("i",{key:1,class:"iconfont icon-07zanting text-[18px] mr-[14px] text-[#999] cursor-pointer",onClick:D(F=>E(_),["stop"])},null,8,St)):(s(),n("i",{key:0,class:"iconfont icon-icon_play text-[18px] mr-[14px] text-[#999] cursor-pointer",onClick:D(F=>E(_),["stop"])},null,8,It)),e.showIcon?(s(),n("img",wt)):I("",!0),a("div",Ct,[a("div",Et,V(_.name),1),e.showDuration&&_.times?(s(),n("div",Mt,V(_.times),1)):I("",!0)])]),e.showDel?(s(),N(h,{key:0,size:20,class:"delete-icon cursor-pointer",onClick:D(F=>k(_.id),["stop"]),color:"#999999"},{default:f(()=>[u(B($))]),_:2},1032,["onClick"])):I("",!0),a("div",Vt,[a("div",Ut,[u(h,{size:16,class:"text-primary",color:"#fff"},{default:f(()=>[u(B(O))]),_:1})])])]),_:2},1032,["value"])]))),128)):(s(),n("div",Nt,[e.showEmpty?(s(),N(Q,{key:0,image:B(oe),description:"暂无系统音色","image-size":207},null,8,["image"])):I("",!0)]))])]),_:1},8,["modelValue"])):(s(),N(Z,{key:0,modelValue:l.value,"onUpdate:modelValue":c[0]||(c[0]=_=>l.value=_),class:"overflow-hidden w-full"},{default:f(()=>[a("div",dt,[e.voices.length?(s(!0),n(C,{key:0},U(e.voices,_=>(s(),n("div",{class:"w-full",key:_.id},[u(i,{value:_.id,border:"",class:"mb-[10px] voice-item relative items-center"},{default:f(()=>[a("div",mt,[_.playing?(s(),n("i",{key:1,class:"iconfont icon-07zanting text-[18px] mr-[14px] text-[#999] cursor-pointer",onClick:D(F=>E(_),["stop"])},null,8,pt)):(s(),n("i",{key:0,class:"iconfont icon-icon_play text-[18px] mr-[14px] text-[#999] cursor-pointer",onClick:D(F=>E(_),["stop"])},null,8,vt)),e.showIcon?(s(),n("img",ft)):I("",!0),a("div",At,[a("div",gt,V(_.name),1),e.showDuration&&_.times?(s(),n("div",yt,V(_.times),1)):I("",!0)])]),e.showDel?(s(),N(h,{key:0,size:20,class:"delete-icon cursor-pointer",onClick:D(F=>k(_.id),["stop"]),color:"#999999"},{default:f(()=>[u(B($))]),_:2},1032,["onClick"])):I("",!0),a("div",_t,[a("div",ht,[u(h,{size:16,class:"text-primary",color:"#fff"},{default:f(()=>[u(B(O))]),_:1})])])]),_:2},1032,["value"])]))),128)):(s(),n("div",xt,[e.showEmpty?(s(),N(Q,{key:0,image:B(oe),description:"暂无系统音色","image-size":207},null,8,["image"])):I("",!0)]))])]),_:1},8,["modelValue"]))]),_:1},512)}}}),Rt=j(Bt,[["__scopeId","data-v-60465dba"]]),Yt={class:"music-setting h-full overflow-auto"},Dt={class:"selected-display w-full"},Gt={class:"form-item flex justify-between items-center mb-[10px]"},Jt={class:"text-[#555] text-[16px]"},Ft={class:"text-[16px]"},Tt={class:"bg-white p-[10px] border border-[#E2E6EC] rounded-[6px] max-h-[100px] overflow-y-auto transition-all duration-300"},Qt={class:"name flex items-center"},Zt={class:"flex-1 ml-[10px] text-ellipsis overflow-hidden text-[#555] h-[32px]"},Lt={key:0,class:"selected-item flex items-center justify-between"},zt={class:"name flex items-center"},jt={class:"flex-1 ml-[10px] text-ellipsis overflow-hidden text-[#555] h-[32px]"},Pt={class:"flex items-center flex-1"},Wt={class:"text-[#555] text-[16px] w-[40px]"},Ot={class:"w-full flex justify-between items-center"},$t={class:"w-[200px]"},Ht={class:"relative"},Kt={key:0,class:"text-center py-2 text-gray-500"},Xt={key:1,class:"text-center py-2 text-gray-500"},qt=z({__name:"MusicSetting",props:{multi:{type:Boolean,default:!0},music:{default:()=>({volume:76,random:1,music:[]})},showClose:{type:Boolean,default:!1}},emits:["change"],setup(y,{expose:M,emit:S}){const m=y,o=S,l=b({volume:76,random:1,music:[],musicIds:[],material:[],materialIds:[]}),k=b(1);L(()=>m.music,v=>{v&&(l.value.volume=v.volume,l.value.random=v.random,l.value.music=v.music)},{immediate:!0});const A=G(()=>m.multi?[...l.value.music,...l.value.material].filter(v=>v):l.value.music||l.value.material),g=b(""),E=b(""),w=b([]),t=async()=>{try{w.value=await Qe({}),w.value.unshift({id:"",name:"默认"})}catch(v){console.error("获取音乐分类失败:",v)}},r=b([]),p=b({page:1,perPage:12}),e=b(1),c=b(!1),h=b(!1),i=b(!0),Q=()=>{i.value=!i.value},Z=b(null),J=async()=>{try{if(c.value)return;c.value=!0;const v={name:g.value,category_id:E.value,...p.value},d=await Ze(v);e.value=d.last_page,p.value.page=d.current_page,h.value=p.value.page>=e.value,p.value.page===1?(r.value=d.data,Z.value&&Z.value.resetScroll()):r.value=[...r.value,...d.data]}catch(v){console.error("获取音乐列表失败:",v)}finally{c.value=!1}},H=()=>{c.value||h.value||(p.value.page++,J())};L([g,E],()=>{p.value.page=1,h.value=!1,J()});const P=G(()=>m.multi?A.value.length:A.value&&A.value.id?1:0),_=v=>{m.multi?(l.value.music=l.value.music.filter(d=>d.id!==v),l.value.musicIds=l.value.musicIds.filter(d=>d!==v),l.value.material=l.value.material.filter(d=>d.id!==v),l.value.materialIds=l.value.materialIds.filter(d=>d!==v)):(l.value.music=null,l.value.musicIds=void 0,l.value.materialIds=void 0,l.value.material=void 0)},F=v=>{const d=new Map;return v.reverse().filter(R=>!d.has(R.id)&&d.set(R.id,!0)).reverse()},me=v=>{const d=JSON.parse(JSON.stringify(v));m.multi?(l.value.material=F([...l.value.music,...d.map(R=>({...R,type:"material",name:R.title}))]),l.value.materialIds=Array.from(new Set([...l.value.musicIds,...d.map(R=>R.id)]))):(l.value.music=null,l.value.musicIds=void 0,l.value.materialIds=d[0].id,d[0].type="material",d[0].name=d[0].title,l.value.material=d[0])};L(()=>m.multi,v=>{v?(l.value.music=[],l.value.musicIds=[]):(l.value.musicIds=void 0,l.value.music=void 0)},{immediate:!0}),L(()=>[l.value,A.value],([v,d])=>{const R={volume:v.volume,random:v.random,music:d};o("change",R)},{deep:!0,immediate:!0});const ve=()=>{k.value===0&&(l.value.music=[],l.value.musicIds=[],l.value.material=[],l.value.materialIds=[],l.value.volume=76,l.value.random=1)};return Ce(async()=>{await t(),await J()}),M({setDefaultValue:v=>{l.value.volume=v.volume,l.value.random=v.random,l.value.music=v.music,l.value.musicIds=v.musicIds,l.value.material=v.material,l.value.materialIds=v.materialIds}}),(v,d)=>{const R=ne,Y=Me,K=q,pe=Ve,te=Re,fe=ue,Ae=Ye,ge=ie,ye=ae,_e=Ee,he=De;return s(),n("div",Yt,[u(_e,{model:l.value,"label-position":"left"},{default:f(()=>[v.showClose?(s(),N(Y,{key:0,label:"是否需要音乐：",class:"flex items-center"},{default:f(()=>[u(R,{modelValue:k.value,"onUpdate:modelValue":d[0]||(d[0]=x=>k.value=x),"active-value":1,"inactive-value":0,"inline-prompt":"","active-text":"开","inactive-text":"关",onChange:ve,size:"large"},null,8,["modelValue"])]),_:1})):I("",!0),k.value?(s(),n(C,{key:1},[l.value.random===0?(s(),n(C,{key:0},[P.value?(s(),N(Y,{key:0},{default:f(()=>[a("div",Dt,[a("div",Gt,[a("span",Jt,"已选择"+V(P.value)+"首音乐：",1),u(pe,{type:"primary",link:"",onClick:Q},{default:f(()=>[a("span",Ft,V(i.value?"收起":"展开"),1),u(K,{class:"font-bold"},{default:f(()=>[i.value?(s(),N(B(Ue),{key:0})):(s(),N(B(Ne),{key:1}))]),_:1})]),_:1})]),le(a("div",Tt,[v.multi?(s(!0),n(C,{key:0},U(A.value,x=>(s(),n("div",{class:"selected-item flex items-center justify-between",key:x.id},[a("div",Qt,[u(te,{type:x.type==="material"?"success":"primary"},{default:f(()=>[X(V(x.type==="material"?"素材库":"音乐库"),1)]),_:2},1032,["type"]),a("div",Zt,V(x.name),1)]),u(K,{size:20,class:"delete-icon cursor-pointer",onClick:D(bl=>_(x.id),["stop"]),color:"#999999"},{default:f(()=>[u(B($))]),_:2},1032,["onClick"])]))),128)):(s(),n(C,{key:1},[A.value?(s(),n("div",Lt,[a("div",zt,[u(te,{type:A.value.type==="material"?"success":"primary"},{default:f(()=>[X(V(A.value.type==="material"?"素材库":"音乐库"),1)]),_:1},8,["type"]),a("div",jt,V(A.value.name),1)]),u(K,{size:20,class:"delete-icon cursor-pointer",onClick:d[1]||(d[1]=D(x=>_(A.value.id),["stop"])),color:"#999999"},{default:f(()=>[u(B($))]),_:1})])):I("",!0)],64))],512),[[Be,i.value]])])]),_:1})):I("",!0)],64)):I("",!0),u(Y,{label:"音乐音量："},{default:f(()=>[a("div",Pt,[u(fe,{modelValue:l.value.volume,"onUpdate:modelValue":d[2]||(d[2]=x=>l.value.volume=x),min:0,max:100,class:"pr-[12px]"},null,8,["modelValue"]),a("span",Wt,V(l.value.volume),1)])]),_:1}),u(Y,{label:"随机音乐：",class:"flex items-center"},{default:f(()=>[u(R,{modelValue:l.value.random,"onUpdate:modelValue":d[3]||(d[3]=x=>l.value.random=x),"active-value":1,"inactive-value":0,"inline-prompt":"","active-text":"开","inactive-text":"关",size:"large"},null,8,["modelValue"])]),_:1}),l.value.random===0?(s(),n(C,{key:1},[u(Y,null,{default:f(()=>[u(Te,{label:"素材库添加：","btn-text":"从素材库添加",onSubmit:me,"multi-select":m.multi,type:"audio"},null,8,["multi-select"])]),_:1}),u(Y,{label:"音乐库：",class:"flex justify-between items-center"},{default:f(()=>[a("div",Ot,[a("div",$t,[u(Ae,{"prefix-icon":"Search",modelValue:g.value,"onUpdate:modelValue":d[4]||(d[4]=x=>g.value=x),placeholder:"请输入关键词",onChange:J},null,8,["modelValue"])])])]),_:1}),u(Y,{style:{"margin-bottom":"12px"},"element-loading-background":"#F2F6F9"},{default:f(()=>[u(ye,{modelValue:E.value,"onUpdate:modelValue":d[5]||(d[5]=x=>E.value=x)},{default:f(()=>[(s(!0),n(C,null,U(w.value,x=>(s(),N(ge,{border:"",key:x.id,value:x.id,class:"mr-[10px]",onChange:J},{default:f(()=>[X(V(x.name),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),le((s(),N(Y,null,{default:f(()=>[a("div",Ht,[u(Rt,{voices:r.value,"show-empty":!1,multi:m.multi,value:l.value.musicIds,"onUpdate:value":d[6]||(d[6]=x=>l.value.musicIds=x),voice:l.value.music,"onUpdate:voice":d[7]||(d[7]=x=>l.value.music=x),"show-icon":!1,"show-del":!1,"show-duration":!0,onLoad:H,ref_key:"voiceListRef",ref:Z},null,8,["voices","multi","value","voice"]),c.value?(s(),n("div",Kt," 加载中... ")):I("",!0),h.value&&r.value.length>0?(s(),n("div",Xt," 没有更多数据了 ")):I("",!0)])]),_:1})),[[he,c.value]])],64)):I("",!0)],64)):I("",!0)]),_:1},8,["model"])])}}}),jl=j(qt,[["__scopeId","data-v-f4f703c7"]]),el=re("fontStyle",()=>{const y=b([]),M=b(!1),S=b(null),m=async()=>{if(!(y.value.length>0)){M.value=!0,S.value=null;try{const r=await ce();y.value=r.titleMap||[]}catch(r){console.error("获取字体样式失败",r),S.value=r.message||"获取字体样式失败"}finally{M.value=!1}}},o=r=>y.value.find(p=>Number(p.id)===Number(r))||null,l=r=>r>=0&&r<y.value.length?y.value[r]:null,k=r=>y.value.findIndex(p=>Number(p.id)===Number(r)),A=(r,p)=>{const e={fontWeight:r.fontWeight||"normal",letterSpacing:r.letterSpacing||"normal",textTransform:r.textTransform||"none",filter:"none",textShadow:"none"};return e.color=p||r.textColor||"#fff",e},g=(r,p)=>{const e=o(r);return e?A(e,p):null},E=(r,p)=>r.map(e=>o(e)).filter(e=>e!==null).map(e=>A(e,p));return{textStyles:y,loading:M,error:S,fetchFontStyles:m,getStyleById:o,getStyleByIndex:l,getStyleIndex:k,computeTextStyle:A,getComputedStyleById:g,getComputedStylesByIds:E,getComputedStylesByIdString:(r,p)=>{if(!r)return[];const e=r.toString().split(",").map(c=>c.trim());return E(e,p)},getStylePreviewStyle:r=>({backgroundColor:r.backgroundColor||"transparent"})}}),tl=re("titleSetting",()=>{const y=el(),M={open:1,font:"思源黑体",size:16,aligning:"center",startime:{mm:"00",ss:"00"},endtime:{mm:"00",ss:"00"},style:"1",color:"#fff",content:"示例标题点击可编辑",x:0,y:0,transparency:100},S=20,m=b([]),o=b(0),l=e=>{m.value=e},k=()=>{m.value.length>=S||(m.value.push(JSON.parse(JSON.stringify(M))),console.log("🚀 ~ addTitle ~ titleList: ",m),o.value=m.value.length-1)},A=e=>{e>=0&&e<m.value.length&&(m.value.splice(e,1),e===o.value?o.value=Math.max(0,e-1):e<o.value&&o.value--)},g=G(()=>m.value.length===0?null:m.value[o.value]||m.value[0]),E=e=>{e>=0&&e<m.value.length&&(o.value=e)},w=G(()=>{const e=g.value;if(!e)return null;const c=JSON.parse(JSON.stringify(e));return c.style&&!y.loading&&y.textStyles.length>0&&(c.computedStyles=y.getComputedStylesByIdString(c.style,c.color)),c}),t=e=>{m.value[o.value]={...JSON.parse(JSON.stringify(M)),...g.value,...e}},r=G(()=>!g.value||!g.value.style?[]:g.value.style.split(",").map(Number));return{titleList:m,activeTitleIndex:o,addTitle:k,removeTitle:A,activeTitle:g,activeTitleWithStyles:w,setTitleList:l,setActiveTitleIndex:E,updateActiveTitle:t,selectedStyleIndices:r,reset:()=>{m.value=[],o.value=0}}}),ll={class:"title-setting h-full overflow-y-auto"},sl={key:0,class:"setting-item flex items-center"},ol={class:"setting-item flex items-center"},al={class:"setting-item flex items-center"},il={class:"setting-controls"},nl={class:"align-buttons"},ul=["onClick"],rl={key:0,src:We,alt:""},cl={key:1,src:Oe,alt:""},dl={key:2,src:$e,alt:""},ml={class:"setting-item"},vl={class:"time-selector"},pl={class:"setting-item"},fl={class:"time-selector"},Al={class:"setting-item flex items-center"},gl={class:"setting-controls"},yl={class:"setting-item flex items-center"},_l={class:"setting-controls pl-[30px]"},hl={class:"flex items-center flex-1"},xl={class:"text-[#555] text-[16px] w-[40px]"},kl=z({__name:"FontStyle",props:{modelValue:{type:Object,default:()=>({})},showTime:{type:Boolean,default:!0},showClose:{type:Boolean,default:!1},styleMultiple:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(y,{emit:M}){const S=y,m=M,o=G({get(){return S.modelValue||k.activeTitle||{open:!0,size:16,color:"#FFFFFF",startime:{mm:"00",ss:"00"},endtime:{mm:"00",ss:"00"},transparency:100,aligning:"left",style:{}}},set(w){m("update:modelValue",w),w&&k.updateActiveTitle(w)}}),l=[12,14,16,18,20,24,28,32,36,40,44,48,54,60,72],k=tl(),A=se(["思源黑体"]),g=se(["left","center","right"]),E=(w="#fff")=>{o.value.style.textColor=w};return(w,t)=>{const r=ne,p=Fe,e=Ge,c=Je,h=ue;return s(),n("div",ll,[y.showClose?(s(),n("div",sl,[t[11]||(t[11]=a("div",{class:"setting-label !mb-[0px]"},"显示：",-1)),u(r,{modelValue:o.value.open,"onUpdate:modelValue":t[0]||(t[0]=i=>o.value.open=i),"inline-prompt":"","active-text":"开","inactive-text":"关","active-value":1,"inactive-value":0},null,8,["modelValue"])])):I("",!0),a("div",ol,[t[12]||(t[12]=a("div",{class:"setting-label !mb-[0px]"},"字体：",-1)),u(e,{modelValue:o.value.font,"onUpdate:modelValue":t[1]||(t[1]=i=>o.value.font=i),class:"font-select",style:{width:"200px"}},{default:f(()=>[(s(!0),n(C,null,U(B(A),i=>(s(),N(p,{key:i,label:i,value:i},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),a("div",al,[t[13]||(t[13]=a("div",{class:"setting-label !mb-0"},"字号：",-1)),a("div",il,[u(e,{modelValue:o.value.size,"onUpdate:modelValue":t[2]||(t[2]=i=>o.value.size=i),class:"font-size-select"},{default:f(()=>[(s(),n(C,null,U(l,i=>u(p,{key:i,label:i,value:i},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),a("div",nl,[(s(!0),n(C,null,U(B(g),i=>(s(),n("div",{class:W(["align-button",{active:o.value.aligning===i}]),onClick:Q=>o.value.aligning=i},[i==="left"?(s(),n("img",rl)):i==="center"?(s(),n("img",cl)):i==="right"?(s(),n("img",dl)):I("",!0)],10,ul))),256))])])]),y.showTime?(s(),n(C,{key:1},[a("div",ml,[t[16]||(t[16]=a("div",{class:"setting-label"},"显示开始时间：",-1)),a("div",vl,[u(e,{modelValue:o.value.startime.mm,"onUpdate:modelValue":t[3]||(t[3]=i=>o.value.startime.mm=i),class:"time-select"},{default:f(()=>[(s(),n(C,null,U(60,i=>u(p,{key:`min-${i-1}`,label:String(i-1).padStart(2,"0"),value:String(i-1).padStart(2,"0")},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),t[14]||(t[14]=a("span",{class:"time-unit"},"分",-1)),u(e,{modelValue:o.value.startime.ss,"onUpdate:modelValue":t[4]||(t[4]=i=>o.value.startime.ss=i),class:"time-select"},{default:f(()=>[(s(),n(C,null,U(60,i=>u(p,{key:`sec-${i-1}`,label:String(i-1).padStart(2,"0"),value:String(i-1).padStart(2,"0")},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),t[15]||(t[15]=a("span",{class:"time-unit"},"秒",-1))])]),a("div",pl,[t[19]||(t[19]=a("div",{class:"setting-label"},"显示结束时间：",-1)),a("div",fl,[u(e,{modelValue:o.value.endtime.mm,"onUpdate:modelValue":t[5]||(t[5]=i=>o.value.endtime.mm=i),class:"time-select"},{default:f(()=>[(s(),n(C,null,U(60,i=>u(p,{key:`min-${i-1}`,label:String(i-1).padStart(2,"0"),value:String(i-1).padStart(2,"0")},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),t[17]||(t[17]=a("span",{class:"time-unit"},"分",-1)),u(e,{modelValue:o.value.endtime.ss,"onUpdate:modelValue":t[6]||(t[6]=i=>o.value.endtime.ss=i),class:"time-select"},{default:f(()=>[(s(),n(C,null,U(60,i=>u(p,{key:`sec-${i-1}`,label:String(i-1).padStart(2,"0"),value:String(i-1).padStart(2,"0")},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),t[18]||(t[18]=a("span",{class:"time-unit"},"秒",-1))])])],64)):I("",!0),a("div",Al,[t[20]||(t[20]=a("div",{class:"setting-label !mb-0"},"文字颜色：",-1)),a("div",gl,[u(c,{modelValue:o.value.color,"onUpdate:modelValue":t[7]||(t[7]=i=>o.value.color=i),onChange:t[8]||(t[8]=i=>E(o.value.color))},null,8,["modelValue"])])]),a("div",yl,[t[21]||(t[21]=a("div",{class:"setting-label !mb-0 whitespace-nowrap"},"不透明度：",-1)),a("div",_l,[a("div",hl,[u(h,{modelValue:o.value.transparency,"onUpdate:modelValue":t[9]||(t[9]=i=>o.value.transparency=i),min:0,max:100,class:"pr-[12px]"},null,8,["modelValue"]),a("span",xl,V(o.value.transparency),1)])])]),u(ct,{modelValue:o.value.style,"onUpdate:modelValue":t[10]||(t[10]=i=>o.value.style=i),"font-color":o.value.color},null,8,["modelValue","font-color"])])}}}),Pl=j(kl,[["__scopeId","data-v-23bf9ed7"]]);export{ct as A,zl as B,Zl as E,Pl as F,jl as M,Rt as V,Ll as Y,We as _,Tl as a,Oe as b,$e as c,el as d,tl as e,de as f,Ql as u};
