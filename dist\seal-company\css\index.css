* {
  margin: 0;
  padding: 0;
}

body {
  width: 100%;
  overflow-x: hidden;
}

section {
  overflow: hidden;
}

.flex {
  display: flex;
  align-items: center;
}

.flex-wrap {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.flex-column {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.container {
  width: 1320px;
  min-width: 1320px;
  margin: auto;
}

.container h3 {
  font-size: 32px;
  font-family: SourceHanSansCN;
  font-weight: 400;
  color: #222222;
  line-height: 45px;
  text-align: center;
}

.section-btn {
  width: 200px;
  height: 50px;
  border: 1px solid #bc0210;
  font-size: 20px;
  font-family: SourceHanSansCN;
  font-weight: 300;
  color: #bc0210;
  line-height: 50px;
  cursor: pointer;
  text-align: center;
  display: block;
  text-decoration: none;
  transition: all ease-in-out 0.3s;
}

/* banner */
.section01 {
  background: url(../newImg/banner.png) no-repeat center;
  background-size: cover;
  height: 550px;
}

.section01 .container {
  width: auto;
  min-width: 1367px;
}

.section01 .container .tag {
  width: 190px;
  height: 40px;
  border-radius: 20px;
  border: 1px solid #FFFFFF;
  font-size: 20px;
  color: #FFFFFF;
  line-height: 40px;
  text-align: center;
}

.section01 .left-part img {
  width: 290px;
}

.section01 .left-part .img-2 {
  margin-left: -100px;
}

.section01 .right-part {
  margin-left: 680px;
}

.section01 .right-part h1 {
  font-size: 46px;
  font-family: SourceHanSansCN;
  font-weight: 400;
  color: #fff;
  margin-top: 27px;
  line-height: 60px;
}

.section01 .right-part h3 {
  font-size: 26px;
  font-family: SourceHanSansCN;
  font-weight: 300;
  color: #222222;
  line-height: 45px;
  margin-bottom: 40px;
  text-align: left;
  line-height: 1;
}

.section01 .section-btn {
  margin-top: 74px;
  width: 180px;
  height: 60px;
  background: #FFFFFF;
  font-size: 24px;
  color: #BC0210;
  line-height: 60px;
  text-align: center;
}

.section02 {
  height: 575px;
  background-color: #fff;
}

.section02 h3 {
  margin-bottom: 70px;
}

.section02 .item-box .item {
  width: calc(100% / 2 - 20px);
  height: 80px;
  border: 1px solid #dbdbdb;
  margin-right: 36px;
  box-sizing: border-box;
  margin-bottom: 20px;
  padding-left: 30px;
  padding-right: 30px;
  cursor: pointer;
  transition: all ease-in-out 0.3s;
}

.section02 .item-box .item:nth-child(2n) {
  margin-right: 0;
}

.section02 .item-box .item:hover {
  border-color: #bc0210;
}

.section02 .item-box .item .text-box {
  width: calc(100% - 23px);
}

.section02 .item-box .item .large {
  font-size: 20px;
  font-family: SourceHanSansCN;
  font-weight: 500;
  color: #333333;
  line-height: 45px;
  margin-left: 14px;
  margin-right: 20px;
}

.section02 .item-box .item .text {
  font-size: 14px;
  font-family: SourceHanSansCN;
  font-weight: 300;
  color: #333333;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: normal;
  width: calc(100% - 120px);
}

.section02 .item-box .item:nth-child(3) .text {
  letter-spacing: -1.7px;
}

/* æ™ºèƒ½ç”¨å° æ ¸å¿ƒåŠŸèƒ½ */
.section03 {
  background-color: #f5f5f6;
  height: 460px;
}

.section03 .item-box {
  justify-content: space-between;
  margin-top: 48px;
}

.section03 .item-box .item {
  width: 165px;
  cursor: pointer;
}

.section03 .item-box .item .top-box {
  position: relative;
  height: 180px;
}

.section03 .item-box .item .top-box .b {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 2;
  transition: all ease-in-out 1s;
}

.section03 .item-box .item .top-box .red {
  position: absolute;
  right: 22px;
  bottom: 32px;
  z-index: 1;
  transition: all ease-in-out 1s;
}

.section03 .item-box .item .top-box .icon {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 4;
  transition: all ease-in-out 1s;
}

.section03 .item-box .item p {
  text-align: center;
  font-size: 18px;
  font-family: SourceHanSansCN;
  font-weight: 500;
  color: #222222;
  line-height: 45px;
  margin-top: 16px;
}

.section03 .item-box .item:hover .top-box .b {
  /* transform: rotateZ(360deg); */
}

.section03 .item-box .item:hover .red {
  /* transform: rotateZ(360deg) scale(1) */
}

.section03 .item-box .item:hover .icon {
  transform: rotateY(360deg);
}

/* æ™ºèƒ½ä¸€ä½“åŒ– ç®¡ç†æ›´è½»æ¾ */
.section04 {
  height: 590px;
}

.section04 .content-box {
  height: 280px;
  border: 1px solid #f4d4d7;
  margin-top: 32px;
}

.section04 .content-box .bot-title {
  height: 57px;
  line-height: 57px;
  text-align: center;
  background: #fff9fa;
  border-top: 1px solid #f4d4d7;
  font-size: 20px;
  font-family: SourceHanSansCN;
  font-weight: 400;
  color: #bc0210;
}

.section04 .item-box {
  justify-content: space-around;
  height: calc(100% - 58px);
}

.section04 .item-box p {
  font-size: 20px;
  font-family: SourceHanSansCN;
  font-weight: 300;
  color: #000033;
  line-height: 45px;
  text-align: center;
}

.section04 .item-box .top-box {
  position: relative;
}

.section04 .item-box .top-box .number {
  font-family: Bahnschrift;
  font-weight: 400;
  color: #bc0210;
  text-align: center;
}

.section04 .item-box .top-box .number .large {
  font-size: 60px;
}

.section04 .item-box .top-box .number .small {
  font-size: 30px;
}

.section04 .item-box .top-box .icon {
  position: absolute;
  right: 5px;
  top: 0;
  display: flex;
}

.section04 .item-box .top-box .icon img:last-child {
  height: 14px;
  width: 9px;
  margin-left: 2px;
  margin-top: -3px;
}

.section04 .item-box .item:last-child .top-box .icon {
  transform: rotateZ(180deg);
  flex-direction: row-reverse;
  right: -10px;
}

/* ç”¨å°å¿«æ· å®‰å…¨é˜²æŽ§ */
.section05 {
  height: 680px;
  background: #f5f5f6;
}

.section05 .item-box {
  justify-content: space-between;
  margin-top: 87px;
}

.section05 .item-box .item {
  width: 280px;
  height: 370px;
  padding: 60px 30px;
  background-color: #ffffff;
  border-radius: 10px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all ease-in-out 0.3s;
}

.section05 .item-box .item .icon {
  transition: all ease-in-out 0.6s;
}

.section05 .item-box .item .title {
  font-size: 18px;
  font-family: SourceHanSansCN;
  font-weight: 400;
  color: #333333;
  margin-top: 32px;
}

.section05 .item-box .item .desc {
  font-size: 14px;
  font-family: SourceHanSansCN;
  font-weight: 300;
  color: #666666;
  line-height: 20px;
  margin-top: 14px;
  text-align: center;
  letter-spacing: 0;
  width: 100%;
}

.section05 .item-box .item:hover {
  /* transform: translateY(-20px); */
}

.section05 .item-box .item:hover .icon {
  transform: rotateY(360deg);
}

/* é™æœ¬å¢žæ•ˆ é˜²èŒƒé£Žé™© */
.section06 {
  height: 600px;
}

.section06 .content-box {
  height: 200px;
  margin-top: 70px;
  border: 1px solid #f4d4d7;
}

.section06 .tab-box .item {
  width: 100%;
  height: 56px;
  line-height: 56px;
  text-align: center;
  background: #fafafa;
  border-bottom: 1px solid #f4d4d7;
  border-left: 1px solid #f4d4d7;
  font-size: 16px;
  font-family: SourceHanSansCN;
  font-weight: 400;
  color: #333333;
  position: relative;
  cursor: pointer;
  transition: all ease-in-out 0.3s;
}

.section06 .tab-box .item::before {
  content: "";
  width: 100%;
  height: 4px;
  background-color: transparent;
  position: absolute;
  left: 0;
  top: 0;
  transition: all ease-in-out 0.3s;
}

.section06 .tab-box .item.active,
.section06 .tab-box .item:hover {
  background-color: #ffffff;
  border-bottom: 1px solid transparent;
  border-left: 1px solid transparent;
  color: #bc0210;
}

.section06 .tab-box .item.active::before,
.section06 .tab-box .item:hover::before {
  background-color: #bc0210;
}

.section06 .tab-box .item:first-child {
  border-left: none;
}

.section06 .content-box .content {
  padding: 53px 20px;
  display: none;
}

.section06 .content-box .content p {
  font-size: 16px;
  font-family: SourceHanSansCN;
  font-weight: 300;
  color: #333333;
  position: relative;
  padding-left: 13px;
}

.section06 .content-box .content p::before {
  content: "";
  width: 8px;
  height: 2px;
  background: #bc0210;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}

/* åº•éƒ¨ */
.section07 {
  height: 280px;
  background: #bc0210 url(../images/u023.png) no-repeat center;
  background-size: cover;
}

.section07 .container h3 {
  font-size: 30px;
  font-family: SourceHanSansCN;
  font-weight: 300;
  color: #ffffff;
}

.section07 .container .title {
  font-size: 22px;
  font-family: SourceHanSansCN;
  font-weight: 300;
  color: #ffffff;
  text-align: center;
  margin-top: 7px;
}

.section07 .container .title span {
  color: #ffeb44;
}

.section07 .form-box {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 30px;
}

.section07 .form-box .form-item {
  width: 300px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  position: relative;
}

.section07 .form-box .form-item input {
  width: 100%;
  height: 100%;
  line-height: 40px;
  padding: 0 10px;
  box-sizing: border-box;
  font-size: 14px;
  font-family: PingFang;
  font-weight: 500;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
}

input:focus {
  outline: none;
}

/* é€šç”¨ */
::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.8);
}

::-moz-placeholder {
  color: rgba(255, 255, 255, 0.8);
}

/* firefox 19+ */
:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.8);
}

/* ie */
input:-moz-placeholder {
  color: rgba(255, 255, 255, 0.8);
}

.section07 .container .section-btn {
  width: 180px;
  height: 40px;
  border: 1px solid #ffffff;
  font-size: 16px;
  font-family: SourceHanSansCN;
  font-weight: 300;
  color: #ffffff;
  line-height: 40px;
  margin: auto;
  margin-top: 48px;
}

/* è‡ªé€‚åº” */
.mobile-box {
  display: none;
}

.form-box .input-error {
  border-color: #F56C6C;
}

.el-form-item__error {
  color: #fff;
  font-size: 12px;
  line-height: 1;
  padding-top: 9px;
  /* padding-left: 10px; */
  position: absolute;
  top: 100%;
  left: 10px;
}

@media screen and (min-width: 1100px) and (max-width: 1367px) {
  .container {
    width: 1100px;
    min-width: 1100px;
  }

  .section01 .container {
    width: 1100px;
    min-width: 1100px;
  }

  .section-btn {
    width: calc(200px * 0.8);
    height: calc(50px * 0.8);
    line-height: calc(50px * 0.8);
    font-size: calc(20px * 0.8);
  }

  .section01 .left-part img {
    width: calc(290px * 0.8);
  }

  .section01 .right-part h1 {
    font-size: calc(46px * 0.6);
  }

  .section01 .right-part h3 {
    font-size: calc(32px * 0.6);
  }
}

@media screen and (min-width: 960px) and (max-width: 1100px) {
  .container {
    width: 960px;
    min-width: 960px;
  }

  .section01 {
    height: auto;
    padding: 50px 0;
  }

  .section01 .container {
    width: 960px;
    min-width: 960px;
  }

  .section-btn {
    width: calc(200px * 0.6);
    height: calc(50px * 0.6);
    line-height: calc(50px * 0.6);
    font-size: calc(20px * 0.6);
  }

  .section01 .left-part img {
    width: calc(290px * 0.6);
  }

  .section01 .right-part h1 {
    font-size: calc(46px * 0.6);
  }

  .section01 .right-part h3 {
    font-size: calc(32px * 0.6);
  }

  .section04 .content-box .bot-title {
    font-size: 18px;
  }

  .section05 .item-box .item {
    padding: 40px 20px;
  }

  .section05 .item-box .item .icon img {
    width: 90px;
    height: 90px;
  }
}

@media screen and (min-width: 768px) and (max-width: 960px) {
  .container {
    width: 98%;
    min-width: auto;
  }

  .container h3 {
    font-size: 28px
  }

  .section01 {
    height: auto;
    padding: 50px 0;
  }

  .section01 .container {
    width: 98%;
    min-width: auto;
  }

  .section-btn {
    width: calc(200px * 0.6);
    height: calc(50px * 0.6);
    line-height: calc(50px * 0.6);
    font-size: calc(20px * 0.6);
  }

  .section01 .left-part img {
    width: calc(290px * 0.5);
  }

  .section01 .right-part h1 {
    font-size: calc(46px * 0.4);
    margin-bottom: 20px;
  }

  .section01 .right-part h3 {
    font-size: calc(32px * 0.4);
    margin-bottom: 20px;
  }

  .section03 {
    height: auto;
    padding: 50px 0;
  }

  .section03 .item-box {
    flex-wrap: wrap;
  }

  .section03 .item-box .item {
    width: calc(100% / 3);
  }

  .section03 .item-box .item .top-box {
    width: 165px;
    margin: auto;
  }

  .section03 .item-box .item p {
    margin-top: 0;
  }

  .section04 .content-box .bot-title {
    font-size: 14px;
  }

  .section05 {
    height: auto;
    padding: 50px 0;
  }

  .section05 .item-box {
    flex-wrap: wrap;
    justify-content: space-around;
  }

  .section05 .item-box .item {
    width: calc(100% / 2 - 70px);
    margin-bottom: 30px;
  }

  .section05 .item-box .line {
    display: none;
  }

  .section07 {
    height: auto;
    padding: 30px 0;
  }

  .section07 .form-box {
    flex-wrap: wrap;
    width: 60%;
    margin: auto;
    margin-top: 30px;
  }

  .section07 .form-box .form-item {
    margin-bottom: 20px;
    width: 100%;
  }
}

/* æ‰‹æœºç«¯ */
@media screen and (max-width: 768px) {
  .pc-box {
    display: none;
  }

  .mobile-box {
    display: block;
  }

  * {
    padding: 0;
    margin: 0;
  }

  @font-face {
    font-family: "SourceHanSansCN";
    src: url("../font/SourceHanSansCN-Regular.otf");
  }

  @font-face {
    font-family: "YouSheBiaoTiHei";
    src: url("../font/YouSheBiaoTiHei-2.ttf");
  }

  html,
  body {
    font-size: 100px !important;
  }

  body {
    font-size: 0.16rem;
    background-color: #bc0210;
    background-image: url("../img/background.png");
    background-repeat: no-repeat;
    background-size: 100% auto;
    min-width: 300px;
  }

  li {
    list-style: none;
  }

  a {
    text-decoration: none;
    color: inherit;
  }

  .clear::after {
    content: "";
    display: block;
    clear: both;
  }

  .fl {
    float: left;
  }

  .fr {
    float: right;
  }

  .container {
    width: 93%;
    margin: auto;
    min-width: 300px;
  }

  div.title {
    padding: 0 0.33rem;
    display: flex;
    align-items: center;
    position: relative;
    height: .3rem;
  }

  div.title img {
    position: absolute;
    width: 0.8rem;
  }

  div.title img:nth-of-type(2) {
    right: -4%;
  }

  div.title h2 {
    text-align: center;
    font-size: 0.18rem;
    font-family: SourceHanSansCN;
    font-weight: 400;
    color: #fff;
    flex: 1;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  .header {
    display: block;
    position: relative;
    background: url('../newImg/bg3.png') no-repeat 0 0;
    background-size: contain;
    padding: 0.81rem 0.12rem 0.19rem;
  }

  .header h3 {
    font-weight: bold;
    font-size: 0.21rem;
    color: #FFFFFF;
    line-height: 0.3rem;
  }

  .header .btn {
    width: 0.9rem;
    height: 0.3rem;
    background: #FFFFFF;
    border-radius: 0.15rem;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 300;
    font-size: 0.12rem;
    color: #BC0210;
    margin-top: 0.25rem;
  }

  .header .display-img {
    position: absolute;
    top: 0.68rem;
    right: 0;
  }

  .header .display-img img {
    width: 1.31rem;
  }

  .upgrade{
    margin-top: 0.26rem;
    background-repeat: no-repeat;
    background-position-y: calc(100% - 0.43rem);
    background-position-x: right;
  }

  .upgrade[data-show="0"]{
    background-image: url('../newImg/bg4.png');
    background-size: 1.2rem 1.54rem;
  }

  .upgrade .tab-wrap{
    margin-top: 0.4rem;
  }

  .upgrade .tab-wrap .tab-panel{
    padding: 0 0.25rem;
    display: none;
  }

  .upgrade .tab-wrap .tab-panel.active{
    display: block;;
  }

  .upgrade .tab-wrap .tab-panel .card{
    background: rgba(255,255,255,0.7);
    box-shadow: 0rem 0rem 0rem 0rem rgba(216,186,189,0.2);
    border-radius: 0.15rem;
    border: 1px solid #FFFFFF;
    box-sizing: border-box;
    width: 100%;
    padding: 0.27rem 0.28rem;
  }

  .upgrade .tab-wrap .tab-panel .card h3{
    font-size: 0.22rem;
    color: #222222;
    text-align: left;
    line-height: 0.3rem;
  }

  .upgrade .tab-wrap .tab-panel .card ul{
    margin-top: 0.32rem;
  }

  .upgrade .tab-wrap .tab-panel .card ul li{
    align-items: start;
  }

  .upgrade .tab-wrap .tab-panel .card ul li + li{
    margin-top: 0.25rem;
  }

  .upgrade .tab-wrap .tab-panel .card ul li img{
    transform: translateY(3px);
    margin-right: 0.08rem;
  }

  .upgrade .tab-wrap .tab-panel .card ul li p{
    font-size: 0.13rem;
    color: #5A5A5A;
    line-height: 0.18rem;
  }

  .upgrade .tab-wrap .tab{
    margin-top: 0.38rem;
    width: 100%;
  }

  .upgrade .tab-wrap .tab .tab-item{
    border: 0.03rem solid #fff;
    background-color: #fff;
    flex: 1;
    height: 0.45rem;
    justify-content: center;
    font-size: 0.15rem;
    color: #222222;
  }

  .upgrade .tab-wrap .tab .tab-item.active{
    background: #BC0210;
    color: #FFFFFF;
  }

  .traditional {
    margin-top: 0.61rem;
  }

  .traditional .content {
    margin-top: 0.3rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
  }

  .traditional .content li {
    width: 46%;
    background: #f9f9f9;
    border-radius: 0.02rem;
    padding: 0.25rem 0.12rem 0.16rem;
    height: 1.5rem;
    box-sizing: border-box;
    margin-bottom: 0.18rem;
    flex-grow: 1;
    margin-right: 0.15rem;
    -webkit-animation-duration: 0.5s;
    animation-duration: 0.5s;
  }

  .traditional .content li:nth-of-type(2n) {
    margin-right: 0;
  }

  .traditional .content li:last-of-type {
    margin-right: 0;
    text-align: center;
    height: auto;
    -webkit-animation-delay: 0.2s;
    animation-delay: 0.2s;
  }

  .traditional .content li:nth-of-type(3),
  .traditional .content li:nth-of-type(4) {
    -webkit-animation-delay: 0.1s;
    animation-delay: 0.1s;
  }

  .traditional .content li .traditional-content-title {
    text-align: center;
  }

  .traditional .content li .traditional-content-title span {
    font-size: 0.15rem;
    font-family: SourceHanSansCN;
    font-weight: 500;
    color: #333;
  }

  .traditional .content li .traditional-content-title span.index {
    font-size: 0.15rem;
    font-family: YouSheBiaoTiHei;
    font-weight: 400;
    color: #bc0210;
  }

  .traditional .content li .traditional-content {
    margin-top: 0.16rem;
    font-size: 0.13rem;
    font-family: SourceHanSansCN;
    font-weight: 300;
    color: #666;
    line-height: 0.18rem;
  }

  .function {
    margin-top: 0.65rem;
  }

  .function .function-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 0.23rem;
  }

  .function .function-list li {
    text-align: center;
    width: 33.33%;
  }

  .function .function-list li img {
    width: 60%;
  }

  .function .function-list li p {
    text-align: center;
    font-size: 0.14rem;
    font-family: SourceHanSansCN;
    font-weight: 400;
    color: #fff;
  }

  .function .intelligent-integration {
    margin-top: 0.54rem;
    background: #fff;
  }

  .function .intelligent-integration .intelligent-integration-title {
    background-image: url("../img/title-bg.png");
    background-repeat: no-repeat;
    background-size: auto .42rem;
    background-position: center;
    font-size: 0.16rem;
    font-family: SourceHanSansCN;
    font-weight: bold;
    color: #fff;
    text-align: center;
    line-height: 0.34rem;
    height: .42rem;
    transform: translateY(-22%);
  }

  .function .intelligent-integration .intelligent-integration-content {
    padding: 0 0.2rem;
    position: relative;
    box-sizing: border-box;
  }

  .function .intelligent-integration .intelligent-integration-content .fl {
    margin-top: 0.14rem;
    padding-top: 0.11rem;
    position: relative;
    text-align: center;
  }

  .function .intelligent-integration .intelligent-integration-content .percent {
    font-size: 0.3rem;
    font-family: Bahnschrift;
    font-weight: 400;
    color: #bc0210;
  }

  .function .intelligent-integration .intelligent-integration-content .percent .num {
    font-size: 0.3rem;
  }

  .function .intelligent-integration .intelligent-integration-content .percent span {
    font-size: 0.15rem;
  }

  .function .intelligent-integration .intelligent-integration-content .arrow {
    position: absolute;
    top: 0;
    right: 0;
    height: 0.16rem;
  }

  .function .intelligent-integration .intelligent-integration-content p {
    margin: 0.17rem;
    font-size: 0.12rem;
    font-family: SourceHanSansCN;
    font-weight: 300;
    color: #003;
  }

  .function .intelligent-integration .intelligent-integration-content-bottom {
    padding: 20px 15px;
    background: #fff9fa;
    border: 1px solid #f4d4d7;
    font-size: .12rem;
    font-family: SourceHanSansCN;
    font-weight: 400;
    color: #222;
    line-height: 0.2rem;
    box-sizing: border-box;
  }

  .safe {
    margin-top: 0.5rem;
  }

  .safe .safe-content {
    margin-top: 0.2rem;
    display: flex;
    flex-direction: column;
    flex: 1;
    background: #fff;
  }

  .safe .safe-content li {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background: #fff;
    border-radius: 0.05rem;
    padding: 0.2rem 0.16rem;
  }

  .safe .safe-content li img {
    width: 0.5rem;
    margin-right: 0.2rem;
  }

  .safe .safe-content li .safe-content-title {
    font-size: 0.15rem;
    font-family: SourceHanSansCN;
    font-weight: 400;
    color: #333;
    line-height: 0.23rem;
  }

  .safe .safe-content li div {
    font-size: 0.12rem;
    font-family: SourceHanSansCN;
    font-weight: 300;
    color: #666;
    line-height: 0.18rem;
  }

  .safe .safe-content li .safe-content-box {
    width: calc(100% - 0.5rem - 0.2rem);
  }

  .safe .against-risk {
    background: #fff;
    margin-top: .6rem;
  }

  .safe .against-risk .safe-content {
    margin-top: 0;
  }

  .safe .against-risk .against-title {
    text-align: center;
    font-size: 0.16rem;
    font-family: SourceHanSansCN;
    font-weight: bold;
    color: #fff;
    line-height: 0.34rem;
    background-image: url("../img/title-bg.png");
    background-repeat: no-repeat;
    background-size: auto .42rem;
    background-position: center;
    transform: translateY(-23%);
    height: .42rem;
  }

  .safe .against-risk ul li {
    border-radius: 0;
    padding-top: 0;
    padding-bottom: 0;
    background-color: transparent;
    margin-bottom: .24rem;
  }

  .safe .against-risk ul li .img {
    background-image: url("../img/hexagon.png");
    background-repeat: no-repeat;
    font-size: 0.16rem;
    font-family: Bahnschrift;
    font-weight: bold;
    color: #bc0210;
    width: 0.27rem;
    height: 0.32rem;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 0.14rem;
  }

  .safe .against-risk ul li .safe-content-box {
    width: calc(100% - 0.27rem - 0.14rem);
  }

  .safe .against-risk ul li .safe-content-box div {
    font-size: 0.13rem;
    line-height: 0.2rem;
    width: 100%;
  }

  .footer {
    margin-top: 0.4rem;
    background: #a7000d;
    padding: 0.2rem 0 0.15rem;
    text-align: center;
  }

  .footer .footer-title {
    font-size: 0.18rem;
    font-family: SourceHanSansCN;
    font-weight: 300;
    color: #fff;
  }

  .footer .footer-content {
    color: #fff;
    font-size: 0.14rem;
    font-family: SourceHanSansCN;
    font-weight: 300;
    margin: 0.08rem auto;
    width: 80%;
  }

  .footer .footer-content span {
    color: #f9b087;
  }

  .footer .form-item {
    position: relative;
    width: 93%;
    max-width: 400px;
    height: 0.4rem;
    margin: 0 auto;
    margin-top: 0.15rem;
  }

  .footer .form-item input {
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.3);
    border-color: transparent;
    color: #fff;
    font-size: 0.12rem;
    padding: 0 0.1rem;
    outline: none;
    box-sizing: border-box;
  }

  .footer .form-item input::-moz-placeholder {
    color: #fff;
    font-size: 0.15rem;
  }

  .footer .form-item input:-ms-input-placeholder {
    color: #fff;
    font-size: 0.15rem;
  }

  .footer .form-item input::placeholder {
    color: #fff;
    font-size: 0.15rem;
  }

  .footer .form-item button {
    width: 1rem;
    height: 80%;
    border: 1px solid #fff;
    background-color: transparent;
    outline: none;
    font-size: 0.12rem;
    font-family: SourceHanSansCN;
    font-weight: 300;
    color: #fff;
  }
}

@media screen and (max-width: 375px) {
  .safe .against-risk {
    background-size: 100% 100%;
  }

  .intelligent-integration {
    background-size: 100% 100%;
  }
}

@media screen and (max-width: 360px) {
  .traditional .content li {
    height: auto;
  }

  .function .intelligent-integration .intelligent-integration-content {
    background-color: #fff;
    padding-bottom: 0.2rem;
  }

  .header .fr img {
    width: 100%;
  }
}

@media screen and (min-width: 700px) and (max-width: 768px) {
  html {
    font-size: 200px;
  }

  .footer .form-item {
    max-width: none;
  }

  .footer .form-item input {
    transform: scale(.8);
  }
}

.message-box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgb(0, 0, 0);
  color: #fff;
  padding: 20px 30px;
  border-radius: 4px;
}

#message-text {
  color: #fff;
}



.section08 {
  background: #F5F5F6;
}

.section08 .container {
  padding: 48px 60px;
  box-sizing: border-box;
}

.section08 .container h3 {
  margin-top: 28px;
}

.section08 .container .tab-wrap {
  margin-top: 59px;
}

.section08 .container .tab-panel {
  background-repeat: no-repeat;
  background-position: right bottom;
  display: none;
  padding-bottom: 41px;
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity ease-in-out 0.3s;
}

.section08 .container .tab-panel .card {
  width: 460px;
  background-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0px 3px 30px 0px rgba(216, 186, 189, 0.2);
  border-radius: 30px;
  border: 2px solid #FFFFFF;
  padding: 40px 60px 73px 60px;
  box-sizing: border-box;
}

.section08 .container .tab-panel .card h3 {
  text-align: left;
  line-height: 45px;
}

.section08 .container .tab-panel .card ul {
  margin-top: 50px;
}

.section08 .container .tab-panel .card ul li {
  align-items: start;
  font-size: 16px;
  color: #5A5A5A;
  line-height: 24px;
}

.section08 .container .tab-panel .card ul li+li {
  margin-top: 26px;
}

.section08 .container .tab-panel .card ul li img {
  width: 16px;
  height: 16px;
  line-height: 24px;
  transform: translateY(4px);
  margin-right: 10px;
}

.section08 .container .tab-panel:nth-child(1) {
  background-image: url('../newImg/bg1.png');
  background-position: calc(100% - 15px) bottom;
  padding-left: 51px;
  justify-content: start;
}

.section08 .container .tab-panel:nth-child(2) {
  background-image: url('../newImg/bg2.png');
  background-position: left bottom;
  justify-content: end;
}

.section08 .container .tab-panel.active {
  display: flex;
  opacity: 1;
}

.section08 .container .tab {
  height: 56px;
  background: #FFFFFF;
  box-shadow: 0px 3px 30px 0px rgba(175, 182, 191, 0.2);
  align-items: normal;
}

.section08 .container .tab .tab-item {
  flex: 1;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  color: #222222;
  cursor: pointer;
}

.section08 .container .tab .tab-item.active {
  background: #BC0210;
  font-size: 18px;
  color: #FFFFFF;
}

@media screen and (max-width: 1039px) {
  .section08 .container .tab-panel .card {
    background: #ffffffd0;
  }
}