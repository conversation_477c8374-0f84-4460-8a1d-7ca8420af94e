<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>资海云-管理智能体</title>
  <meta name="description" content="资海云-管理智能体">
  <meta name="keywords" content="资海云-管理智能体">
  <link rel="stylesheet" href="https://zcloud.obs.cn-north-4.myhuaweicloud.com/animate.min.css">
  <link rel="stylesheet" href="./css/index.css">
</head>

<body>
<!-- 资海云头部 -->
<div id="zhyHomeHeader"></div>

<!-- banner -->
<section class="banner">
  <div class="container">
    <h1 class="tag flex-center ani" data-animation="bounceInLeft">资海管理智能体1.0</h1>
    <h2 class=" ani animate__delay-1s" data-animation="bounceInLeft">重塑企业智能管理新生态</h2>
    <h3 class=" ani animate__delay-2s" data-animation="bounceInLeft">全方位AI赋能，数据安全与智能优化并重，打造企业核心竞争力</h3>
  </div>
</section>

<!-- 产品介绍 -->
<section class="product-introduction">
  <hgroup class="title">
    <h4 class="ani animate__delay-3s" data-animation="flipInX">产品介绍</h4>
    <h5 class="ani animate__delay-4s" data-animation="flipInX">Product Introduction</h5>
  </hgroup>
  <div class="content-wrap relative container">
    <div class="intro ani animate__delay-5s" data-animation="lightSpeedInLeft">
      <div class="has-before">
        <p>
          资海管理智能体1.0，是专为现代中小企业打造的一站式管理智能一体化硬件设备。它内置了企业全场景管理软件，通过优秀的算法模型，为企业提供全方位、安全可靠的企业管理私有化部署方案。
        </p>
        <br>
        <br>
        <p>
          无论是数据处理、算法定制，还是Ai智能分析，资海管理智能体1.0都能帮助企业实现高效管理和数据安全保障，推动企业智能化、数字化转型，迈向更加智能、高效的发展道路。
        </p>
      </div>
    </div>
    <div class="img absolute flex-center">
      <img src="./img/img1.png" class="ani animate__delay-5s" data-animation="lightSpeedInRight" alt="">
    </div>
  </div>
</section>

<!-- 核心功能 -->
<section class="core-function">
  <hgroup class="title">
    <h4 class="ani" data-animation="flipInX">核心功能</h4>
    <h5 class="ani animate__delay-1s" data-animation="flipInX">Core Functions</h5>
  </hgroup>
  <div class="content-wrap container">
    <div class="flex-between pc">
      <div class="col ani animate__delay-3s" data-animation="flipInY">
        <div class="func-item">
          <div class="img">
            <img src="./img/func1.png" alt="">
          </div>
          <div class="func-title">数据加密存储</div>
          <div class="func-desc">安全无忧</div>
        </div>
        <div class="func-item">
          <div class="img">
            <img src="./img/func5.png" alt="">
          </div>
          <div class="func-title">算法私有部署</div>
          <div class="func-desc">自主可控</div>
        </div>
      </div>
      <div class="col ani animate__delay-2s" data-animation="flipInY">
        <div class="func-item">
          <div class="img">
            <img src="./img/func2.png" alt="">
          </div>
          <div class="func-title">数据隔离保护</div>
          <div class="func-desc">安全防护</div>
        </div>
        <div class="func-item">
          <div class="img">
            <img src="./img/func6.png" alt="">
          </div>
          <div class="func-title">性能持续调优</div>
          <div class="func-desc">性能卓越</div>
        </div>
      </div>
      <div class="col ani animate__delay-1s" data-animation="flipInY">
        <div class="func-item">
          <div class="img">
            <img src="./img/func3.png" alt="">
          </div>
          <div class="func-title">数据备份恢复</div>
          <div class="func-desc">随时还原</div>
        </div>
        <div class="func-item">
          <div class="img">
            <img src="./img/func7.png" alt="">
          </div>
          <div class="func-title">机器智能学习</div>
          <div class="func-desc">自我优化</div>
        </div>
      </div>
      <div class="col ani" data-animation="flipInY">
        <div class="func-item">
          <div class="img">
            <img src="./img/func4.png" alt="">
          </div>
          <div class="func-title">存储成本优化</div>
          <div class="func-desc">降本增效</div>
        </div>
        <div class="func-item">
          <div class="img">
            <img src="./img/func8.png" alt="">
          </div>
          <div class="func-title">动态策略调整</div>
          <div class="func-desc">灵活管理</div>
        </div>
      </div>
    </div>
    <div class="flex-between phone">
      <div class="func-item ani animate__delay-4s" data-animation="flipInY">
        <div class="img">
          <img src="./img/func1.png" alt="">
        </div>
        <div class="func-title">数据加密存储</div>
        <div class="func-desc">安全无忧</div>
      </div>
      <div class="func-item ani animate__delay-3s" data-animation="flipInY">
        <div class="img">
          <img src="./img/func5.png" alt="">
        </div>
        <div class="func-title">算法私有部署</div>
        <div class="func-desc">自主可控</div>
      </div>
      <div class="func-item ani animate__delay-2s" data-animation="flipInY">
        <div class="img">
          <img src="./img/func2.png" alt="">
        </div>
        <div class="func-title">数据隔离保护</div>
        <div class="func-desc">安全防护</div>
      </div>
      <div class="func-item ani animate__delay-4s" data-animation="flipInY">
        <div class="img">
          <img src="./img/func6.png" alt="">
        </div>
        <div class="func-title">性能持续调优</div>
        <div class="func-desc">性能卓越</div>
      </div>
      <div class="func-item ani animate__delay-3s" data-animation="flipInY">
        <div class="img">
          <img src="./img/func3.png" alt="">
        </div>
        <div class="func-title">数据备份恢复</div>
        <div class="func-desc">随时还原</div>
      </div>
      <div class="func-item ani animate__delay-2s" data-animation="flipInY">
        <div class="img">
          <img src="./img/func7.png" alt="">
        </div>
        <div class="func-title">机器智能学习</div>
        <div class="func-desc">自我优化</div>
      </div>
      <div class="func-item ani animate__delay-4s" data-animation="flipInY">
        <div class="img">
          <img src="./img/func4.png" alt="">
        </div>
        <div class="func-title">存储成本优化</div>
        <div class="func-desc">降本增效</div>
      </div>
      <div class="func-item ani animate__delay-3s" data-animation="flipInY">
        <div class="img">
          <img src="./img/func8.png" alt="">
        </div>
        <div class="func-title">动态策略调整</div>
        <div class="func-desc">灵活管理</div>
      </div>
    </div>
  </div>
</section>

<!-- 三大优势 -->
<section class="advantages">
  <hgroup class="title white">
    <h4 class="ani" data-animation="flipInX">三大优势</h4>
    <h5 class="ani animate__delay-1s" data-animation="flipInX">Three Advantages</h5>
  </hgroup>
  <div class="content-wrap container pc">
    <ul class="flex-align-stretch flex-justify-between overview">
      <li class="ani animate__delay-2s" data-animation="fadeInDown">
        <div class="wrap has-before" data-index="01">
          <div class="box">
            <div class="img">
              <img src="./img/adv1.png" alt="">
            </div>
            <div class="adv-title">安全高效的<br>数据存储解决方案</div>
          </div>
        </div>
      </li>
      <li class="ani animate__delay-3s" data-animation="fadeInDown">
        <div class="wrap has-before" data-index="02">
          <div class="box">
            <div class="img">
              <img src="./img/adv2.png" alt="">
            </div>
            <div class="adv-title">算法私有化部署，<br>定制化需求</div>
          </div>
        </div>
      </li>
      <li class="ani animate__delay-4s" data-animation="fadeInDown">
        <div class="wrap has-before" data-index="03">
          <div class="box">
            <div class="img">
              <img src="./img/adv3.png" alt="">
            </div>
            <div class="adv-title">机器学习技术，<br>智能优化</div>
          </div>
        </div>
      </li>
    </ul>
    <div class="content ani animate__delay-2s" data-animation="fadeIn">
      <div class="wrap has-before active">
        <ul>
          <li>
            数据安全：提供先进的数据存储解决方案，确保企业数据的安全性和完整性。通过多层加密技术和严格的访问控制，防止数据泄露和非法访问。
          </li>
          <li>
            高效处理：内置高性能存储硬件和优化的数据管理系统，大幅提升数据读写速度，满足企业对数据处理时效性的高要求。
          </li>
        </ul>
      </div>
      <div class="wrap has-before">
        <ul>
          <li>
            灵活定制：支持算法私有化部署，企业可以根据自身业务需求，定制专属算法，确保数据处理和业务逻辑的完全匹配。
          </li>
          <li>
            数据安全提升：算法私有化不仅满足了企业的个性化需求，还从根本上避免了数据外流的风险，保障企业核心数据安全。
          </li>
          <li>
            性能优化：定制化算法能充分利用件资源，提高数据处理性能，实现更快速、更精准的业务决策。
          </li>
        </ul>
      </div>
      <div class="wrap has-before">
        <ul>
          <li>
            智能分析：内置先进的机器学习引擎，自动分析企业数据，发现潜在规律和趋势，为企业决策提供科学依据。
          </li>
          <li>
            自我学习：具备自我学习能力，不断优化算法和模型，提升数据处理和分析的准确性。
          </li>
          <li>
            持续优化：通过持续学习和自我优化，不断适应企业变化，为企业提供更加智能、灵活的管理支持。
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div class="phone content-wrap container">
    <ul>
      <li class="ani animate__delay-3s" data-animation="jackInTheBox">
        <div class="adv-title flex">
          <p>安全高效的数据存储解决方案</p>
        </div>
        <div class="desc">
          <ul>
            <li>数据安全：提供先进的数据存储解决方案，确保企业数据的安全性和完整性。通过多层加密技术和严格的访问控制，防止数据泄露和非法访问。</li>
            <li>高效处理：内置高性能存储硬件和优化的数据管理系统，大幅提升数据读写速度，满足企业对数据处理时效性的高要求。</li>
          </ul>
          <img src="./img/adv1.png" alt="">
        </div>
      </li>
      <li class="ani animate__delay-4s" data-animation="jackInTheBox">
        <div class="adv-title flex">
          <p>算法私有化部署，定制化需求</p>
        </div>
        <div class="desc">
          <ul>
            <li>灵活定制：支持算法私有化部署，企业可以根据自身业务需求，定制专属算法，确保数据处理和业务逻辑的完全匹配。</li>
            <li>数据安全提升：算法私有化不仅满足了企业的个性化需求，还从根本上避免了数据外流的风险，保障企业核心数据安全。</li>
            <li>性能优化：定制化算法能充分利用硬件资源，提高数据处理性能，实现更快速、更精准的业务决策。</li>
          </ul>
          <img src="./img/adv2.png" alt="">
        </div>
      </li>
      <li class="ani animate__delay-5s" data-animation="jackInTheBox">
        <div class="adv-title flex">
          <p>机器学习技术，智能优化</p>
        </div>
        <div class="desc">
          <ul>
            <li>智能分析：内置先进的机器学习引擎，自动分析企业数据，发现潜在规律和趋势，为企业决策提供科学依据。</li>
            <li>自我学习：具备自我学习能力，不断优化算法和模型，提升数据处理和分析的准确性。</li>
            <li>持续优化：通过持续学习和自我优化，不断适应企业变化，为企业提供更加智能、灵活的管理支持。</li>
          </ul>
          <img src="./img/adv3.png" alt="">
        </div>
      </li>
    </ul>
  </div>
</section>

<!-- 咨询表单 -->
<section class="concat-us">
  <hgroup>
    <h4 class="form-title ani" data-animation="fadeInUp">资海管理智能体1.0，<br>开启您的企业管理新时代！</h4>
    <h5 class="form-en-title ani" data-animation="fadeInUp">Zihai Management Intelligent Agent 1.0, ushering in a new era of enterprise management!</h5>
  </hgroup>
  <div class="form ani animate__delay-1s" data-animation="fadeInUp">
    <div class="input-wrap">
      <input type="text" id="company" placeholder="公司名称">
    </div>
    <div class="input-wrap">
      <input type="text" data-required="true" id="name" placeholder="联系人">
    </div>
    <div class="input-wrap">
      <input type="text" data-required="true" id="telephone" placeholder="电话">
    </div>
    <div class="input-wrap">
      <input type="text" data-required="true" id="content" placeholder="内容">
    </div>
  </div>
  <div class="btn-wrap flex-center">
    <div class="consulting-button flex-center ani" data-animation="swing">立即获取体验账号</div>
  </div>
</section>

<!-- 底部 -->
<footer id="zhyFooter"></footer>
</body>

<script src="./js/jquery-3.7.1.min.js"></script>

<script>
  var environment = "pro";
</script>
<script type="text/javascript" src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/js-cookie.js?v=1227"></script>
<script src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/homeHeader.js?v=03"></script>
<script src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/footer.js?v=1228"></script>
<script src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/share.js"></script>
<script src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/consult.js"></script>
<script>
  $(function () {
    $('.overview li').on('mouseenter', function () {
      let index = $(this).index() + 1
      $('.content-wrap .content .wrap:nth-child(' + index + ')').addClass('active').siblings().removeClass('active')
    })
    function addAnimate() {
      $(".ani").each((i, v) => {
        let animateName = $(v).data("animation");
        // 滚动条的垂直位置。
        let windowScrollTop = $(window).scrollTop();
        // 元素据文档顶端的距离
        let offsetTop = $(v).offset().top;
        // 窗口大小高度
        let documentHeight = $(window).height();
        // 自身高度
        let selfHeight = $(v).height();
        if (offsetTop - windowScrollTop < documentHeight + 200) {
          $(v).addClass("animate__animated animate__" + animateName);
          if (selfHeight + offsetTop < windowScrollTop) {
            $(v).removeClass("animate__animated animate__" + animateName);
          }
        } else {
          $(v).removeClass("animate__animated animate__" + animateName);
        }
      });
    }

    function throttle(fn, delay) {
      let timer = null;
      return function() {
        let context = this;
        let args = arguments;
        if (!timer) {
          timer = setTimeout(function() {
            fn.apply(context, args);
            timer = null;
          }, delay);
        }
      }
    }

    $(window).on("scroll", throttle(addAnimate, 500));

    addAnimate();
  })
  $(function () {
    shareInit({
      title: '资海管理智能体1.0',
      desc: '资海管理智能体1.0，开启您的企业管理新时代！'
    })
    new ConsultForm({
      formEle: $(".form"),
      submitBtn: $('.consulting-button'),
      formData: {
        company: $('#company'),
        name: $('#name'),
        telephone: $('#telephone'),
        remarks: "资海管理智能体1.0咨询",
        content: $('#content')
      },
    })
  })
  function removeDelay() {
    if(window.innerWidth <= 500){
      $('.advantages .phone > ul > li').removeClass('animate__delay-3s').removeClass('animate__delay-4s').removeClass('animate__delay-5s')
    }
  }
  removeDelay()
  window.onresize = function () {
    removeDelay()
  }
</script>

</html>
