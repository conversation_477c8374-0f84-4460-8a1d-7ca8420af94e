webpackJsonp([61],{"/lWJ":function(e,t){},Yi6L:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});n("8PcR");var c={render:function(){var e=this,t=e.$createElement,c=e._self._c||t;return c("div",{staticClass:"box"},[c("div",{staticClass:"card flex-column flex-align-center"},[c("img",{attrs:{src:n("hhu/"),alt:""}}),e._v(" "),c("div",[e._v("恭喜您，产品购买成功！")]),e._v(" "),c("el-button",{on:{click:function(t){return e.$router.push("/console/order")}}},[e._v("查看订单列表")])],1)])},staticRenderFns:[]};var r=n("VU/8")({name:"BuySuccess"},c,!1,function(e){n("/lWJ")},"data-v-9aab7fe6",null);t.default=r.exports}});