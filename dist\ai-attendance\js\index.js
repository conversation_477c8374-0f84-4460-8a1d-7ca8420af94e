$(".section-2 .idx1_con ul li")
  .off("click")
  .on("click", function () {
    $(this).addClass("active").siblings().removeClass("active");
    var idx = $(this).index();
    $(".section-2 .idx1_con .con")
      .eq(idx)
      .addClass("active")
      .siblings()
      .removeClass("active");
  });
$(".consulting-button").on("click", function () {
  focusInput();
});
$(".btn").on("click", function () {
  focusInput();
});
function focusInput() {
  $("#company").focus();
}

shareInit({
  title: '智感考勤AI版',
  desc: '智感考勤AI版，让考勤更智能、更高效、更安全'
})

window.addEventListener("load", function () {
  console.log('[ $(\'#company\').val() ] >', $('#company').val())
  new ConsultForm({
    formEle: $(".form"),
    submitBtn: $('.consulting-button'),
    formData: {
      company: $('#company'),
      name: $('#name'),
      telephone: $('#telephone'),
      remarks: "智感考勤AI版咨询",
    },
  })
})

var swiper = new Swiper('.idx_type', {
  autoplay:{
      delay:3500,
      disableOnInteraction:false,//默认为true 用户操作后就不轮播。--false 为可以自动轮播
  },
  pagination:{
      el:'.swiper-pagination',
      clickable:false,//分页器是否能点击
  },
});

if(window.innerWidth <= 1024) {
  $('.pain-point-list li').each((i, v) => {
    if(i % 2 === 0) {
      $(v).data('animation', 'fadeInRight')
    }else{      
      $(v).data('animation', 'fadeInLeft')
    }
  })
}