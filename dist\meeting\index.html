<!DOCTYPE html>
<html lang="zh-CN">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>AI会议纪要</title>
	<style>
        body {
            margin: 0;
            padding: 0;
            background: #F6F6F6;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            color: #222;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .app-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            height: 48px;
            background: #fff;
            box-sizing: border-box;
            flex-shrink: 0;
        }
        .app-bar .title {
            flex: 1;
            text-align: center;
            font-size: 18px;
            color: #222;
        }
        .wrap {
            background: #fff;
        }
        .flex-1 {
            flex: 1;
        }
        .wrap.flex-1{
            overflow: hidden;
            padding-bottom: 30px;
        }
        .tabs-wrap{
            height: 100%;
            overflow: hidden;
        }
        .meeting-info {
            padding: 10px 0;
        }
        .meeting-info .date {
            font-size: 14px;
            color: #000;
        }
        .meeting-info .time {
            margin-top: 13px;
            display: flex;
            align-items: center;
            color: #888;
            font-size: 13px;
            line-height: 1;
        }
        .meeting-info .icon {
            width: 13px;
            margin-right: 7px;
        }
        .slider-row {
            display: flex;
            align-items: center;
            margin-top: 8px;
            padding-left: 7px;
        }
        .time-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 7px;
            width: calc(100% - 12px - 24px);
        }
        .slider {
            flex: 1;
            height: 8px;
            background: #F3F4F6;
            border-radius: 4px;
            position: relative;
            margin-right: 12px;
            min-width: 40px;
        }
        .slider .progress {
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 0%;
            background: #5183F6;
            border-radius: 4px;
        }
        .slider .thumb {
            position: absolute;
            left: 0%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: #fff;
            border: 4px solid #5183F6;
            border-radius: 50%;
            box-shadow: 0 2px 6px rgba(77, 124, 255, 0.15);
            z-index: 2;
        }
        .time-container .time {
            font-size: 13px;
            color: #999999;
            min-width: 40px;
        }
        .slider-row .play,
        .slider-row .stop {
            width: 24px;
            height: 24px;
            cursor: pointer;
            object-fit: contain;
        }
        .slider-row .stop {
            display: none;
        }
        .tags {
            padding: 0 20px 12px 20px;
            margin-top: 13px;
            display: flex;
        }
        .tag {
            background: rgba(81, 130, 246, 0.15);
            border-radius: 2px;
            color: #5183F6;
            padding: 3px 5px;
            font-size: 11px;
            font-weight: 500;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        .mt-10 {
            margin-top: 10px;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #F0F0F0;
            background: #fff;
            height: 48px;
            box-sizing: border-box;
        }
        .tab {
            height: 100%;
            flex: 1;
            text-align: center;
            padding: 12px 0 8px 0;
            font-size: 15px;
            color: #111111;
            cursor: pointer;
            background: #fff;
            position: relative;
            box-sizing: border-box;
        }
        .tab::after {
            content: '';
            width: 20px;
            height: 3px;
            background: #5183F6;
            border-radius: 2px;
            transition: width 0.3s ease;
            position: absolute;
            bottom: 2px;
            left: 50%;
            transform: translateX(-50%);
            display: none;
        }
        .tab.active {
            color: #5183F6;
            background: #fff;
        }
        .tab.active::after {
            display: block;
        }
        .tab.inactive {
            color: #111111;
        }
        .content {
            display: none;
            margin-top: 25px;
            height: calc(100% - 25px - 48px);
            overflow-y: auto;
            padding-bottom: 30px;
        }
        .content.active {
            display: block;
        }
        .msg-list {
            padding: 0;
            margin: 0;
            list-style: none;
        }
        .msg-item {
            display: flex;
            width: 100%;
            align-items: flex-start;
        }
        .msg-item .avatar + div{
            flex: 1;
        }
        .msg-item + .msg-item {
            margin-top: 23px;
        }
        .todo-item {
            background: #EBF1FF;
            border-radius: 10px;
            padding: 16px 17px;
            font-size: 14px;
            color: #111111;
            line-height: 1.5;
        }
        .todo-item + .todo-item {
            margin-top: 10px;
        }
        .avatar {
            width: 26px;
            height: 26px;
            background: #5183F6;
            border-radius: 50%;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: 500;
            margin-right: 12px;
            flex-shrink: 0;
        }
        .avatar img {
            width: 26px;
            height: 26px;
            border-radius: 50%;
        }
        .msg-title {
            display: flex;
            align-items: center;
            margin-bottom: 9px;
            cursor: pointer;
            user-select: none;
        }
        .msg-title span {
            font-size: 14px;
            color: #111111;
            flex: 1;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }
        .msg-title time {
            font-size: 14px;
            color: #999999;
            margin-left: 10px;
        }
        .msg-title .collapse-arrow {
            width: 10px;
            margin-left: 8px;
            transition: transform 0.3s ease;
            cursor: pointer;
            flex-shrink: 0;
            transform: rotate(-180deg);
        }
        .msg-title .collapse-arrow.collapsed {
            transform: rotate(0deg);
        }
        .msg-bubble {
            background: #fff;
            font-size: 13px;
            color: #666666;
            position: relative;
            min-width: 180px;
            max-width: 90vw;
            word-break: break-all;
            line-height: 1.5;
            overflow: hidden;
            transition: max-height 0.3s ease, opacity 0.3s ease;
            max-height: 1000px;
            opacity: 1;
        }
        .msg-bubble.collapsed {
            max-height: 0;
            opacity: 0;
            margin-bottom: 0;
        }
        .msg-meta {
            font-size: 13px;
            color: #bdbdbd;
            margin-top: 4px;
            margin-left: 44px;
        }
        @media (max-width: 500px) {
            .msg-bubble {
                max-width: 98vw;
            }
            .meeting-info,
            .slider-container,
            .tags,
            .content {
                padding-left: 15px;
                padding-right: 15px;
            }
        }
        @media (min-width: 501px) {
            .msg-bubble,
            .meeting-info,
            .slider-container,
            .tags,
            .tabs-wrap,
            .to-app a,
            .content {
                max-width: 500px;
                padding-left: 15px;
                padding-right: 15px;
                margin-left: auto;
                margin-right: auto;
            }
        }
        .to-app {
            background: #fff;
            padding: 20px 15px;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .to-app a {
            text-decoration: none;
            cursor: pointer;
            display: block;
            width: 100%;
            height: 100%;
            text-align: center;
            line-height: 40px;
            border-radius: 4px;
            background: #5183F6;
            color: #fff;
        }
        /* 加载动画样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #5183F6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
	</style>
</head>

<body>
<div class="loading-overlay">
    <div class="loading-spinner"></div>
</div>
<!--<div class="app-bar">
	<span class="title">AI会议纪要</span>
</div>-->
<div class="wrap" style="padding-top: 10px;">
	<div class="meeting-info">
		<div class="date">
			x月x日 xx:xx:xx会议
		</div>
		<div class="time">
			<img src="./img/icon1.png" class="icon"/>
			<span>xxxx-xx-xx xx:xx</span>
		</div>
	</div>
	<div class="slider-container">
		<div class="slider-row">
			<div class="slider" id="slider">
				<div class="progress" id="slider-progress"></div>
				<div class="thumb" id="slider-thumb"></div>
			</div>
			<img class="play" src="./img/bofang.png"/>
			<img class="stop" src="./img/zanting.png"/>
		</div>
		<div class="time-container">
			<span class="time" id="current-time">00:00</span>
			<span class="time" id="total-time">00:00</span>
		</div>
		<audio id="audio-player" src="" style="display: none"></audio>
	</div>
	<div class="tags">
	</div>
</div>
<div class="wrap mt-10 flex-1">
	<div class="tabs-wrap">
		<div class="tabs">
			<div class="tab active" data-tab="transform">转写内容</div>
			<div class="tab inactive" data-tab="record">会议纪要</div>
			<div class="tab inactive" data-tab="todo">待办事项</div>
		</div>
		<div class="content transform active">
			<ul class="msg-list">
			</ul>
		</div>
		<div class="content record">
			<ul class="msg-list">
			</ul>
		</div>
		<div class="content todo">
			<ul class="msg-list">
			</ul>
		</div>
	</div>
</div>
<script src="./js/jquery-3.7.1.min.js"></script>
<script>
  // 获取url中id数据
  const urlParams = new URLSearchParams(window.location.search);
  const id = urlParams.get('id');
  const token = urlParams.get('token');
  const baseUrl = 'https://china9.cn/api'

  document.addEventListener('DOMContentLoaded', () => {
    console.log('会议纪要页面加载完成');

    // 标签页切换功能
    const tabs = document.querySelectorAll('.tab');
    const contents = document.querySelectorAll('.content');

    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        // 移除所有标签的激活状态
        tabs.forEach(t => {
          t.classList.remove('active');
          t.classList.add('inactive');
        });

        // 移除所有内容的显示状态
        contents.forEach(content => {
          content.classList.remove('active');
          content.classList.add('inactive');
        });

        // 激活当前标签
        tab.classList.remove('inactive');
        tab.classList.add('active');

        // 显示对应的内容
        const tabType = tab.getAttribute('data-tab');
        const targetContent = document.querySelector(`.content.${tabType}`);
        if (targetContent) {
          targetContent.classList.remove('inactive');
          targetContent.classList.add('active');
        }
      });
    });
  });

  function toggleCollapse(element) {
    const bubble = element.closest('.msg-item').querySelector('.msg-bubble');
    const arrow = element.closest('.msg-title').querySelector('.collapse-arrow');
    bubble.classList.toggle('collapsed');
    arrow.classList.toggle('collapsed');
  }

  // 音频播放控制和进度条交互
  (function () {
    const audio = document.getElementById('audio-player');
    const slider = document.getElementById('slider');
    const progress = document.getElementById('slider-progress');
    const thumb = document.getElementById('slider-thumb');
    const currentTimeSpan = document.getElementById('current-time');
    const totalTimeSpan = document.getElementById('total-time');
    const playBtn = document.querySelector('.slider-row .play');
    const stopBtn = document.querySelector('.slider-row .stop');

    const min = 0;
    const max = 1;
    let value = 0;
    let dragging = false;
    let sliderRect = null;

    function getData() {
      if (id && token) {
        // 显示加载动画
        $('.loading-overlay').css('display', 'flex');

        $.post(baseUrl + '/meeting/meetingInfo', {
          id: id,
          token: token
        }).then(res => {
          if (res.code === 200) {
            const data = res.data;
            $('.meeting-info .date').text(data.title);
            $('.meeting-info .time span').text(data.start_time);
            $('#audio-player').attr('src', data.url);
            totalTimeSpan.textContent = data.filetime;
            let keywords = data.keywords;
            if (keywords && keywords.length) {
              let str = '';
              keywords.forEach(keyword => {
                str += `<span class="tag">${keyword.title}</span>`;
              });
              $('.tags').html(str);
            }
            let transformContent = data.jsontext;
            if (transformContent && transformContent.length) {
              let str = '';
              transformContent.forEach(item => {
                str += `<li class="msg-item">
                        <div class="avatar">
                          <img src="./img/avatar.png" alt="">
                        </div>
                        <div>
                          <div class="msg-title">
                            <span>${item.spktitle}</span>
                            <time>${item.starttime}</time>
                          </div>
                          <div class="msg-bubble">
                            ${item.text}
                          </div>
                        </div>
                      </li>`;
                $('.transform .msg-list').html(str);
              })
            }
            let recordContent = data.minutestext;
            if (recordContent && recordContent.length) {
              let str = '';
              recordContent.forEach(item => {
                str += `<li class="msg-item">
                        <div class="avatar">
                          <img src="./img/cord.png" alt="">
                        </div>
                        <div>
                          <div class="msg-title">
                            <span>${item.title}</span>
                            <img src="./img/arrow.png" alt="" class="collapse-arrow" onclick="toggleCollapse(this)">
                          </div>
                          <div class="msg-bubble">
                            ${item.content}
                          </div>
                        </div>
                      </li>`;
                $('.record .msg-list').html(str);
              })
            }
            let todoContent = data.backlog;
            if (todoContent && todoContent.length) {
              let str = '';
              todoContent.forEach(item => {
                str += `<li class="todo-item">
                        ${item.content}
                      </li>`;
                $('.todo .msg-list').html(str);
              })
            }
          }
        }).always(() => {
          // 隐藏加载动画
          $('.loading-overlay').css('display', 'none');
        });
      }
    }
    getData();

    // 格式化时间显示
    function formatTime(seconds) {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    // 设置滑块位置
    function setSlider(val, smooth = false) {
      value = Math.max(min, Math.min(max, val));
      const percent = value * 100;
      progress.style.width = percent + '%';
      thumb.style.left = percent + '%';
      if (smooth) {
        progress.style.transition = 'width 0.12s cubic-bezier(.4,0,.2,1)';
        thumb.style.transition = 'left 0.12s cubic-bezier(.4,0,.2,1)';
      } else {
        progress.style.transition = 'none';
        thumb.style.transition = 'none';
      }
    }

    // 从事件获取滑块值
    function getValueFromEvent(e) {
      let clientX;
      if (e.touches) {
        clientX = e.touches[0].clientX;
      } else {
        clientX = e.clientX;
      }
      let x = clientX - sliderRect.left;
      let percent = x / sliderRect.width;
      return Math.max(min, Math.min(max, percent));
    }

    // 更新音频播放时间
    function updateAudioTime() {
      if (audio.duration && !dragging) {
        const currentTime = audio.currentTime;
        const duration = audio.duration;
        const progress = currentTime / duration;

        setSlider(progress);
        currentTimeSpan.textContent = formatTime(currentTime);
        totalTimeSpan.textContent = formatTime(duration);
      }
    }

    // 拖拽开始
    function onDragStart(e) {
      dragging = true;
      sliderRect = slider.getBoundingClientRect();
      document.body.style.userSelect = 'none';
      const newValue = getValueFromEvent(e);
      setSlider(newValue);

      // 更新音频播放位置
      if (audio.duration) {
        audio.currentTime = newValue * audio.duration;
      }
    }

    // 拖拽移动
    function onDragMove(e) {
      if (!dragging) return;
      const newValue = getValueFromEvent(e);
      setSlider(newValue);

      // 实时更新音频播放位置
      if (audio.duration) {
        audio.currentTime = newValue * audio.duration;
      }
    }

    // 拖拽结束
    function onDragEnd(e) {
      if (!dragging) return;
      dragging = false;
      const newValue = getValueFromEvent(e);
      setSlider(newValue, true);
      document.body.style.userSelect = '';

      // 最终设置音频播放位置
      if (audio.duration) {
        audio.currentTime = newValue * audio.duration;
      }
    }

    // 音频事件监听
    audio.addEventListener('loadedmetadata', () => {
      totalTimeSpan.textContent = formatTime(audio.duration);
      setSlider(0);
    });

    audio.addEventListener('timeupdate', updateAudioTime);

    audio.addEventListener('ended', () => {
      playBtn.style.display = 'inline-block';
      stopBtn.style.display = 'none';
      setSlider(0);
      currentTimeSpan.textContent = '00:00';
    });

    // 滑块事件监听
    thumb.addEventListener('mousedown', onDragStart);
    slider.addEventListener('mousedown', onDragStart);
    document.addEventListener('mousemove', onDragMove);
    document.addEventListener('mouseup', onDragEnd);

    thumb.addEventListener('touchstart', onDragStart, {passive: false});
    slider.addEventListener('touchstart', onDragStart, {passive: false});
    document.addEventListener('touchmove', onDragMove, {passive: false});
    document.addEventListener('touchend', onDragEnd, {passive: false});

    // 播放/暂停按钮控制
    playBtn.addEventListener('click', function () {
      if (audio.src) {
        audio.play().then(() => {
          playBtn.style.display = 'none';
          stopBtn.style.display = 'inline-block';
        }).catch(err => {
          console.error('播放失败:', err);
        });
      }
    });

    stopBtn.addEventListener('click', function () {
      audio.pause();
      stopBtn.style.display = 'none';
      playBtn.style.display = 'inline-block';
    });

    setSlider(value);
  })();


</script>
</body>

</html>
