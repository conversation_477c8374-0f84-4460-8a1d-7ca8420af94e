webpackJsonp([98],{"0Nzw":function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var l=e("pI5c"),s={name:"sureOrder",components:{payComponent:e("rSnQ").a},data:function(){return{uploadLoading:!1,tableLoading:!1,dialogVisible:!1,loading_weixin:!0,tableData:[],amount:0,cheapPrice:0,allChecked:!1,textarea:"",no:"",pay_type:0,order_no:"",wallet:0,walletPay:0}},created:function(){this.no=this.$route.query.no,this.getOrderDetail(),this.walletMe()},methods:{getOrderDetail:function(){var t=this;this.tableLoading=!0,Object(l._15)({no:this.no}).then(function(a){t.tableLoading=!1,t.tableData.push(a.data),t.amount=a.data.money})},walletMe:function(){var t=this;Object(l._34)({}).then(function(a){t.wallet=a.data.amount})},closeDialog:function(){this.dialogVisible=!1,this.uploadLoading=!1},pay:function(){0===this.pay_type?this.$message.error("请选择支付方式"):this.no&&this.$refs.payComponent.pay(this.no,this.pay_type)},changePayRadio:function(t){this.walletPay=4===t?this.wallet>this.amount?this.amount:this.wallet:0}}},n={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"app-container"},[e("el-steps",{attrs:{active:1,"finish-status":"success"}},[e("el-step",{attrs:{title:"确认订单"}}),t._v(" "),e("el-step",{attrs:{title:"选择支付方式"}}),t._v(" "),e("el-step",{attrs:{title:"支付成功"}})],1),t._v(" "),e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("h3",[t._v("待支付订单")])]),t._v(" "),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,"highlight-current-row":""}},[e("el-table-column",{attrs:{fixed:"",prop:"title",label:"产品名称"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.title))])]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"产品配置"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v("渠道版(包年)")])]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"购买方式"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v("周期购买")])]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"购买时长"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.month))])]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"购买价格"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticStyle:{color:"firebrick"}},[t._v("￥"+t._s(t.amount))])]}}])})],1)],1),t._v(" "),e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"30px"}},[e("div",[e("span",[t._v("使用余额")]),t._v(" "),e("el-checkbox",{staticStyle:{"margin-left":"30px",display:"none"}},[t._v("使用账户余额抵扣")]),t._v(" "),e("span",[t._v("（当前账户余额")]),t._v(" "),e("span",{staticStyle:{color:"orangered"}},[t._v("￥"+t._s(t.wallet))]),t._v(" "),e("span",[t._v("）")]),t._v(" "),e("span",{staticStyle:{float:"right",display:"none"}},[e("span",[t._v("抵扣")]),t._v(" "),e("span",{staticStyle:{color:"orangered"}},[t._v("￥0.00")])])],1)]),t._v(" "),e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"30px"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("h1",[t._v("支付方式")])]),t._v(" "),e("div",{staticClass:"pay_type"},[e("el-radio-group",{on:{change:t.changePayRadio},model:{value:t.pay_type,callback:function(a){t.pay_type=a},expression:"pay_type"}},[e("el-radio",{attrs:{label:1}},[t._v("支付宝")]),t._v(" "),e("el-radio",{attrs:{label:2}},[t._v("微信")]),t._v(" "),e("el-radio",{staticStyle:{display:"none"},attrs:{label:3}},[t._v("银联")]),t._v(" "),e("el-radio",{attrs:{label:4}},[t._v("余额")])],1)],1)]),t._v(" "),e("div",{staticClass:"footer"},[e("div",{staticClass:"text"},[e("div",[e("span",[t._v("实际金额:")]),t._v(" "),e("span",[t._v("￥"+t._s(t.amount))])]),t._v(" "),e("div",[e("span",[t._v("应付金额:")]),t._v(" "),e("span",[t._v("￥"+t._s(t.amount))]),t._v(" "),e("span",[t._v(" - 使用余额：")]),t._v(" "),e("span",[t._v("￥"+t._s(t.walletPay))])])]),t._v(" "),e("el-button",{attrs:{loading:t.uploadLoading},on:{click:t.pay}},[t._v("支付")])],1),t._v(" "),e("payComponent",{ref:"payComponent"})],1)},staticRenderFns:[]};var o=e("VU/8")(s,n,!1,function(t){e("RcZh")},"data-v-08cd51b1",null);a.default=o.exports},RcZh:function(t,a){}});