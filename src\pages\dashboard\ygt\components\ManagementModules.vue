<template>
  <div class="management-modules">
    <div class="module-grid">
      <div v-for="item in modules" :key="item.id" class="module-card" :style="{
        background: item.gradient,
        borderColor: item.borderColor
      }" @click="handleModuleClick(item)">
        <div class="model-content">
          <div class="module-icon">
            <img :src="item.icon" :alt="item.name" />
          </div>
          <h3 class="module-title">{{ item.name }}</h3>
          <div class="module-tag" :style="{ background: item.tagBg }">{{ item.tag }}</div>
          <p class="module-description">{{ item.description }}</p>
        </div>
        <div class="btn-wrap" :style="{ borderColor: item.lineColor }">
          <div class="module-button" :style="{ background: item.buttonColor }">
            <span>点击进入</span>
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ManagementModules",
  data() {
    return {
      modules: [
        {
          id: 1,
          name: "AI老板",
          tag: '让老板更省心，让员工更轻松',
          description: "自动拆解工作内容，输出标准化、可执行的员工工作清单",
          icon: require("@/assets/image/dashboard/ygt/ai.png"),
          gradient: "linear-gradient(117deg, #D6E8FF, #EBFBFE)",
          borderColor: "#D6E8FF",
          tagBg: '#CEE5FF',
          lineColor: '#D7E9FF',
          buttonColor: "linear-gradient(90deg, #167BFF, #2F8CFD)",
          route: "/ai-boss"
        },
        {
          id: 2,
          name: "行政管理",
          tag: '数智化行政办公平台',
          description: "深入业务全场景，提升企业的办公效率",
          icon: require("@/assets/image/dashboard/ygt/xz.png"),
          gradient: "linear-gradient(117deg, #CCFFF3, #ECFFFA)",
          borderColor: "#C2F9EC",
          tagBg: '#BCF8EA',
          lineColor: '#C2F9EC',
          buttonColor: "linear-gradient(90deg, #03BF72, #07D381)",
          route: "/administration"
        },
        {
          id: 3,
          name: "人资管理",
          tag: '一站式人力资源管理平台',
          description: "赋能老板决策，全面掌控人才“选、育、用、留”",
          icon: require("@/assets/image/dashboard/ygt/rz.png"),
          gradient: "linear-gradient(117deg, #D7D9FF, #F3F1FE)",
          borderColor: "#DEE0FF",
          tagBg: '#D3D5FF',
          lineColor: '#E7E7FF',
          buttonColor: "linear-gradient(90deg, #5453E2, #6362F3)",
          route: "/hr-management"
        },
        {
          id: 4,
          name: "法务管理",
          tag: '新一代Ai赋能',
          description: "合同全生命周期管理系统",
          icon: require("@/assets/image/dashboard/ygt/fw.png"),
          gradient: "linear-gradient(117deg, #D6E8FF, #EBFBFE)",
          borderColor: "#D6E8FF",
          tagBg: '#CEE5FF',
          lineColor: '#D7E9FF',
          buttonColor: "linear-gradient(90deg, #167BFF, #2F8CFD)",
          route: "/legal-management"
        },
        {
          id: 5,
          name: "财务管理",
          tag: '实时移动查看经营报表',
          description: "让财务工作化繁为简，老板全面掌控财务状态",
          icon: require("@/assets/image/dashboard/ygt/cw.png"),
          gradient: "linear-gradient(117deg, #F0DDFE, #F3ECFF)",
          borderColor: "#F0DDFE",
          tagBg: '#EBD2FF',
          lineColor: '#E7E7FF',
          buttonColor: "linear-gradient(90deg, #8553E2, #7D62F3)",
          route: "/finance-management"
        }
      ]
    };
  },
  methods: {
    handleModuleClick(module) {
      // 这里可以根据需要进行路由跳转或其他操作
      console.log("点击了模块:", module.name);
      // 示例：路由跳转
      // this.$router.push(module.route);

      // 或者触发父组件事件
      this.$emit('module-click', module);
    }
  }
};
</script>

<style scoped lang="scss">
.management-modules {
  background: #FFFFFF;
  border-radius: 10px;
  padding: 16px;
  margin-top: 16px;
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  margin: 0 auto;
}

.module-card {
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid;

  &:hover {
    transform: translateY(-2px);
  }

  .model-content {
    padding: 8px 30px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .module-icon {
      width: 200px;
      height: 200px;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .module-title {
      font-weight: 800;
      font-size: 22px;
      color: #000000;
      margin-top: -44px;
      margin-bottom: 12px;
    }
    .module-tag {
      font-size: 14px;
      color: #36484E;
      padding: 4px 9px;
      background: #CEE5FF;
      border-radius: 6px;
      margin-bottom: 12px;
    }

    .module-description {
      font-size: 14px;
      color: #36484E;
      line-height: 20px;
      margin-bottom: 12px;
      text-align: center;
    }
  }
  .btn-wrap {
    width: 100%;
    padding: 22px 70px 37px;
    border-top: #D7E9FF solid 1px;
    box-sizing: border-box;
  }
  .module-button {
    width: 100%;
    height: 40px;
    border-radius: 4px;
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(90deg, #167BFF, #2F8CFD);
    box-shadow: 0px 4px 12px 0px rgba(206, 229, 255, 0.31);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      margin-bottom: 2px;
      margin-right: 2px;
    }

    &:hover {
      opacity: 0.9;
    }
  }

}


// 响应式设计
@media (max-width: 1200px) {
  .module-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 992px) {
  .module-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .management-modules {
    padding: 30px 4%;
  }

  .module-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .module-card {
    padding: 20px 15px;
  }
}

@media (max-width: 480px) {
  .module-grid {
    grid-template-columns: 1fr;
  }
}
</style>
