<template>
  <div class="management-modules">
    <div class="module-grid">
      <div 
        v-for="item in modules" 
        :key="item.id"
        class="module-card"
        :style="{ backgroundColor: item.bgColor }"
        @click="handleModuleClick(item)"
      >
        <div class="model-content">
          <div class="module-icon">
            <img :src="item.icon" :alt="item.name" />
          </div>
          <h3 class="module-title">{{ item.name }}</h3>
          <p class="module-description">{{ item.description }}</p>
        </div>
        <button 
          class="module-button"
          :style="{ backgroundColor: item.buttonColor }"
        >
          点击进入 >
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ManagementModules",
  data() {
    return {
      modules: [
        {
          id: 1,
          name: "AI老板",
          description: "让老板工作更轻松，让团队协作更高效，让企业管理更智能",
          icon: require("@/assets/image/dashboard/ygt/ai.png"),
          bgColor: "#E3F2FD",
          buttonColor: "#2196F3",
          route: "/ai-boss"
        },
        {
          id: 2,
          name: "行政管理",
          description: "深入业务流程，掌握企业运营的公文管理",
          icon: require("@/assets/image/dashboard/ygt/xz.png"),
          bgColor: "#E8F5E8",
          buttonColor: "#4CAF50",
          route: "/administration"
        },
        {
          id: 3,
          name: "人资管理",
          description: "规范企业流程，全面提升人力资源管理效率",
          icon: require("@/assets/image/dashboard/ygt/rz.png"),
          bgColor: "#F3E5F5",
          buttonColor: "#9C27B0",
          route: "/hr-management"
        },
        {
          id: 4,
          name: "法务管理",
          description: "合同生成和管理服务系统",
          icon: require("@/assets/image/dashboard/ygt/fw.png"),
          bgColor: "#E1F5FE",
          buttonColor: "#03A9F4",
          route: "/legal-management"
        },
        {
          id: 5,
          name: "财务管理",
          description: "让财务工作更高效，让企业资金更安全",
          icon: require("@/assets/image/dashboard/ygt/cw.png"),
          bgColor: "#F3E5F5",
          buttonColor: "#9C27B0",
          route: "/finance-management"
        }
      ]
    };
  },
  methods: {
    handleModuleClick(module) {
      // 这里可以根据需要进行路由跳转或其他操作
      console.log("点击了模块:", module.name);
      // 示例：路由跳转
      // this.$router.push(module.route);
      
      // 或者触发父组件事件
      this.$emit('module-click', module);
    }
  }
};
</script>

<style scoped lang="scss">
.management-modules {
  background: #FFFFFF;
  border-radius: 10px;
  padding: 16px;
  margin-top: 16px;
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  margin: 0 auto;
}

.module-card {
  background: white;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
  .model-content {
    padding: 8px 34px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .module-icon {
      width: 60px;
      height: 60px;
      margin: 0 auto 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .module-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-bottom: 12px;
    }

    .module-description {
      font-size: 14px;
      color: #666;
      line-height: 1.5;
      margin-bottom: 20px;
      min-height: 42px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .module-button {
      width: 100%;
      height: 36px;
      border: none;
      border-radius: 6px;
      color: white;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        opacity: 0.9;
        transform: translateY(-1px);
      }
    }

  }
}


// 响应式设计
@media (max-width: 1200px) {
  .module-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 992px) {
  .module-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .management-modules {
    padding: 30px 4%;
  }
  
  .module-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
  
  .module-card {
    padding: 20px 15px;
  }
}

@media (max-width: 480px) {
  .module-grid {
    grid-template-columns: 1fr;
  }
}
</style>
