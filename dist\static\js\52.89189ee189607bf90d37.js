webpackJsonp([52],{K6aL:function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var o=t("pFYg"),r=t.n(o),s=t("pI5c"),l=t("Wxq9"),n=t("2Eno"),i=t("lbHh"),c=t.n(i),d={name:"create",data:function(){return{domain:"china9.cn",loading:!1,provinceAndCityData:l.regionData,upload_url:"/api/upload",trade:[],formData:{},certificateData:{},logo:"",props:{value:"id",label:"name",children:"children"},options:{},rules:{company_name:[{required:!0,message:"请输入企业名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],located:[{required:!0,message:"请输入所在城市",trigger:"blur"}],address:[{required:!0,message:"请输入详细地址",trigger:"blur"}],industry:[{required:!0,message:"请输入行业类型",trigger:"blur"}],scale:[{required:!0,message:"请选择公司规模",trigger:"blur"}],website:[{required:!1,message:"请输入企业网址",trigger:"blur"}]}}},computed:{token:function(){return this.$cookies.get("token")||this.$cookies.get("access_token")||this.$store.getters.token}},created:function(){this.getOptions()},methods:{handleSuccess:function(e,a){e.data.length&&(this.formData.logo=e.data[0],this.logo=URL.createObjectURL(a.raw))},getOptions:function(){var e=this;this.loading=!0,this.options.ORGANIZATION_CITY=this.provinceAndCityData,this.options.ORGANIZATION_SCALE=Object(n.a)(),Object(s.s)().then(function(a){e.loading=!1,e.options.ORGANIZATION_TRADE=e.getTreeData(a.data)})},getTreeData:function(e){for(var a=0;a<e.length;a++)e[a].child.length<1?e[a].child=void 0:this.getTreeData(e[a].child);return e},companyCreate:function(){var e=this;this.$refs.companyInfoForm.validate(function(a){if(!a)return e.$message.error("请将信息填写完整"),!1;"object"===r()(e.formData.located)&&(e.formData.located=e.formData.located.join(",")),"object"===r()(e.formData.industry)&&e.formData.industry.length>0&&(e.formData.industry=e.formData.industry.join(",")),e.loading=!0,Object(s.q)(e.formData).then(function(a){e.loading=!1,200===a.code&&(e.$message.success("提交成功"),a.data.token&&(c.a.set("token",a.data.token,{path:"/",domain:e.domain,expires:new Date(1e3*a.data.expires_in)}),c.a.set("access_token",a.data.token,{path:"/",domain:e.domain,expires:new Date(1e3*a.data.expires_in)})),Object(s._31)().then(function(a){e.loading=!1,e.$store.commit("setUser",a.data)}),e.$router.push("/company/detail"))}).catch(function(){e.loading=!1})})}}},m={render:function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"app-container",staticStyle:{width:"100%"}},[t("el-card",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"box-card"},[t("el-row",[t("el-col",[t("div",{staticClass:"header_title"},[t("h1",[e._v("创建企业信息")])]),e._v(" "),t("el-form",{ref:"companyInfoForm",staticClass:"demo-form-inline",attrs:{inline:!0,rules:e.rules,model:e.formData,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"企业名称",prop:"company_name"}},[t("el-input",{attrs:{placeholder:"请输入企业名称",span:12,clearable:""},model:{value:e.formData.company_name,callback:function(a){e.$set(e.formData,"company_name",a)},expression:"formData.company_name"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"所在城市",prop:"located"}},[t("el-cascader",{attrs:{options:e.options.ORGANIZATION_CITY,filterable:"",clearable:"",placeholder:"可搜索"},model:{value:e.formData.located,callback:function(a){e.$set(e.formData,"located",a)},expression:"formData.located"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"详细地址",prop:"address"}},[t("el-input",{attrs:{placeholder:"请输入详细地址",clearable:""},model:{value:e.formData.address,callback:function(a){e.$set(e.formData,"address",a)},expression:"formData.address"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"企业LOGO",prop:"logo"}},[t("el-upload",{staticClass:"avatar-uploader",attrs:{name:"files[]",data:{token:e.token},action:e.upload_url,"show-file-list":!1,"on-success":e.handleSuccess}},[e.logo?t("el-image",{staticClass:"avatar",attrs:{fit:"contain",src:e.logo}}):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"})],1)],1),e._v(" "),t("el-form-item",{attrs:{label:"行业类型",prop:"industry"}},[t("el-cascader",{attrs:{options:e.options.ORGANIZATION_TRADE,props:{label:"title",children:"child",value:"id"},clearable:""},model:{value:e.formData.industry,callback:function(a){e.$set(e.formData,"industry",a)},expression:"formData.industry"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"公司规模",prop:"scale"}},[t("el-select",{attrs:{placeholder:"请选择公司规模"},model:{value:e.formData.scale,callback:function(a){e.$set(e.formData,"scale",a)},expression:"formData.scale"}},e._l(e.options.ORGANIZATION_SCALE,function(e,a){return t("el-option",{key:a,attrs:{label:e,value:e}})}),1)],1),e._v(" "),t("el-form-item",{attrs:{label:"企业网址",prop:"website"}},[t("el-input",{attrs:{placeholder:"请输入企业网址",clearable:""},model:{value:e.formData.website,callback:function(a){e.$set(e.formData,"website",a)},expression:"formData.website"}})],1)],1),e._v(" "),t("el-button",{attrs:{type:"primary"},on:{click:e.companyCreate}},[e._v("创建企业")])],1)],1)],1)],1)},staticRenderFns:[]};var u=t("VU/8")(d,m,!1,function(e){t("rWZu"),t("n0YJ")},"data-v-4d664ba4",null);a.default=u.exports},n0YJ:function(e,a){},rWZu:function(e,a){}});