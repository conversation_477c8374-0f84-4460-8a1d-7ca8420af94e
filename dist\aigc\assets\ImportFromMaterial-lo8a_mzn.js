import{k as Q,l as T,A as r,c as g,a as d,I as M,w as s,o as n,au as Z,b as u,K as j,p as L,aS as q,j as x,t as y,h as O,a2 as _,Q as b,_ as N,n as $,aA as ee,m as V,F as Y,f as I,a6 as D,a7 as te}from"./index-BBeD0eDz.js";/* empty css               *//* empty css                *//* empty css               */import{e as le,f as G,F as P,g as ae,b as oe,v as se,m as ie,L as ne}from"./List-CZfQXqH2.js";import{a as X}from"./request-Ciyrqj7N.js";const ue="data:image/png;base64,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",de={class:"upload-dialog"},re={class:"el-upload__tip text-center"},pe={class:"dialog-footer text-center pt-[20px]"},ce=Q({__name:"UploadDialog",props:{modelValue:{type:Boolean},fileType:{default:"all"}},emits:["update:modelValue","upload-success"],setup(w,{emit:J}){const f=w,o=J,H=T({get:()=>f.modelValue,set:l=>o("update:modelValue",l)}),S=r({token:X.get("token")||X.get("access_token")||""}),E=T(()=>le()),U=T(()=>f.fileType&&f.fileType!=="all"?`${f.fileType}/*`:Object.keys(E.value).filter(l=>l!=="all").map(l=>`${l}/*`).join(",")),B=T(()=>{var e;if(f.fileType&&f.fileType!=="all"){const t=G.find(m=>m.key===f.fileType),a=(t==null?void 0:t.label)||f.fileType,v=f.fileType.toUpperCase(),i=(e=P[v])==null?void 0:e.size;if(i){const m=(i/1024/1024).toFixed(0);return`支持上传${a}文件，最大${m}MB`}}return`支持上传${G.filter(t=>t.key!=="all").map(t=>t.label).join("、")}等多种格式文件`}),p=r(""),c=r(null),F=l=>{var v;const e=l.type.split("/")[0];if(!E.value[e]||!U.value.includes(e+"/*"))return b.error(`不支持的文件类型: ${l.type}`),!1;const t=e.toUpperCase(),a=(v=P[t])==null?void 0:v.size;if(!a)return b.error(`未找到文件类型 ${e} 的大小限制配置`),!1;if(l.size>a){const i=(a/1024/1024).toFixed(0);return b.error(`文件 ${l.name} 超出大小限制 (${i}MB)`),!1}return!0},K=l=>{if(!F(l))return!1;c.value=null;const e=_.service({lock:!0,text:"上传中...",background:"rgba(255, 255, 255, 0.7)"});return c.value=e,!0},C=r(null),h=r({}),R=async(l,e)=>{try{if(l.code===200){h.value=l.data;const t=e.raw.type.split("/")[0],a=ae(t||"image"),v=t==="video"?l.data.url:l.data,i=await oe({title:e.raw.name,category_id:0,flod_id:0,type:1,filetype:a,bigtype:1,url:v,cover:t==="video"?l.data.cover:"",size:e.raw.size,times:t==="video"?l.data.times||0:"",fbl:h.value.fbl,fps:h.value.fps,zhb:h.value.zhb});i?(console.log("🚀 ~ handleUploadSuccess ~ res: ",i),b.success("上传成功"),o("upload-success",[{id:i,...l.data}]),t==="video"&&i&&se.addToQueue(i,e.raw.name)):b.error("上传到素材库失败")}else b.error(l.message||"上传失败")}catch(t){console.error("上传到素材库失败:",t),b.error("上传到素材库失败，请重试")}finally{c.value&&(c.value.close(),c.value=null),C.value&&C.value.clearFiles(),W.value=[],h.value={},z()}},W=r([]),A=l=>{W.value.push(l),l.raw.type.startsWith("video/")?p.value="https://apidev.china9.cn/api/bucket/upload":p.value="https://apidev.china9.cn/api/bucket/uploadall"},z=()=>{H.value=!1},k=()=>{z()};return(l,e)=>{const t=j,a=Z,v=O,i=M;return n(),g("div",de,[d(i,{modelValue:H.value,"onUpdate:modelValue":e[0]||(e[0]=m=>H.value=m),title:"上传文件",width:"500px","before-close":z,class:"upload-dialog-dialog"},{footer:s(()=>[u("div",pe,[d(v,{onClick:z,size:"large",class:"w-[100px]"},{default:s(()=>e[2]||(e[2]=[x("取消")])),_:1}),d(v,{type:"primary",onClick:k,size:"large",class:"w-[100px]"},{default:s(()=>e[3]||(e[3]=[x("确认")])),_:1})])]),default:s(()=>[d(a,{class:"material-upload",drag:"",action:p.value,"auto-upload":!0,"file-list":W.value,"on-change":A,data:S.value,multiple:"",accept:U.value,"show-file-list":!0,"before-upload":K,"on-success":R,limit:10},{tip:s(()=>[u("div",re,y(B.value),1)]),default:s(()=>[d(t,{class:"el-icon--upload"},{default:s(()=>[d(L(q))]),_:1}),e[1]||(e[1]=u("div",{class:"el-upload__text"},[x(" 拖拽文件到此处，或 "),u("em",null,"点击上传")],-1))]),_:1},8,["action","file-list","data","accept"])]),_:1},8,["modelValue"])])}}}),me=N(ce,[["__scopeId","data-v-efa4c01e"]]),fe={class:"import-title flex items-center"},ve={class:"text-[#555] text-[16px]"},ye={class:"text-[#555] text-[16px]"},ge={key:2,class:"flex flex-col items-center"},xe={key:0,class:"text-[--el-color-primary] text-[16px] mt-[17px]"},he={key:3,class:"flex flex-col items-center w-full h-full"},be={key:0,class:"label text-[#333] text-[16px] mt-[12px]"},Ce={class:"btn-wrapper mt-[18px]"},ke={class:"btn-wrapper mt-[18px]"},Ve={class:"text-[#555] text-[16px]"},He={class:"dialog-footer text-center"},We=Q({__name:"ImportFromMaterial",props:{label:{default:""},theme:{default:"button"},btnText:{default:"从素材库选择"},btn2Text:{default:"上传素材"},imgWidth:{default:"92px"},imgHeight:{default:"92px"},plusWidth:{default:30},type:{default:"all"},alertType:{default:"import"},multiSelect:{type:Boolean,default:!0},exportApi:{default:null},exportParams:{default:()=>({id:void 0})},activeIndex:{default:"material"},size:{default:"medium"},uploadType:{default:"material"}},emits:["submit","cancel","select"],setup(w,{expose:J,emit:f}){var l;const o=w,H=r(o.activeIndex),S=r({bigtype:((l=ie.find(e=>e.key===o.activeIndex))==null?void 0:l.value)||0,category_id:0,flod_id:0}),E=T(()=>o.exportParams?{...S.value,...o.exportParams}:S.value),U=T(()=>o.alertType==="export"?"收藏至素材库":"添加素材"),B=f,p=r(!1),c=r([]),F=()=>{p.value=!0},K=()=>{p.value=!1,B("cancel",p.value)},C=r(),h=r(!1),R=async()=>{var e;if(o.alertType==="import")B("submit",c.value);else if(C.value){const t=E.value;if(t.flod_id=C.value.getFolderId(),t.category_id=C.value.getCategoryId(),!o.exportApi||!E.value.id)return;try{h.value=!0,await o.exportApi(t),b.success("导出成功"),(e=C.value)==null||e.refresh()}catch(a){console.log(a),b.error("导出失败")}finally{h.value=!1}}p.value=!1},W=e=>{o.multiSelect?(c.value=[...c.value,...e],c.value=[...new Set(c.value)]):c.value=[...e]},A=r(!1),z=e=>{B("submit",e),W(e)},k=()=>{o.uploadType==="local"?A.value=!0:p.value=!0};return J({openDialog:F}),(e,t)=>{const a=O,v=j,i=M;return n(),g("div",{class:$(["import-from-material",e.size])},[u("div",fe,[ee(e.$slots,"default",{openDialog:F},void 0,!0),e.$slots.default?V("",!0):(n(),g(Y,{key:0},[e.theme==="button"?(n(),g(Y,{key:0},[u("span",ve,y(e.label),1),e.label?(n(),I(a,{key:0,type:"primary",class:"ml-[19px] rounded-[4px] import-btn",onClick:k},{default:s(()=>[x(y(e.btnText),1)]),_:1})):V("",!0)],64)):e.theme==="text"?(n(),g(Y,{key:1},[u("span",ye,y(e.label),1),e.label?(n(),I(a,{key:0,type:"primary",link:"",class:"ml-[19px] rounded-[4px]",onClick:k},{default:s(()=>[x(y(e.btnText),1)]),_:1})):V("",!0)],64)):e.theme==="img"?(n(),g("div",ge,[u("div",{class:"add-icon border border-dashed border-[#186DF5] rounded-[6px] flex justify-center items-center cursor-pointer",onClick:k,style:D({width:e.imgWidth,height:e.imgWidth||e.imgHeight})},[d(v,{color:"#186DF5",size:e.plusWidth},{default:s(()=>[d(L(te))]),_:1},8,["size"])],4),e.label?(n(),g("div",xe,y(e.label),1)):V("",!0)])):e.theme==="article"?(n(),g("div",he,[u("div",{class:"add-icon border border-dashed border-[#186DF5] rounded-[10px] flex flex-col justify-center items-center cursor-pointer",style:D({width:e.imgWidth,height:e.imgHeight})},[t[4]||(t[4]=u("img",{class:"image",src:ue,alt:""},null,-1)),e.label?(n(),g("div",be,y(e.label),1)):V("",!0),u("div",Ce,[d(a,{type:"primary",class:"btn-library rounded-[4px] w-[120px] h-[38px]",onClick:k},{default:s(()=>[x(y(e.btnText),1)]),_:1})]),u("div",ke,[d(a,{class:"btn-upload rounded-[4px] w-[120px] h-[38px] default-article",onClick:t[0]||(t[0]=m=>A.value=!0)},{default:s(()=>[x(y(e.btn2Text),1)]),_:1})])],4)])):(n(),g(Y,{key:4},[u("span",Ve,y(e.label),1),e.label?(n(),I(a,{key:0,type:"primary",class:"ml-[19px] rounded-[4px] import-btn",onClick:k},{default:s(()=>[x(y(e.btnText),1)]),_:1})):V("",!0)],64))],64))]),d(i,{title:U.value,modelValue:p.value,"onUpdate:modelValue":t[2]||(t[2]=m=>p.value=m),width:"84vw","align-center":""},{footer:s(()=>[u("div",He,[d(a,{onClick:K,class:"w-[100px]"},{default:s(()=>t[5]||(t[5]=[x("取消")])),_:1}),d(a,{type:"primary",onClick:R,loading:h.value,class:"w-[100px]"},{default:s(()=>t[6]||(t[6]=[x("确定")])),_:1},8,["loading"])])]),default:s(()=>[p.value?(n(),I(ne,{key:0,ref_key:"listRef",ref:C,from:"dialog",type:o.type,style:{height:"70vh"},onSelect:W,"alert-type":o.alertType,"active-index":H.value,"onUpdate:activeIndex":t[1]||(t[1]=m=>H.value=m),"multi-select":o.multiSelect,class:"import-media-dialog"},null,8,["type","alert-type","active-index","multi-select"])):V("",!0)]),_:1},8,["title","modelValue"]),d(me,{modelValue:A.value,"onUpdate:modelValue":t[3]||(t[3]=m=>A.value=m),onUploadSuccess:z,"file-type":o.type},null,8,["modelValue","file-type"])],2)}}}),Ue=N(We,[["__scopeId","data-v-62ea00df"]]);export{Ue as I};
