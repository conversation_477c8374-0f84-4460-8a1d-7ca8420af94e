webpackJsonp([74],{RkXh:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i("zL8q"),r=i("cMGX"),o=i("FCNb"),a=i("pI5c");function s(){return l()+"/"+l()+function(){var e=new Date,t=e.getHours(),i=e.getMinutes(),n=e.getSeconds(),r="";r+=t>=10?t:"0"+t;r+=i>=10?i:"0"+i;r+=n>=10?n:"0"+n;return r}()+Math.floor(1e4*(Math.random()+Math.floor(9*Math.random()+1)))}function l(){var e=new Date,t=e.getFullYear(),i=e.getMonth()+1,n=e.getDate(),r=""+t;return r+=i>=10?i:"0"+i,r+=n>=10?n:"0"+n}var c=i("wU6q"),d={name:"list",components:{Pagination:r.a,yunpanPermission:o.a,CardWrap:c.a},data:function(){return{dirLoading:!1,queryList:{page:1,limit:20},total:0,tableData:[],multipleSelection:[],dialog_modal:{},dirDialogTitle:"",file_dialog_modal:{},fileDialogTitle:"",permDialogTitle:"",current_dir:{id:0,dir_name:"",title:""},parent_dir_id:0,path_data:[0],dirDialogVisible:!1,fileDialogVisible:!1,permDialogVisible:!1,formLabelWidth:"120px",rules:{dir_name:[{required:!0,message:"文件夹名称不能为空",trigger:"blur"}]},file_rules:{file_name:[{required:!0,message:"文件名称不能为空",trigger:"blur"}]},fileList:[],file:"",ak:"",sk:"",st:"",upload_path:"",bucket:"",endpoint:"",showProgress:!1,progress:0,OrgPersonTree:"",permkey:0,permdata:[],loadingInstance:null,permFileId:0,sort_key:"",sort_order:""}},created:function(){this.getDirList(),this.getAccessKey()},filters:{sizeFormat:function(e){if(0===e)return"0 B";var t=Math.floor(Math.log(e)/Math.log(1024));return(e/Math.pow(1024,t)).toPrecision(3)+" "+["B","KB","MB","GB","TB","PB","EB","ZB","YB"][t]}},methods:{getDirList:function(){var e=this;this.dirLoading=!0,Object(a.N)({dir_id:this.current_dir.id,sort_field:this.sort_key,sort_order:this.sort_order,page:this.queryList.page,limit:this.queryList.limit}).then(function(t){e.dirLoading=!1,e.total=t.data.total,e.tableData=t.data.list,e.tableData.forEach(function(e,t){"dir"==e.type?(e.type="目录",e.title=e.dir_name):(e.type="文件",e.title=e.file_name+"."+e.file_type)}),console.log(t)})},gotoUpDir:function(){var e=this.path_data.pop();this.current_dir.id!=e?(this.current_dir={id:e,dir_name:"",title:""},this.getDirList()):this.$message.warning("当前已是最上级")},changeDir:function(e){this.path_data.push(this.current_dir.id),this.current_dir={id:e,dir_name:"",title:""},this.getDirList()},destory:function(){var e=this;0===this.multipleSelection.length?this.$message.error("最少选中一个文件或目录"):this.$confirm("删除选中的目录和文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t=[],i=[];e.multipleSelection.forEach(function(e){"目录"==e.type?t.push(e.id):i.push(e.id)}),e.dirLoading=!0,Object(a.h)({dir_ids:t.join(","),file_ids:i.join(",")}).then(function(t){e.dirLoading=!1,e.dirSuccess("删除成功")})})},handleSelectionChange:function(e){this.multipleSelection=e},handleSortChange:function(e){this.sort_key=e.prop,this.sort_order=e.order,this.getDirList()},uploadFile:function(){this.file_dialog_modal={file_name:"",dir_id:void 0!==this.current_dir.id?this.current_dir.id:0},this.fileList=[],this.fileDialogTitle="添加文件",this.dirDialogVisible=!1,this.fileDialogVisible=!0},dirHandle:function(e){switch(e){case 1:this.dialog_modal={dir_name:"",pid:void 0!==this.current_dir.id?this.current_dir.id:0},this.dirDialogTitle="新建文件夹";break;case 2:this.dialog_modal={id:this.current_dir.id,dir_name:this.current_dir.dir_name,pid:this.current_dir.pid,order_id:this.current_dir.order_id},this.dirDialogTitle="修改文件夹";break;case 3:this.dialog_modal={dir_name:"",pid:0},this.dirDialogTitle="新建顶层文件夹"}this.fileDialogVisible=!1,this.dirDialogVisible=!0},editDir:function(e){this.dialog_modal={id:e.id,dir_name:e.dir_name,pid:e.pid,order_id:0},this.dirDialogTitle="修改文件夹",this.fileDialogVisible=!1,this.dirDialogVisible=!0},deleteDir:function(e){var t=this;this.$confirm("此操作将永久删除该文件夹及其包含的文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(a.M)({id:e.id}).then(function(e){200===e.code&&t.dirSuccess("删除成功")}).catch(function(){t.$message({type:"error",message:"删除失败"})})})},deleteFile:function(e){var t=this;this.$confirm("此操作将永久删除该文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(a.T)({id:e.id}).then(function(e){200===e.code&&t.dirSuccess("删除成功")}).catch(function(){t.$message({type:"error",message:"删除失败"})})})},dirSubmit:function(){var e=this;this.$refs.dialog.validate(function(t){if(!t)return!1;switch(e.dirDialogTitle){case"新建文件夹":Object(a.L)(e.dialog_modal).then(function(t){200===t.code&&e.dirSuccess("新建成功")});break;case"修改文件夹":Object(a.O)(e.dialog_modal).then(function(t){200===t.code&&(e.current_dir.title=e.dialog_modal.title,e.dirSuccess("修改完成"))});break;case"新建顶层文件夹":Object(a.L)(e.dialog_modal).then(function(t){200===t.code&&e.dirSuccess("新建成功")})}})},viewFile:function(e){window.open(e.file_url)},permFile:function(e){var t=this,i=this;""==this.OrgPersonTree?(this.loadingInstance=n.Loading.service({text:"加载中",target:".table"}),Object(a._1)().then(function(e){200===e.code?(i.OrgPersonTree=e.data[0],i.permDialogVisible=!0):console.log(e),t.loadingInstance.close()})):i.permDialogVisible=!0,Object(a.Y)({file_id:e.id}).then(function(e){200===e.code&&(i.permdata=e.data.list)}),i.permkey=e.id},parentFn:function(e){this.permdata=e},permSubmit:function(){},fileSubmit:function(){var e=this;this.$refs.dialog.validate(function(t){if(!t)return!1;switch(e.dirDialogTitle){case"新建文件夹":Object(a.L)(e.dialog_modal).then(function(t){200===t.code&&e.dirSuccess("新建成功")});break;case"修改文件夹":Object(a.O)(e.dialog_modal).then(function(t){200===t.code&&(e.current_dir.title=e.dialog_modal.title,e.dirSuccess("修改完成"))});break;case"新建顶层文件夹":Object(a.L)(e.dialog_modal).then(function(t){200===t.code&&e.dirSuccess("新建成功")})}})},dirDelete:function(){var e=this;this.$confirm("删除文件夹, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(a.M)({id:e.current_dir.id}).then(function(t){200===t.code&&e.dirSuccess("删除成功")})})},dirSuccess:function(e){this.$message.success(e),this.getDirList(),this.dirDialogVisible=!1},permSuccess:function(e){this.$message.success(e),this.permDialogVisible=!1},fileSuccess:function(e){this.$message.success(e),this.getDirList(),this.fileDialogVisible=!1},handleRemove:function(e,t){console.log(e,t)},handlePreview:function(e){console.log(e)},handleExceed:function(e,t){this.$message.warning("当前限制选择 3 个文件，本次选择了 "+e.length+" 个文件，共选择了 "+(e.length+t.length)+" 个文件")},handleChange:function(e,t){this.fileList=t,this.file=e.raw},beforeRemove:function(e,t){return this.$confirm("确定移除 "+e.name+"？")},submitForm:function(){this.$refs.upload.submit()},getAccessKey:function(){var e=this;Object(a.a)().then(function(t){e.ak=t.data.credential.access,e.sk=t.data.credential.secret,e.st=t.data.credential.securitytoken,e.bucket=t.data.bucket,e.endpoint=t.data.endpoint,e.upload_path=t.data.path})},getObsClient:function(){return this.getAccessKey(),new ObsClient({access_key_id:this.ak,secret_access_key:this.sk,security_token:this.st,server:this.endpoint,timeout:300})},handleSuccess:function(e,t,i){console.log(i)},handleError:function(e,t,i){console.log(i)},upload:function(e){var t=this,i=e.file.name.substring(e.file.name.lastIndexOf(".")+1).toLowerCase();Object(a.k)({dir_id:this.current_dir.id,file_name:e.file.name,file_type:i}).then(function(i){if(200===i.code&&1==i.data.is_duplicate)return t.$message.warning("文件名重复"),!1;200===i.code&&0==i.data.is_duplicate&&t.obsUpload(e)})},obsUpload:function(e){var t=this,i=e.file.name.substring(e.file.name.lastIndexOf(".")+1).toLowerCase(),n=t.upload_path+s()+"."+i;this.getObsClient().putObject({Bucket:t.bucket,Key:n,Metadata:{property:"property-value"},SourceFile:e.file,ProgressCallback:function(t,i,n){e.onProgress({total:100,percent:Math.floor(100*t/i)})}}).then(function(i){if(200==i.CommonMsg.Status){e.onSuccess("上传成功"),console.log(e.file),console.log(i);var r=e.file.name;Object(a.S)({dir_id:t.current_dir.id,file_name:e.file.name,file_type:r.substring(r.lastIndexOf(".")+1).toLowerCase(),file_url:"https://zcloud.obs.cn-north-4.myhuaweicloud.com/"+n,file_size:e.file.size}).then(function(e){200===e.code&&t.fileSuccess("文件上传成功")})}else e.onError("上传失败"),t.$message({message:"文件上传失败",type:"error"})}).catch(function(i){console.error(i),e.onError("上传失败"),t.$message({message:"文件上传失败",type:"error"})})}}},u={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("card-wrap",{attrs:{title:"企业云盘"}},[i("div",{staticClass:"container-wrapper"},[i("div",{staticClass:"flex-ali-center",staticStyle:{"justify-content":"space-between","margin-bottom":"16px"}},[i("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.queryList,size:"small"}},[i("el-form-item",{staticStyle:{"margin-bottom":"0"}},[i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.gotoUpDir()}}},[e._v("返回上级目录")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.uploadFile()}}},[e._v("上传文件")])],1)],1),e._v(" "),i("div",{staticClass:"flex-ali-center"},[i("span",[e._v(e._s(e.current_dir.title))]),e._v(" "),i("div",{staticClass:"flex-ali-center"},[i("el-button",{staticStyle:{padding:"0 3px"},attrs:{size:"small",type:"text",icon:"el-icon-refresh"},on:{click:function(t){return e.getDirList()}}},[e._v("刷新")]),e._v(" "),i("el-button",{staticStyle:{padding:"0 3px"},attrs:{size:"small",type:"text",icon:"el-icon-plus"},on:{click:function(t){return e.dirHandle(1)}}},[e._v("创建文件夹\n            ")])],1)])],1),e._v(" "),i("div",{staticClass:"table"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.dirLoading,expression:"dirLoading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:"100%"},on:{"selection-change":e.handleSelectionChange,"sort-change":e.handleSortChange}},[i("el-table-column",{attrs:{type:"selection",width:"55"}}),e._v(" "),i("el-table-column",{attrs:{label:"名称",prop:"title",sortable:"custom"},scopedSlots:e._u([{key:"default",fn:function(t){return["目录"==t.row.type?i("a",{staticClass:"folderText",attrs:{type:"primary"},on:{click:function(i){return e.changeDir(t.row.id)}}},[i("i",{staticClass:"el-icon-folder"}),e._v(" "+e._s(t.row.title))]):i("span",{staticClass:"buttonText"},[i("i",{staticClass:"el-icon-document"}),e._v(" "+e._s(t.row.title))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"类型",prop:"type"}}),e._v(" "),i("el-table-column",{attrs:{label:"大小",prop:"file_size"},scopedSlots:e._u([{key:"default",fn:function(t){return["文件"==t.row.type?[e._v("\n                "+e._s(e._f("sizeFormat")(t.row.file_size))+"\n              ")]:i("span",[e._v("--")])]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"created_at",label:"创建时间",sortable:"custom"}}),e._v(" "),i("el-table-column",{attrs:{label:"操作",width:"280"},scopedSlots:e._u([{key:"default",fn:function(t){return["目录"==t.row.type?[i("el-button",{attrs:{size:"mini",icon:"el-icon-edit",type:"primary"},on:{click:function(i){return e.editDir(t.row)}}},[e._v("编辑")]),e._v(" "),i("el-button",{attrs:{size:"mini",icon:"el-icon-delete",type:"danger"},on:{click:function(i){return e.deleteDir(t.row)}}},[e._v("删除")])]:[i("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-view"},on:{click:function(i){return e.viewFile(t.row)}}},[e._v("查看")]),e._v(" "),i("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-user"},on:{click:function(i){return e.permFile(t.row)}}},[e._v("权限")]),e._v(" "),i("el-button",{attrs:{size:"mini",icon:"el-icon-delete",type:"danger"},on:{click:function(i){return e.deleteFile(t.row)}}},[e._v("删除")])]]}}])})],1)],1),e._v(" "),i("el-row",{directives:[{name:"show",rawName:"v-show",value:e.total>0||e.tableData.length>0,expression:"total > 0 || tableData.length > 0"}],staticStyle:{"margin-top":"20px"}},[i("el-col",[i("el-button",{attrs:{type:"warning",size:"small"},on:{click:function(t){return e.destory()}}},[e._v("删除")])],1)],1),e._v(" "),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticStyle:{"text-align":"center"},attrs:{total:e.total,page:e.queryList.page,limit:e.queryList.limit},on:{"update:page":function(t){return e.$set(e.queryList,"page",t)},"update:limit":function(t){return e.$set(e.queryList,"limit",t)},pagination:e.getDirList}})],1)]),e._v(" "),i("el-dialog",{attrs:{title:e.dirDialogTitle,visible:e.dirDialogVisible,width:"50%"},on:{"update:visible":function(t){e.dirDialogVisible=t}}},[i("el-form",{ref:"dialog",attrs:{model:e.dialog_modal,rules:e.rules}},[i("el-form-item",{attrs:{label:"文件夹名称","label-width":e.formLabelWidth,prop:"dir_name"}},[i("el-input",{staticStyle:{width:"300px"},attrs:{autocomplete:"off",placeholder:"请输入名称"},model:{value:e.dialog_modal.dir_name,callback:function(t){e.$set(e.dialog_modal,"dir_name",t)},expression:"dialog_modal.dir_name"}})],1)],1),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"small"},on:{click:function(t){e.dirDialogVisible=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.dirSubmit}},[e._v("确 定")])],1)],1),e._v(" "),i("el-dialog",{attrs:{title:"设置文件权限",visible:e.permDialogVisible,width:"50%"},on:{"update:visible":function(t){e.permDialogVisible=t}}},[i("yunpanPermission",{key:e.permkey,attrs:{inidata:e.permdata,OrgPersonTree:e.OrgPersonTree},on:{childFn:e.parentFn}}),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"small"},on:{click:function(t){e.permDialogVisible=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.permSubmit}},[e._v("确 定")])],1)],1),e._v(" "),i("el-dialog",{attrs:{title:e.fileDialogTitle,visible:e.fileDialogVisible,width:"50%"},on:{"update:visible":function(t){e.fileDialogVisible=t}}},[i("el-form",{ref:"file_dialog",attrs:{model:e.file_dialog_modal,rules:e.file_rules}},[i("el-form-item",[i("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{action:"","on-preview":e.handlePreview,"on-remove":e.handleRemove,"on-change":e.handleChange,"on-success":e.handleSuccess,"on-error":e.handleError,"before-remove":e.beforeRemove,"auto-upload":!1,"http-request":e.upload,multiple:"",limit:3,"on-exceed":e.handleExceed,"file-list":e.fileList}},[i("el-button",{attrs:{slot:"trigger",size:"small",type:"primary"},slot:"trigger"},[e._v("选择文件")]),e._v(" "),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"success"},on:{click:e.submitForm}},[e._v("上传到服务器")])],1)],1),e._v(" "),i("el-progress",{directives:[{name:"show",rawName:"v-show",value:e.showProgress,expression:"showProgress"}],attrs:{percentage:e.progress,"stroke-width":15,"text-inside":!0}})],1),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"small"},on:{click:function(t){e.fileDialogVisible=!1}}},[e._v("取 消")])],1)],1)],1)},staticRenderFns:[]};var f=i("VU/8")(d,u,!1,function(e){i("x5cT")},"data-v-6b420d0e",null);t.default=f.exports},x5cT:function(e,t){}});