<template>
  <div class="ygt-wrapper">
    <HeaderBanner />
    <ManagementModules @module-click="handleModuleClick" />
  </div>
</template>

<script>
import HeaderBanner from './components/HeaderBanner.vue'
import ManagementModules from './components/ManagementModules.vue'

export default {
  name: "dashboardYgt",
  components: {
    HeaderBanner,
    ManagementModules
  },
  methods: {
    handleModuleClick(module) {
      console.log('父组件接收到模块点击事件:', module.name);
      // 这里可以处理模块点击逻辑，比如路由跳转
      // this.$router.push(module.route);
    }
  }
};
</script>

<style scoped lang="scss">
.ygt-wrapper {
  padding: 16px;
  background: #F2F6F9;
  overflow-y: auto;
}
</style>