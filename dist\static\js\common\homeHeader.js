/* eslint-disable */

//token
var access_token=Cookies.get("access_token") || Cookies.get("token");

// 跳转链接域名
var zhyHomeHeaderLinkDomain = "https://dev.china9.cn/#/";
var zhyHomeHeaderApiDomain="https://apidev.china9.cn/";
var zhyImageDomain="https://zcloud.obs.cn-north-4.myhuaweicloud.com/";
var cookieDomain = "china9.cn";
if ("undefined" != typeof environment){
  if (environment==="pro"){
    zhyHomeHeaderLinkDomain="https://www.china9.cn/#/";
    zhyHomeHeaderApiDomain="https://api.china9.cn/";
    cookieDomain = "china9.cn";
  }else if(environment === 'local'){
    zhyHomeHeaderLinkDomain="http://"+window.location.host+"/#/";
    cookieDomain = document.location.hostname;
  }else {
    zhyFooterLinkDomain="https://dev.china9.cn/#/";
    zhyFooterApiDomain="https://apidev.china9.cn/";
    cookieDomain = "china9.cn";
  }
}else {
  zhyFooterLinkDomain="https://dev.china9.cn/#/";
  zhyFooterApiDomain="https://apidev.china9.cn/";
  cookieDomain = "china9.cn";
}

// 导航信息
var routerTable = [
  {
    "title": "云产品",
    "children": [
      {
        "title": "人资圈",
        "children": []
      },
      {
        "title": "易管通",
        "children": []
      },
      {
        "title": "建站通",
        "children": []
      },
      {
        "title": "IDC管理",
        "children": []
      },
      {
        "title": "云经理",
        "children": []
      }
    ]
  },
  {
    "title": "解决方案",
    "url": "https://www.china9.cn/#/home/<USER>",
  },
  {
    "title": "支持与服务",
    "url": "https://www.china9.cn/#/home/<USER>"
  },
  {
    "title": "合作伙伴与生态",
    "url": "https://www.china9.cn/#/home/<USER>"
  },
  {
    "title": "申请代理",
    "url": "https://www.china9.cn/#/landingPage/applyAgent"
  },
  {
    "title": "申请体验",
    "url": "https://www.china9.cn/#/landingPage/applyExperience"
  },
  {
    "title": "开放平台",
    "url": "https://www.china9.cn/#/openPlatformHome/home"
  }
];

var homeHeaderLoadNum = 0;

var version = '202503270932';

function getCssPromise(href, idName) {
  return new Promise(resolve => {
    $.get(href, function (res) {
      var style = document.createElement('style');
      style.id = idName;
      style.innerHTML = res;
      if(idName === 'elementCss') {
        // 获取head中的第一个元素
        var firstElement = document.head.firstChild;
        // 将新的style元素插入到第一个元素之前
        document.head.insertBefore(style, firstElement);
      }else{
        document.head.appendChild(style);
      }
      resolve({code: 200, id: idName, content: style})
    })
  })
}

//  先加载样式，防止先出现没有样式的内容
if (!document.getElementById("ziHaiHomeHeaderCloud")){
  var elementCssUrl = 'https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/index.css?v=' + version;
  var commonCssUrl = 'https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/ziHaiHomeHeaderCloud.css?v=' + version;
  if (environment === "local") {
    commonCssUrl = '/static/js/common/ziHaiHomeHeaderCloud.css';
    elementCssUrl = '/static/js/common/index.css';
  }
  var cssList = [
    getCssPromise(commonCssUrl, 'ziHaiHomeHeaderCloud'),
    getCssPromise(elementCssUrl, 'elementCss')
  ];
  Promise.all(cssList).then((res) => {
    var success = res.filter(item => item.code === 200);
    if (success.length === 2) {
      checkHasConsoleEle();
    }
  });
}

function checkHasConsoleEle() {
  setTimeout(function (){
    if (homeHeaderLoadNum < 2) {
      if (document.getElementById("zhyHomeHeader")){
        addHomeHeader(false,'','');
        getHomeHeaderData();
        $("#zhyHomeHeader .el-menu.el-menu--horizontal").on("click",function (e) {
          if (e.target.tagName==="A"){
            $(e.target).parent("li").addClass("is-active").siblings("li").removeClass("is-active");
          }
        })
      }else{
        homeHeaderLoadNum++;
        checkHasConsoleEle();
      }
    }
  },500)
}

if (window.location.href.includes('logout')){
  homeLogout()
}

// 获取头部信息
function getHomeHeaderData() {
  $.get(zhyHomeHeaderApiDomain+"api/headerCommon", {access_token, enterpriseSide: 'pc'}, res => {
    if (res.code === 200) {
      routerTable = res.data;
      getHomeUsesrInfo();
    }else {
      getHomeUsesrInfo();
    }
  })
}

//  资海云头部
function addHomeHeader(hasData,avatar,phone) {
  if (!avatar){
    avatar=zhyImageDomain+"avatar_bitmap.png";
  }
  if (!phone){
    phone="";
  }
  let str1 = '<div style="width: 100%" class="zhyHomeHeaderPc">' +
      '<div class="zhyHomeHeader-title">' +
    '<a href="'+zhyHomeHeaderLinkDomain+'home">' +
    '        <img src="data:image/png;base64,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">' +
    '</a>' +
    '    </div>' +
    '    <ul role="menubar" class="el-menu el-menu--horizontal el-menu" style="display: flex">';
  // let url=window.location.href
  routerTable.forEach(function (value, index) {
    if (!value.url) {
      value.url = "javascript:;";
    }
    if (value.url === window.location.href && value.title !== '云产品'){
      str1 += '<li role="menuitem" tabindex="' + index + '" class="el-menu-item is-active">';
    }else{
      if (index === 0) {
        str1 += '<li role="menuitem" tabindex="' + index + '" class="el-menu-item is-active">';
      } else {
        str1 += '<li role="menuitem" tabindex="' + index + '" class="el-menu-item">';
      }
    }
    if (value.title === '申请代理'){
      str1+='<a href="' + value.url + '">' + value.title + '<i class="iconNew">hot</i>';
    }else if(value.title === '申请体验'){
      str1+='<a href="' + value.url + '">' + value.title + '<i class="iconNew">new</i>';
    }else{
      str1+='<a href="' + value.url + '">' + value.title;
    }

    if(value.children){
      str1+='<i class="iconfont">&gt;</i>'
    }
    str1 += '</a>'
    // 二级菜单
    if (value.children) {
      // 查找是否有孙子菜单
      let hasChildren = value.children.some(item => item.children && item.children.length > 0);
      window.console.log(hasChildren, 'hasChildren');
      let noChild = hasChildren ? '' : 'no-child'
      let noChildStyle = hasChildren ? '' : 'display: block;'
      str1 += '<div role="tooltip" class="el-popover el-popper" tabindex="0" style="transform-origin: center top;z-index: 2015;">' +
        '<div style="position:absolute;left: 0;top: 0;width: 100%;height: 100%;background: #fff;z-index: 2016;"></div>' +
        '  <ul class="dropdown_menu '+ noChild +'" style="position:relative;z-index: 2017;'+noChildStyle+'">';
      value.children.forEach(function (v, i) {
        str1 += '<li>' +
          '<ul>' +
          ' <li><p></p>';
        if(v.url){
          str1+='<a class="drop_menu" href="' + v.url + '" target="_blank">' + v.title + '</a>';
        }else{
          str1 +='<span class="drop_menu">' + v.title + '</span></li>';
        }
        if (v.children) {
          v.children.forEach(function (item) {
            if (!item.url) {
              item.url = "javascript:;";
            }
            str1 += '<li><a href="' + item.url + '" target="_blank">' + item.title + '</a></li>';
          })
        }
        str1 += '</ul>' +
          '</li>';
      })
      str1 += '<div style="clear: both;"></div>' +
        '  </ul>' +
        '</div>'
    }
    str1 += '</li>'
  })
  let helpDoc=zhyHomeHeaderLinkDomain+'home/document';
  let console=hasData?zhyHomeHeaderLinkDomain+'console/display/console':'https://account.china9.cn/login.html?oauth_callback=' + encodeURIComponent(window.location.href);
  str1 += '</ul>' +
    '    <div class="icon">' +
    '        <span>' +
    '            <svg style="width: 21px;margin-right: 4px;" xmlns="http://www.w3.org/2000/svg" class="icon" width="200px" height="200.00px" viewBox="0 0 1024 1024" version="1.1"><path fill="#2c3e50" d="M469.5 463h-150c-16.6 0-30-13.4-30-30s13.4-30 30-30h150c16.6 0 30 13.4 30 30s-13.4 30-30 30zM700 583H320c-16.6 0-30-13.4-30-30s13.4-30 30-30h380c16.6 0 30 13.4 30 30s-13.4 30-30 30zM700 703H320c-16.6 0-30-13.4-30-30s13.4-30 30-30h380c16.6 0 30 13.4 30 30s-13.4 30-30 30z"/><path fill="#2c3e50" d="M536.1 173l246.1 246.1c5.6 5.6 8.8 13.3 8.8 21.2V823c0 16.6-13.4 30-30 30H261c-16.6 0-30-13.4-30-30V203c0-16.6 13.4-30 30-30h275.1m24.9-60H231c-33.1 0-60 26.9-60 60v680c0 33.1 26.9 60 60 60h560c33.1 0 60-26.9 60-60V403L561 113z"/><path fill="#2c3e50" d="M621 257.9l85.1 85.1H621v-85.1M561 113v260c0 16.6 13.4 30 30 30h260L561 113z"/></svg>' +
    '            <p><a href="'+helpDoc+'">帮助文档</a></p>' +
    '        </span>' +
    '        <span>' +
    '            <svg style="width: 18px;margin-right: 6px;" xmlns="http://www.w3.org/2000/svg" class="icon" width="200px" height="194.12px" viewBox="0 0 1055 1024" version="1.1"><path fill="#2c3e50" d="M93.090909 0h868.848485a93.090909 93.090909 0 0 1 93.090909 93.090909v620.606061a93.090909 93.090909 0 0 1-93.090909 93.090909H93.090909a93.090909 93.090909 0 0 1-93.090909-93.090909V93.090909a93.090909 93.090909 0 0 1 93.090909-93.090909z m0 62.060606a31.030303 31.030303 0 0 0-31.030303 31.030303v620.606061a31.030303 31.030303 0 0 0 31.030303 31.030303h868.848485a31.030303 31.030303 0 0 0 31.030303-31.030303V93.090909a31.030303 31.030303 0 0 0-31.030303-31.030303H93.090909zM186.181818 279.272727m31.030303 0l620.606061 0q31.030303 0 31.030303 31.030303l0 0q0 31.030303-31.030303 31.030303l-620.606061 0q-31.030303 0-31.030303-31.030303l0 0q0-31.030303 31.030303-31.030303ZM186.181818 496.484848m31.030303 0l620.606061 0q31.030303 0 31.030303 31.030304l0 0q0 31.030303-31.030303 31.030303l-620.606061 0q-31.030303 0-31.030303-31.030303l0 0q0-31.030303 31.030303-31.030304ZM310.30303 837.818182m31.030303 0l372.363637 0q31.030303 0 31.030303 31.030303l0 0q0 31.030303-31.030303 31.030303l-372.363637 0q-31.030303 0-31.030303-31.030303l0 0q0-31.030303 31.030303-31.030303ZM124.121212 961.939394m31.030303 0l744.727273 0q31.030303 0 31.030303 31.030303l0 0q0 31.030303-31.030303 31.030303l-744.727273 0q-31.030303 0-31.030303-31.030303l0 0q0-31.030303 31.030303-31.030303ZM310.30303 403.393939m0-31.030303l0-124.121212q0-31.030303 31.030303-31.030303l0 0q31.030303 0 31.030303 31.030303l0 124.121212q0 31.030303-31.030303 31.030303l0 0q-31.030303 0-31.030303-31.030303ZM682.666667 620.606061m0-31.030303l0-124.121213q0-31.030303 31.030303-31.030303l0 0q31.030303 0 31.030303 31.030303l0 124.121213q0 31.030303-31.030303 31.030303l0 0q-31.030303 0-31.030303-31.030303Z"/></svg>' +
    '            <p><a href="'+console+'" target="_parent">控制台</a></p>' +
    '        </span>' +
    '    </div>';
  if (!hasData){
    str1 += '<button type="button" class="el-button login el-button--text" onclick="homeLogin()"><span>登录</span></button>' +
      '<button type="button" class="el-button regist el-button--danger" onclick="homeRegister()"><span>立即注册</span></button>';
  }else{
    str1 += '<div class="avatar-container right-menu-item hover-effect el-dropdown" style="position: relative;">' +
      '     <a href="'+zhyHomeHeaderLinkDomain+'personal" style="display:block">'+
      '        <div class="avatar-wrapper el-dropdown-selfdefine" aria-haspopup="list" aria-controls="dropdown-menu-3967" role="button" tabindex="0" style="display: flex;align-items: center">' +
      '            <div class="el-image">' +
      '                <img src="' + avatar + '" class="el-image__inner" style="object-fit: cover;">' +
      '            </div>' +
      '            <div class="zhyHomeHeaderPhone" style="margin-right: 20px">'+phone+'</div>' +
      '        </div>' +
      '        <ul class="el-dropdown-menu el-popper" id="dropdown-menu-3967">' +
      '            <li tabindex="-1" class="el-dropdown-menu__item">' +
      '                <a href="' + zhyHomeHeaderLinkDomain + 'personal" class="" style="color: inherit">' +
      '                    个人资料' +
      '                </a>' +
      '            </li>' +
      '            </a>' +
      '            <li tabindex="-1" class="el-dropdown-menu__item el-dropdown-menu__item--divided" onclick="homeLogout()">' +
      '                <span style="display: block;">退出登录</span>' +
      '            </li>' +
      '        </ul>' +
      '      </a>'+
      '    </div>';
  }
  str1+='<div tabindex="-1" role="dialog" aria-modal="true" id="overdue-alert" aria-label="标题名称" class="el-message-box__wrapper" style="z-index: 9999999999;display: none;background: rgba(0,0,0,0.3);">' +
    '     <div class="el-message-box">' +
    '         <div class="el-message-box__header">' +
    '              <div class="el-message-box__title">' +
    '                   <span>会话过期</span>' +
    '              </div>' +
    '         </div>' +
    '         <div class="el-message-box__content">' +
    '             <div class="el-message-box__container">' +
    '                 <div class="el-message-box__message">' +
    '                     <p>您已登录超时，请重新登录</p>' +
    '                 </div>' +
    '             </div>' +
    '         <div class="el-message-box__input" style="display: none;">' +
    '             <div class="el-input">' +
    '                     <input type="text" autocomplete="off" placeholder="" class="el-input__inner">' +
    '                 </div>' +
    '                 <div class="el-message-box__errormsg" style="visibility: hidden;"></div>' +
    '             </div>' +
    '         </div>' +
    '<div class="el-message-box__btns">' +
    '     <button type="button" class="el-button el-button--default el-button--small el-button--danger" onclick="homeLogout()">' +
    '       <span>\n' +
    '          确定\n' +
    '        </span>' +
    '     </button>' +
    '</div>' +
    '</div>' +
    '</div>' +
    '</div>';

  str1+='<div class="zhyHomeHeaderPhone" style="display: none">' +
    '        <div class="zhyHomeHeader-logo-wrap"><img class="zhyHomeHeader-logo" src="data:image/png;base64,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">' +
    '<span>企业一站式SaaS云平台</span>' +
    '</div>';
  if (!hasData){
    str1 += '<div class="zhyHomeHeaderPhone-button-wrap">' +
      '<button type="button" class="el-button login el-button--text" onclick="homeLogin()"><span>登录</span></button>' +
        '<button type="button" class="el-button regist el-button--danger" onclick="homeRegister()"><span>立即注册</span></button>';
  }else{
    str1 += '<div style="display: flex;align-items: center">'
    str1 += '<div class="avatar-container right-menu-item hover-effect el-dropdown" style="position: relative;">' +
        '     <a href="'+zhyHomeHeaderLinkDomain+'personal" style="display:block">'+
        '        <div class="avatar-wrapper el-dropdown-selfdefine" aria-haspopup="list" aria-controls="dropdown-menu-3967" role="button" tabindex="0" style="display: flex;align-items: center">' +
        '            <div class="el-image">' +
        '                <img src="' + avatar + '" class="el-image__inner" style="object-fit: cover;">' +
        '            </div>' +
        '            <div class="zhyHomeHeaderPhone">'+phone+'</div>' +
        '        </div>' +
        '        <ul class="el-dropdown-menu el-popper" id="dropdown-menu-3967">' +
        '            <li tabindex="-1" class="el-dropdown-menu__item">' +
        '                <a href="' + zhyHomeHeaderLinkDomain + 'personal" class="" style="color: inherit">' +
        '                    个人资料' +
        '                </a>' +
        '            </li>' +
        '            </a>' +
        '            <li tabindex="-1" class="el-dropdown-menu__item el-dropdown-menu__item--divided" onclick="homeLogout()">' +
        '                <span style="display: block;">退出登录</span>' +
        '            </li>' +
        '        </ul>' +
        '      </a>'+
        '    </div>';
  }
        str1 += '<div class="zhyHomeHeader-menu" onclick="showMenu(event)">' +
          '<span></span>' +
          '<span></span>' +
          '<span></span>' +
        '</div>';
  str1+='</div>'
      str1 += '</div>' +
      '</div>'+
      '<div class="menu-wrap zhyHomeHeaderPhone" style="display: none">' +
      '<ul class="el-menu-vertical-demo el-menu">'
  routerTable.forEach(function (value, index) {
    if (zhyHomeHeaderLinkDomain.includes(document.location.host)){
      str1 += '<li role="menuitem" tabindex="' + index + '" class="el-menu-item ' + (value.url === window.location.href ? 'is-active' : '') + '">';
    }else{
      str1 += '<li role="menuitem" tabindex="' + index + '" class="el-menu-item ' + (index === 1 ? 'is-active' : '') + '">';
    }
    str1 += '<div>'
    if (!value.url) {
      value.url = "javascript:;";
    }
    str1+='<a href="' + value.url + '">' + value.title;
    str1 += '</a>'
    if(value.children){
      str1+='<i class="iconfont" onclick="showSubMenu(event)">&gt;</i>'
    }
    str1 += '</div>'
    // 二级菜单
    if (value.children) {
      str1 += '<ul class="dropdown_menu" style="display: none">';
      value.children.forEach(function (v, i) {
        str1+='<li class="el-menu-item"><div><a>' + v.title + '</a>'
        if(v.children){
          str1+='<i class="iconfont" onclick="showSubMenu(event)">&gt;</i>'
        }
        str1+='</div>';
        if (v.children) {
          str1 += '<ul class="dropdown_menu" style="display: none">'
          v.children.forEach(function (item) {
            if (!item.url) {
              item.url = "javascript:;";
            }
            str1 += '<li class="el-menu-item"><a href="' + item.url + '" target="_blank">' + item.title + '</a></li>';
          })
          str1 += '</ul>'
        }
        str1+='</li>'
      })
      str1 += '<div style="clear: both;"></div>' +
          '  </ul>'
    }
    str1 += '</li>'
  })
  str1+='<li class="el-menu-item"><div><a href="'+helpDoc+'">帮助文档</a></div></li>'
  str1+='<li class="el-menu-item"><div><a href="'+console+'" target="_parent">控制台</a></div></li>'
    str1+='</ul>' +
        '</div>';

  $("#zhyHomeHeader").html(str1);

  $("#zhyHomeHeader .el-menu.el-menu--horizontal .el-menu-item").each(function (i, v){
    if($(v).find("a").attr("href") === window.location.href){
      $("#zhyHomeHeader .el-menu.el-menu--horizontal .el-menu-item").eq(0).removeClass("is-active")
    }
  })
}

// 移动端展示二级导航
function showSubMenu(e){
  $(e.target).toggleClass('show').parent().siblings().fadeToggle()
}
// 移动端展示导航
function showMenu(e){
  $(e.target).toggleClass('open').parent().parent().siblings('.menu-wrap').fadeToggle()
}

// 监听hash路由，解决路由变化后返回加不上头部的问题
window.addEventListener("hashchange", function () {
  // 操作
  getHomeHeaderData();
})

// 获取个人和公司信息
function getHomeUsesrInfo() {
  $.post(zhyHomeHeaderApiDomain+"api/user/info", {access_token,type:'pc', enterpriseSide: 'pc'}, res => {
    if (res.code === 200) {
      loginFlag = true;
      if (res.data && Object.prototype.toString.call(res.data) === "[object Object]") {
        let avatar = res.data.avatar,
          phone = res.data.phone||"";

        phone = phone.toString().substr(0, 3) + '****' + phone.slice(-4);

        addHomeHeader(true,avatar,phone);
      }
    }else{
      loginFlag = false;
      addHomeHeader(false,'','');
    }
  })
}

function clearAllCookie() {
  var keys = document.cookie.match(/[^ =;]+(?=\=)/g);
  if (keys) {
    for (var i = keys.length; i--; )
      document.cookie = keys[i] + "=0;expires=" + new Date(0).toUTCString();
  }
}

// 退出登录
function homeLogout() {
  $.post(zhyHomeHeaderApiDomain+"api/logout", {access_token}, function () {

    Cookies.remove('access_token', {path: '/', domain: cookieDomain})
    Cookies.remove('token', {path: '/', domain: cookieDomain})
    Cookies.remove('gcc', {path: '/', domain: cookieDomain})
    Cookies.remove('site_id', {path: '/', domain: cookieDomain})
    Cookies.remove('china_site_id', {path: '/', domain: cookieDomain})
    Cookies.remove('jzt_20_site_id', {path: '/', domain: cookieDomain})
    Cookies.remove('company_unique_id', {path: '/', domain: cookieDomain})

    clearAllCookie();
    sessionStorage.clear();
    localStorage.clear();

    try{
      $.post('https://zhjzt.china9.cn/newclient/Login/newoutLogin').done(function (){
        let url = zhyHomeHeaderLinkDomain
        if (window.location.href.includes("logout")) {
          url = zhyHomeHeaderLinkDomain + "home";
        }
        window.location.href = url;
      })
    }catch (e){
      console.log(e)
    }finally {
      window.location.href="https://china9.cn/#/home"
    }
  })
}

// 登录
function homeLogin(){
  window.location.href = 'https://account.china9.cn/login.html?oauth_callback=' + encodeURIComponent(window.location.href)
}

function homeRegister(){
  window.location.href = 'https://account.china9.cn//register.html?oauth_callback=' + encodeURIComponent(window.location.href)
}

// 过期清cookies
var minitesHome=60;
var timeHome = new Date(new Date().getTime() + minitesHome  * 1000 * 60);

var loginFlag = false;
window.addEventListener('click',function (){
  if (!Cookies.get('token') || !Cookies.get('access_token')) {
    if (loginFlag){
      return $("#overdue-alert").show();
    }else{
      return false
    }
  }
  timeHome = new Date(new Date().getTime() + minitesHome  * 1000 * 60);
  Cookies.set('access_token',Cookies.get('access_token'), {path: '/', domain: cookieDomain,expires:timeHome})
  Cookies.set('token',Cookies.get('token'), {path: '/', domain: cookieDomain,expires:timeHome})
  Cookies.set('gcc',Cookies.get('gcc'), {path: '/', domain: cookieDomain,expires:timeHome})
})

// 百度统计
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?0dbb4f63a579d2d7a009fa1df830affd";
  var s = document.getElementsByTagName("script")[0];
  s.parentNode.insertBefore(hm, s);
})();
