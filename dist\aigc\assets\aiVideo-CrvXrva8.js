import{a as n,s as l}from"./aiVideo-B86MWzMI.js";import{A as i}from"./index-BBeD0eDz.js";function f(){const a=i(!1),t=async o=>new Promise(async(s,c)=>{a.value=!0;try{const e=await l(o);s(e)}catch(e){console.log("🚀 ~ saveAiVideoParams ~ error: ",e)}finally{a.value=!1}}),r=i(!1);return{saveLoading:a,saveAiVideoParams:t,createLoading:r,createAiVideo:async o=>new Promise(async(s,c)=>{r.value=!0;try{const e=await n(o);s(e)}catch(e){console.log("🚀 ~ createAiVideo ~ error: ",e)}finally{r.value=!1}})}}export{f as u};
