import{k as K,l as w,A as v,c as h,b as i,m as M,a as c,t as V,w as d,h as W,F,s as U,o as x,j as A,Z as z,K as J,p as _,$ as O,a0 as P,a1 as Q,g as Z,n as q,i as G,a2 as y,Q as f,y as H,a3 as X,a4 as Y,_ as ee}from"./index-BBeD0eDz.js";/* empty css               *//* empty css                 */import{c as te}from"./common-DBXWCL9C.js";import{u as ae,d as ne}from"./aiDocument-BqsEIq7y.js";import{u as oe}from"./aiVideo-CrvXrva8.js";const se={class:"generated-content h-full flex flex-col justify-between bg-secondary"},ce={class:"content-header flex items-center justify-between"},le={class:"checkbox-all"},ie={class:"flex items-center"},de={class:"selection-tip mr-[14px]"},re={class:"content-list flex-1 overflow-y-auto"},ue={class:"item-header flex items-center justify-between"},fe={class:"item-actions"},pe=["contenteditable","onBlur","onKeydown"],ge=K({__name:"Writing",props:{data:{type:Array,default:()=>[]},maxSelectCount:{type:Number,default:1}},emits:["edit","delete","update:data"],setup(k,{emit:E}){const{saveLoading:B,saveAiVideoParams:S,createLoading:$,createAiVideo:L}=oe(),N=H(),p=k,I=E,r=w({get(){return p.data},set(e){I("update:data",e)}});v(!1),v(!1);const R=e=>{console.log("handleItemChecked",e),r.value.filter(a=>a.checked).length>p.maxSelectCount&&(e.checked=!1,f({message:`最多只能选择${p.maxSelectCount}条文案`,type:"warning"}))},C=w(()=>r.value.filter(e=>e.checked)),g=v({}),m=v(!1),T=e=>{r.value.forEach(t=>{t!==e&&(t.edit=!1)}),e.edit=!0,X(()=>{const t=r.value.findIndex(a=>a===e);if(t!==-1&&g.value[t]){const a=g.value[t];a.focus();const n=document.createRange(),o=window.getSelection();n.selectNodeContents(a),n.collapse(!1),o==null||o.removeAllRanges(),o==null||o.addRange(n)}})},b=async(e,t)=>{var o;if(!e.edit)return;const a=((o=g.value[t])==null?void 0:o.textContent)||e.content,n=e.content;if(e.edit=!1,g.value[t]=null,a!==n){if(m.value)return;m.value=!0;const s=y.service({lock:!0,text:"正在保存...",background:"rgba(0, 0, 0, 0.7)"});try{await ae({id:e.id,content:a}),e.content=a,f({message:"保存成功",type:"success"})}catch(u){console.error("保存失败:",u),f({message:"保存失败，请重试",type:"error"}),e.content=n}finally{m.value=!1,s.close()}}else e.content=a},j=e=>{Y.confirm("确定要删除该文案吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{let t=y.service({lock:!0,text:"正在删除...",background:"rgba(255, 255 255, 0.7)"});try{await ne({id:e.id}),f({message:"删除成功",type:"success"}),r.value=r.value.filter(a=>a.id!=e.id)}catch(a){console.error("删除失败:",a)}finally{t.close()}}).catch(()=>{})},D=async()=>{const e=C.value.length;if(e===0){f({message:"请至少选择一条文案",type:"warning"});return}if(e>p.maxSelectCount){f({message:`所选文案不能超过${p.maxSelectCount}条`,type:"warning"});return}let t=C.value.map(o=>o.content);if($.value||B.value)return;let a=y.service({lock:!0,text:"文案解析中...",background:"rgba(255, 255 255, 0.7)"});const n=await S({theme:JSON.stringify(t),iswenan:2});n&&n.id&&(await L({id:n.id}),a.close(),N.push("/smart-material/ai-preview/"+n.id))};return(e,t)=>{const a=z,n=W,o=J;return x(),h("div",se,[i("div",ce,[i("div",le,[M("",!0)]),i("div",ie,[i("span",de,"* 选择文案生成视频，所选文案条数不可超过"+V(k.maxSelectCount)+"条",1),c(n,{type:"primary",onClick:D},{default:d(()=>t[2]||(t[2]=[A("生成视频")])),_:1})])]),i("div",re,[(x(!0),h(F,null,U(r.value,(s,u)=>(x(),h("div",{class:"content-item",key:u},[i("div",ue,[c(a,{modelValue:s.checked,"onUpdate:modelValue":l=>s.checked=l,onChange:l=>R(s)},{default:d(()=>t[3]||(t[3]=[A("选择")])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),i("div",fe,[c(n,{text:"",class:"copy-icon !px-[0px]",onClick:l=>_(te)(s.content)},{default:d(()=>[c(o,null,{default:d(()=>[c(_(O))]),_:1})]),_:2},1032,["onClick"]),c(n,{text:"",class:"copy-icon !px-[0px]",onClick:l=>T(s),disabled:m.value},{default:d(()=>[c(o,null,{default:d(()=>[c(_(P))]),_:1})]),_:2},1032,["onClick","disabled"]),c(n,{text:"",class:"copy-icon !px-[0px]",onClick:l=>j(s)},{default:d(()=>[c(o,null,{default:d(()=>[c(_(Q))]),_:1})]),_:2},1032,["onClick"])])]),i("div",{class:q(["item-content",{editing:s.edit}]),contenteditable:s.edit,ref_for:!0,ref:l=>{s.edit&&(g.value[u]=l)},onBlur:l=>b(s,u),onKeydown:Z(G(l=>b(s,u),["prevent"]),["enter"])},V(s.content),43,pe)]))),128))])])}}}),ke=ee(ge,[["__scopeId","data-v-9474b4fc"]]);export{ke as W};
