var indexApp = new Vue({
  el: '#index-app',
  data() {
    return {
      formName: 'ymp',
      unique_id: '',
      cateName: '',
      cateList: ['小微企业', '中小企业', '大型企业', '上市企业'],
      industryName: '',
      industryList: ['互联网科技', '教育培训', '快递物流', '批发零售', '商务服务', '其他行业'],
      customer1: false,
      customer2: false,
      customer3: false,
      customer4: false,
      customer5: false,
      showForm: false,
      form: {},
      check: false,
      isPost: false,
      // typeObj: { 'ymp': '云名片', 'znyz': '智能印章', 'qywzjs': '全域网站建设', 'spgj': '视频管家', 'zgkq': '智感考勤' },
      typeObj: {
        'ymp': {
          name: '云名片',
          desc: '助力企业低成本获客'
        },
        'znyz': {
          name: '智能印章',
          desc: '智慧印章管理 给传统印章加上了一把“智慧锁”让印章使用更安全'
        },
        'qywzjs': {
          name: '全域网站建设',
          desc: '全域网站建设、营销、推广管理平台'
        },
        'spgj': {
          name: '视频管家',
          desc: '为企业用户搭建起可靠、可控的视频监控体系与安全保障能力'
        },
        'zgkq': {
          name: '智感考勤',
          desc: '开启智能考勤新时代'
        }
      }
    }
  },
  watch: {
    cateName(v) {
      var that = this
      if (v) {
        that.scrollToBottom()
        setTimeout(function () {
          that.customer4 = true
          that.scrollToBottom()
        }, 1000)
      }
    },
    industryName(v) {
      var that = this
      if (v) {
        that.scrollToBottom()
        setTimeout(function () {
          that.customer5 = true
          that.scrollToBottom()
          setTimeout(function () {
            that.showForm = true
          }, 3000)
        }, 1000)
      }
    }
  },
  mounted() {
    var that = this
    var type = getUrlParam('type'), unique_id = getUrlParam('unique_id')
    if (type) that.formName = type
    if (unique_id) that.unique_id = unique_id
    document.title = '资海云' + that.typeObj[that.formName].name
    that.shareInit()
    setTimeout(function () {
      that.customer1 = true
      setTimeout(function () {
        that.customer2 = true
        setTimeout(function () {
          that.customer3 = true
        }, 1000)
      }, 2000)
    }, 1000)
  },
  methods: {
    cateClick(item) {
      if (!this.cateName) this.cateName = item
    },
    industryClick(item) {
      if (!this.industryName) this.industryName = item
    },
    // 滚动到底部
    scrollToBottom() {
      // this.$nextTick 将回调延迟到下次DOM更新循环之后执行。在修改数据之后立即使用它，然后等待DOM更新
      this.$nextTick(() => {
        // dom 元素更新后执行滚动条到底部 否则不生效
        let scrollElem = this.$refs.chatBox;
        // console.log('scrollHeight: ', scrollElem.scrollHeight);
        scrollElem.scrollTo({
          top: scrollElem.scrollHeight,
          behavior: 'instant'
        });
      });
    },
    // 初始化分享
    shareInit() {
      var that = this
      var imgUrl = 'http://www.china9.cn/h5Promotion/images/' + that.formName + '-logo.jpg';
      var lineLink = window.location.href;
      var shareTitle = '资海云' + that.typeObj[that.formName].name;
      var descContent = that.typeObj[that.formName].desc;
      var linkMy = encodeURIComponent(location.href.split('#')[0])
      // lineLink = linkMy
      $.post('https://api.china9.cn/api/wechat_Share', {
        url: lineLink
      }, function (response, status) {
        if (status == 'success') {
          if (response.code == 200) {
            console.log(response.data)
            var data = response.data
            var appId = data.appId;
            var timestamp = data.timestamp;
            var nonceStr = data.nonceStr;
            var signature = data.signature;
            var lineLink = data.url;
            wx.config({
              debug: false,
              appId: appId,
              timestamp: timestamp,
              nonceStr: nonceStr,
              signature: signature,
              jsApiList: ['checkJsApi', 'onMenuShareTimeline', 'onMenuShareAppMessage']
            });
            wx.ready(function () {
              console.log(imgUrl)
              wx.onMenuShareTimeline({
                title: shareTitle, // 分享标题
                link: lineLink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                imgUrl: imgUrl//, // 分享图标
                // success: function() {
                // 	// 用户确认分享后执行的回调函数
                // },
                // cancel: function() {
                // 	// 用户取消分享后执行的回调函数
                // }
              }),
              wx.onMenuShareAppMessage({
                title: shareTitle, // 分享标题
                desc: descContent, // 分享描述
                link: lineLink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                imgUrl: imgUrl, // 分享图标
                type: '', // 分享类型,music、video或link，不填默认为link
                dataUrl: ''//, // 如果type是music或video，则要提供数据链接，默认为空
                , success: function () {
                  // 用户确认分享后执行的回调函数
                  // alert('分享成功')
                },
                cancel: function () {
                  // 用户取消分享后执行的回调函数
                  // alert('分享取消')
                }
              })
            });
          }
        }
      })
    },
    // 立即获取
    toSubmit() {
      var that = this, params = JSON.parse(JSON.stringify(that.form))
      var phoneReg = /^1[3456789]\d{9}$/, regName = /^[\u0391-\uFFE5]+$/
      if (that.isPost) {
        return alert('请稍等，正在提交')
      } else if (!params.name) {
        return alert('请输入姓名')
      } else if (!params.phone) {
        return alert('请输入手机号')
      } else if (!phoneReg.test(params.phone)) {
        return alert('请输入正确的手机号')
      } else if (!params.company) {
        return alert('请输入公司名称')
      } else if (!that.check) {
        return alert('请同意隐私协议')
      }
      // 提交
      that.isPost = true
      $.post('https://card-api.china9.cn/api/user/leave_message', {
        type: 7,
        product: that.typeObj[that.formName].name,
        user: params.name,
        name: params.company,
        telephone: params.phone,
        cate: that.cateName,
        industry: that.industryName,
        unique_id: that.unique_id
      }, function (res, status) {
        that.isPost = false
        // console.log(res);
        if (status == 'success') {
          if (res.code == 200) {
            alert(res.data.message);
            that.form = {}
            that.check = false
            that.showForm = false
          } else {
            alert('提交失败，稍后再试');
          }
        } else {
          alert('提交失败，稍后再试')
        }
      })
    }
  }
})

//获取地址栏参数//可以是中文参数
function getUrlParam(key) {
  // 获取参数
  var url = window.location.search;
  // 正则筛选地址栏
  var reg = new RegExp("(^|&)" + key + "=([^&]*)(&|$)");
  // 匹配目标参数
  var result = url.substr(1).match(reg);
  //返回参数值
  return result ? decodeURIComponent(result[2]) : null;
}