webpackJsonp([51],{"8SwI":function(t,e){},TCS1:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=a("cMGX"),l=a("wU6q"),n=a("pI5c"),r={name:"amendPwd",components:{payComponent:a("rSnQ").a},props:{dialogVisible:{required:!1,type:Boolean,default:function(){return!1}},no:{required:!0,type:[String,Number],default:function(){return""}}},watch:{no:function(t,e){this.getDetail()}},data:function(){return{tableLoading:!1,btn_loading:!1,infoData:{no:"",type:0},formData:{}}},computed:{showBtns:function(){return!this.formData.order_id}},methods:{getDetail:function(){var t=this;this.tableLoading=!0,Object(n._15)({no:this.no}).then(function(e){t.tableLoading=!1,t.formData=e.data}).catch(function(){t.tableLoading=!1})},cancel:function(){var t=this;this.$confirm("取消该订单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(n._13)({no:t.no}).then(function(e){t.$message.success("订单取消成功"),t.close()})})},destory:function(){var t=this;this.$confirm("取消该订单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(n._13)({no:t.no}).then(function(e){t.$message.success("订单取消成功"),t.close()})})},pay:function(){this.formData&&(this.formData.amount=this.formData.money,this.formData.order_no=this.formData.no,this.$router.push({path:"/pay",query:{no:this.no}}))},close:function(){this.$emit("closepop",!0)}}},i={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{title:"订单管理",visible:t.dialogVisible,width:"600px","before-close":t.close},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticClass:"demo-form-inline",attrs:{inline:!0,model:t.formData,"label-position":"right","label-width":"90px",size:"small"}},[a("div",{staticClass:"container"},[a("el-form-item",{attrs:{label:"产品名称",prop:"title"}},[t._v(t._s(t.formData.title))]),t._v(" "),a("el-form-item",{attrs:{label:"产品配置",prop:"desc"}},[t._v(t._s(t.formData.desc))]),t._v(" "),a("el-form-item",{attrs:{label:"购买方式"}},[t._v("周期时长")]),t._v(" "),a("el-form-item",{attrs:{label:"时长",prop:"month"}},[t._v(t._s(t.formData.month))]),t._v(" "),a("el-form-item",{attrs:{label:"下单时间",prop:"month"}},[t._v(t._s(t.formData.time))]),t._v(" "),a("el-form-item",{class:[{blue:"已付款"===t.formData.status},{orange:"待付款"===t.formData.status},{red:"已退款"===t.formData.status}],attrs:{label:"支付状态",prop:"status"}},[t._v(t._s(t.formData.status))])],1)]),t._v(" "),t.showBtns?["待付款"===t.formData.status?a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.pay()}}},[t._v("去付款")]):t._e(),t._v(" "),"待付款"===t.formData.status?a("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.cancel()}}},[t._v("取消订单")]):t._e(),t._v(" "),"已超时"===t.formData.status?a("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.destory()}}},[t._v("删除订单")]):t._e()]:t._e(),t._v(" "),a("payComponent",{ref:"payComponent"})],2)},staticRenderFns:[]};var s=a("VU/8")(r,i,!1,function(t){a("dW6e")},"data-v-7dc9c130",null).exports,c={name:"order",components:{Pagination:o.a,detailOrder:s,CardWrap:l.a},data:function(){return{tableLoading:!1,tableData:[],total:0,queryList:{page:1,perPage:10},detailOrderVisible:!1,orderData:{}}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.tableLoading=!0,Object(n._16)(this.queryList).then(function(e){t.tableLoading=!1,t.tableData=e.data.data,t.total=e.data.total}).catch(function(){t.tableLoading=!1})},showDetail:function(t){this.$router.push({path:"/console/order/detail",query:{no:t.no}})},closePop:function(t){this.getList(),this.detailOrderVisible=!1}}},u={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("card-wrap",{attrs:{title:"订单管理"}},[a("div",{staticClass:"container-wrapper"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.queryList,size:"small"}},[a("el-form-item",{attrs:{label:"产品名称"}},[a("el-input",{attrs:{placeholder:"产品名称",clearable:""},model:{value:t.queryList.name,callback:function(e){t.$set(t.queryList,"name",e)},expression:"queryList.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"费用类型"}},[a("el-select",{attrs:{clearable:""},model:{value:t.queryList.order_type,callback:function(e){t.$set(t.queryList,"order_type",e)},expression:"queryList.order_type"}},[a("el-option",{attrs:{label:"开通产品",value:1}}),t._v(" "),a("el-option",{attrs:{label:"续费产品",value:2}}),t._v(" "),a("el-option",{attrs:{label:"升级产品",value:3}})],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"下单时间"}},[a("el-date-picker",{attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.queryList.time,callback:function(e){t.$set(t.queryList,"time",e)},expression:"queryList.time"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"支付状态"}},[a("el-select",{attrs:{placeholder:"全部",clearable:""},model:{value:t.queryList.status,callback:function(e){t.$set(t.queryList,"status",e)},expression:"queryList.status"}},[a("el-option",{attrs:{label:"待付款",value:1}}),t._v(" "),a("el-option",{attrs:{label:"已付款",value:2}}),t._v(" "),a("el-option",{attrs:{label:"已取消",value:3}}),t._v(" "),a("el-option",{attrs:{label:"超时取消",value:4}}),t._v(" "),a("el-option",{attrs:{label:"申请退款",value:5}}),t._v(" "),a("el-option",{attrs:{label:"已退款",value:6}})],1)],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v("搜索")])],1)],1),t._v(" "),a("div",{staticClass:"table"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,height:"100%",stripe:""}},[a("el-table-column",{attrs:{prop:"no",label:"订单编号"}}),t._v(" "),a("el-table-column",{attrs:{prop:"type",label:"费用类型"}}),t._v(" "),a("el-table-column",{attrs:{prop:"title",label:"产品名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"desc",label:"产品配置"}}),t._v(" "),a("el-table-column",{attrs:{label:"购买方式"}},[a("span",[t._v("周期购买")])]),t._v(" "),a("el-table-column",{attrs:{prop:"month",label:"购买时长"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.month))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"time",label:"下单时间"}}),t._v(" "),a("el-table-column",{attrs:{prop:"status",label:"支付状态"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(a){t.orderData=e.row,t.detailOrderVisible=!0}}},[t._v("详情")])]}}])})],1)],1),t._v(" "),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticStyle:{"text-align":"center"},attrs:{total:t.total,page:t.queryList.page,limit:t.queryList.perPage},on:{"update:page":function(e){return t.$set(t.queryList,"page",e)},"update:limit":function(e){return t.$set(t.queryList,"perPage",e)},pagination:t.getList}})],1)]),t._v(" "),a("detail-order",{ref:"order",attrs:{dialogVisible:t.detailOrderVisible,no:t.orderData.no},on:{closepop:t.closePop}})],1)},staticRenderFns:[]};var p=a("VU/8")(c,u,!1,function(t){a("8SwI")},"data-v-5d6b472a",null);e.default=p.exports},dW6e:function(t,e){}});