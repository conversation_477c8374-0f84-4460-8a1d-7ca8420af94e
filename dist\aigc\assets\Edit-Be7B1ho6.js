import{k as Et,c as Te,o as he,a as N,a8 as ov,p as Ne,a9 as Ae,w as j,F as $r,s as Gr,f as dt,aa as uv,_ as Rt,l as Yu,V as et,A as q,b as te,m as vn,n as ds,q as Nt,v as kt,j as ut,Z as av,K as ra,ab as lv,h as Hr,a0 as cv,g as ia,i as sa,t as $n,a2 as Kt,Q as St,y as oa,a3 as Ur,a4 as fv,S as ht,ac as ua,I as hv,ad as aa,X as dv,E as la,d as ca,ae as pv,e as ps,a7 as gv,af as vv,B as fa,ag as mv,G as _v,a6 as yv,ah as qu,ai as fs,aj as xv,ak as wv,al as bv,am as Av,an as Cv,ao as Nr,ap as Sv,aq as Ev,u as Rv,H as Iv}from"./index-BBeD0eDz.js";/* empty css               */import{_ as Tv}from"./PageBack-BpcOadQW.js";import{_ as Lv}from"./no-data-CNqxJ4EX.js";/* empty css                 */import"./request-Ciyrqj7N.js";import{u as Mv}from"./aiVideo-CrvXrva8.js";import{u as Ov,e as Pv,a as ha,b as da,f as Ju,s as Dv,h as Wv,c as Fv,d as Bv,g as Nv,i as kv,j as Uv}from"./videoClip-Crxw3Jsi.js";import{A as $v}from"./AIDocument-DJ_OUx-W.js";/* empty css               *//* empty css                *//* empty css                  *//* empty css                     *//* empty css                *//* empty css              */import{E as Gv}from"./empty-CSpEo1eL.js";import{u as Hv,a as zv,E as Vv,F as Qu,M as Zv,B as Yv,Y as qv}from"./FontStyle-COQmCunw.js";/* empty css                    */import{_ as Jv}from"./AddMaterial-C0wyZ-zd.js";/* empty css                     */import"./input-delete-BxT9zbwn.js";import"./aiVideo-B86MWzMI.js";import"./ProductSelector-BTeBhjvS.js";/* empty css                */import"./ProductDrawer-1eJ7VyQy.js";import"./index-B2D9HeJa.js";import"./AddBtn-KywrLb53.js";import"./ImportFromMaterial-lo8a_mzn.js";import"./List-CZfQXqH2.js";import"./video-cover-DBcJ77EJ.js";import"./index-vH7ypFZe.js";const Qv={class:"content h-[38px]"},Kv=Et({__name:"Steps",setup($){const o=Ae("steps",[]),i=Ae("activeStep",0),f=Ae("updateSteps",w=>{}),g=w=>{f(w)};return(w,b)=>{const C=uv,M=ov;return he(),Te("div",Qv,[N(M,{active:Ne(i),"finish-status":"success"},{default:j(()=>[(he(!0),Te($r,null,Gr(Ne(o),(E,L)=>(he(),dt(C,{class:"cursor-pointer",key:L,title:E.title,onClick:R=>g(L)},null,8,["title","onClick"]))),128))]),_:1},8,["active"])])}}}),Xv=Rt(Kv,[["__scopeId","data-v-c0df4c3f"]]),jv={class:"generated-content h-full flex flex-col justify-between bg-secondary"},e0={class:"content-header flex items-center justify-between"},t0={class:"checkbox-all"},n0={class:"content-list flex-1 overflow-y-auto"},r0={class:"item-actions"},i0=["contenteditable","onBlur","onKeydown"],s0=Et({__name:"Writing",props:{data:{type:Array,default:()=>[]},maxSelectCount:{type:Number,default:1}},emits:["edit","delete","update:data","generate"],setup($,{emit:o}){Mv();const i=Ae("id");oa();const f=$,g=o,w=Yu({get(){return f.data},set(O){g("update:data",O)}});et(()=>f.data,O=>{O.length===1&&Ur(()=>{w.value[0].checked=!0})},{immediate:!0}),q(!1),q(!1);const b=O=>{w.value.filter(S=>S.checked).length>f.maxSelectCount&&(O.checked=!1,St({message:`最多只能选择${f.maxSelectCount}条文案`,type:"warning"}))};Yu(()=>w.value.filter(O=>O.checked));const C=q({}),M=q(!1);Ae("updateForm",O=>{});const E=O=>{w.value.forEach(y=>{y!==O&&(y.edit=!1)}),O.edit=!0,Ur(()=>{const y=w.value.findIndex(S=>S===O);if(y!==-1&&C.value[y]){const S=C.value[y];S.focus();const I=document.createRange(),v=window.getSelection();I.selectNodeContents(S),I.collapse(!1),v==null||v.removeAllRanges(),v==null||v.addRange(I)}})},L=async(O,y)=>{var v;if(!O.edit)return;const S=((v=C.value[y])==null?void 0:v.textContent)||O.content,I=O.content;if(O.edit=!1,C.value[y]=null,S!==I){if(M.value)return;M.value=!0;const T=Kt.service({lock:!0,text:"正在保存...",background:"rgba(0, 0, 0, 0.7)"});try{await Ov({id:i.value,copywriting:S}),O.content=S,St({message:"保存成功",type:"success"})}catch(D){console.error("保存失败:",D),St({message:"保存失败，请重试",type:"error"}),O.content=I}finally{M.value=!1,T.close()}}else O.content=S},R=()=>{g("generate")};return(O,y)=>{const S=av,I=ra,v=Hr;return he(),Te("div",jv,[te("div",e0,[te("div",t0,[vn("",!0)])]),te("div",n0,[(he(!0),Te($r,null,Gr(w.value,(T,D)=>(he(),Te("div",{class:"content-item",key:D},[te("div",{class:ds([`item-header flex items-center ${w.value.length>1?"justify-between":"justify-end"}`])},[Nt(N(S,{modelValue:T.checked,"onUpdate:modelValue":B=>T.checked=B,onChange:B=>b(T)},{default:j(()=>y[2]||(y[2]=[ut("选择")])),_:2},1032,["modelValue","onUpdate:modelValue","onChange"]),[[kt,w.value.length>1]]),te("div",r0,[N(v,{text:"",class:"copy-icon !px-[0px]",onClick:R},{default:j(()=>[N(I,null,{default:j(()=>[N(Ne(lv))]),_:1})]),_:1}),N(v,{text:"",class:"copy-icon !px-[0px]",onClick:B=>E(T),disabled:M.value},{default:j(()=>[N(I,null,{default:j(()=>[N(Ne(cv))]),_:1})]),_:2},1032,["onClick","disabled"])])],2),te("div",{class:ds(["item-content",{editing:T.edit}]),contenteditable:T.edit,ref_for:!0,ref:B=>{T.edit&&(C.value[D]=B)},onBlur:B=>L(T,D),onKeydown:ia(sa(B=>L(T,D),["prevent"]),["enter"])},$n(T.content),43,i0)]))),128))])])}}}),o0=Rt(s0,[["__scopeId","data-v-daeb46e6"]]),pa=()=>{const $=q([]),o=q(!1),i=q({page:1,limit:10}),f=q(0),g=async(I={})=>new Promise(async(v,T)=>{o.value=!0;try{const D=await Nv({...i.value,...I});$.value=D.data,f.value=D.total,v(D)}catch(D){T(D)}finally{o.value=!1}});return{mixHistoryList:$,mixHistoryListLoading:o,mixHistoryListQ:i,mixHistoryTotal:f,getMixHistoryList:g,delMixHistory:async I=>new Promise(async(v,T)=>{await fv.confirm("确认删除该项目吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});let D=Kt.service({fullscreen:!0,text:"删除中...",background:"rgba(255, 255, 255, 0.7)"});try{const B=await Bv({id:I});St({message:"删除成功",type:"success"}),v(B)}catch(B){T(B)}finally{D.close()}}),copyMixHistory:async I=>new Promise(async(v,T)=>{let D=Kt.service({fullscreen:!0,text:"复制中...",background:"rgba(255, 255, 255, 0.7)"});try{const B=await Fv({id:I});St({message:"复制成功",type:"success"}),v(B)}catch(B){T(B)}finally{D.close()}}),refreshMixHistoryList:()=>{i.value.page=1,g()},getVideoName:()=>new Promise(async(I,v)=>{try{const T=await Wv({});I(T)}catch(T){v(T)}finally{}}),saveVideoParams:I=>new Promise(async(v,T)=>{try{const D=await Dv(I);v(D)}catch(D){T(D)}finally{}}),generateAiCopywriting:async I=>new Promise(async(v,T)=>{let D=Kt.service({fullscreen:!0,text:"生成中...",background:"rgba(255, 255, 255, 0.7)"});try{const B=await Ju(I);v(B)}catch(B){T(B)}finally{D.close()}}),updateCopywriting:async I=>new Promise(async(v,T)=>{let D=Kt.service({fullscreen:!0,text:"生成中...",background:"rgba(255, 255, 255, 0.7)"});try{const B=await Ju(I);v(B)}catch(B){T(B)}finally{D.close()}}),generateJson:async I=>new Promise(async(v,T)=>{let D=Kt.service({fullscreen:!0,text:"生成中...",background:"rgba(255, 255, 255, 0.7)"});try{const B=await da({id:I});v(B)}catch(B){T(B)}finally{D.close()}}),generateNarration:async I=>new Promise(async(v,T)=>{let D=Kt.service({fullscreen:!0,text:"生成中...",background:"rgba(255, 255, 255, 0.7)"});try{const B=await ha({id:I});v(B)}catch(B){T(B)}finally{D.close()}}),exportVideo:async I=>new Promise(async(v,T)=>{try{const D=await Pv({id:I});v(D)}catch(D){T(D)}finally{}})}},u0={class:"content-wrapper h-full"},a0={class:"left-section h-full flex flex-col justify-between"},l0={class:"right-section"},c0={key:0,class:"preview-area"},f0=Et({__name:"First",setup($,{expose:o}){const{generateAiCopywriting:i}=pa(),f=Ae("form"),g=Ae("updateForm"),w=Ae("id");ht("isMultiple",!1);const b=q(!1),C=q([]),M=q(null),E=q(!1),L=Ae("updateOverList",I=>{});let R=q(!1);et(()=>f,I=>{if(b.value=!!I.copywriting,b.value&&L([0,1,2,3]),C.value=I!=null&&I.copywriting?[{content:I.copywriting,checked:!0}]:[],R.value){R.value=!1;return}Ur(()=>{var v;if(R.value=!0,!M.value){console.error("AIDocument 组件未找到");return}(v=M.value)==null||v.setFormData(I)})},{deep:!0});const O=I=>{var v;R.value=!0,(v=M.value)==null||v.setFormData(I)},y=async I=>{if(b.value=!0,!w)return;const v=await i({id:w.value});v&&(v.content=v.copywriting,C.value=[v],L([0]))},S=I=>{if(R.value){R.value=!1;return}const v=JSON.parse(JSON.stringify(I)),T=JSON.parse(v.theme);Array.isArray(T)?v.theme=T:v.theme=T?[T]:[],g(v),L([])};return o({updateFormData:O}),(I,v)=>(he(),Te("div",u0,[te("div",a0,[N($v,{ref_key:"aiDocumentRef",ref:M,postLoading:E.value,onGenerate:y,onChange:S,"max-length":1,"show-auto-subtitle":!1},null,8,["postLoading"])]),te("div",l0,[b.value?(he(),dt(o0,{key:1,data:C.value,maxSelectCount:1,onGenerate:y},null,8,["data"])):(he(),Te("div",c0,v[0]||(v[0]=[te("div",{class:"preview-icon"},[te("img",{src:Lv,alt:"文档图标"})],-1),te("div",{class:"preview-text"},"爆款文案即将生成",-1)])))])]))}}),Ku=Rt(f0,[["__scopeId","data-v-5555a392"]]),h0=""+new URL("loading-DZLGrqD0.gif",import.meta.url).href,d0={key:0,class:"loading-mask"},p0={class:"loading-content"},g0={class:"loading-text"},v0=Et({__name:"LoadingMask",props:{visible:{type:Boolean},text:{}},setup($){return(o,i)=>o.visible?(he(),Te("div",d0,[te("div",p0,[i[0]||(i[0]=te("img",{src:h0,alt:"loading",class:"loading-image"},null,-1)),te("div",g0,$n(o.text),1)])])):vn("",!0)}}),ga=Rt(v0,[["__scopeId","data-v-3ddb75db"]]),m0={class:"content-wrapper h-full relative"},_0={class:"text-center w-full mt-[20px]"},y0=Et({__name:"Second",setup($){const o=Ae("id"),i=Ae("activeStep"),f=Ae("updateOverList",Z=>{}),g=q([]),w=q(!1),b=q("加载中..."),C=async()=>{if(o.value)try{w.value=!0,g.value=await da({id:o.value}),g.value.length&&f([0,1])}catch(Z){console.log(Z)}finally{w.value=!1}};et(()=>i.value,Z=>{Z===1&&C()},{immediate:!0});const M=q(!1),E=q({}),L=(Z,k)=>{M.value=!0,E.value=Z,E.value.index=k},R=q(""),O=q(!1),y=()=>{O.value=!0},S=()=>{E.value.keys.push(R.value),O.value=!1},I=Z=>{E.value.keys.splice(E.value.keys.indexOf(Z),1)},v={sentiment:[{required:!0,message:"请选择情感倾向",trigger:"change"}],index:[{required:!0,message:"请选择id",trigger:"change"}],keys:[{required:!0,message:"请输入关键词",trigger:"blur"},{validator:(Z,k,Q)=>{k.length>10?Q(new Error("最多输入10个关键词")):Q()},trigger:"blur"}],title:[{required:!0,message:"请输入脚本内容",trigger:"blur"}]},T=q(),D=async()=>{if(!await T.value.validate())return;const k={keyid:E.value.index,keys:E.value.keys,title:E.value.title,id:o.value,sentiment:E.value.sentiment};try{b.value="保存中...",w.value=!0,await Hv(k),M.value=!1,await C()}catch(Q){console.log(Q)}finally{w.value=!1}},B=()=>{M.value=!1};return(Z,k)=>{const Q=aa,ae=Hr,U=dv,V=ua,se=pv,Pe=ps,ke=ca,Xt=la,Ut=hv;return he(),Te("div",m0,[N(ga,{visible:w.value},null,8,["visible"]),N(V,{data:g.value,height:"100%"},{empty:j(()=>[N(U,{image:Ne(Gv),"image-size":207,description:" "},{default:j(()=>[N(ae,{type:"primary",onClick:C},{default:j(()=>k[4]||(k[4]=[ut("再次生成")])),_:1})]),_:1},8,["image"])]),default:j(()=>[N(Q,{type:"index",label:"编号",width:"100"}),N(Q,{prop:"keys",label:"关键词"},{default:j(({row:we})=>[te("span",null,$n(we.keys.join(",")),1)]),_:1}),N(Q,{prop:"title",label:"脚本内容"}),N(Q,{label:"操作",width:"120"},{default:j(({row:we,$index:$t})=>[N(ae,{type:"primary",link:"",onClick:It=>L(we,$t)},{default:j(()=>k[3]||(k[3]=[ut("编辑")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),N(Ut,{title:"编辑脚本",modelValue:M.value,"onUpdate:modelValue":k[2]||(k[2]=we=>M.value=we)},{default:j(()=>[N(Xt,{model:E.value,class:"mt-[14px]",rules:v,ref_key:"formRef",ref:T,size:"large"},{default:j(()=>[N(ke,{label:"关键词",prop:"keys",class:"keys"},{default:j(()=>[(he(!0),Te($r,null,Gr(E.value.keys,we=>(he(),dt(se,{key:we,closable:"","disable-transitions":!1,onClose:$t=>I(we),class:"mr-[10px] mb-[10px]"},{default:j(()=>[ut($n(we),1)]),_:2},1032,["onClose"]))),128)),O.value?(he(),dt(Pe,{key:0,ref:"InputRef",modelValue:R.value,"onUpdate:modelValue":k[0]||(k[0]=we=>R.value=we),class:"mb-[10px]",onKeyup:ia(S,["enter"]),onBlur:S,style:{height:"32px","margin-right":"10px",width:"100px"}},null,8,["modelValue"])):(he(),dt(ae,{key:1,class:"button-new-tag mb-[10px]",size:"large",style:{height:"32px"},onClick:y,icon:Ne(gv)},{default:j(()=>k[5]||(k[5]=[ut(" 添加 ")])),_:1},8,["icon"]))]),_:1}),N(ke,{label:"脚本内容",prop:"title"},{default:j(()=>[N(Pe,{modelValue:E.value.title,"onUpdate:modelValue":k[1]||(k[1]=we=>E.value.title=we),placeholder:"请输入脚本内容"},null,8,["modelValue"])]),_:1}),N(ke,{"label-width":"0"},{default:j(()=>[te("div",_0,[N(ae,{type:"primary",onClick:D,style:{width:"100px"}},{default:j(()=>k[6]||(k[6]=[ut("保存")])),_:1}),N(ae,{onClick:B,style:{width:"100px"}},{default:j(()=>k[7]||(k[7]=[ut("取消")])),_:1})])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),Xu=Rt(y0,[["__scopeId","data-v-785ab451"]]),x0="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAANhJREFUKFOl0j1KQ2EUhOF3zhewsLDx56ZMZcDC0kYQXIOFuId0goUWaQxkA9auQrdhuixAUcQ2heD5Rmwu5FY3eOp5mGKODiarF8ExPc6wUDNZuUe2jfwDmE+LO9k7SPeGEAy67W2DXS9FHBkvQWd2PkoxAqfQFGn8h1sQ37lXt8obYNBPZj2N8AVo5GQeAy3WANR9iFcjCTvTJ1H0JBjWcBNVH2vA6EryobMuiTiv6YdS9Aw0mR6WovdOA1+1cquIXahTmRnSNXgb+wZp3gW95th8h01f4xfa0WpZE0ysJQAAAABJRU5ErkJggg==";function Ee($,o,i,f){return new(i||(i=Promise))(function(g,w){function b(E){try{M(f.next(E))}catch(L){w(L)}}function C(E){try{M(f.throw(E))}catch(L){w(L)}}function M(E){var L;E.done?g(E.value):(L=E.value,L instanceof i?L:new i(function(R){R(L)})).then(b,C)}M((f=f.apply($,o||[])).next())})}class Hn{constructor(){this.listeners={}}on(o,i,f){if(this.listeners[o]||(this.listeners[o]=new Set),this.listeners[o].add(i),f==null?void 0:f.once){const g=()=>{this.un(o,g),this.un(o,i)};return this.on(o,g),g}return()=>this.un(o,i)}un(o,i){var f;(f=this.listeners[o])===null||f===void 0||f.delete(i)}once(o,i){return this.on(o,i,{once:!0})}unAll(){this.listeners={}}emit(o,...i){this.listeners[o]&&this.listeners[o].forEach(f=>f(...i))}}const kr={decode:function($,o){return Ee(this,void 0,void 0,function*(){const i=new AudioContext({sampleRate:o});return i.decodeAudioData($).finally(()=>i.close())})},createBuffer:function($,o){return typeof $[0]=="number"&&($=[$]),function(i){const f=i[0];if(f.some(g=>g>1||g<-1)){const g=f.length;let w=0;for(let b=0;b<g;b++){const C=Math.abs(f[b]);C>w&&(w=C)}for(const b of i)for(let C=0;C<g;C++)b[C]/=w}}($),{duration:o,length:$[0].length,sampleRate:$[0].length/o,numberOfChannels:$.length,getChannelData:i=>$==null?void 0:$[i],copyFromChannel:AudioBuffer.prototype.copyFromChannel,copyToChannel:AudioBuffer.prototype.copyToChannel}}};function va($,o){const i=o.xmlns?document.createElementNS(o.xmlns,$):document.createElement($);for(const[f,g]of Object.entries(o))if(f==="children")for(const[w,b]of Object.entries(o))typeof b=="string"?i.appendChild(document.createTextNode(b)):i.appendChild(va(w,b));else f==="style"?Object.assign(i.style,g):f==="textContent"?i.textContent=g:i.setAttribute(f,g.toString());return i}function ju($,o,i){const f=va($,o||{});return i==null||i.appendChild(f),f}var w0=Object.freeze({__proto__:null,createElement:ju,default:ju});const b0={fetchBlob:function($,o,i){return Ee(this,void 0,void 0,function*(){const f=yield fetch($,i);if(f.status>=400)throw new Error(`Failed to fetch ${$}: ${f.status} (${f.statusText})`);return function(g,w){Ee(this,void 0,void 0,function*(){if(!g.body||!g.headers)return;const b=g.body.getReader(),C=Number(g.headers.get("Content-Length"))||0;let M=0;const E=R=>Ee(this,void 0,void 0,function*(){M+=(R==null?void 0:R.length)||0;const O=Math.round(M/C*100);w(O)}),L=()=>Ee(this,void 0,void 0,function*(){let R;try{R=yield b.read()}catch{return}R.done||(E(R.value),yield L())});L()})}(f.clone(),o),f.blob()})}};class A0 extends Hn{constructor(o){super(),this.isExternalMedia=!1,o.media?(this.media=o.media,this.isExternalMedia=!0):this.media=document.createElement("audio"),o.mediaControls&&(this.media.controls=!0),o.autoplay&&(this.media.autoplay=!0),o.playbackRate!=null&&this.onMediaEvent("canplay",()=>{o.playbackRate!=null&&(this.media.playbackRate=o.playbackRate)},{once:!0})}onMediaEvent(o,i,f){return this.media.addEventListener(o,i,f),()=>this.media.removeEventListener(o,i,f)}getSrc(){return this.media.currentSrc||this.media.src||""}revokeSrc(){const o=this.getSrc();o.startsWith("blob:")&&URL.revokeObjectURL(o)}canPlayType(o){return this.media.canPlayType(o)!==""}setSrc(o,i){const f=this.getSrc();if(o&&f===o)return;this.revokeSrc();const g=i instanceof Blob&&(this.canPlayType(i.type)||!o)?URL.createObjectURL(i):o;f&&(this.media.src="");try{this.media.src=g}catch{this.media.src=o}}destroy(){this.isExternalMedia||(this.media.pause(),this.media.remove(),this.revokeSrc(),this.media.src="",this.media.load())}setMediaElement(o){this.media=o}play(){return Ee(this,void 0,void 0,function*(){return this.media.play()})}pause(){this.media.pause()}isPlaying(){return!this.media.paused&&!this.media.ended}setTime(o){this.media.currentTime=Math.max(0,Math.min(o,this.getDuration()))}getDuration(){return this.media.duration}getCurrentTime(){return this.media.currentTime}getVolume(){return this.media.volume}setVolume(o){this.media.volume=o}getMuted(){return this.media.muted}setMuted(o){this.media.muted=o}getPlaybackRate(){return this.media.playbackRate}isSeeking(){return this.media.seeking}setPlaybackRate(o,i){i!=null&&(this.media.preservesPitch=i),this.media.playbackRate=o}getMediaElement(){return this.media}setSinkId(o){return this.media.setSinkId(o)}}class mn extends Hn{constructor(o,i){super(),this.timeouts=[],this.isScrollable=!1,this.audioData=null,this.resizeObserver=null,this.lastContainerWidth=0,this.isDragging=!1,this.subscriptions=[],this.unsubscribeOnScroll=[],this.subscriptions=[],this.options=o;const f=this.parentFromOptionsContainer(o.container);this.parent=f;const[g,w]=this.initHtml();f.appendChild(g),this.container=g,this.scrollContainer=w.querySelector(".scroll"),this.wrapper=w.querySelector(".wrapper"),this.canvasWrapper=w.querySelector(".canvases"),this.progressWrapper=w.querySelector(".progress"),this.cursor=w.querySelector(".cursor"),i&&w.appendChild(i),this.initEvents()}parentFromOptionsContainer(o){let i;if(typeof o=="string"?i=document.querySelector(o):o instanceof HTMLElement&&(i=o),!i)throw new Error("Container not found");return i}initEvents(){const o=i=>{const f=this.wrapper.getBoundingClientRect(),g=i.clientX-f.left,w=i.clientY-f.top;return[g/f.width,w/f.height]};if(this.wrapper.addEventListener("click",i=>{const[f,g]=o(i);this.emit("click",f,g)}),this.wrapper.addEventListener("dblclick",i=>{const[f,g]=o(i);this.emit("dblclick",f,g)}),this.options.dragToSeek!==!0&&typeof this.options.dragToSeek!="object"||this.initDrag(),this.scrollContainer.addEventListener("scroll",()=>{const{scrollLeft:i,scrollWidth:f,clientWidth:g}=this.scrollContainer,w=i/f,b=(i+g)/f;this.emit("scroll",w,b,i,i+g)}),typeof ResizeObserver=="function"){const i=this.createDelay(100);this.resizeObserver=new ResizeObserver(()=>{i().then(()=>this.onContainerResize()).catch(()=>{})}),this.resizeObserver.observe(this.scrollContainer)}}onContainerResize(){const o=this.parent.clientWidth;o===this.lastContainerWidth&&this.options.height!=="auto"||(this.lastContainerWidth=o,this.reRender())}initDrag(){this.subscriptions.push(function(o,i,f,g,w=3,b=0,C=100){if(!o)return()=>{};const M=matchMedia("(pointer: coarse)").matches;let E=()=>{};const L=R=>{if(R.button!==b)return;R.preventDefault(),R.stopPropagation();let O=R.clientX,y=R.clientY,S=!1;const I=Date.now(),v=k=>{if(k.preventDefault(),k.stopPropagation(),M&&Date.now()-I<C)return;const Q=k.clientX,ae=k.clientY,U=Q-O,V=ae-y;if(S||Math.abs(U)>w||Math.abs(V)>w){const se=o.getBoundingClientRect(),{left:Pe,top:ke}=se;S||(f==null||f(O-Pe,y-ke),S=!0),i(U,V,Q-Pe,ae-ke),O=Q,y=ae}},T=k=>{if(S){const Q=k.clientX,ae=k.clientY,U=o.getBoundingClientRect(),{left:V,top:se}=U;g==null||g(Q-V,ae-se)}E()},D=k=>{k.relatedTarget&&k.relatedTarget!==document.documentElement||T(k)},B=k=>{S&&(k.stopPropagation(),k.preventDefault())},Z=k=>{S&&k.preventDefault()};document.addEventListener("pointermove",v),document.addEventListener("pointerup",T),document.addEventListener("pointerout",D),document.addEventListener("pointercancel",D),document.addEventListener("touchmove",Z,{passive:!1}),document.addEventListener("click",B,{capture:!0}),E=()=>{document.removeEventListener("pointermove",v),document.removeEventListener("pointerup",T),document.removeEventListener("pointerout",D),document.removeEventListener("pointercancel",D),document.removeEventListener("touchmove",Z),setTimeout(()=>{document.removeEventListener("click",B,{capture:!0})},10)}};return o.addEventListener("pointerdown",L),()=>{E(),o.removeEventListener("pointerdown",L)}}(this.wrapper,(o,i,f)=>{this.emit("drag",Math.max(0,Math.min(1,f/this.wrapper.getBoundingClientRect().width)))},o=>{this.isDragging=!0,this.emit("dragstart",Math.max(0,Math.min(1,o/this.wrapper.getBoundingClientRect().width)))},o=>{this.isDragging=!1,this.emit("dragend",Math.max(0,Math.min(1,o/this.wrapper.getBoundingClientRect().width)))}))}getHeight(o,i){var f;const g=((f=this.audioData)===null||f===void 0?void 0:f.numberOfChannels)||1;if(o==null)return 128;if(!isNaN(Number(o)))return Number(o);if(o==="auto"){const w=this.parent.clientHeight||128;return i!=null&&i.every(b=>!b.overlay)?w/g:w}return 128}initHtml(){const o=document.createElement("div"),i=o.attachShadow({mode:"open"}),f=this.options.cspNonce&&typeof this.options.cspNonce=="string"?this.options.cspNonce.replace(/"/g,""):"";return i.innerHTML=`
      <style${f?` nonce="${f}"`:""}>
        :host {
          user-select: none;
          min-width: 1px;
        }
        :host audio {
          display: block;
          width: 100%;
        }
        :host .scroll {
          overflow-x: auto;
          overflow-y: hidden;
          width: 100%;
          position: relative;
        }
        :host .noScrollbar {
          scrollbar-color: transparent;
          scrollbar-width: none;
        }
        :host .noScrollbar::-webkit-scrollbar {
          display: none;
          -webkit-appearance: none;
        }
        :host .wrapper {
          position: relative;
          overflow: visible;
          z-index: 2;
        }
        :host .canvases {
          min-height: ${this.getHeight(this.options.height,this.options.splitChannels)}px;
        }
        :host .canvases > div {
          position: relative;
        }
        :host canvas {
          display: block;
          position: absolute;
          top: 0;
          image-rendering: pixelated;
        }
        :host .progress {
          pointer-events: none;
          position: absolute;
          z-index: 2;
          top: 0;
          left: 0;
          width: 0;
          height: 100%;
          overflow: hidden;
        }
        :host .progress > div {
          position: relative;
        }
        :host .cursor {
          pointer-events: none;
          position: absolute;
          z-index: 5;
          top: 0;
          left: 0;
          height: 100%;
          border-radius: 2px;
        }
      </style>

      <div class="scroll" part="scroll">
        <div class="wrapper" part="wrapper">
          <div class="canvases" part="canvases"></div>
          <div class="progress" part="progress"></div>
          <div class="cursor" part="cursor"></div>
        </div>
      </div>
    `,[o,i]}setOptions(o){if(this.options.container!==o.container){const i=this.parentFromOptionsContainer(o.container);i.appendChild(this.container),this.parent=i}o.dragToSeek!==!0&&typeof this.options.dragToSeek!="object"||this.initDrag(),this.options=o,this.reRender()}getWrapper(){return this.wrapper}getWidth(){return this.scrollContainer.clientWidth}getScroll(){return this.scrollContainer.scrollLeft}setScroll(o){this.scrollContainer.scrollLeft=o}setScrollPercentage(o){const{scrollWidth:i}=this.scrollContainer,f=i*o;this.setScroll(f)}destroy(){var o,i;this.subscriptions.forEach(f=>f()),this.container.remove(),(o=this.resizeObserver)===null||o===void 0||o.disconnect(),(i=this.unsubscribeOnScroll)===null||i===void 0||i.forEach(f=>f()),this.unsubscribeOnScroll=[]}createDelay(o=10){let i,f;const g=()=>{i&&clearTimeout(i),f&&f()};return this.timeouts.push(g),()=>new Promise((w,b)=>{g(),f=b,i=setTimeout(()=>{i=void 0,f=void 0,w()},o)})}convertColorValues(o){if(!Array.isArray(o))return o||"";if(o.length<2)return o[0]||"";const i=document.createElement("canvas"),f=i.getContext("2d"),g=i.height*(window.devicePixelRatio||1),w=f.createLinearGradient(0,0,0,g),b=1/(o.length-1);return o.forEach((C,M)=>{const E=M*b;w.addColorStop(E,C)}),w}getPixelRatio(){return Math.max(1,window.devicePixelRatio||1)}renderBarWaveform(o,i,f,g){const w=o[0],b=o[1]||o[0],C=w.length,{width:M,height:E}=f.canvas,L=E/2,R=this.getPixelRatio(),O=i.barWidth?i.barWidth*R:1,y=i.barGap?i.barGap*R:i.barWidth?O/2:0,S=i.barRadius||0,I=M/(O+y)/C,v=S&&"roundRect"in f?"roundRect":"rect";f.beginPath();let T=0,D=0,B=0;for(let Z=0;Z<=C;Z++){const k=Math.round(Z*I);if(k>T){const U=Math.round(D*L*g),V=U+Math.round(B*L*g)||1;let se=L-U;i.barAlign==="top"?se=0:i.barAlign==="bottom"&&(se=E-V),f[v](T*(O+y),se,O,V,S),T=k,D=0,B=0}const Q=Math.abs(w[Z]||0),ae=Math.abs(b[Z]||0);Q>D&&(D=Q),ae>B&&(B=ae)}f.fill(),f.closePath()}renderLineWaveform(o,i,f,g){const w=b=>{const C=o[b]||o[0],M=C.length,{height:E}=f.canvas,L=E/2,R=f.canvas.width/M;f.moveTo(0,L);let O=0,y=0;for(let S=0;S<=M;S++){const I=Math.round(S*R);if(I>O){const T=L+(Math.round(y*L*g)||1)*(b===0?-1:1);f.lineTo(O,T),O=I,y=0}const v=Math.abs(C[S]||0);v>y&&(y=v)}f.lineTo(O,L)};f.beginPath(),w(0),w(1),f.fill(),f.closePath()}renderWaveform(o,i,f){if(f.fillStyle=this.convertColorValues(i.waveColor),i.renderFunction)return void i.renderFunction(o,f);let g=i.barHeight||1;if(i.normalize){const w=Array.from(o[0]).reduce((b,C)=>Math.max(b,Math.abs(C)),0);g=w?1/w:1}i.barWidth||i.barGap||i.barAlign?this.renderBarWaveform(o,i,f,g):this.renderLineWaveform(o,i,f,g)}renderSingleCanvas(o,i,f,g,w,b,C){const M=this.getPixelRatio(),E=document.createElement("canvas");E.width=Math.round(f*M),E.height=Math.round(g*M),E.style.width=`${f}px`,E.style.height=`${g}px`,E.style.left=`${Math.round(w)}px`,b.appendChild(E);const L=E.getContext("2d");if(this.renderWaveform(o,i,L),E.width>0&&E.height>0){const R=E.cloneNode(),O=R.getContext("2d");O.drawImage(E,0,0),O.globalCompositeOperation="source-in",O.fillStyle=this.convertColorValues(i.progressColor),O.fillRect(0,0,E.width,E.height),C.appendChild(R)}}renderMultiCanvas(o,i,f,g,w,b){const C=this.getPixelRatio(),{clientWidth:M}=this.scrollContainer,E=f/C;let L=Math.min(mn.MAX_CANVAS_WIDTH,M,E),R={};if(L===0)return;if(i.barWidth||i.barGap){const v=i.barWidth||.5,T=v+(i.barGap||v/2);L%T!=0&&(L=Math.floor(L/T)*T)}const O=v=>{if(v<0||v>=y||R[v])return;R[v]=!0;const T=v*L,D=Math.min(E-T,L);if(D<=0)return;const B=o.map(Z=>{const k=Math.floor(T/E*Z.length),Q=Math.floor((T+D)/E*Z.length);return Z.slice(k,Q)});this.renderSingleCanvas(B,i,D,g,T,w,b)},y=Math.ceil(E/L);if(!this.isScrollable){for(let v=0;v<y;v++)O(v);return}const S=this.scrollContainer.scrollLeft/E,I=Math.floor(S*y);if(O(I-1),O(I),O(I+1),y>1){const v=this.on("scroll",()=>{const{scrollLeft:T}=this.scrollContainer,D=Math.floor(T/E*y);Object.keys(R).length>mn.MAX_NODES&&(w.innerHTML="",b.innerHTML="",R={}),O(D-1),O(D),O(D+1)});this.unsubscribeOnScroll.push(v)}}renderChannel(o,i,f,g){var{overlay:w}=i,b=function(L,R){var O={};for(var y in L)Object.prototype.hasOwnProperty.call(L,y)&&R.indexOf(y)<0&&(O[y]=L[y]);if(L!=null&&typeof Object.getOwnPropertySymbols=="function"){var S=0;for(y=Object.getOwnPropertySymbols(L);S<y.length;S++)R.indexOf(y[S])<0&&Object.prototype.propertyIsEnumerable.call(L,y[S])&&(O[y[S]]=L[y[S]])}return O}(i,["overlay"]);const C=document.createElement("div"),M=this.getHeight(b.height,b.splitChannels);C.style.height=`${M}px`,w&&g>0&&(C.style.marginTop=`-${M}px`),this.canvasWrapper.style.minHeight=`${M}px`,this.canvasWrapper.appendChild(C);const E=C.cloneNode();this.progressWrapper.appendChild(E),this.renderMultiCanvas(o,b,f,M,C,E)}render(o){return Ee(this,void 0,void 0,function*(){var i;this.timeouts.forEach(M=>M()),this.timeouts=[],this.canvasWrapper.innerHTML="",this.progressWrapper.innerHTML="",this.options.width!=null&&(this.scrollContainer.style.width=typeof this.options.width=="number"?`${this.options.width}px`:this.options.width);const f=this.getPixelRatio(),g=this.scrollContainer.clientWidth,w=Math.ceil(o.duration*(this.options.minPxPerSec||0));this.isScrollable=w>g;const b=this.options.fillParent&&!this.isScrollable,C=(b?g:w)*f;if(this.wrapper.style.width=b?"100%":`${w}px`,this.scrollContainer.style.overflowX=this.isScrollable?"auto":"hidden",this.scrollContainer.classList.toggle("noScrollbar",!!this.options.hideScrollbar),this.cursor.style.backgroundColor=`${this.options.cursorColor||this.options.progressColor}`,this.cursor.style.width=`${this.options.cursorWidth}px`,this.audioData=o,this.emit("render"),this.options.splitChannels)for(let M=0;M<o.numberOfChannels;M++){const E=Object.assign(Object.assign({},this.options),(i=this.options.splitChannels)===null||i===void 0?void 0:i[M]);this.renderChannel([o.getChannelData(M)],E,C,M)}else{const M=[o.getChannelData(0)];o.numberOfChannels>1&&M.push(o.getChannelData(1)),this.renderChannel(M,this.options,C,0)}Promise.resolve().then(()=>this.emit("rendered"))})}reRender(){if(this.unsubscribeOnScroll.forEach(f=>f()),this.unsubscribeOnScroll=[],!this.audioData)return;const{scrollWidth:o}=this.scrollContainer,{right:i}=this.progressWrapper.getBoundingClientRect();if(this.render(this.audioData),this.isScrollable&&o!==this.scrollContainer.scrollWidth){const{right:f}=this.progressWrapper.getBoundingClientRect();let g=f-i;g*=2,g=g<0?Math.floor(g):Math.ceil(g),g/=2,this.scrollContainer.scrollLeft+=g}}zoom(o){this.options.minPxPerSec=o,this.reRender()}scrollIntoView(o,i=!1){const{scrollLeft:f,scrollWidth:g,clientWidth:w}=this.scrollContainer,b=o*g,C=f,M=f+w,E=w/2;if(this.isDragging)b+30>M?this.scrollContainer.scrollLeft+=30:b-30<C&&(this.scrollContainer.scrollLeft-=30);else{(b<C||b>M)&&(this.scrollContainer.scrollLeft=b-(this.options.autoCenter?E:0));const L=b-f-E;i&&this.options.autoCenter&&L>0&&(this.scrollContainer.scrollLeft+=Math.min(L,10))}{const L=this.scrollContainer.scrollLeft,R=L/g,O=(L+w)/g;this.emit("scroll",R,O,L,L+w)}}renderProgress(o,i){if(isNaN(o))return;const f=100*o;this.canvasWrapper.style.clipPath=`polygon(${f}% 0, 100% 0, 100% 100%, ${f}% 100%)`,this.progressWrapper.style.width=`${f}%`,this.cursor.style.left=`${f}%`,this.cursor.style.transform=`translateX(-${Math.round(f)===100?this.options.cursorWidth:0}px)`,this.isScrollable&&this.options.autoScroll&&this.scrollIntoView(o,i)}exportImage(o,i,f){return Ee(this,void 0,void 0,function*(){const g=this.canvasWrapper.querySelectorAll("canvas");if(!g.length)throw new Error("No waveform data");if(f==="dataURL"){const w=Array.from(g).map(b=>b.toDataURL(o,i));return Promise.resolve(w)}return Promise.all(Array.from(g).map(w=>new Promise((b,C)=>{w.toBlob(M=>{M?b(M):C(new Error("Could not export image"))},o,i)})))})}}mn.MAX_CANVAS_WIDTH=8e3,mn.MAX_NODES=10;class C0 extends Hn{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on("tick",()=>{requestAnimationFrame(()=>{this.emit("tick")})}),this.emit("tick")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}class hs extends Hn{constructor(o=new AudioContext){super(),this.bufferNode=null,this.playStartTime=0,this.playedDuration=0,this._muted=!1,this._playbackRate=1,this._duration=void 0,this.buffer=null,this.currentSrc="",this.paused=!0,this.crossOrigin=null,this.seeking=!1,this.autoplay=!1,this.addEventListener=this.on,this.removeEventListener=this.un,this.audioContext=o,this.gainNode=this.audioContext.createGain(),this.gainNode.connect(this.audioContext.destination)}load(){return Ee(this,void 0,void 0,function*(){})}get src(){return this.currentSrc}set src(o){if(this.currentSrc=o,this._duration=void 0,!o)return this.buffer=null,void this.emit("emptied");fetch(o).then(i=>{if(i.status>=400)throw new Error(`Failed to fetch ${o}: ${i.status} (${i.statusText})`);return i.arrayBuffer()}).then(i=>this.currentSrc!==o?null:this.audioContext.decodeAudioData(i)).then(i=>{this.currentSrc===o&&(this.buffer=i,this.emit("loadedmetadata"),this.emit("canplay"),this.autoplay&&this.play())})}_play(){var o;if(!this.paused)return;this.paused=!1,(o=this.bufferNode)===null||o===void 0||o.disconnect(),this.bufferNode=this.audioContext.createBufferSource(),this.buffer&&(this.bufferNode.buffer=this.buffer),this.bufferNode.playbackRate.value=this._playbackRate,this.bufferNode.connect(this.gainNode);let i=this.playedDuration*this._playbackRate;(i>=this.duration||i<0)&&(i=0,this.playedDuration=0),this.bufferNode.start(this.audioContext.currentTime,i),this.playStartTime=this.audioContext.currentTime,this.bufferNode.onended=()=>{this.currentTime>=this.duration&&(this.pause(),this.emit("ended"))}}_pause(){var o;this.paused=!0,(o=this.bufferNode)===null||o===void 0||o.stop(),this.playedDuration+=this.audioContext.currentTime-this.playStartTime}play(){return Ee(this,void 0,void 0,function*(){this.paused&&(this._play(),this.emit("play"))})}pause(){this.paused||(this._pause(),this.emit("pause"))}stopAt(o){const i=o-this.currentTime,f=this.bufferNode;f==null||f.stop(this.audioContext.currentTime+i),f==null||f.addEventListener("ended",()=>{f===this.bufferNode&&(this.bufferNode=null,this.pause())},{once:!0})}setSinkId(o){return Ee(this,void 0,void 0,function*(){return this.audioContext.setSinkId(o)})}get playbackRate(){return this._playbackRate}set playbackRate(o){this._playbackRate=o,this.bufferNode&&(this.bufferNode.playbackRate.value=o)}get currentTime(){return(this.paused?this.playedDuration:this.playedDuration+(this.audioContext.currentTime-this.playStartTime))*this._playbackRate}set currentTime(o){const i=!this.paused;i&&this._pause(),this.playedDuration=o/this._playbackRate,i&&this._play(),this.emit("seeking"),this.emit("timeupdate")}get duration(){var o,i;return(o=this._duration)!==null&&o!==void 0?o:((i=this.buffer)===null||i===void 0?void 0:i.duration)||0}set duration(o){this._duration=o}get volume(){return this.gainNode.gain.value}set volume(o){this.gainNode.gain.value=o,this.emit("volumechange")}get muted(){return this._muted}set muted(o){this._muted!==o&&(this._muted=o,this._muted?this.gainNode.disconnect():this.gainNode.connect(this.audioContext.destination))}canPlayType(o){return/^(audio|video)\//.test(o)}getGainNode(){return this.gainNode}getChannelData(){const o=[];if(!this.buffer)return o;const i=this.buffer.numberOfChannels;for(let f=0;f<i;f++)o.push(this.buffer.getChannelData(f));return o}}const S0={waveColor:"#999",progressColor:"#555",cursorWidth:1,minPxPerSec:0,fillParent:!0,interact:!0,dragToSeek:!1,autoScroll:!0,autoCenter:!0,sampleRate:8e3};class Gn extends A0{static create(o){return new Gn(o)}constructor(o){const i=o.media||(o.backend==="WebAudio"?new hs:void 0);super({media:i,mediaControls:o.mediaControls,autoplay:o.autoplay,playbackRate:o.audioRate}),this.plugins=[],this.decodedData=null,this.stopAtPosition=null,this.subscriptions=[],this.mediaSubscriptions=[],this.abortController=null,this.options=Object.assign({},S0,o),this.timer=new C0;const f=i?void 0:this.getMediaElement();this.renderer=new mn(this.options,f),this.initPlayerEvents(),this.initRendererEvents(),this.initTimerEvents(),this.initPlugins();const g=this.options.url||this.getSrc()||"";Promise.resolve().then(()=>{this.emit("init");const{peaks:w,duration:b}=this.options;(g||w&&b)&&this.load(g,w,b).catch(()=>null)})}updateProgress(o=this.getCurrentTime()){return this.renderer.renderProgress(o/this.getDuration(),this.isPlaying()),o}initTimerEvents(){this.subscriptions.push(this.timer.on("tick",()=>{if(!this.isSeeking()){const o=this.updateProgress();this.emit("timeupdate",o),this.emit("audioprocess",o),this.stopAtPosition!=null&&this.isPlaying()&&o>=this.stopAtPosition&&this.pause()}}))}initPlayerEvents(){this.isPlaying()&&(this.emit("play"),this.timer.start()),this.mediaSubscriptions.push(this.onMediaEvent("timeupdate",()=>{const o=this.updateProgress();this.emit("timeupdate",o)}),this.onMediaEvent("play",()=>{this.emit("play"),this.timer.start()}),this.onMediaEvent("pause",()=>{this.emit("pause"),this.timer.stop(),this.stopAtPosition=null}),this.onMediaEvent("emptied",()=>{this.timer.stop(),this.stopAtPosition=null}),this.onMediaEvent("ended",()=>{this.emit("timeupdate",this.getDuration()),this.emit("finish"),this.stopAtPosition=null}),this.onMediaEvent("seeking",()=>{this.emit("seeking",this.getCurrentTime())}),this.onMediaEvent("error",()=>{var o;this.emit("error",(o=this.getMediaElement().error)!==null&&o!==void 0?o:new Error("Media error")),this.stopAtPosition=null}))}initRendererEvents(){this.subscriptions.push(this.renderer.on("click",(o,i)=>{this.options.interact&&(this.seekTo(o),this.emit("interaction",o*this.getDuration()),this.emit("click",o,i))}),this.renderer.on("dblclick",(o,i)=>{this.emit("dblclick",o,i)}),this.renderer.on("scroll",(o,i,f,g)=>{const w=this.getDuration();this.emit("scroll",o*w,i*w,f,g)}),this.renderer.on("render",()=>{this.emit("redraw")}),this.renderer.on("rendered",()=>{this.emit("redrawcomplete")}),this.renderer.on("dragstart",o=>{this.emit("dragstart",o)}),this.renderer.on("dragend",o=>{this.emit("dragend",o)}));{let o;this.subscriptions.push(this.renderer.on("drag",i=>{if(!this.options.interact)return;let f;this.renderer.renderProgress(i),clearTimeout(o),this.isPlaying()?f=0:this.options.dragToSeek===!0?f=200:typeof this.options.dragToSeek=="object"&&this.options.dragToSeek!==void 0&&(f=this.options.dragToSeek.debounceTime),o=setTimeout(()=>{this.seekTo(i)},f),this.emit("interaction",i*this.getDuration()),this.emit("drag",i)}))}}initPlugins(){var o;!((o=this.options.plugins)===null||o===void 0)&&o.length&&this.options.plugins.forEach(i=>{this.registerPlugin(i)})}unsubscribePlayerEvents(){this.mediaSubscriptions.forEach(o=>o()),this.mediaSubscriptions=[]}setOptions(o){this.options=Object.assign({},this.options,o),o.duration&&!o.peaks&&(this.decodedData=kr.createBuffer(this.exportPeaks(),o.duration)),o.peaks&&o.duration&&(this.decodedData=kr.createBuffer(o.peaks,o.duration)),this.renderer.setOptions(this.options),o.audioRate&&this.setPlaybackRate(o.audioRate),o.mediaControls!=null&&(this.getMediaElement().controls=o.mediaControls)}registerPlugin(o){return o._init(this),this.plugins.push(o),this.subscriptions.push(o.once("destroy",()=>{this.plugins=this.plugins.filter(i=>i!==o)})),o}getWrapper(){return this.renderer.getWrapper()}getWidth(){return this.renderer.getWidth()}getScroll(){return this.renderer.getScroll()}setScroll(o){return this.renderer.setScroll(o)}setScrollTime(o){const i=o/this.getDuration();this.renderer.setScrollPercentage(i)}getActivePlugins(){return this.plugins}loadAudio(o,i,f,g){return Ee(this,void 0,void 0,function*(){var w;if(this.emit("load",o),!this.options.media&&this.isPlaying()&&this.pause(),this.decodedData=null,this.stopAtPosition=null,!i&&!f){const C=this.options.fetchParams||{};window.AbortController&&!C.signal&&(this.abortController=new AbortController,C.signal=(w=this.abortController)===null||w===void 0?void 0:w.signal);const M=L=>this.emit("loading",L);i=yield b0.fetchBlob(o,M,C);const E=this.options.blobMimeType;E&&(i=new Blob([i],{type:E}))}this.setSrc(o,i);const b=yield new Promise(C=>{const M=g||this.getDuration();M?C(M):this.mediaSubscriptions.push(this.onMediaEvent("loadedmetadata",()=>C(this.getDuration()),{once:!0}))});if(!o&&!i){const C=this.getMediaElement();C instanceof hs&&(C.duration=b)}if(f)this.decodedData=kr.createBuffer(f,b||0);else if(i){const C=yield i.arrayBuffer();this.decodedData=yield kr.decode(C,this.options.sampleRate)}this.decodedData&&(this.emit("decode",this.getDuration()),this.renderer.render(this.decodedData)),this.emit("ready",this.getDuration())})}load(o,i,f){return Ee(this,void 0,void 0,function*(){try{return yield this.loadAudio(o,void 0,i,f)}catch(g){throw this.emit("error",g),g}})}loadBlob(o,i,f){return Ee(this,void 0,void 0,function*(){try{return yield this.loadAudio("",o,i,f)}catch(g){throw this.emit("error",g),g}})}zoom(o){if(!this.decodedData)throw new Error("No audio loaded");this.renderer.zoom(o),this.emit("zoom",o)}getDecodedData(){return this.decodedData}exportPeaks({channels:o=2,maxLength:i=8e3,precision:f=1e4}={}){if(!this.decodedData)throw new Error("The audio has not been decoded yet");const g=Math.min(o,this.decodedData.numberOfChannels),w=[];for(let b=0;b<g;b++){const C=this.decodedData.getChannelData(b),M=[],E=C.length/i;for(let L=0;L<i;L++){const R=C.slice(Math.floor(L*E),Math.ceil((L+1)*E));let O=0;for(let y=0;y<R.length;y++){const S=R[y];Math.abs(S)>Math.abs(O)&&(O=S)}M.push(Math.round(O*f)/f)}w.push(M)}return w}getDuration(){let o=super.getDuration()||0;return o!==0&&o!==1/0||!this.decodedData||(o=this.decodedData.duration),o}toggleInteraction(o){this.options.interact=o}setTime(o){this.stopAtPosition=null,super.setTime(o),this.updateProgress(o),this.emit("timeupdate",o)}seekTo(o){const i=this.getDuration()*o;this.setTime(i)}play(o,i){const f=Object.create(null,{play:{get:()=>super.play}});return Ee(this,void 0,void 0,function*(){o!=null&&this.setTime(o);const g=yield f.play.call(this);return i!=null&&(this.media instanceof hs?this.media.stopAt(i):this.stopAtPosition=i),g})}playPause(){return Ee(this,void 0,void 0,function*(){return this.isPlaying()?this.pause():this.play()})}stop(){this.pause(),this.setTime(0)}skip(o){this.setTime(this.getCurrentTime()+o)}empty(){this.load("",[[0]],.001)}setMediaElement(o){this.unsubscribePlayerEvents(),super.setMediaElement(o),this.initPlayerEvents()}exportImage(){return Ee(this,arguments,void 0,function*(o="image/png",i=1,f="dataURL"){return this.renderer.exportImage(o,i,f)})}destroy(){var o;this.emit("destroy"),(o=this.abortController)===null||o===void 0||o.abort(),this.plugins.forEach(i=>i.destroy()),this.subscriptions.forEach(i=>i()),this.unsubscribePlayerEvents(),this.timer.destroy(),this.renderer.destroy(),super.destroy()}}Gn.BasePlugin=class extends Hn{constructor($){super(),this.subscriptions=[],this.options=$}onInit(){}_init($){this.wavesurfer=$,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach($=>$())}},Gn.dom=w0;const E0={class:"audio-visualizer"},R0={class:"waveform-container flex items-center"},I0={class:"flex items-center mr-2"},T0={key:0,class:"ml-2"},L0=Et({__name:"SoundWave",props:{url:{type:String,default:""},height:{type:Number,default:150},waveColor:{type:String,default:"#999999"},progressColor:{type:String,default:"#186DF5"},cursorWidth:{type:Number,default:2}},setup($){const o=$,i=q(null),f=q(null),g=q(!1),w=q(""),b=q(""),C=vv(null),M=q(0),E=q(0),L=q(80),R=q(1),O=()=>{if(!f.value)return;const{waveColor:v,progressColor:T,height:D,cursorWidth:B}=o;i.value=Gn.create({container:f.value,waveColor:v,progressColor:T,height:D,barWidth:2,barGap:1,barRadius:2,cursorWidth:B,cursorColor:"#ffffff",hideScrollbar:!0,interact:!0}),i.value.setVolume(L.value/100),i.value.on("ready",()=>{var Z;E.value=((Z=i.value)==null?void 0:Z.getDuration())||0,y("准备播放","success",fs)}),i.value.on("play",()=>{g.value=!0,y("播放中","success",xv)}),i.value.on("pause",()=>{g.value=!1,y("已暂停","info",fs)}),i.value.on("finish",()=>{g.value=!1,y("播放完成","info",fs)}),i.value.on("loading",Z=>{y(`加载中: ${Z}%`,"warning",qu)}),i.value.on("error",Z=>{y(`错误: ${Z}`,"danger",wv)}),i.value.on("audioprocess",()=>{var Z;M.value=((Z=i.value)==null?void 0:Z.getCurrentTime())||0}),I()},y=(v,T,D)=>{w.value=v,b.value=T,C.value=D},S=()=>{i.value&&b.value!="danger"&&i.value.playPause()},I=()=>{i.value&&(y("加载示例音频中...","warning",qu),i.value.load(o.url))};return et(()=>o.waveColor,v=>{v&&i.value.setOptions({waveColor:v})}),et(()=>o.progressColor,v=>{v&&i.value.setOptions({progressColor:v})}),et(L,v=>{i.value&&i.value.setVolume(v/100)}),et(R,v=>{i.value&&i.value.setPlaybackRate(v)}),fa(()=>{O()}),mv(()=>{i.value&&i.value.destroy()}),(v,T)=>{const D=ra;return he(),Te("div",E0,[te("div",R0,[te("div",I0,[N(D,{onClick:S,size:$.height,class:"cursor-pointer"},{default:j(()=>[(he(),dt(_v(C.value)))]),_:1},8,["size"]),b.value=="danger"?(he(),Te("p",T0,"音频解析失败")):vn("",!0)]),te("div",{ref_key:"waveform",ref:f,class:"waveform flex-1",style:yv({height:`${$.height}px`})},null,4)])])}}}),M0=Rt(L0,[["__scopeId","data-v-60882f44"]]),O0={class:"content-wrapper h-full relative"},P0={class:"text-content cursor-pointer"},D0=["onClick"],W0={class:"text-right flex-1"},F0=Et({__name:"Third",setup($){const o=q([]),i=q(!1),f=Ae("id"),g=Ae("activeStep"),w=(y,S,I)=>{S&&!/^[a-zA-Z]+$/.test(S)?I(new Error("只能输入英文字母")):I()},b=Ae("updateOverList",y=>{}),C=async()=>{if(f.value)try{i.value=!0;const y=await ha({id:f.value});o.value=y.map(S=>({...S,text:S.title.split(""),selectedIndex:void 0,itemForm:{id:S.id,pinyin:"",type:void 0}})),b([0,1,2])}catch(y){console.log(y)}finally{i.value=!1}},M=q([]),E=(y,S)=>{y&&(M.value[S]=y)},L=(y,S,I)=>{I.selectedIndex=S,I.itemForm={id:I.id,pinyin:"",type:void 0},console.log(`选中行ID: ${I.id}, 字符: ${y}, 索引: ${S}`)},R={pinyin:[{required:!0,message:"请输入发音修改为",trigger:"blur"},{validator:w,message:"只能输入英文字母",trigger:"blur"}],type:[{required:!0,message:"请选择声调修改为",trigger:"change"}]},O=async(y,S)=>{const I=M.value[S];if(!I){console.error(`未找到行索引 ${S} 的表单引用`),St.error("表单内部错误，请刷新重试");return}try{if(!await I.validate()){console.log("表单验证失败");return}if(y.itemForm){const T=y.selectedIndex?y.title.slice(0,y.selectedIndex+1)+"$&&$"+y.title.slice(y.selectedIndex+1):y.title;i.value=!0,await zv({id:f.value,pinyin:y.itemForm.pinyin,type:y.itemForm.type,keyid:S,title:T}),St.success("修改成功"),await C()}else console.error("Current row itemForm is undefined",y),St.error("表单数据错误，请重试")}catch(v){console.error("提交修改失败:",v),St.error("修改失败，请重试")}finally{i.value=!1,I&&I.resetFields(),y.selectedIndex=void 0,y.itemForm={id:y.id,pinyin:"",type:void 0}}};return et(()=>g.value,y=>{y===2&&C()},{immediate:!0}),(y,S)=>{const I=aa,v=ps,T=ca,D=Av,B=bv,Z=Hr,k=ua;return he(),Te("div",O0,[N(ga,{visible:i.value},null,8,["visible"]),N(k,{data:o.value,"header-cell-class-name":"table-header",height:"100%"},{default:j(()=>[N(I,{type:"index",label:"编号",width:"100"}),N(I,{prop:"keywords",label:"旁白语音"},{default:j(({row:Q})=>[N(M0,{url:Q.url,height:22,cursorWidth:0},null,8,["url"])]),_:1}),N(I,{label:"语音内容"},{header:j(()=>S[0]||(S[0]=[te("div",{class:"text-content flex justify-between items-center"},[te("span",null," 语音内容 "),te("div",{class:"flex items-center"},[te("img",{src:x0,alt:""}),te("span",{class:"text-[--el-color-primary] ml-[4px] text-[12px]"},"点击对应文字可进行发音修改")])],-1)])),default:j(({row:Q,$index:ae})=>[te("div",P0,[(he(!0),Te($r,null,Gr(Q.text,(U,V)=>(he(),dt(Ne(Cv),{key:V,effect:"light","raw-content":"",trigger:"click",placement:"top"},{content:j(()=>[N(Ne(la),{model:Q.itemForm,class:"px-[9px] py-[15px] pb-0",rules:R,ref_for:!0,ref:se=>E(se,ae)},{default:j(()=>[N(T,{label:"发音修改为：",prop:"pinyin"},{default:j(()=>[N(v,{modelValue:Q.itemForm.pinyin,"onUpdate:modelValue":se=>Q.itemForm.pinyin=se,placeholder:"请输入发音修改为（仅支持英文字母）",clearable:"",maxlength:"30"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),N(T,{label:"声调修改为：",prop:"type"},{default:j(()=>[N(B,{modelValue:Q.itemForm.type,"onUpdate:modelValue":se=>Q.itemForm.type=se},{default:j(()=>[N(D,{value:1,label:"1声"}),N(D,{value:2,label:"2声"}),N(D,{value:3,label:"3声"}),N(D,{value:4,label:"4声"}),N(D,{value:5,label:"轻声"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),N(T,null,{default:j(()=>[te("div",W0,[N(Z,{loading:i.value,type:"primary",onClick:se=>O(Q,ae)},{default:j(()=>S[1]||(S[1]=[ut(" 确定 ")])),_:2},1032,["loading","onClick"])])]),_:2},1024)]),_:2},1032,["model"])]),default:j(()=>[te("span",{class:ds([{active:Q.selectedIndex===V},"relative"]),onClick:sa(se=>L(U,V,Q),["stop"])},$n(U),11,D0)]),_:2},1024))),128))])]),_:1})]),_:1},8,["data"])])}}}),ea=Rt(F0,[["__scopeId","data-v-a601428c"]]),B0="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyVpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDYuMC1jMDAzIDExNi5kZGM3YmM0LCAyMDIxLzA4LzE3LTEzOjE4OjM3ICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgMjEuMiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RDUwQjUyNjIyOEJDMTFGMEIwRDBEQ0FERkNBRDVBNkIiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RDUwQjUyNjMyOEJDMTFGMEIwRDBEQ0FERkNBRDVBNkIiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpENTBCNTI2MDI4QkMxMUYwQjBEMERDQURGQ0FENUE2QiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpENTBCNTI2MTI4QkMxMUYwQjBEMERDQURGQ0FENUE2QiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PqWCAIIAAADcSURBVHjaYvz//z8DuYAFRISFhcH4kUC8BIiZsKgF2RIDxMtAnFWrVkE0IwETIL4HxMVYNPcAsSlMM9xmJACy8QUQb4KyeYH4E9TWUgYsinGBAiD+AMQpuBTg05wOxJeAOBVvgGEBFkCsBnUuCOiSYrMfEE+CyoNCPwSfzZeB2B9JvAqJHQelnaC0EhBvAGI9mGYdIFaEsgWAWA6LRQJQWhHmDXQ/34CG8kMc3pmKL8BmAvEOIObBovEL1FBnbJodgZibQHLWB2IzdM3XgLiahDxxBUQwUpKrAAIMACyOJwxN0hVQAAAAAElFTkSuQmCC",N0={class:"content-wrapper h-full flex items-stretch"},k0={class:"w-[74%] bg-secondary px-[21px] py-[20px] flex-shrink-0 flex justify-center"},U0={class:"cover-preview h-full"},$0={class:"bg-secondary px-[21px] py-[20px] ml-[20px] flex-1 flex flex-col"},G0={class:"pt-[20px] flex-1 overflow-auto"},H0={class:"setting-item flex items-center mb-[16px]"},z0={class:"setting-controls"},V0=Et({__name:"Forth",setup($){const o=q("title"),i=[{label:"标题样式",value:"title",icon:Yv},{label:"字幕样式",value:"subtitles",icon:B0},{label:"背景音乐",value:"music",icon:qv}];ht("isMultiple",!0);const f=Ae("form",{open:1,font:"思源黑体",size:14,aligning:"center",color:"#FFFFFF",text:"",startime:{mm:"00",ss:"00"},endtime:{mm:"00",ss:"00"}}),g=Ae("updateForm",y=>{}),w=q({open:1,font:"思源黑体",size:14,aligning:"center",startime:{mm:"00",ss:"00"},endtime:{mm:"00",ss:"30"},color:"#FFFFFF",text:"",x:0,y:0,transparency:100,style:{}}),b=q({open:1,font:"思源黑体",size:14,aligning:"center",color:"#FFFFFF",text:"",x:0,y:0,transparency:100,style:{}});et(()=>w.value,y=>{g({titleSettings:y})},{deep:!0}),et(()=>b.value,y=>{g({subtitleSettings:y})},{deep:!0});const C=y=>{g({musicSettings:y})},M=y=>{g({coverSettings:y})},E=q(!1),L=q(null),R=Ae("activeStep",0),O=q(null);return et(()=>[f,R.value],([y,S])=>{if(E.value){E.value=!1;return}S===3&&Ur(()=>{E.value=!0,w.value=Object.assign(w.value,y==null?void 0:y.titleSettings),b.value=Object.assign(b.value,y==null?void 0:y.subtitleSettings)})},{deep:!0}),(y,S)=>{const I=ps;return he(),Te("div",N0,[te("div",k0,[te("div",U0,[N(Jv,{image:Ne(f).coverSettings.url,"onUpdate:image":S[0]||(S[0]=v=>Ne(f).coverSettings.url=v),class:"bg-white rounded-[10px] border border-[#E2E6EC] h-full",onChange:M},null,8,["image"])])]),te("div",$0,[N(Vv,{class:"w-full",tabs:i,activeTab:o.value,"onUpdate:activeTab":S[1]||(S[1]=v=>o.value=v)},null,8,["activeTab"]),te("div",G0,[Nt(te("div",null,[te("div",H0,[S[6]||(S[6]=te("div",{class:"setting-label !mb-0 text-[#606266] text-[14px]"},"标题内容：",-1)),te("div",z0,[N(I,{modelValue:w.value.text,"onUpdate:modelValue":S[2]||(S[2]=v=>w.value.text=v),placeholder:"请输入标题内容"},null,8,["modelValue"])])]),N(Qu,{ref:"titleSettingRef","show-time":!0,"show-close":!1,styleMultiple:!1,modelValue:w.value,"onUpdate:modelValue":S[3]||(S[3]=v=>w.value=v)},null,8,["modelValue"])],512),[[kt,o.value==="title"]]),Nt(N(Qu,{class:"subtitle-settings-wrap",ref_key:"subtitleSettingRef",ref:L,"show-time":!1,"show-close":!1,styleMultiple:!1,modelValue:b.value,"onUpdate:modelValue":S[4]||(S[4]=v=>b.value=v)},null,8,["modelValue"]),[[kt,o.value=="subtitles"]]),Nt(N(Zv,{ref_key:"musicSettingRef",ref:O,multi:!1,onChange:C,music:Ne(f).musicSettings,"onUpdate:music":S[5]||(S[5]=v=>Ne(f).musicSettings=v)},null,8,["music"]),[[kt,o.value==="music"]])])])])}}}),ta=Rt(V0,[["__scopeId","data-v-8c6e2bc1"]]);var Un={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */var Z0=Un.exports,na;function Y0(){return na||(na=1,function($,o){(function(){var i,f="4.17.21",g=200,w="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",b="Expected a function",C="Invalid `variable` option passed into `_.template`",M="__lodash_hash_undefined__",E=500,L="__lodash_placeholder__",R=1,O=2,y=4,S=1,I=2,v=1,T=2,D=4,B=8,Z=16,k=32,Q=64,ae=128,U=256,V=512,se=30,Pe="...",ke=800,Xt=16,Ut=1,we=2,$t=3,It=1/0,pt=9007199254740991,zn=17976931348623157e292,Vn=NaN,at=**********,ma=at-1,_a=at>>>1,ya=[["ary",ae],["bind",v],["bindKey",T],["curry",B],["curryRight",Z],["flip",V],["partial",k],["partialRight",Q],["rearg",U]],jt="[object Arguments]",Zn="[object Array]",xa="[object AsyncFunction]",_n="[object Boolean]",yn="[object Date]",wa="[object DOMException]",Yn="[object Error]",qn="[object Function]",gs="[object GeneratorFunction]",tt="[object Map]",xn="[object Number]",ba="[object Null]",gt="[object Object]",vs="[object Promise]",Aa="[object Proxy]",wn="[object RegExp]",nt="[object Set]",bn="[object String]",Jn="[object Symbol]",Ca="[object Undefined]",An="[object WeakMap]",Sa="[object WeakSet]",Cn="[object ArrayBuffer]",en="[object DataView]",zr="[object Float32Array]",Vr="[object Float64Array]",Zr="[object Int8Array]",Yr="[object Int16Array]",qr="[object Int32Array]",Jr="[object Uint8Array]",Qr="[object Uint8ClampedArray]",Kr="[object Uint16Array]",Xr="[object Uint32Array]",Ea=/\b__p \+= '';/g,Ra=/\b(__p \+=) '' \+/g,Ia=/(__e\(.*?\)|\b__t\)) \+\n'';/g,ms=/&(?:amp|lt|gt|quot|#39);/g,_s=/[&<>"']/g,Ta=RegExp(ms.source),La=RegExp(_s.source),Ma=/<%-([\s\S]+?)%>/g,Oa=/<%([\s\S]+?)%>/g,ys=/<%=([\s\S]+?)%>/g,Pa=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Da=/^\w*$/,Wa=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,jr=/[\\^$.*+?()[\]{}|]/g,Fa=RegExp(jr.source),ei=/^\s+/,Ba=/\s/,Na=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,ka=/\{\n\/\* \[wrapped with (.+)\] \*/,Ua=/,? & /,$a=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Ga=/[()=,{}\[\]\/\s]/,Ha=/\\(\\)?/g,za=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,xs=/\w*$/,Va=/^[-+]0x[0-9a-f]+$/i,Za=/^0b[01]+$/i,Ya=/^\[object .+?Constructor\]$/,qa=/^0o[0-7]+$/i,Ja=/^(?:0|[1-9]\d*)$/,Qa=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Qn=/($^)/,Ka=/['\n\r\u2028\u2029\\]/g,Kn="\\ud800-\\udfff",Xa="\\u0300-\\u036f",ja="\\ufe20-\\ufe2f",el="\\u20d0-\\u20ff",ws=Xa+ja+el,bs="\\u2700-\\u27bf",As="a-z\\xdf-\\xf6\\xf8-\\xff",tl="\\xac\\xb1\\xd7\\xf7",nl="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",rl="\\u2000-\\u206f",il=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Cs="A-Z\\xc0-\\xd6\\xd8-\\xde",Ss="\\ufe0e\\ufe0f",Es=tl+nl+rl+il,ti="['’]",sl="["+Kn+"]",Rs="["+Es+"]",Xn="["+ws+"]",Is="\\d+",ol="["+bs+"]",Ts="["+As+"]",Ls="[^"+Kn+Es+Is+bs+As+Cs+"]",ni="\\ud83c[\\udffb-\\udfff]",ul="(?:"+Xn+"|"+ni+")",Ms="[^"+Kn+"]",ri="(?:\\ud83c[\\udde6-\\uddff]){2}",ii="[\\ud800-\\udbff][\\udc00-\\udfff]",tn="["+Cs+"]",Os="\\u200d",Ps="(?:"+Ts+"|"+Ls+")",al="(?:"+tn+"|"+Ls+")",Ds="(?:"+ti+"(?:d|ll|m|re|s|t|ve))?",Ws="(?:"+ti+"(?:D|LL|M|RE|S|T|VE))?",Fs=ul+"?",Bs="["+Ss+"]?",ll="(?:"+Os+"(?:"+[Ms,ri,ii].join("|")+")"+Bs+Fs+")*",cl="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",fl="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Ns=Bs+Fs+ll,hl="(?:"+[ol,ri,ii].join("|")+")"+Ns,dl="(?:"+[Ms+Xn+"?",Xn,ri,ii,sl].join("|")+")",pl=RegExp(ti,"g"),gl=RegExp(Xn,"g"),si=RegExp(ni+"(?="+ni+")|"+dl+Ns,"g"),vl=RegExp([tn+"?"+Ts+"+"+Ds+"(?="+[Rs,tn,"$"].join("|")+")",al+"+"+Ws+"(?="+[Rs,tn+Ps,"$"].join("|")+")",tn+"?"+Ps+"+"+Ds,tn+"+"+Ws,fl,cl,Is,hl].join("|"),"g"),ml=RegExp("["+Os+Kn+ws+Ss+"]"),_l=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,yl=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],xl=-1,de={};de[zr]=de[Vr]=de[Zr]=de[Yr]=de[qr]=de[Jr]=de[Qr]=de[Kr]=de[Xr]=!0,de[jt]=de[Zn]=de[Cn]=de[_n]=de[en]=de[yn]=de[Yn]=de[qn]=de[tt]=de[xn]=de[gt]=de[wn]=de[nt]=de[bn]=de[An]=!1;var fe={};fe[jt]=fe[Zn]=fe[Cn]=fe[en]=fe[_n]=fe[yn]=fe[zr]=fe[Vr]=fe[Zr]=fe[Yr]=fe[qr]=fe[tt]=fe[xn]=fe[gt]=fe[wn]=fe[nt]=fe[bn]=fe[Jn]=fe[Jr]=fe[Qr]=fe[Kr]=fe[Xr]=!0,fe[Yn]=fe[qn]=fe[An]=!1;var wl={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},bl={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Al={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},Cl={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Sl=parseFloat,El=parseInt,ks=typeof Nr=="object"&&Nr&&Nr.Object===Object&&Nr,Rl=typeof self=="object"&&self&&self.Object===Object&&self,Ce=ks||Rl||Function("return this")(),oi=o&&!o.nodeType&&o,Gt=oi&&!0&&$&&!$.nodeType&&$,Us=Gt&&Gt.exports===oi,ui=Us&&ks.process,Ze=function(){try{var h=Gt&&Gt.require&&Gt.require("util").types;return h||ui&&ui.binding&&ui.binding("util")}catch{}}(),$s=Ze&&Ze.isArrayBuffer,Gs=Ze&&Ze.isDate,Hs=Ze&&Ze.isMap,zs=Ze&&Ze.isRegExp,Vs=Ze&&Ze.isSet,Zs=Ze&&Ze.isTypedArray;function Ue(h,m,p){switch(p.length){case 0:return h.call(m);case 1:return h.call(m,p[0]);case 2:return h.call(m,p[0],p[1]);case 3:return h.call(m,p[0],p[1],p[2])}return h.apply(m,p)}function Il(h,m,p,W){for(var Y=-1,oe=h==null?0:h.length;++Y<oe;){var ye=h[Y];m(W,ye,p(ye),h)}return W}function Ye(h,m){for(var p=-1,W=h==null?0:h.length;++p<W&&m(h[p],p,h)!==!1;);return h}function Tl(h,m){for(var p=h==null?0:h.length;p--&&m(h[p],p,h)!==!1;);return h}function Ys(h,m){for(var p=-1,W=h==null?0:h.length;++p<W;)if(!m(h[p],p,h))return!1;return!0}function Tt(h,m){for(var p=-1,W=h==null?0:h.length,Y=0,oe=[];++p<W;){var ye=h[p];m(ye,p,h)&&(oe[Y++]=ye)}return oe}function jn(h,m){var p=h==null?0:h.length;return!!p&&nn(h,m,0)>-1}function ai(h,m,p){for(var W=-1,Y=h==null?0:h.length;++W<Y;)if(p(m,h[W]))return!0;return!1}function pe(h,m){for(var p=-1,W=h==null?0:h.length,Y=Array(W);++p<W;)Y[p]=m(h[p],p,h);return Y}function Lt(h,m){for(var p=-1,W=m.length,Y=h.length;++p<W;)h[Y+p]=m[p];return h}function li(h,m,p,W){var Y=-1,oe=h==null?0:h.length;for(W&&oe&&(p=h[++Y]);++Y<oe;)p=m(p,h[Y],Y,h);return p}function Ll(h,m,p,W){var Y=h==null?0:h.length;for(W&&Y&&(p=h[--Y]);Y--;)p=m(p,h[Y],Y,h);return p}function ci(h,m){for(var p=-1,W=h==null?0:h.length;++p<W;)if(m(h[p],p,h))return!0;return!1}var Ml=fi("length");function Ol(h){return h.split("")}function Pl(h){return h.match($a)||[]}function qs(h,m,p){var W;return p(h,function(Y,oe,ye){if(m(Y,oe,ye))return W=oe,!1}),W}function er(h,m,p,W){for(var Y=h.length,oe=p+(W?1:-1);W?oe--:++oe<Y;)if(m(h[oe],oe,h))return oe;return-1}function nn(h,m,p){return m===m?Vl(h,m,p):er(h,Js,p)}function Dl(h,m,p,W){for(var Y=p-1,oe=h.length;++Y<oe;)if(W(h[Y],m))return Y;return-1}function Js(h){return h!==h}function Qs(h,m){var p=h==null?0:h.length;return p?di(h,m)/p:Vn}function fi(h){return function(m){return m==null?i:m[h]}}function hi(h){return function(m){return h==null?i:h[m]}}function Ks(h,m,p,W,Y){return Y(h,function(oe,ye,ce){p=W?(W=!1,oe):m(p,oe,ye,ce)}),p}function Wl(h,m){var p=h.length;for(h.sort(m);p--;)h[p]=h[p].value;return h}function di(h,m){for(var p,W=-1,Y=h.length;++W<Y;){var oe=m(h[W]);oe!==i&&(p=p===i?oe:p+oe)}return p}function pi(h,m){for(var p=-1,W=Array(h);++p<h;)W[p]=m(p);return W}function Fl(h,m){return pe(m,function(p){return[p,h[p]]})}function Xs(h){return h&&h.slice(0,no(h)+1).replace(ei,"")}function $e(h){return function(m){return h(m)}}function gi(h,m){return pe(m,function(p){return h[p]})}function Sn(h,m){return h.has(m)}function js(h,m){for(var p=-1,W=h.length;++p<W&&nn(m,h[p],0)>-1;);return p}function eo(h,m){for(var p=h.length;p--&&nn(m,h[p],0)>-1;);return p}function Bl(h,m){for(var p=h.length,W=0;p--;)h[p]===m&&++W;return W}var Nl=hi(wl),kl=hi(bl);function Ul(h){return"\\"+Cl[h]}function $l(h,m){return h==null?i:h[m]}function rn(h){return ml.test(h)}function Gl(h){return _l.test(h)}function Hl(h){for(var m,p=[];!(m=h.next()).done;)p.push(m.value);return p}function vi(h){var m=-1,p=Array(h.size);return h.forEach(function(W,Y){p[++m]=[Y,W]}),p}function to(h,m){return function(p){return h(m(p))}}function Mt(h,m){for(var p=-1,W=h.length,Y=0,oe=[];++p<W;){var ye=h[p];(ye===m||ye===L)&&(h[p]=L,oe[Y++]=p)}return oe}function tr(h){var m=-1,p=Array(h.size);return h.forEach(function(W){p[++m]=W}),p}function zl(h){var m=-1,p=Array(h.size);return h.forEach(function(W){p[++m]=[W,W]}),p}function Vl(h,m,p){for(var W=p-1,Y=h.length;++W<Y;)if(h[W]===m)return W;return-1}function Zl(h,m,p){for(var W=p+1;W--;)if(h[W]===m)return W;return W}function sn(h){return rn(h)?ql(h):Ml(h)}function rt(h){return rn(h)?Jl(h):Ol(h)}function no(h){for(var m=h.length;m--&&Ba.test(h.charAt(m)););return m}var Yl=hi(Al);function ql(h){for(var m=si.lastIndex=0;si.test(h);)++m;return m}function Jl(h){return h.match(si)||[]}function Ql(h){return h.match(vl)||[]}var Kl=function h(m){m=m==null?Ce:on.defaults(Ce.Object(),m,on.pick(Ce,yl));var p=m.Array,W=m.Date,Y=m.Error,oe=m.Function,ye=m.Math,ce=m.Object,mi=m.RegExp,Xl=m.String,qe=m.TypeError,nr=p.prototype,jl=oe.prototype,un=ce.prototype,rr=m["__core-js_shared__"],ir=jl.toString,le=un.hasOwnProperty,ec=0,ro=function(){var e=/[^.]+$/.exec(rr&&rr.keys&&rr.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),sr=un.toString,tc=ir.call(ce),nc=Ce._,rc=mi("^"+ir.call(le).replace(jr,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),or=Us?m.Buffer:i,Ot=m.Symbol,ur=m.Uint8Array,io=or?or.allocUnsafe:i,ar=to(ce.getPrototypeOf,ce),so=ce.create,oo=un.propertyIsEnumerable,lr=nr.splice,uo=Ot?Ot.isConcatSpreadable:i,En=Ot?Ot.iterator:i,Ht=Ot?Ot.toStringTag:i,cr=function(){try{var e=qt(ce,"defineProperty");return e({},"",{}),e}catch{}}(),ic=m.clearTimeout!==Ce.clearTimeout&&m.clearTimeout,sc=W&&W.now!==Ce.Date.now&&W.now,oc=m.setTimeout!==Ce.setTimeout&&m.setTimeout,fr=ye.ceil,hr=ye.floor,_i=ce.getOwnPropertySymbols,uc=or?or.isBuffer:i,ao=m.isFinite,ac=nr.join,lc=to(ce.keys,ce),xe=ye.max,Re=ye.min,cc=W.now,fc=m.parseInt,lo=ye.random,hc=nr.reverse,yi=qt(m,"DataView"),Rn=qt(m,"Map"),xi=qt(m,"Promise"),an=qt(m,"Set"),In=qt(m,"WeakMap"),Tn=qt(ce,"create"),dr=In&&new In,ln={},dc=Jt(yi),pc=Jt(Rn),gc=Jt(xi),vc=Jt(an),mc=Jt(In),pr=Ot?Ot.prototype:i,Ln=pr?pr.valueOf:i,co=pr?pr.toString:i;function u(e){if(ve(e)&&!J(e)&&!(e instanceof re)){if(e instanceof Je)return e;if(le.call(e,"__wrapped__"))return hu(e)}return new Je(e)}var cn=function(){function e(){}return function(t){if(!ge(t))return{};if(so)return so(t);e.prototype=t;var n=new e;return e.prototype=i,n}}();function gr(){}function Je(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=i}u.templateSettings={escape:Ma,evaluate:Oa,interpolate:ys,variable:"",imports:{_:u}},u.prototype=gr.prototype,u.prototype.constructor=u,Je.prototype=cn(gr.prototype),Je.prototype.constructor=Je;function re(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=at,this.__views__=[]}function _c(){var e=new re(this.__wrapped__);return e.__actions__=De(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=De(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=De(this.__views__),e}function yc(){if(this.__filtered__){var e=new re(this);e.__dir__=-1,e.__filtered__=!0}else e=this.clone(),e.__dir__*=-1;return e}function xc(){var e=this.__wrapped__.value(),t=this.__dir__,n=J(e),r=t<0,s=n?e.length:0,a=Pf(0,s,this.__views__),l=a.start,c=a.end,d=c-l,_=r?c:l-1,x=this.__iteratees__,A=x.length,P=0,F=Re(d,this.__takeCount__);if(!n||!r&&s==d&&F==d)return Wo(e,this.__actions__);var H=[];e:for(;d--&&P<F;){_+=t;for(var X=-1,z=e[_];++X<A;){var ne=x[X],ie=ne.iteratee,ze=ne.type,Oe=ie(z);if(ze==we)z=Oe;else if(!Oe){if(ze==Ut)continue e;break e}}H[P++]=z}return H}re.prototype=cn(gr.prototype),re.prototype.constructor=re;function zt(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function wc(){this.__data__=Tn?Tn(null):{},this.size=0}function bc(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}function Ac(e){var t=this.__data__;if(Tn){var n=t[e];return n===M?i:n}return le.call(t,e)?t[e]:i}function Cc(e){var t=this.__data__;return Tn?t[e]!==i:le.call(t,e)}function Sc(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Tn&&t===i?M:t,this}zt.prototype.clear=wc,zt.prototype.delete=bc,zt.prototype.get=Ac,zt.prototype.has=Cc,zt.prototype.set=Sc;function vt(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Ec(){this.__data__=[],this.size=0}function Rc(e){var t=this.__data__,n=vr(t,e);if(n<0)return!1;var r=t.length-1;return n==r?t.pop():lr.call(t,n,1),--this.size,!0}function Ic(e){var t=this.__data__,n=vr(t,e);return n<0?i:t[n][1]}function Tc(e){return vr(this.__data__,e)>-1}function Lc(e,t){var n=this.__data__,r=vr(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this}vt.prototype.clear=Ec,vt.prototype.delete=Rc,vt.prototype.get=Ic,vt.prototype.has=Tc,vt.prototype.set=Lc;function mt(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Mc(){this.size=0,this.__data__={hash:new zt,map:new(Rn||vt),string:new zt}}function Oc(e){var t=Ir(this,e).delete(e);return this.size-=t?1:0,t}function Pc(e){return Ir(this,e).get(e)}function Dc(e){return Ir(this,e).has(e)}function Wc(e,t){var n=Ir(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this}mt.prototype.clear=Mc,mt.prototype.delete=Oc,mt.prototype.get=Pc,mt.prototype.has=Dc,mt.prototype.set=Wc;function Vt(e){var t=-1,n=e==null?0:e.length;for(this.__data__=new mt;++t<n;)this.add(e[t])}function Fc(e){return this.__data__.set(e,M),this}function Bc(e){return this.__data__.has(e)}Vt.prototype.add=Vt.prototype.push=Fc,Vt.prototype.has=Bc;function it(e){var t=this.__data__=new vt(e);this.size=t.size}function Nc(){this.__data__=new vt,this.size=0}function kc(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}function Uc(e){return this.__data__.get(e)}function $c(e){return this.__data__.has(e)}function Gc(e,t){var n=this.__data__;if(n instanceof vt){var r=n.__data__;if(!Rn||r.length<g-1)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new mt(r)}return n.set(e,t),this.size=n.size,this}it.prototype.clear=Nc,it.prototype.delete=kc,it.prototype.get=Uc,it.prototype.has=$c,it.prototype.set=Gc;function fo(e,t){var n=J(e),r=!n&&Qt(e),s=!n&&!r&&Bt(e),a=!n&&!r&&!s&&pn(e),l=n||r||s||a,c=l?pi(e.length,Xl):[],d=c.length;for(var _ in e)(t||le.call(e,_))&&!(l&&(_=="length"||s&&(_=="offset"||_=="parent")||a&&(_=="buffer"||_=="byteLength"||_=="byteOffset")||wt(_,d)))&&c.push(_);return c}function ho(e){var t=e.length;return t?e[Mi(0,t-1)]:i}function Hc(e,t){return Tr(De(e),Zt(t,0,e.length))}function zc(e){return Tr(De(e))}function wi(e,t,n){(n!==i&&!st(e[t],n)||n===i&&!(t in e))&&_t(e,t,n)}function Mn(e,t,n){var r=e[t];(!(le.call(e,t)&&st(r,n))||n===i&&!(t in e))&&_t(e,t,n)}function vr(e,t){for(var n=e.length;n--;)if(st(e[n][0],t))return n;return-1}function Vc(e,t,n,r){return Pt(e,function(s,a,l){t(r,s,n(s),l)}),r}function po(e,t){return e&&ct(t,be(t),e)}function Zc(e,t){return e&&ct(t,Fe(t),e)}function _t(e,t,n){t=="__proto__"&&cr?cr(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function bi(e,t){for(var n=-1,r=t.length,s=p(r),a=e==null;++n<r;)s[n]=a?i:ns(e,t[n]);return s}function Zt(e,t,n){return e===e&&(n!==i&&(e=e<=n?e:n),t!==i&&(e=e>=t?e:t)),e}function Qe(e,t,n,r,s,a){var l,c=t&R,d=t&O,_=t&y;if(n&&(l=s?n(e,r,s,a):n(e)),l!==i)return l;if(!ge(e))return e;var x=J(e);if(x){if(l=Wf(e),!c)return De(e,l)}else{var A=Ie(e),P=A==qn||A==gs;if(Bt(e))return No(e,c);if(A==gt||A==jt||P&&!s){if(l=d||P?{}:ru(e),!c)return d?Cf(e,Zc(l,e)):Af(e,po(l,e))}else{if(!fe[A])return s?e:{};l=Ff(e,A,c)}}a||(a=new it);var F=a.get(e);if(F)return F;a.set(e,l),Ou(e)?e.forEach(function(z){l.add(Qe(z,t,n,z,e,a))}):Lu(e)&&e.forEach(function(z,ne){l.set(ne,Qe(z,t,n,ne,e,a))});var H=_?d?Gi:$i:d?Fe:be,X=x?i:H(e);return Ye(X||e,function(z,ne){X&&(ne=z,z=e[ne]),Mn(l,ne,Qe(z,t,n,ne,e,a))}),l}function Yc(e){var t=be(e);return function(n){return go(n,e,t)}}function go(e,t,n){var r=n.length;if(e==null)return!r;for(e=ce(e);r--;){var s=n[r],a=t[s],l=e[s];if(l===i&&!(s in e)||!a(l))return!1}return!0}function vo(e,t,n){if(typeof e!="function")throw new qe(b);return Nn(function(){e.apply(i,n)},t)}function On(e,t,n,r){var s=-1,a=jn,l=!0,c=e.length,d=[],_=t.length;if(!c)return d;n&&(t=pe(t,$e(n))),r?(a=ai,l=!1):t.length>=g&&(a=Sn,l=!1,t=new Vt(t));e:for(;++s<c;){var x=e[s],A=n==null?x:n(x);if(x=r||x!==0?x:0,l&&A===A){for(var P=_;P--;)if(t[P]===A)continue e;d.push(x)}else a(t,A,r)||d.push(x)}return d}var Pt=Ho(lt),mo=Ho(Ci,!0);function qc(e,t){var n=!0;return Pt(e,function(r,s,a){return n=!!t(r,s,a),n}),n}function mr(e,t,n){for(var r=-1,s=e.length;++r<s;){var a=e[r],l=t(a);if(l!=null&&(c===i?l===l&&!He(l):n(l,c)))var c=l,d=a}return d}function Jc(e,t,n,r){var s=e.length;for(n=K(n),n<0&&(n=-n>s?0:s+n),r=r===i||r>s?s:K(r),r<0&&(r+=s),r=n>r?0:Du(r);n<r;)e[n++]=t;return e}function _o(e,t){var n=[];return Pt(e,function(r,s,a){t(r,s,a)&&n.push(r)}),n}function Se(e,t,n,r,s){var a=-1,l=e.length;for(n||(n=Nf),s||(s=[]);++a<l;){var c=e[a];t>0&&n(c)?t>1?Se(c,t-1,n,r,s):Lt(s,c):r||(s[s.length]=c)}return s}var Ai=zo(),yo=zo(!0);function lt(e,t){return e&&Ai(e,t,be)}function Ci(e,t){return e&&yo(e,t,be)}function _r(e,t){return Tt(t,function(n){return bt(e[n])})}function Yt(e,t){t=Wt(t,e);for(var n=0,r=t.length;e!=null&&n<r;)e=e[ft(t[n++])];return n&&n==r?e:i}function xo(e,t,n){var r=t(e);return J(e)?r:Lt(r,n(e))}function Le(e){return e==null?e===i?Ca:ba:Ht&&Ht in ce(e)?Of(e):Vf(e)}function Si(e,t){return e>t}function Qc(e,t){return e!=null&&le.call(e,t)}function Kc(e,t){return e!=null&&t in ce(e)}function Xc(e,t,n){return e>=Re(t,n)&&e<xe(t,n)}function Ei(e,t,n){for(var r=n?ai:jn,s=e[0].length,a=e.length,l=a,c=p(a),d=1/0,_=[];l--;){var x=e[l];l&&t&&(x=pe(x,$e(t))),d=Re(x.length,d),c[l]=!n&&(t||s>=120&&x.length>=120)?new Vt(l&&x):i}x=e[0];var A=-1,P=c[0];e:for(;++A<s&&_.length<d;){var F=x[A],H=t?t(F):F;if(F=n||F!==0?F:0,!(P?Sn(P,H):r(_,H,n))){for(l=a;--l;){var X=c[l];if(!(X?Sn(X,H):r(e[l],H,n)))continue e}P&&P.push(H),_.push(F)}}return _}function jc(e,t,n,r){return lt(e,function(s,a,l){t(r,n(s),a,l)}),r}function Pn(e,t,n){t=Wt(t,e),e=uu(e,t);var r=e==null?e:e[ft(Xe(t))];return r==null?i:Ue(r,e,n)}function wo(e){return ve(e)&&Le(e)==jt}function ef(e){return ve(e)&&Le(e)==Cn}function tf(e){return ve(e)&&Le(e)==yn}function Dn(e,t,n,r,s){return e===t?!0:e==null||t==null||!ve(e)&&!ve(t)?e!==e&&t!==t:nf(e,t,n,r,Dn,s)}function nf(e,t,n,r,s,a){var l=J(e),c=J(t),d=l?Zn:Ie(e),_=c?Zn:Ie(t);d=d==jt?gt:d,_=_==jt?gt:_;var x=d==gt,A=_==gt,P=d==_;if(P&&Bt(e)){if(!Bt(t))return!1;l=!0,x=!1}if(P&&!x)return a||(a=new it),l||pn(e)?eu(e,t,n,r,s,a):Lf(e,t,d,n,r,s,a);if(!(n&S)){var F=x&&le.call(e,"__wrapped__"),H=A&&le.call(t,"__wrapped__");if(F||H){var X=F?e.value():e,z=H?t.value():t;return a||(a=new it),s(X,z,n,r,a)}}return P?(a||(a=new it),Mf(e,t,n,r,s,a)):!1}function rf(e){return ve(e)&&Ie(e)==tt}function Ri(e,t,n,r){var s=n.length,a=s,l=!r;if(e==null)return!a;for(e=ce(e);s--;){var c=n[s];if(l&&c[2]?c[1]!==e[c[0]]:!(c[0]in e))return!1}for(;++s<a;){c=n[s];var d=c[0],_=e[d],x=c[1];if(l&&c[2]){if(_===i&&!(d in e))return!1}else{var A=new it;if(r)var P=r(_,x,d,e,t,A);if(!(P===i?Dn(x,_,S|I,r,A):P))return!1}}return!0}function bo(e){if(!ge(e)||Uf(e))return!1;var t=bt(e)?rc:Ya;return t.test(Jt(e))}function sf(e){return ve(e)&&Le(e)==wn}function of(e){return ve(e)&&Ie(e)==nt}function uf(e){return ve(e)&&Wr(e.length)&&!!de[Le(e)]}function Ao(e){return typeof e=="function"?e:e==null?Be:typeof e=="object"?J(e)?Eo(e[0],e[1]):So(e):Vu(e)}function Ii(e){if(!Bn(e))return lc(e);var t=[];for(var n in ce(e))le.call(e,n)&&n!="constructor"&&t.push(n);return t}function af(e){if(!ge(e))return zf(e);var t=Bn(e),n=[];for(var r in e)r=="constructor"&&(t||!le.call(e,r))||n.push(r);return n}function Ti(e,t){return e<t}function Co(e,t){var n=-1,r=We(e)?p(e.length):[];return Pt(e,function(s,a,l){r[++n]=t(s,a,l)}),r}function So(e){var t=zi(e);return t.length==1&&t[0][2]?su(t[0][0],t[0][1]):function(n){return n===e||Ri(n,e,t)}}function Eo(e,t){return Zi(e)&&iu(t)?su(ft(e),t):function(n){var r=ns(n,e);return r===i&&r===t?rs(n,e):Dn(t,r,S|I)}}function yr(e,t,n,r,s){e!==t&&Ai(t,function(a,l){if(s||(s=new it),ge(a))lf(e,t,l,n,yr,r,s);else{var c=r?r(qi(e,l),a,l+"",e,t,s):i;c===i&&(c=a),wi(e,l,c)}},Fe)}function lf(e,t,n,r,s,a,l){var c=qi(e,n),d=qi(t,n),_=l.get(d);if(_){wi(e,n,_);return}var x=a?a(c,d,n+"",e,t,l):i,A=x===i;if(A){var P=J(d),F=!P&&Bt(d),H=!P&&!F&&pn(d);x=d,P||F||H?J(c)?x=c:me(c)?x=De(c):F?(A=!1,x=No(d,!0)):H?(A=!1,x=ko(d,!0)):x=[]:kn(d)||Qt(d)?(x=c,Qt(c)?x=Wu(c):(!ge(c)||bt(c))&&(x=ru(d))):A=!1}A&&(l.set(d,x),s(x,d,r,a,l),l.delete(d)),wi(e,n,x)}function Ro(e,t){var n=e.length;if(n)return t+=t<0?n:0,wt(t,n)?e[t]:i}function Io(e,t,n){t.length?t=pe(t,function(a){return J(a)?function(l){return Yt(l,a.length===1?a[0]:a)}:a}):t=[Be];var r=-1;t=pe(t,$e(G()));var s=Co(e,function(a,l,c){var d=pe(t,function(_){return _(a)});return{criteria:d,index:++r,value:a}});return Wl(s,function(a,l){return bf(a,l,n)})}function cf(e,t){return To(e,t,function(n,r){return rs(e,r)})}function To(e,t,n){for(var r=-1,s=t.length,a={};++r<s;){var l=t[r],c=Yt(e,l);n(c,l)&&Wn(a,Wt(l,e),c)}return a}function ff(e){return function(t){return Yt(t,e)}}function Li(e,t,n,r){var s=r?Dl:nn,a=-1,l=t.length,c=e;for(e===t&&(t=De(t)),n&&(c=pe(e,$e(n)));++a<l;)for(var d=0,_=t[a],x=n?n(_):_;(d=s(c,x,d,r))>-1;)c!==e&&lr.call(c,d,1),lr.call(e,d,1);return e}function Lo(e,t){for(var n=e?t.length:0,r=n-1;n--;){var s=t[n];if(n==r||s!==a){var a=s;wt(s)?lr.call(e,s,1):Di(e,s)}}return e}function Mi(e,t){return e+hr(lo()*(t-e+1))}function hf(e,t,n,r){for(var s=-1,a=xe(fr((t-e)/(n||1)),0),l=p(a);a--;)l[r?a:++s]=e,e+=n;return l}function Oi(e,t){var n="";if(!e||t<1||t>pt)return n;do t%2&&(n+=e),t=hr(t/2),t&&(e+=e);while(t);return n}function ee(e,t){return Ji(ou(e,t,Be),e+"")}function df(e){return ho(gn(e))}function pf(e,t){var n=gn(e);return Tr(n,Zt(t,0,n.length))}function Wn(e,t,n,r){if(!ge(e))return e;t=Wt(t,e);for(var s=-1,a=t.length,l=a-1,c=e;c!=null&&++s<a;){var d=ft(t[s]),_=n;if(d==="__proto__"||d==="constructor"||d==="prototype")return e;if(s!=l){var x=c[d];_=r?r(x,d,c):i,_===i&&(_=ge(x)?x:wt(t[s+1])?[]:{})}Mn(c,d,_),c=c[d]}return e}var Mo=dr?function(e,t){return dr.set(e,t),e}:Be,gf=cr?function(e,t){return cr(e,"toString",{configurable:!0,enumerable:!1,value:ss(t),writable:!0})}:Be;function vf(e){return Tr(gn(e))}function Ke(e,t,n){var r=-1,s=e.length;t<0&&(t=-t>s?0:s+t),n=n>s?s:n,n<0&&(n+=s),s=t>n?0:n-t>>>0,t>>>=0;for(var a=p(s);++r<s;)a[r]=e[r+t];return a}function mf(e,t){var n;return Pt(e,function(r,s,a){return n=t(r,s,a),!n}),!!n}function xr(e,t,n){var r=0,s=e==null?r:e.length;if(typeof t=="number"&&t===t&&s<=_a){for(;r<s;){var a=r+s>>>1,l=e[a];l!==null&&!He(l)&&(n?l<=t:l<t)?r=a+1:s=a}return s}return Pi(e,t,Be,n)}function Pi(e,t,n,r){var s=0,a=e==null?0:e.length;if(a===0)return 0;t=n(t);for(var l=t!==t,c=t===null,d=He(t),_=t===i;s<a;){var x=hr((s+a)/2),A=n(e[x]),P=A!==i,F=A===null,H=A===A,X=He(A);if(l)var z=r||H;else _?z=H&&(r||P):c?z=H&&P&&(r||!F):d?z=H&&P&&!F&&(r||!X):F||X?z=!1:z=r?A<=t:A<t;z?s=x+1:a=x}return Re(a,ma)}function Oo(e,t){for(var n=-1,r=e.length,s=0,a=[];++n<r;){var l=e[n],c=t?t(l):l;if(!n||!st(c,d)){var d=c;a[s++]=l===0?0:l}}return a}function Po(e){return typeof e=="number"?e:He(e)?Vn:+e}function Ge(e){if(typeof e=="string")return e;if(J(e))return pe(e,Ge)+"";if(He(e))return co?co.call(e):"";var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}function Dt(e,t,n){var r=-1,s=jn,a=e.length,l=!0,c=[],d=c;if(n)l=!1,s=ai;else if(a>=g){var _=t?null:If(e);if(_)return tr(_);l=!1,s=Sn,d=new Vt}else d=t?[]:c;e:for(;++r<a;){var x=e[r],A=t?t(x):x;if(x=n||x!==0?x:0,l&&A===A){for(var P=d.length;P--;)if(d[P]===A)continue e;t&&d.push(A),c.push(x)}else s(d,A,n)||(d!==c&&d.push(A),c.push(x))}return c}function Di(e,t){return t=Wt(t,e),e=uu(e,t),e==null||delete e[ft(Xe(t))]}function Do(e,t,n,r){return Wn(e,t,n(Yt(e,t)),r)}function wr(e,t,n,r){for(var s=e.length,a=r?s:-1;(r?a--:++a<s)&&t(e[a],a,e););return n?Ke(e,r?0:a,r?a+1:s):Ke(e,r?a+1:0,r?s:a)}function Wo(e,t){var n=e;return n instanceof re&&(n=n.value()),li(t,function(r,s){return s.func.apply(s.thisArg,Lt([r],s.args))},n)}function Wi(e,t,n){var r=e.length;if(r<2)return r?Dt(e[0]):[];for(var s=-1,a=p(r);++s<r;)for(var l=e[s],c=-1;++c<r;)c!=s&&(a[s]=On(a[s]||l,e[c],t,n));return Dt(Se(a,1),t,n)}function Fo(e,t,n){for(var r=-1,s=e.length,a=t.length,l={};++r<s;){var c=r<a?t[r]:i;n(l,e[r],c)}return l}function Fi(e){return me(e)?e:[]}function Bi(e){return typeof e=="function"?e:Be}function Wt(e,t){return J(e)?e:Zi(e,t)?[e]:fu(ue(e))}var _f=ee;function Ft(e,t,n){var r=e.length;return n=n===i?r:n,!t&&n>=r?e:Ke(e,t,n)}var Bo=ic||function(e){return Ce.clearTimeout(e)};function No(e,t){if(t)return e.slice();var n=e.length,r=io?io(n):new e.constructor(n);return e.copy(r),r}function Ni(e){var t=new e.constructor(e.byteLength);return new ur(t).set(new ur(e)),t}function yf(e,t){var n=t?Ni(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}function xf(e){var t=new e.constructor(e.source,xs.exec(e));return t.lastIndex=e.lastIndex,t}function wf(e){return Ln?ce(Ln.call(e)):{}}function ko(e,t){var n=t?Ni(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}function Uo(e,t){if(e!==t){var n=e!==i,r=e===null,s=e===e,a=He(e),l=t!==i,c=t===null,d=t===t,_=He(t);if(!c&&!_&&!a&&e>t||a&&l&&d&&!c&&!_||r&&l&&d||!n&&d||!s)return 1;if(!r&&!a&&!_&&e<t||_&&n&&s&&!r&&!a||c&&n&&s||!l&&s||!d)return-1}return 0}function bf(e,t,n){for(var r=-1,s=e.criteria,a=t.criteria,l=s.length,c=n.length;++r<l;){var d=Uo(s[r],a[r]);if(d){if(r>=c)return d;var _=n[r];return d*(_=="desc"?-1:1)}}return e.index-t.index}function $o(e,t,n,r){for(var s=-1,a=e.length,l=n.length,c=-1,d=t.length,_=xe(a-l,0),x=p(d+_),A=!r;++c<d;)x[c]=t[c];for(;++s<l;)(A||s<a)&&(x[n[s]]=e[s]);for(;_--;)x[c++]=e[s++];return x}function Go(e,t,n,r){for(var s=-1,a=e.length,l=-1,c=n.length,d=-1,_=t.length,x=xe(a-c,0),A=p(x+_),P=!r;++s<x;)A[s]=e[s];for(var F=s;++d<_;)A[F+d]=t[d];for(;++l<c;)(P||s<a)&&(A[F+n[l]]=e[s++]);return A}function De(e,t){var n=-1,r=e.length;for(t||(t=p(r));++n<r;)t[n]=e[n];return t}function ct(e,t,n,r){var s=!n;n||(n={});for(var a=-1,l=t.length;++a<l;){var c=t[a],d=r?r(n[c],e[c],c,n,e):i;d===i&&(d=e[c]),s?_t(n,c,d):Mn(n,c,d)}return n}function Af(e,t){return ct(e,Vi(e),t)}function Cf(e,t){return ct(e,tu(e),t)}function br(e,t){return function(n,r){var s=J(n)?Il:Vc,a=t?t():{};return s(n,e,G(r,2),a)}}function fn(e){return ee(function(t,n){var r=-1,s=n.length,a=s>1?n[s-1]:i,l=s>2?n[2]:i;for(a=e.length>3&&typeof a=="function"?(s--,a):i,l&&Me(n[0],n[1],l)&&(a=s<3?i:a,s=1),t=ce(t);++r<s;){var c=n[r];c&&e(t,c,r,a)}return t})}function Ho(e,t){return function(n,r){if(n==null)return n;if(!We(n))return e(n,r);for(var s=n.length,a=t?s:-1,l=ce(n);(t?a--:++a<s)&&r(l[a],a,l)!==!1;);return n}}function zo(e){return function(t,n,r){for(var s=-1,a=ce(t),l=r(t),c=l.length;c--;){var d=l[e?c:++s];if(n(a[d],d,a)===!1)break}return t}}function Sf(e,t,n){var r=t&v,s=Fn(e);function a(){var l=this&&this!==Ce&&this instanceof a?s:e;return l.apply(r?n:this,arguments)}return a}function Vo(e){return function(t){t=ue(t);var n=rn(t)?rt(t):i,r=n?n[0]:t.charAt(0),s=n?Ft(n,1).join(""):t.slice(1);return r[e]()+s}}function hn(e){return function(t){return li(Hu(Gu(t).replace(pl,"")),e,"")}}function Fn(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=cn(e.prototype),r=e.apply(n,t);return ge(r)?r:n}}function Ef(e,t,n){var r=Fn(e);function s(){for(var a=arguments.length,l=p(a),c=a,d=dn(s);c--;)l[c]=arguments[c];var _=a<3&&l[0]!==d&&l[a-1]!==d?[]:Mt(l,d);if(a-=_.length,a<n)return Qo(e,t,Ar,s.placeholder,i,l,_,i,i,n-a);var x=this&&this!==Ce&&this instanceof s?r:e;return Ue(x,this,l)}return s}function Zo(e){return function(t,n,r){var s=ce(t);if(!We(t)){var a=G(n,3);t=be(t),n=function(c){return a(s[c],c,s)}}var l=e(t,n,r);return l>-1?s[a?t[l]:l]:i}}function Yo(e){return xt(function(t){var n=t.length,r=n,s=Je.prototype.thru;for(e&&t.reverse();r--;){var a=t[r];if(typeof a!="function")throw new qe(b);if(s&&!l&&Rr(a)=="wrapper")var l=new Je([],!0)}for(r=l?r:n;++r<n;){a=t[r];var c=Rr(a),d=c=="wrapper"?Hi(a):i;d&&Yi(d[0])&&d[1]==(ae|B|k|U)&&!d[4].length&&d[9]==1?l=l[Rr(d[0])].apply(l,d[3]):l=a.length==1&&Yi(a)?l[c]():l.thru(a)}return function(){var _=arguments,x=_[0];if(l&&_.length==1&&J(x))return l.plant(x).value();for(var A=0,P=n?t[A].apply(this,_):x;++A<n;)P=t[A].call(this,P);return P}})}function Ar(e,t,n,r,s,a,l,c,d,_){var x=t&ae,A=t&v,P=t&T,F=t&(B|Z),H=t&V,X=P?i:Fn(e);function z(){for(var ne=arguments.length,ie=p(ne),ze=ne;ze--;)ie[ze]=arguments[ze];if(F)var Oe=dn(z),Ve=Bl(ie,Oe);if(r&&(ie=$o(ie,r,s,F)),a&&(ie=Go(ie,a,l,F)),ne-=Ve,F&&ne<_){var _e=Mt(ie,Oe);return Qo(e,t,Ar,z.placeholder,n,ie,_e,c,d,_-ne)}var ot=A?n:this,Ct=P?ot[e]:e;return ne=ie.length,c?ie=Zf(ie,c):H&&ne>1&&ie.reverse(),x&&d<ne&&(ie.length=d),this&&this!==Ce&&this instanceof z&&(Ct=X||Fn(Ct)),Ct.apply(ot,ie)}return z}function qo(e,t){return function(n,r){return jc(n,e,t(r),{})}}function Cr(e,t){return function(n,r){var s;if(n===i&&r===i)return t;if(n!==i&&(s=n),r!==i){if(s===i)return r;typeof n=="string"||typeof r=="string"?(n=Ge(n),r=Ge(r)):(n=Po(n),r=Po(r)),s=e(n,r)}return s}}function ki(e){return xt(function(t){return t=pe(t,$e(G())),ee(function(n){var r=this;return e(t,function(s){return Ue(s,r,n)})})})}function Sr(e,t){t=t===i?" ":Ge(t);var n=t.length;if(n<2)return n?Oi(t,e):t;var r=Oi(t,fr(e/sn(t)));return rn(t)?Ft(rt(r),0,e).join(""):r.slice(0,e)}function Rf(e,t,n,r){var s=t&v,a=Fn(e);function l(){for(var c=-1,d=arguments.length,_=-1,x=r.length,A=p(x+d),P=this&&this!==Ce&&this instanceof l?a:e;++_<x;)A[_]=r[_];for(;d--;)A[_++]=arguments[++c];return Ue(P,s?n:this,A)}return l}function Jo(e){return function(t,n,r){return r&&typeof r!="number"&&Me(t,n,r)&&(n=r=i),t=At(t),n===i?(n=t,t=0):n=At(n),r=r===i?t<n?1:-1:At(r),hf(t,n,r,e)}}function Er(e){return function(t,n){return typeof t=="string"&&typeof n=="string"||(t=je(t),n=je(n)),e(t,n)}}function Qo(e,t,n,r,s,a,l,c,d,_){var x=t&B,A=x?l:i,P=x?i:l,F=x?a:i,H=x?i:a;t|=x?k:Q,t&=~(x?Q:k),t&D||(t&=-4);var X=[e,t,s,F,A,H,P,c,d,_],z=n.apply(i,X);return Yi(e)&&au(z,X),z.placeholder=r,lu(z,e,t)}function Ui(e){var t=ye[e];return function(n,r){if(n=je(n),r=r==null?0:Re(K(r),292),r&&ao(n)){var s=(ue(n)+"e").split("e"),a=t(s[0]+"e"+(+s[1]+r));return s=(ue(a)+"e").split("e"),+(s[0]+"e"+(+s[1]-r))}return t(n)}}var If=an&&1/tr(new an([,-0]))[1]==It?function(e){return new an(e)}:as;function Ko(e){return function(t){var n=Ie(t);return n==tt?vi(t):n==nt?zl(t):Fl(t,e(t))}}function yt(e,t,n,r,s,a,l,c){var d=t&T;if(!d&&typeof e!="function")throw new qe(b);var _=r?r.length:0;if(_||(t&=-97,r=s=i),l=l===i?l:xe(K(l),0),c=c===i?c:K(c),_-=s?s.length:0,t&Q){var x=r,A=s;r=s=i}var P=d?i:Hi(e),F=[e,t,n,r,s,x,A,a,l,c];if(P&&Hf(F,P),e=F[0],t=F[1],n=F[2],r=F[3],s=F[4],c=F[9]=F[9]===i?d?0:e.length:xe(F[9]-_,0),!c&&t&(B|Z)&&(t&=-25),!t||t==v)var H=Sf(e,t,n);else t==B||t==Z?H=Ef(e,t,c):(t==k||t==(v|k))&&!s.length?H=Rf(e,t,n,r):H=Ar.apply(i,F);var X=P?Mo:au;return lu(X(H,F),e,t)}function Xo(e,t,n,r){return e===i||st(e,un[n])&&!le.call(r,n)?t:e}function jo(e,t,n,r,s,a){return ge(e)&&ge(t)&&(a.set(t,e),yr(e,t,i,jo,a),a.delete(t)),e}function Tf(e){return kn(e)?i:e}function eu(e,t,n,r,s,a){var l=n&S,c=e.length,d=t.length;if(c!=d&&!(l&&d>c))return!1;var _=a.get(e),x=a.get(t);if(_&&x)return _==t&&x==e;var A=-1,P=!0,F=n&I?new Vt:i;for(a.set(e,t),a.set(t,e);++A<c;){var H=e[A],X=t[A];if(r)var z=l?r(X,H,A,t,e,a):r(H,X,A,e,t,a);if(z!==i){if(z)continue;P=!1;break}if(F){if(!ci(t,function(ne,ie){if(!Sn(F,ie)&&(H===ne||s(H,ne,n,r,a)))return F.push(ie)})){P=!1;break}}else if(!(H===X||s(H,X,n,r,a))){P=!1;break}}return a.delete(e),a.delete(t),P}function Lf(e,t,n,r,s,a,l){switch(n){case en:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case Cn:return!(e.byteLength!=t.byteLength||!a(new ur(e),new ur(t)));case _n:case yn:case xn:return st(+e,+t);case Yn:return e.name==t.name&&e.message==t.message;case wn:case bn:return e==t+"";case tt:var c=vi;case nt:var d=r&S;if(c||(c=tr),e.size!=t.size&&!d)return!1;var _=l.get(e);if(_)return _==t;r|=I,l.set(e,t);var x=eu(c(e),c(t),r,s,a,l);return l.delete(e),x;case Jn:if(Ln)return Ln.call(e)==Ln.call(t)}return!1}function Mf(e,t,n,r,s,a){var l=n&S,c=$i(e),d=c.length,_=$i(t),x=_.length;if(d!=x&&!l)return!1;for(var A=d;A--;){var P=c[A];if(!(l?P in t:le.call(t,P)))return!1}var F=a.get(e),H=a.get(t);if(F&&H)return F==t&&H==e;var X=!0;a.set(e,t),a.set(t,e);for(var z=l;++A<d;){P=c[A];var ne=e[P],ie=t[P];if(r)var ze=l?r(ie,ne,P,t,e,a):r(ne,ie,P,e,t,a);if(!(ze===i?ne===ie||s(ne,ie,n,r,a):ze)){X=!1;break}z||(z=P=="constructor")}if(X&&!z){var Oe=e.constructor,Ve=t.constructor;Oe!=Ve&&"constructor"in e&&"constructor"in t&&!(typeof Oe=="function"&&Oe instanceof Oe&&typeof Ve=="function"&&Ve instanceof Ve)&&(X=!1)}return a.delete(e),a.delete(t),X}function xt(e){return Ji(ou(e,i,gu),e+"")}function $i(e){return xo(e,be,Vi)}function Gi(e){return xo(e,Fe,tu)}var Hi=dr?function(e){return dr.get(e)}:as;function Rr(e){for(var t=e.name+"",n=ln[t],r=le.call(ln,t)?n.length:0;r--;){var s=n[r],a=s.func;if(a==null||a==e)return s.name}return t}function dn(e){var t=le.call(u,"placeholder")?u:e;return t.placeholder}function G(){var e=u.iteratee||os;return e=e===os?Ao:e,arguments.length?e(arguments[0],arguments[1]):e}function Ir(e,t){var n=e.__data__;return kf(t)?n[typeof t=="string"?"string":"hash"]:n.map}function zi(e){for(var t=be(e),n=t.length;n--;){var r=t[n],s=e[r];t[n]=[r,s,iu(s)]}return t}function qt(e,t){var n=$l(e,t);return bo(n)?n:i}function Of(e){var t=le.call(e,Ht),n=e[Ht];try{e[Ht]=i;var r=!0}catch{}var s=sr.call(e);return r&&(t?e[Ht]=n:delete e[Ht]),s}var Vi=_i?function(e){return e==null?[]:(e=ce(e),Tt(_i(e),function(t){return oo.call(e,t)}))}:ls,tu=_i?function(e){for(var t=[];e;)Lt(t,Vi(e)),e=ar(e);return t}:ls,Ie=Le;(yi&&Ie(new yi(new ArrayBuffer(1)))!=en||Rn&&Ie(new Rn)!=tt||xi&&Ie(xi.resolve())!=vs||an&&Ie(new an)!=nt||In&&Ie(new In)!=An)&&(Ie=function(e){var t=Le(e),n=t==gt?e.constructor:i,r=n?Jt(n):"";if(r)switch(r){case dc:return en;case pc:return tt;case gc:return vs;case vc:return nt;case mc:return An}return t});function Pf(e,t,n){for(var r=-1,s=n.length;++r<s;){var a=n[r],l=a.size;switch(a.type){case"drop":e+=l;break;case"dropRight":t-=l;break;case"take":t=Re(t,e+l);break;case"takeRight":e=xe(e,t-l);break}}return{start:e,end:t}}function Df(e){var t=e.match(ka);return t?t[1].split(Ua):[]}function nu(e,t,n){t=Wt(t,e);for(var r=-1,s=t.length,a=!1;++r<s;){var l=ft(t[r]);if(!(a=e!=null&&n(e,l)))break;e=e[l]}return a||++r!=s?a:(s=e==null?0:e.length,!!s&&Wr(s)&&wt(l,s)&&(J(e)||Qt(e)))}function Wf(e){var t=e.length,n=new e.constructor(t);return t&&typeof e[0]=="string"&&le.call(e,"index")&&(n.index=e.index,n.input=e.input),n}function ru(e){return typeof e.constructor=="function"&&!Bn(e)?cn(ar(e)):{}}function Ff(e,t,n){var r=e.constructor;switch(t){case Cn:return Ni(e);case _n:case yn:return new r(+e);case en:return yf(e,n);case zr:case Vr:case Zr:case Yr:case qr:case Jr:case Qr:case Kr:case Xr:return ko(e,n);case tt:return new r;case xn:case bn:return new r(e);case wn:return xf(e);case nt:return new r;case Jn:return wf(e)}}function Bf(e,t){var n=t.length;if(!n)return e;var r=n-1;return t[r]=(n>1?"& ":"")+t[r],t=t.join(n>2?", ":" "),e.replace(Na,`{
/* [wrapped with `+t+`] */
`)}function Nf(e){return J(e)||Qt(e)||!!(uo&&e&&e[uo])}function wt(e,t){var n=typeof e;return t=t??pt,!!t&&(n=="number"||n!="symbol"&&Ja.test(e))&&e>-1&&e%1==0&&e<t}function Me(e,t,n){if(!ge(n))return!1;var r=typeof t;return(r=="number"?We(n)&&wt(t,n.length):r=="string"&&t in n)?st(n[t],e):!1}function Zi(e,t){if(J(e))return!1;var n=typeof e;return n=="number"||n=="symbol"||n=="boolean"||e==null||He(e)?!0:Da.test(e)||!Pa.test(e)||t!=null&&e in ce(t)}function kf(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function Yi(e){var t=Rr(e),n=u[t];if(typeof n!="function"||!(t in re.prototype))return!1;if(e===n)return!0;var r=Hi(n);return!!r&&e===r[0]}function Uf(e){return!!ro&&ro in e}var $f=rr?bt:cs;function Bn(e){var t=e&&e.constructor,n=typeof t=="function"&&t.prototype||un;return e===n}function iu(e){return e===e&&!ge(e)}function su(e,t){return function(n){return n==null?!1:n[e]===t&&(t!==i||e in ce(n))}}function Gf(e){var t=Pr(e,function(r){return n.size===E&&n.clear(),r}),n=t.cache;return t}function Hf(e,t){var n=e[1],r=t[1],s=n|r,a=s<(v|T|ae),l=r==ae&&n==B||r==ae&&n==U&&e[7].length<=t[8]||r==(ae|U)&&t[7].length<=t[8]&&n==B;if(!(a||l))return e;r&v&&(e[2]=t[2],s|=n&v?0:D);var c=t[3];if(c){var d=e[3];e[3]=d?$o(d,c,t[4]):c,e[4]=d?Mt(e[3],L):t[4]}return c=t[5],c&&(d=e[5],e[5]=d?Go(d,c,t[6]):c,e[6]=d?Mt(e[5],L):t[6]),c=t[7],c&&(e[7]=c),r&ae&&(e[8]=e[8]==null?t[8]:Re(e[8],t[8])),e[9]==null&&(e[9]=t[9]),e[0]=t[0],e[1]=s,e}function zf(e){var t=[];if(e!=null)for(var n in ce(e))t.push(n);return t}function Vf(e){return sr.call(e)}function ou(e,t,n){return t=xe(t===i?e.length-1:t,0),function(){for(var r=arguments,s=-1,a=xe(r.length-t,0),l=p(a);++s<a;)l[s]=r[t+s];s=-1;for(var c=p(t+1);++s<t;)c[s]=r[s];return c[t]=n(l),Ue(e,this,c)}}function uu(e,t){return t.length<2?e:Yt(e,Ke(t,0,-1))}function Zf(e,t){for(var n=e.length,r=Re(t.length,n),s=De(e);r--;){var a=t[r];e[r]=wt(a,n)?s[a]:i}return e}function qi(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}var au=cu(Mo),Nn=oc||function(e,t){return Ce.setTimeout(e,t)},Ji=cu(gf);function lu(e,t,n){var r=t+"";return Ji(e,Bf(r,Yf(Df(r),n)))}function cu(e){var t=0,n=0;return function(){var r=cc(),s=Xt-(r-n);if(n=r,s>0){if(++t>=ke)return arguments[0]}else t=0;return e.apply(i,arguments)}}function Tr(e,t){var n=-1,r=e.length,s=r-1;for(t=t===i?r:t;++n<t;){var a=Mi(n,s),l=e[a];e[a]=e[n],e[n]=l}return e.length=t,e}var fu=Gf(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(Wa,function(n,r,s,a){t.push(s?a.replace(Ha,"$1"):r||n)}),t});function ft(e){if(typeof e=="string"||He(e))return e;var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}function Jt(e){if(e!=null){try{return ir.call(e)}catch{}try{return e+""}catch{}}return""}function Yf(e,t){return Ye(ya,function(n){var r="_."+n[0];t&n[1]&&!jn(e,r)&&e.push(r)}),e.sort()}function hu(e){if(e instanceof re)return e.clone();var t=new Je(e.__wrapped__,e.__chain__);return t.__actions__=De(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}function qf(e,t,n){(n?Me(e,t,n):t===i)?t=1:t=xe(K(t),0);var r=e==null?0:e.length;if(!r||t<1)return[];for(var s=0,a=0,l=p(fr(r/t));s<r;)l[a++]=Ke(e,s,s+=t);return l}function Jf(e){for(var t=-1,n=e==null?0:e.length,r=0,s=[];++t<n;){var a=e[t];a&&(s[r++]=a)}return s}function Qf(){var e=arguments.length;if(!e)return[];for(var t=p(e-1),n=arguments[0],r=e;r--;)t[r-1]=arguments[r];return Lt(J(n)?De(n):[n],Se(t,1))}var Kf=ee(function(e,t){return me(e)?On(e,Se(t,1,me,!0)):[]}),Xf=ee(function(e,t){var n=Xe(t);return me(n)&&(n=i),me(e)?On(e,Se(t,1,me,!0),G(n,2)):[]}),jf=ee(function(e,t){var n=Xe(t);return me(n)&&(n=i),me(e)?On(e,Se(t,1,me,!0),i,n):[]});function eh(e,t,n){var r=e==null?0:e.length;return r?(t=n||t===i?1:K(t),Ke(e,t<0?0:t,r)):[]}function th(e,t,n){var r=e==null?0:e.length;return r?(t=n||t===i?1:K(t),t=r-t,Ke(e,0,t<0?0:t)):[]}function nh(e,t){return e&&e.length?wr(e,G(t,3),!0,!0):[]}function rh(e,t){return e&&e.length?wr(e,G(t,3),!0):[]}function ih(e,t,n,r){var s=e==null?0:e.length;return s?(n&&typeof n!="number"&&Me(e,t,n)&&(n=0,r=s),Jc(e,t,n,r)):[]}function du(e,t,n){var r=e==null?0:e.length;if(!r)return-1;var s=n==null?0:K(n);return s<0&&(s=xe(r+s,0)),er(e,G(t,3),s)}function pu(e,t,n){var r=e==null?0:e.length;if(!r)return-1;var s=r-1;return n!==i&&(s=K(n),s=n<0?xe(r+s,0):Re(s,r-1)),er(e,G(t,3),s,!0)}function gu(e){var t=e==null?0:e.length;return t?Se(e,1):[]}function sh(e){var t=e==null?0:e.length;return t?Se(e,It):[]}function oh(e,t){var n=e==null?0:e.length;return n?(t=t===i?1:K(t),Se(e,t)):[]}function uh(e){for(var t=-1,n=e==null?0:e.length,r={};++t<n;){var s=e[t];r[s[0]]=s[1]}return r}function vu(e){return e&&e.length?e[0]:i}function ah(e,t,n){var r=e==null?0:e.length;if(!r)return-1;var s=n==null?0:K(n);return s<0&&(s=xe(r+s,0)),nn(e,t,s)}function lh(e){var t=e==null?0:e.length;return t?Ke(e,0,-1):[]}var ch=ee(function(e){var t=pe(e,Fi);return t.length&&t[0]===e[0]?Ei(t):[]}),fh=ee(function(e){var t=Xe(e),n=pe(e,Fi);return t===Xe(n)?t=i:n.pop(),n.length&&n[0]===e[0]?Ei(n,G(t,2)):[]}),hh=ee(function(e){var t=Xe(e),n=pe(e,Fi);return t=typeof t=="function"?t:i,t&&n.pop(),n.length&&n[0]===e[0]?Ei(n,i,t):[]});function dh(e,t){return e==null?"":ac.call(e,t)}function Xe(e){var t=e==null?0:e.length;return t?e[t-1]:i}function ph(e,t,n){var r=e==null?0:e.length;if(!r)return-1;var s=r;return n!==i&&(s=K(n),s=s<0?xe(r+s,0):Re(s,r-1)),t===t?Zl(e,t,s):er(e,Js,s,!0)}function gh(e,t){return e&&e.length?Ro(e,K(t)):i}var vh=ee(mu);function mu(e,t){return e&&e.length&&t&&t.length?Li(e,t):e}function mh(e,t,n){return e&&e.length&&t&&t.length?Li(e,t,G(n,2)):e}function _h(e,t,n){return e&&e.length&&t&&t.length?Li(e,t,i,n):e}var yh=xt(function(e,t){var n=e==null?0:e.length,r=bi(e,t);return Lo(e,pe(t,function(s){return wt(s,n)?+s:s}).sort(Uo)),r});function xh(e,t){var n=[];if(!(e&&e.length))return n;var r=-1,s=[],a=e.length;for(t=G(t,3);++r<a;){var l=e[r];t(l,r,e)&&(n.push(l),s.push(r))}return Lo(e,s),n}function Qi(e){return e==null?e:hc.call(e)}function wh(e,t,n){var r=e==null?0:e.length;return r?(n&&typeof n!="number"&&Me(e,t,n)?(t=0,n=r):(t=t==null?0:K(t),n=n===i?r:K(n)),Ke(e,t,n)):[]}function bh(e,t){return xr(e,t)}function Ah(e,t,n){return Pi(e,t,G(n,2))}function Ch(e,t){var n=e==null?0:e.length;if(n){var r=xr(e,t);if(r<n&&st(e[r],t))return r}return-1}function Sh(e,t){return xr(e,t,!0)}function Eh(e,t,n){return Pi(e,t,G(n,2),!0)}function Rh(e,t){var n=e==null?0:e.length;if(n){var r=xr(e,t,!0)-1;if(st(e[r],t))return r}return-1}function Ih(e){return e&&e.length?Oo(e):[]}function Th(e,t){return e&&e.length?Oo(e,G(t,2)):[]}function Lh(e){var t=e==null?0:e.length;return t?Ke(e,1,t):[]}function Mh(e,t,n){return e&&e.length?(t=n||t===i?1:K(t),Ke(e,0,t<0?0:t)):[]}function Oh(e,t,n){var r=e==null?0:e.length;return r?(t=n||t===i?1:K(t),t=r-t,Ke(e,t<0?0:t,r)):[]}function Ph(e,t){return e&&e.length?wr(e,G(t,3),!1,!0):[]}function Dh(e,t){return e&&e.length?wr(e,G(t,3)):[]}var Wh=ee(function(e){return Dt(Se(e,1,me,!0))}),Fh=ee(function(e){var t=Xe(e);return me(t)&&(t=i),Dt(Se(e,1,me,!0),G(t,2))}),Bh=ee(function(e){var t=Xe(e);return t=typeof t=="function"?t:i,Dt(Se(e,1,me,!0),i,t)});function Nh(e){return e&&e.length?Dt(e):[]}function kh(e,t){return e&&e.length?Dt(e,G(t,2)):[]}function Uh(e,t){return t=typeof t=="function"?t:i,e&&e.length?Dt(e,i,t):[]}function Ki(e){if(!(e&&e.length))return[];var t=0;return e=Tt(e,function(n){if(me(n))return t=xe(n.length,t),!0}),pi(t,function(n){return pe(e,fi(n))})}function _u(e,t){if(!(e&&e.length))return[];var n=Ki(e);return t==null?n:pe(n,function(r){return Ue(t,i,r)})}var $h=ee(function(e,t){return me(e)?On(e,t):[]}),Gh=ee(function(e){return Wi(Tt(e,me))}),Hh=ee(function(e){var t=Xe(e);return me(t)&&(t=i),Wi(Tt(e,me),G(t,2))}),zh=ee(function(e){var t=Xe(e);return t=typeof t=="function"?t:i,Wi(Tt(e,me),i,t)}),Vh=ee(Ki);function Zh(e,t){return Fo(e||[],t||[],Mn)}function Yh(e,t){return Fo(e||[],t||[],Wn)}var qh=ee(function(e){var t=e.length,n=t>1?e[t-1]:i;return n=typeof n=="function"?(e.pop(),n):i,_u(e,n)});function yu(e){var t=u(e);return t.__chain__=!0,t}function Jh(e,t){return t(e),e}function Lr(e,t){return t(e)}var Qh=xt(function(e){var t=e.length,n=t?e[0]:0,r=this.__wrapped__,s=function(a){return bi(a,e)};return t>1||this.__actions__.length||!(r instanceof re)||!wt(n)?this.thru(s):(r=r.slice(n,+n+(t?1:0)),r.__actions__.push({func:Lr,args:[s],thisArg:i}),new Je(r,this.__chain__).thru(function(a){return t&&!a.length&&a.push(i),a}))});function Kh(){return yu(this)}function Xh(){return new Je(this.value(),this.__chain__)}function jh(){this.__values__===i&&(this.__values__=Pu(this.value()));var e=this.__index__>=this.__values__.length,t=e?i:this.__values__[this.__index__++];return{done:e,value:t}}function ed(){return this}function td(e){for(var t,n=this;n instanceof gr;){var r=hu(n);r.__index__=0,r.__values__=i,t?s.__wrapped__=r:t=r;var s=r;n=n.__wrapped__}return s.__wrapped__=e,t}function nd(){var e=this.__wrapped__;if(e instanceof re){var t=e;return this.__actions__.length&&(t=new re(this)),t=t.reverse(),t.__actions__.push({func:Lr,args:[Qi],thisArg:i}),new Je(t,this.__chain__)}return this.thru(Qi)}function rd(){return Wo(this.__wrapped__,this.__actions__)}var id=br(function(e,t,n){le.call(e,n)?++e[n]:_t(e,n,1)});function sd(e,t,n){var r=J(e)?Ys:qc;return n&&Me(e,t,n)&&(t=i),r(e,G(t,3))}function od(e,t){var n=J(e)?Tt:_o;return n(e,G(t,3))}var ud=Zo(du),ad=Zo(pu);function ld(e,t){return Se(Mr(e,t),1)}function cd(e,t){return Se(Mr(e,t),It)}function fd(e,t,n){return n=n===i?1:K(n),Se(Mr(e,t),n)}function xu(e,t){var n=J(e)?Ye:Pt;return n(e,G(t,3))}function wu(e,t){var n=J(e)?Tl:mo;return n(e,G(t,3))}var hd=br(function(e,t,n){le.call(e,n)?e[n].push(t):_t(e,n,[t])});function dd(e,t,n,r){e=We(e)?e:gn(e),n=n&&!r?K(n):0;var s=e.length;return n<0&&(n=xe(s+n,0)),Fr(e)?n<=s&&e.indexOf(t,n)>-1:!!s&&nn(e,t,n)>-1}var pd=ee(function(e,t,n){var r=-1,s=typeof t=="function",a=We(e)?p(e.length):[];return Pt(e,function(l){a[++r]=s?Ue(t,l,n):Pn(l,t,n)}),a}),gd=br(function(e,t,n){_t(e,n,t)});function Mr(e,t){var n=J(e)?pe:Co;return n(e,G(t,3))}function vd(e,t,n,r){return e==null?[]:(J(t)||(t=t==null?[]:[t]),n=r?i:n,J(n)||(n=n==null?[]:[n]),Io(e,t,n))}var md=br(function(e,t,n){e[n?0:1].push(t)},function(){return[[],[]]});function _d(e,t,n){var r=J(e)?li:Ks,s=arguments.length<3;return r(e,G(t,4),n,s,Pt)}function yd(e,t,n){var r=J(e)?Ll:Ks,s=arguments.length<3;return r(e,G(t,4),n,s,mo)}function xd(e,t){var n=J(e)?Tt:_o;return n(e,Dr(G(t,3)))}function wd(e){var t=J(e)?ho:df;return t(e)}function bd(e,t,n){(n?Me(e,t,n):t===i)?t=1:t=K(t);var r=J(e)?Hc:pf;return r(e,t)}function Ad(e){var t=J(e)?zc:vf;return t(e)}function Cd(e){if(e==null)return 0;if(We(e))return Fr(e)?sn(e):e.length;var t=Ie(e);return t==tt||t==nt?e.size:Ii(e).length}function Sd(e,t,n){var r=J(e)?ci:mf;return n&&Me(e,t,n)&&(t=i),r(e,G(t,3))}var Ed=ee(function(e,t){if(e==null)return[];var n=t.length;return n>1&&Me(e,t[0],t[1])?t=[]:n>2&&Me(t[0],t[1],t[2])&&(t=[t[0]]),Io(e,Se(t,1),[])}),Or=sc||function(){return Ce.Date.now()};function Rd(e,t){if(typeof t!="function")throw new qe(b);return e=K(e),function(){if(--e<1)return t.apply(this,arguments)}}function bu(e,t,n){return t=n?i:t,t=e&&t==null?e.length:t,yt(e,ae,i,i,i,i,t)}function Au(e,t){var n;if(typeof t!="function")throw new qe(b);return e=K(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=i),n}}var Xi=ee(function(e,t,n){var r=v;if(n.length){var s=Mt(n,dn(Xi));r|=k}return yt(e,r,t,n,s)}),Cu=ee(function(e,t,n){var r=v|T;if(n.length){var s=Mt(n,dn(Cu));r|=k}return yt(t,r,e,n,s)});function Su(e,t,n){t=n?i:t;var r=yt(e,B,i,i,i,i,i,t);return r.placeholder=Su.placeholder,r}function Eu(e,t,n){t=n?i:t;var r=yt(e,Z,i,i,i,i,i,t);return r.placeholder=Eu.placeholder,r}function Ru(e,t,n){var r,s,a,l,c,d,_=0,x=!1,A=!1,P=!0;if(typeof e!="function")throw new qe(b);t=je(t)||0,ge(n)&&(x=!!n.leading,A="maxWait"in n,a=A?xe(je(n.maxWait)||0,t):a,P="trailing"in n?!!n.trailing:P);function F(_e){var ot=r,Ct=s;return r=s=i,_=_e,l=e.apply(Ct,ot),l}function H(_e){return _=_e,c=Nn(ne,t),x?F(_e):l}function X(_e){var ot=_e-d,Ct=_e-_,Zu=t-ot;return A?Re(Zu,a-Ct):Zu}function z(_e){var ot=_e-d,Ct=_e-_;return d===i||ot>=t||ot<0||A&&Ct>=a}function ne(){var _e=Or();if(z(_e))return ie(_e);c=Nn(ne,X(_e))}function ie(_e){return c=i,P&&r?F(_e):(r=s=i,l)}function ze(){c!==i&&Bo(c),_=0,r=d=s=c=i}function Oe(){return c===i?l:ie(Or())}function Ve(){var _e=Or(),ot=z(_e);if(r=arguments,s=this,d=_e,ot){if(c===i)return H(d);if(A)return Bo(c),c=Nn(ne,t),F(d)}return c===i&&(c=Nn(ne,t)),l}return Ve.cancel=ze,Ve.flush=Oe,Ve}var Id=ee(function(e,t){return vo(e,1,t)}),Td=ee(function(e,t,n){return vo(e,je(t)||0,n)});function Ld(e){return yt(e,V)}function Pr(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new qe(b);var n=function(){var r=arguments,s=t?t.apply(this,r):r[0],a=n.cache;if(a.has(s))return a.get(s);var l=e.apply(this,r);return n.cache=a.set(s,l)||a,l};return n.cache=new(Pr.Cache||mt),n}Pr.Cache=mt;function Dr(e){if(typeof e!="function")throw new qe(b);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}function Md(e){return Au(2,e)}var Od=_f(function(e,t){t=t.length==1&&J(t[0])?pe(t[0],$e(G())):pe(Se(t,1),$e(G()));var n=t.length;return ee(function(r){for(var s=-1,a=Re(r.length,n);++s<a;)r[s]=t[s].call(this,r[s]);return Ue(e,this,r)})}),ji=ee(function(e,t){var n=Mt(t,dn(ji));return yt(e,k,i,t,n)}),Iu=ee(function(e,t){var n=Mt(t,dn(Iu));return yt(e,Q,i,t,n)}),Pd=xt(function(e,t){return yt(e,U,i,i,i,t)});function Dd(e,t){if(typeof e!="function")throw new qe(b);return t=t===i?t:K(t),ee(e,t)}function Wd(e,t){if(typeof e!="function")throw new qe(b);return t=t==null?0:xe(K(t),0),ee(function(n){var r=n[t],s=Ft(n,0,t);return r&&Lt(s,r),Ue(e,this,s)})}function Fd(e,t,n){var r=!0,s=!0;if(typeof e!="function")throw new qe(b);return ge(n)&&(r="leading"in n?!!n.leading:r,s="trailing"in n?!!n.trailing:s),Ru(e,t,{leading:r,maxWait:t,trailing:s})}function Bd(e){return bu(e,1)}function Nd(e,t){return ji(Bi(t),e)}function kd(){if(!arguments.length)return[];var e=arguments[0];return J(e)?e:[e]}function Ud(e){return Qe(e,y)}function $d(e,t){return t=typeof t=="function"?t:i,Qe(e,y,t)}function Gd(e){return Qe(e,R|y)}function Hd(e,t){return t=typeof t=="function"?t:i,Qe(e,R|y,t)}function zd(e,t){return t==null||go(e,t,be(t))}function st(e,t){return e===t||e!==e&&t!==t}var Vd=Er(Si),Zd=Er(function(e,t){return e>=t}),Qt=wo(function(){return arguments}())?wo:function(e){return ve(e)&&le.call(e,"callee")&&!oo.call(e,"callee")},J=p.isArray,Yd=$s?$e($s):ef;function We(e){return e!=null&&Wr(e.length)&&!bt(e)}function me(e){return ve(e)&&We(e)}function qd(e){return e===!0||e===!1||ve(e)&&Le(e)==_n}var Bt=uc||cs,Jd=Gs?$e(Gs):tf;function Qd(e){return ve(e)&&e.nodeType===1&&!kn(e)}function Kd(e){if(e==null)return!0;if(We(e)&&(J(e)||typeof e=="string"||typeof e.splice=="function"||Bt(e)||pn(e)||Qt(e)))return!e.length;var t=Ie(e);if(t==tt||t==nt)return!e.size;if(Bn(e))return!Ii(e).length;for(var n in e)if(le.call(e,n))return!1;return!0}function Xd(e,t){return Dn(e,t)}function jd(e,t,n){n=typeof n=="function"?n:i;var r=n?n(e,t):i;return r===i?Dn(e,t,i,n):!!r}function es(e){if(!ve(e))return!1;var t=Le(e);return t==Yn||t==wa||typeof e.message=="string"&&typeof e.name=="string"&&!kn(e)}function ep(e){return typeof e=="number"&&ao(e)}function bt(e){if(!ge(e))return!1;var t=Le(e);return t==qn||t==gs||t==xa||t==Aa}function Tu(e){return typeof e=="number"&&e==K(e)}function Wr(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=pt}function ge(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}function ve(e){return e!=null&&typeof e=="object"}var Lu=Hs?$e(Hs):rf;function tp(e,t){return e===t||Ri(e,t,zi(t))}function np(e,t,n){return n=typeof n=="function"?n:i,Ri(e,t,zi(t),n)}function rp(e){return Mu(e)&&e!=+e}function ip(e){if($f(e))throw new Y(w);return bo(e)}function sp(e){return e===null}function op(e){return e==null}function Mu(e){return typeof e=="number"||ve(e)&&Le(e)==xn}function kn(e){if(!ve(e)||Le(e)!=gt)return!1;var t=ar(e);if(t===null)return!0;var n=le.call(t,"constructor")&&t.constructor;return typeof n=="function"&&n instanceof n&&ir.call(n)==tc}var ts=zs?$e(zs):sf;function up(e){return Tu(e)&&e>=-9007199254740991&&e<=pt}var Ou=Vs?$e(Vs):of;function Fr(e){return typeof e=="string"||!J(e)&&ve(e)&&Le(e)==bn}function He(e){return typeof e=="symbol"||ve(e)&&Le(e)==Jn}var pn=Zs?$e(Zs):uf;function ap(e){return e===i}function lp(e){return ve(e)&&Ie(e)==An}function cp(e){return ve(e)&&Le(e)==Sa}var fp=Er(Ti),hp=Er(function(e,t){return e<=t});function Pu(e){if(!e)return[];if(We(e))return Fr(e)?rt(e):De(e);if(En&&e[En])return Hl(e[En]());var t=Ie(e),n=t==tt?vi:t==nt?tr:gn;return n(e)}function At(e){if(!e)return e===0?e:0;if(e=je(e),e===It||e===-1/0){var t=e<0?-1:1;return t*zn}return e===e?e:0}function K(e){var t=At(e),n=t%1;return t===t?n?t-n:t:0}function Du(e){return e?Zt(K(e),0,at):0}function je(e){if(typeof e=="number")return e;if(He(e))return Vn;if(ge(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=ge(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=Xs(e);var n=Za.test(e);return n||qa.test(e)?El(e.slice(2),n?2:8):Va.test(e)?Vn:+e}function Wu(e){return ct(e,Fe(e))}function dp(e){return e?Zt(K(e),-9007199254740991,pt):e===0?e:0}function ue(e){return e==null?"":Ge(e)}var pp=fn(function(e,t){if(Bn(t)||We(t)){ct(t,be(t),e);return}for(var n in t)le.call(t,n)&&Mn(e,n,t[n])}),Fu=fn(function(e,t){ct(t,Fe(t),e)}),Br=fn(function(e,t,n,r){ct(t,Fe(t),e,r)}),gp=fn(function(e,t,n,r){ct(t,be(t),e,r)}),vp=xt(bi);function mp(e,t){var n=cn(e);return t==null?n:po(n,t)}var _p=ee(function(e,t){e=ce(e);var n=-1,r=t.length,s=r>2?t[2]:i;for(s&&Me(t[0],t[1],s)&&(r=1);++n<r;)for(var a=t[n],l=Fe(a),c=-1,d=l.length;++c<d;){var _=l[c],x=e[_];(x===i||st(x,un[_])&&!le.call(e,_))&&(e[_]=a[_])}return e}),yp=ee(function(e){return e.push(i,jo),Ue(Bu,i,e)});function xp(e,t){return qs(e,G(t,3),lt)}function wp(e,t){return qs(e,G(t,3),Ci)}function bp(e,t){return e==null?e:Ai(e,G(t,3),Fe)}function Ap(e,t){return e==null?e:yo(e,G(t,3),Fe)}function Cp(e,t){return e&&lt(e,G(t,3))}function Sp(e,t){return e&&Ci(e,G(t,3))}function Ep(e){return e==null?[]:_r(e,be(e))}function Rp(e){return e==null?[]:_r(e,Fe(e))}function ns(e,t,n){var r=e==null?i:Yt(e,t);return r===i?n:r}function Ip(e,t){return e!=null&&nu(e,t,Qc)}function rs(e,t){return e!=null&&nu(e,t,Kc)}var Tp=qo(function(e,t,n){t!=null&&typeof t.toString!="function"&&(t=sr.call(t)),e[t]=n},ss(Be)),Lp=qo(function(e,t,n){t!=null&&typeof t.toString!="function"&&(t=sr.call(t)),le.call(e,t)?e[t].push(n):e[t]=[n]},G),Mp=ee(Pn);function be(e){return We(e)?fo(e):Ii(e)}function Fe(e){return We(e)?fo(e,!0):af(e)}function Op(e,t){var n={};return t=G(t,3),lt(e,function(r,s,a){_t(n,t(r,s,a),r)}),n}function Pp(e,t){var n={};return t=G(t,3),lt(e,function(r,s,a){_t(n,s,t(r,s,a))}),n}var Dp=fn(function(e,t,n){yr(e,t,n)}),Bu=fn(function(e,t,n,r){yr(e,t,n,r)}),Wp=xt(function(e,t){var n={};if(e==null)return n;var r=!1;t=pe(t,function(a){return a=Wt(a,e),r||(r=a.length>1),a}),ct(e,Gi(e),n),r&&(n=Qe(n,R|O|y,Tf));for(var s=t.length;s--;)Di(n,t[s]);return n});function Fp(e,t){return Nu(e,Dr(G(t)))}var Bp=xt(function(e,t){return e==null?{}:cf(e,t)});function Nu(e,t){if(e==null)return{};var n=pe(Gi(e),function(r){return[r]});return t=G(t),To(e,n,function(r,s){return t(r,s[0])})}function Np(e,t,n){t=Wt(t,e);var r=-1,s=t.length;for(s||(s=1,e=i);++r<s;){var a=e==null?i:e[ft(t[r])];a===i&&(r=s,a=n),e=bt(a)?a.call(e):a}return e}function kp(e,t,n){return e==null?e:Wn(e,t,n)}function Up(e,t,n,r){return r=typeof r=="function"?r:i,e==null?e:Wn(e,t,n,r)}var ku=Ko(be),Uu=Ko(Fe);function $p(e,t,n){var r=J(e),s=r||Bt(e)||pn(e);if(t=G(t,4),n==null){var a=e&&e.constructor;s?n=r?new a:[]:ge(e)?n=bt(a)?cn(ar(e)):{}:n={}}return(s?Ye:lt)(e,function(l,c,d){return t(n,l,c,d)}),n}function Gp(e,t){return e==null?!0:Di(e,t)}function Hp(e,t,n){return e==null?e:Do(e,t,Bi(n))}function zp(e,t,n,r){return r=typeof r=="function"?r:i,e==null?e:Do(e,t,Bi(n),r)}function gn(e){return e==null?[]:gi(e,be(e))}function Vp(e){return e==null?[]:gi(e,Fe(e))}function Zp(e,t,n){return n===i&&(n=t,t=i),n!==i&&(n=je(n),n=n===n?n:0),t!==i&&(t=je(t),t=t===t?t:0),Zt(je(e),t,n)}function Yp(e,t,n){return t=At(t),n===i?(n=t,t=0):n=At(n),e=je(e),Xc(e,t,n)}function qp(e,t,n){if(n&&typeof n!="boolean"&&Me(e,t,n)&&(t=n=i),n===i&&(typeof t=="boolean"?(n=t,t=i):typeof e=="boolean"&&(n=e,e=i)),e===i&&t===i?(e=0,t=1):(e=At(e),t===i?(t=e,e=0):t=At(t)),e>t){var r=e;e=t,t=r}if(n||e%1||t%1){var s=lo();return Re(e+s*(t-e+Sl("1e-"+((s+"").length-1))),t)}return Mi(e,t)}var Jp=hn(function(e,t,n){return t=t.toLowerCase(),e+(n?$u(t):t)});function $u(e){return is(ue(e).toLowerCase())}function Gu(e){return e=ue(e),e&&e.replace(Qa,Nl).replace(gl,"")}function Qp(e,t,n){e=ue(e),t=Ge(t);var r=e.length;n=n===i?r:Zt(K(n),0,r);var s=n;return n-=t.length,n>=0&&e.slice(n,s)==t}function Kp(e){return e=ue(e),e&&La.test(e)?e.replace(_s,kl):e}function Xp(e){return e=ue(e),e&&Fa.test(e)?e.replace(jr,"\\$&"):e}var jp=hn(function(e,t,n){return e+(n?"-":"")+t.toLowerCase()}),eg=hn(function(e,t,n){return e+(n?" ":"")+t.toLowerCase()}),tg=Vo("toLowerCase");function ng(e,t,n){e=ue(e),t=K(t);var r=t?sn(e):0;if(!t||r>=t)return e;var s=(t-r)/2;return Sr(hr(s),n)+e+Sr(fr(s),n)}function rg(e,t,n){e=ue(e),t=K(t);var r=t?sn(e):0;return t&&r<t?e+Sr(t-r,n):e}function ig(e,t,n){e=ue(e),t=K(t);var r=t?sn(e):0;return t&&r<t?Sr(t-r,n)+e:e}function sg(e,t,n){return n||t==null?t=0:t&&(t=+t),fc(ue(e).replace(ei,""),t||0)}function og(e,t,n){return(n?Me(e,t,n):t===i)?t=1:t=K(t),Oi(ue(e),t)}function ug(){var e=arguments,t=ue(e[0]);return e.length<3?t:t.replace(e[1],e[2])}var ag=hn(function(e,t,n){return e+(n?"_":"")+t.toLowerCase()});function lg(e,t,n){return n&&typeof n!="number"&&Me(e,t,n)&&(t=n=i),n=n===i?at:n>>>0,n?(e=ue(e),e&&(typeof t=="string"||t!=null&&!ts(t))&&(t=Ge(t),!t&&rn(e))?Ft(rt(e),0,n):e.split(t,n)):[]}var cg=hn(function(e,t,n){return e+(n?" ":"")+is(t)});function fg(e,t,n){return e=ue(e),n=n==null?0:Zt(K(n),0,e.length),t=Ge(t),e.slice(n,n+t.length)==t}function hg(e,t,n){var r=u.templateSettings;n&&Me(e,t,n)&&(t=i),e=ue(e),t=Br({},t,r,Xo);var s=Br({},t.imports,r.imports,Xo),a=be(s),l=gi(s,a),c,d,_=0,x=t.interpolate||Qn,A="__p += '",P=mi((t.escape||Qn).source+"|"+x.source+"|"+(x===ys?za:Qn).source+"|"+(t.evaluate||Qn).source+"|$","g"),F="//# sourceURL="+(le.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++xl+"]")+`
`;e.replace(P,function(z,ne,ie,ze,Oe,Ve){return ie||(ie=ze),A+=e.slice(_,Ve).replace(Ka,Ul),ne&&(c=!0,A+=`' +
__e(`+ne+`) +
'`),Oe&&(d=!0,A+=`';
`+Oe+`;
__p += '`),ie&&(A+=`' +
((__t = (`+ie+`)) == null ? '' : __t) +
'`),_=Ve+z.length,z}),A+=`';
`;var H=le.call(t,"variable")&&t.variable;if(!H)A=`with (obj) {
`+A+`
}
`;else if(Ga.test(H))throw new Y(C);A=(d?A.replace(Ea,""):A).replace(Ra,"$1").replace(Ia,"$1;"),A="function("+(H||"obj")+`) {
`+(H?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(c?", __e = _.escape":"")+(d?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+A+`return __p
}`;var X=zu(function(){return oe(a,F+"return "+A).apply(i,l)});if(X.source=A,es(X))throw X;return X}function dg(e){return ue(e).toLowerCase()}function pg(e){return ue(e).toUpperCase()}function gg(e,t,n){if(e=ue(e),e&&(n||t===i))return Xs(e);if(!e||!(t=Ge(t)))return e;var r=rt(e),s=rt(t),a=js(r,s),l=eo(r,s)+1;return Ft(r,a,l).join("")}function vg(e,t,n){if(e=ue(e),e&&(n||t===i))return e.slice(0,no(e)+1);if(!e||!(t=Ge(t)))return e;var r=rt(e),s=eo(r,rt(t))+1;return Ft(r,0,s).join("")}function mg(e,t,n){if(e=ue(e),e&&(n||t===i))return e.replace(ei,"");if(!e||!(t=Ge(t)))return e;var r=rt(e),s=js(r,rt(t));return Ft(r,s).join("")}function _g(e,t){var n=se,r=Pe;if(ge(t)){var s="separator"in t?t.separator:s;n="length"in t?K(t.length):n,r="omission"in t?Ge(t.omission):r}e=ue(e);var a=e.length;if(rn(e)){var l=rt(e);a=l.length}if(n>=a)return e;var c=n-sn(r);if(c<1)return r;var d=l?Ft(l,0,c).join(""):e.slice(0,c);if(s===i)return d+r;if(l&&(c+=d.length-c),ts(s)){if(e.slice(c).search(s)){var _,x=d;for(s.global||(s=mi(s.source,ue(xs.exec(s))+"g")),s.lastIndex=0;_=s.exec(x);)var A=_.index;d=d.slice(0,A===i?c:A)}}else if(e.indexOf(Ge(s),c)!=c){var P=d.lastIndexOf(s);P>-1&&(d=d.slice(0,P))}return d+r}function yg(e){return e=ue(e),e&&Ta.test(e)?e.replace(ms,Yl):e}var xg=hn(function(e,t,n){return e+(n?" ":"")+t.toUpperCase()}),is=Vo("toUpperCase");function Hu(e,t,n){return e=ue(e),t=n?i:t,t===i?Gl(e)?Ql(e):Pl(e):e.match(t)||[]}var zu=ee(function(e,t){try{return Ue(e,i,t)}catch(n){return es(n)?n:new Y(n)}}),wg=xt(function(e,t){return Ye(t,function(n){n=ft(n),_t(e,n,Xi(e[n],e))}),e});function bg(e){var t=e==null?0:e.length,n=G();return e=t?pe(e,function(r){if(typeof r[1]!="function")throw new qe(b);return[n(r[0]),r[1]]}):[],ee(function(r){for(var s=-1;++s<t;){var a=e[s];if(Ue(a[0],this,r))return Ue(a[1],this,r)}})}function Ag(e){return Yc(Qe(e,R))}function ss(e){return function(){return e}}function Cg(e,t){return e==null||e!==e?t:e}var Sg=Yo(),Eg=Yo(!0);function Be(e){return e}function os(e){return Ao(typeof e=="function"?e:Qe(e,R))}function Rg(e){return So(Qe(e,R))}function Ig(e,t){return Eo(e,Qe(t,R))}var Tg=ee(function(e,t){return function(n){return Pn(n,e,t)}}),Lg=ee(function(e,t){return function(n){return Pn(e,n,t)}});function us(e,t,n){var r=be(t),s=_r(t,r);n==null&&!(ge(t)&&(s.length||!r.length))&&(n=t,t=e,e=this,s=_r(t,be(t)));var a=!(ge(n)&&"chain"in n)||!!n.chain,l=bt(e);return Ye(s,function(c){var d=t[c];e[c]=d,l&&(e.prototype[c]=function(){var _=this.__chain__;if(a||_){var x=e(this.__wrapped__),A=x.__actions__=De(this.__actions__);return A.push({func:d,args:arguments,thisArg:e}),x.__chain__=_,x}return d.apply(e,Lt([this.value()],arguments))})}),e}function Mg(){return Ce._===this&&(Ce._=nc),this}function as(){}function Og(e){return e=K(e),ee(function(t){return Ro(t,e)})}var Pg=ki(pe),Dg=ki(Ys),Wg=ki(ci);function Vu(e){return Zi(e)?fi(ft(e)):ff(e)}function Fg(e){return function(t){return e==null?i:Yt(e,t)}}var Bg=Jo(),Ng=Jo(!0);function ls(){return[]}function cs(){return!1}function kg(){return{}}function Ug(){return""}function $g(){return!0}function Gg(e,t){if(e=K(e),e<1||e>pt)return[];var n=at,r=Re(e,at);t=G(t),e-=at;for(var s=pi(r,t);++n<e;)t(n);return s}function Hg(e){return J(e)?pe(e,ft):He(e)?[e]:De(fu(ue(e)))}function zg(e){var t=++ec;return ue(e)+t}var Vg=Cr(function(e,t){return e+t},0),Zg=Ui("ceil"),Yg=Cr(function(e,t){return e/t},1),qg=Ui("floor");function Jg(e){return e&&e.length?mr(e,Be,Si):i}function Qg(e,t){return e&&e.length?mr(e,G(t,2),Si):i}function Kg(e){return Qs(e,Be)}function Xg(e,t){return Qs(e,G(t,2))}function jg(e){return e&&e.length?mr(e,Be,Ti):i}function ev(e,t){return e&&e.length?mr(e,G(t,2),Ti):i}var tv=Cr(function(e,t){return e*t},1),nv=Ui("round"),rv=Cr(function(e,t){return e-t},0);function iv(e){return e&&e.length?di(e,Be):0}function sv(e,t){return e&&e.length?di(e,G(t,2)):0}return u.after=Rd,u.ary=bu,u.assign=pp,u.assignIn=Fu,u.assignInWith=Br,u.assignWith=gp,u.at=vp,u.before=Au,u.bind=Xi,u.bindAll=wg,u.bindKey=Cu,u.castArray=kd,u.chain=yu,u.chunk=qf,u.compact=Jf,u.concat=Qf,u.cond=bg,u.conforms=Ag,u.constant=ss,u.countBy=id,u.create=mp,u.curry=Su,u.curryRight=Eu,u.debounce=Ru,u.defaults=_p,u.defaultsDeep=yp,u.defer=Id,u.delay=Td,u.difference=Kf,u.differenceBy=Xf,u.differenceWith=jf,u.drop=eh,u.dropRight=th,u.dropRightWhile=nh,u.dropWhile=rh,u.fill=ih,u.filter=od,u.flatMap=ld,u.flatMapDeep=cd,u.flatMapDepth=fd,u.flatten=gu,u.flattenDeep=sh,u.flattenDepth=oh,u.flip=Ld,u.flow=Sg,u.flowRight=Eg,u.fromPairs=uh,u.functions=Ep,u.functionsIn=Rp,u.groupBy=hd,u.initial=lh,u.intersection=ch,u.intersectionBy=fh,u.intersectionWith=hh,u.invert=Tp,u.invertBy=Lp,u.invokeMap=pd,u.iteratee=os,u.keyBy=gd,u.keys=be,u.keysIn=Fe,u.map=Mr,u.mapKeys=Op,u.mapValues=Pp,u.matches=Rg,u.matchesProperty=Ig,u.memoize=Pr,u.merge=Dp,u.mergeWith=Bu,u.method=Tg,u.methodOf=Lg,u.mixin=us,u.negate=Dr,u.nthArg=Og,u.omit=Wp,u.omitBy=Fp,u.once=Md,u.orderBy=vd,u.over=Pg,u.overArgs=Od,u.overEvery=Dg,u.overSome=Wg,u.partial=ji,u.partialRight=Iu,u.partition=md,u.pick=Bp,u.pickBy=Nu,u.property=Vu,u.propertyOf=Fg,u.pull=vh,u.pullAll=mu,u.pullAllBy=mh,u.pullAllWith=_h,u.pullAt=yh,u.range=Bg,u.rangeRight=Ng,u.rearg=Pd,u.reject=xd,u.remove=xh,u.rest=Dd,u.reverse=Qi,u.sampleSize=bd,u.set=kp,u.setWith=Up,u.shuffle=Ad,u.slice=wh,u.sortBy=Ed,u.sortedUniq=Ih,u.sortedUniqBy=Th,u.split=lg,u.spread=Wd,u.tail=Lh,u.take=Mh,u.takeRight=Oh,u.takeRightWhile=Ph,u.takeWhile=Dh,u.tap=Jh,u.throttle=Fd,u.thru=Lr,u.toArray=Pu,u.toPairs=ku,u.toPairsIn=Uu,u.toPath=Hg,u.toPlainObject=Wu,u.transform=$p,u.unary=Bd,u.union=Wh,u.unionBy=Fh,u.unionWith=Bh,u.uniq=Nh,u.uniqBy=kh,u.uniqWith=Uh,u.unset=Gp,u.unzip=Ki,u.unzipWith=_u,u.update=Hp,u.updateWith=zp,u.values=gn,u.valuesIn=Vp,u.without=$h,u.words=Hu,u.wrap=Nd,u.xor=Gh,u.xorBy=Hh,u.xorWith=zh,u.zip=Vh,u.zipObject=Zh,u.zipObjectDeep=Yh,u.zipWith=qh,u.entries=ku,u.entriesIn=Uu,u.extend=Fu,u.extendWith=Br,us(u,u),u.add=Vg,u.attempt=zu,u.camelCase=Jp,u.capitalize=$u,u.ceil=Zg,u.clamp=Zp,u.clone=Ud,u.cloneDeep=Gd,u.cloneDeepWith=Hd,u.cloneWith=$d,u.conformsTo=zd,u.deburr=Gu,u.defaultTo=Cg,u.divide=Yg,u.endsWith=Qp,u.eq=st,u.escape=Kp,u.escapeRegExp=Xp,u.every=sd,u.find=ud,u.findIndex=du,u.findKey=xp,u.findLast=ad,u.findLastIndex=pu,u.findLastKey=wp,u.floor=qg,u.forEach=xu,u.forEachRight=wu,u.forIn=bp,u.forInRight=Ap,u.forOwn=Cp,u.forOwnRight=Sp,u.get=ns,u.gt=Vd,u.gte=Zd,u.has=Ip,u.hasIn=rs,u.head=vu,u.identity=Be,u.includes=dd,u.indexOf=ah,u.inRange=Yp,u.invoke=Mp,u.isArguments=Qt,u.isArray=J,u.isArrayBuffer=Yd,u.isArrayLike=We,u.isArrayLikeObject=me,u.isBoolean=qd,u.isBuffer=Bt,u.isDate=Jd,u.isElement=Qd,u.isEmpty=Kd,u.isEqual=Xd,u.isEqualWith=jd,u.isError=es,u.isFinite=ep,u.isFunction=bt,u.isInteger=Tu,u.isLength=Wr,u.isMap=Lu,u.isMatch=tp,u.isMatchWith=np,u.isNaN=rp,u.isNative=ip,u.isNil=op,u.isNull=sp,u.isNumber=Mu,u.isObject=ge,u.isObjectLike=ve,u.isPlainObject=kn,u.isRegExp=ts,u.isSafeInteger=up,u.isSet=Ou,u.isString=Fr,u.isSymbol=He,u.isTypedArray=pn,u.isUndefined=ap,u.isWeakMap=lp,u.isWeakSet=cp,u.join=dh,u.kebabCase=jp,u.last=Xe,u.lastIndexOf=ph,u.lowerCase=eg,u.lowerFirst=tg,u.lt=fp,u.lte=hp,u.max=Jg,u.maxBy=Qg,u.mean=Kg,u.meanBy=Xg,u.min=jg,u.minBy=ev,u.stubArray=ls,u.stubFalse=cs,u.stubObject=kg,u.stubString=Ug,u.stubTrue=$g,u.multiply=tv,u.nth=gh,u.noConflict=Mg,u.noop=as,u.now=Or,u.pad=ng,u.padEnd=rg,u.padStart=ig,u.parseInt=sg,u.random=qp,u.reduce=_d,u.reduceRight=yd,u.repeat=og,u.replace=ug,u.result=Np,u.round=nv,u.runInContext=h,u.sample=wd,u.size=Cd,u.snakeCase=ag,u.some=Sd,u.sortedIndex=bh,u.sortedIndexBy=Ah,u.sortedIndexOf=Ch,u.sortedLastIndex=Sh,u.sortedLastIndexBy=Eh,u.sortedLastIndexOf=Rh,u.startCase=cg,u.startsWith=fg,u.subtract=rv,u.sum=iv,u.sumBy=sv,u.template=hg,u.times=Gg,u.toFinite=At,u.toInteger=K,u.toLength=Du,u.toLower=dg,u.toNumber=je,u.toSafeInteger=dp,u.toString=ue,u.toUpper=pg,u.trim=gg,u.trimEnd=vg,u.trimStart=mg,u.truncate=_g,u.unescape=yg,u.uniqueId=zg,u.upperCase=xg,u.upperFirst=is,u.each=xu,u.eachRight=wu,u.first=vu,us(u,function(){var e={};return lt(u,function(t,n){le.call(u.prototype,n)||(e[n]=t)}),e}(),{chain:!1}),u.VERSION=f,Ye(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){u[e].placeholder=u}),Ye(["drop","take"],function(e,t){re.prototype[e]=function(n){n=n===i?1:xe(K(n),0);var r=this.__filtered__&&!t?new re(this):this.clone();return r.__filtered__?r.__takeCount__=Re(n,r.__takeCount__):r.__views__.push({size:Re(n,at),type:e+(r.__dir__<0?"Right":"")}),r},re.prototype[e+"Right"]=function(n){return this.reverse()[e](n).reverse()}}),Ye(["filter","map","takeWhile"],function(e,t){var n=t+1,r=n==Ut||n==$t;re.prototype[e]=function(s){var a=this.clone();return a.__iteratees__.push({iteratee:G(s,3),type:n}),a.__filtered__=a.__filtered__||r,a}}),Ye(["head","last"],function(e,t){var n="take"+(t?"Right":"");re.prototype[e]=function(){return this[n](1).value()[0]}}),Ye(["initial","tail"],function(e,t){var n="drop"+(t?"":"Right");re.prototype[e]=function(){return this.__filtered__?new re(this):this[n](1)}}),re.prototype.compact=function(){return this.filter(Be)},re.prototype.find=function(e){return this.filter(e).head()},re.prototype.findLast=function(e){return this.reverse().find(e)},re.prototype.invokeMap=ee(function(e,t){return typeof e=="function"?new re(this):this.map(function(n){return Pn(n,e,t)})}),re.prototype.reject=function(e){return this.filter(Dr(G(e)))},re.prototype.slice=function(e,t){e=K(e);var n=this;return n.__filtered__&&(e>0||t<0)?new re(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==i&&(t=K(t),n=t<0?n.dropRight(-t):n.take(t-e)),n)},re.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},re.prototype.toArray=function(){return this.take(at)},lt(re.prototype,function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),r=/^(?:head|last)$/.test(t),s=u[r?"take"+(t=="last"?"Right":""):t],a=r||/^find/.test(t);s&&(u.prototype[t]=function(){var l=this.__wrapped__,c=r?[1]:arguments,d=l instanceof re,_=c[0],x=d||J(l),A=function(ne){var ie=s.apply(u,Lt([ne],c));return r&&P?ie[0]:ie};x&&n&&typeof _=="function"&&_.length!=1&&(d=x=!1);var P=this.__chain__,F=!!this.__actions__.length,H=a&&!P,X=d&&!F;if(!a&&x){l=X?l:new re(this);var z=e.apply(l,c);return z.__actions__.push({func:Lr,args:[A],thisArg:i}),new Je(z,P)}return H&&X?e.apply(this,c):(z=this.thru(A),H?r?z.value()[0]:z.value():z)})}),Ye(["pop","push","shift","sort","splice","unshift"],function(e){var t=nr[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);u.prototype[e]=function(){var s=arguments;if(r&&!this.__chain__){var a=this.value();return t.apply(J(a)?a:[],s)}return this[n](function(l){return t.apply(J(l)?l:[],s)})}}),lt(re.prototype,function(e,t){var n=u[t];if(n){var r=n.name+"";le.call(ln,r)||(ln[r]=[]),ln[r].push({name:t,func:n})}}),ln[Ar(i,T).name]=[{name:"wrapper",func:i}],re.prototype.clone=_c,re.prototype.reverse=yc,re.prototype.value=xc,u.prototype.at=Qh,u.prototype.chain=Kh,u.prototype.commit=Xh,u.prototype.next=jh,u.prototype.plant=td,u.prototype.reverse=nd,u.prototype.toJSON=u.prototype.valueOf=u.prototype.value=rd,u.prototype.first=u.prototype.head,En&&(u.prototype[En]=ed),u},on=Kl();Gt?((Gt.exports=on)._=on,oi._=on):Ce._=on}).call(Z0)}(Un,Un.exports)),Un.exports}var q0=Y0();const J0={class:"mix-edit-container"},Q0={class:"content mt-[18px] flex flex-col flex-1 h-[calc(100% - 48px)] overflow-hidden"},K0={class:"flex flex-col h-full overflow-hidden"},X0={class:"h-[calc(100%-38px)] pb-[30px]"},j0={class:"flex items-center justify-center btns"},em=Et({__name:"Edit",setup($){const o=q(!1),i=oa(),f=q("");ht("id",f);const g=q([]);ht("overList",g),ht("updateOverList",U=>{g.value=U});const b=Sv([{title:"一、文案生成",component:Ku},{title:"二、脚本创作",component:Xu},{title:"三、旁白语音",component:ea},{title:"四、成片调整",component:ta}]),C=q(0),M=()=>{g.value.includes(C.value)&&C.value<b.length-1&&C.value++},E=()=>{C.value>0&&C.value--};ht("updateSteps",U=>{C.value!==U&&(g.value.includes(U-1)||U===0)&&(C.value=U)}),ht("activeStep",C),ht("steps",b);const R=Ev({name:"",theme:[""],titleSettings:{open:!0,size:16,color:"#FFFFFF",startime:{mm:"00",ss:"00"},endtime:{mm:"00",ss:"30"},transparency:100,aligning:"left"},subtitleSettings:{open:!0,size:16,color:"#FFFFFF",startime:{mm:"00",ss:"00"},endtime:{mm:"00",ss:"30"},transparency:100},musicSettings:{music:{},random:0,volume:76},coverSettings:{}}),O=U=>{Object.assign(R,U)};ht("updateForm",O),ht("form",R);const y=async()=>{try{const U=await kv({id:f.value});if(U.code===200&&U.data){const{data:V}=U;V.theme?Array.isArray(V.theme)?V.theme=V.theme[0]:V.theme=[V.theme]:V.theme=[],V.name||v(),V.subtitle=!!V.subtitle;const se=V.configuration;V.titleSettings=se.title||{},V.subtitleSettings=se.subtitle||{},V.musicSettings={music:{id:se.music.id||"",url:se.music.url||"",type:se.music.type===2?"material":"system",name:se.music.name||""},random:se.music.random||0,volume:se.music.size||0},V.coverSettings=se.img||{},V.duration=""+V.duration,O(V)}}catch(U){console.log(U)}},{getVideoName:S,saveVideoParams:I}=pa(),v=async()=>{const U=await S();U.name&&O({name:U.name})},T=async()=>{var Pe,ke,Xt,Ut,we,$t,It,pt,zn;if(!R.product_id)return;const U=JSON.parse(JSON.stringify(R.titleSettings));U.text=U.text?U.text:U.content?U.content:"",U.content&&delete U.content;const V={...R,subtitle:+R.subtitle,theme:R.theme&&R.theme[0]?R.theme[0]:"",id:o.value?"":f.value,num:1,configuration:JSON.stringify({title:U||"",subtitle:R.subtitleSettings||"",music:{id:((ke=(Pe=R.musicSettings)==null?void 0:Pe.music)==null?void 0:ke.id)||"",url:((Ut=(Xt=R.musicSettings)==null?void 0:Xt.music)==null?void 0:Ut.url)||"",type:(($t=(we=R.musicSettings)==null?void 0:we.music)==null?void 0:$t.type)==="material"?2:1,size:((It=R.musicSettings)==null?void 0:It.volume)||0,random:((pt=R.musicSettings)==null?void 0:pt.random)||0},img:{url:((zn=R.coverSettings)==null?void 0:zn.url)||""}})},se=await I(V);f.value=se.id,o.value=!1},D=Rv();fa(()=>{D.query.id?(f.value=""+D.query.id,y(),D.query.reEdit&&(o.value=!0)):v()});const B=q0.debounce(T,500);et(()=>R,()=>{B()},{deep:!0});const Z=()=>{i.push("/smart-material/precise-filming")},k=U=>{O({name:U})},Q=q(!1),ae=async()=>{try{Q.value=!0,await T(),await Uv({id:f.value})&&await i.push("/smart-material/precise-preview/"+f.value)}catch(U){console.log(U)}finally{Q.value=!1}};return(U,V)=>{const se=Iv,Pe=Hr;return he(),Te("div",J0,[N(Tv,{title:R.name,"onUpdate:title":V[0]||(V[0]=ke=>R.name=ke),backText:"精准成片",editable:!0,onBack:Z,onTitleChange:k},null,8,["title"]),te("div",Q0,[N(se,{showHeader:!1},{default:j(()=>[N(Xv)]),_:1}),N(se,{showHeader:!1,class:"flex-1 mt-4"},{default:j(()=>[te("div",K0,[te("div",X0,[Nt(N(Ku,{ref:"firstRef"},null,512),[[kt,C.value===0]]),Nt(N(Xu,{ref:"secondRef"},null,512),[[kt,C.value===1]]),Nt(N(ea,{ref:"thirdRef"},null,512),[[kt,C.value===2]]),Nt(N(ta,{ref:"forthRef"},null,512),[[kt,C.value===3]])]),te("div",j0,[C.value>0?(he(),dt(Pe,{key:0,onClick:E,class:"w-[120px]"},{default:j(()=>V[1]||(V[1]=[ut("上一步")])),_:1})):vn("",!0),C.value<Ne(b).length-1?(he(),dt(Pe,{key:1,onClick:M,type:"primary",disabled:!g.value.includes(C.value),class:"w-[120px]"},{default:j(()=>V[2]||(V[2]=[ut("下一步")])),_:1},8,["disabled"])):vn("",!0),C.value===Ne(b).length-1?(he(),dt(Pe,{key:2,onClick:ae,type:"primary",class:"w-[120px]",loading:Q.value},{default:j(()=>V[3]||(V[3]=[ut("保存")])),_:1},8,["loading"])):vn("",!0)])])]),_:1})])])}}}),Om=Rt(em,[["__scopeId","data-v-a6ae3f96"]]);export{Om as default};
