import{k as D,l as d,A as I,V as W,f as S,w as C,I as R,o as m,b as n,t as w,a as V,j as q,h as U,Q as _,_ as T,c as x,a6 as k,F as E,s as L,n as $,b2 as j}from"./index-BBeD0eDz.js";/* empty css               *//* empty css                *//* empty css               */const z={class:"qr-code-content"},B={class:"qr-code-area"},Q={class:"qr-code-container"},F=["src","alt"],N={class:"text-[#999999] text-[16px]"},A={class:"button-area mt-[30px]"},H=D({__name:"QRCodeDialog",props:{modelValue:{type:Boolean,default:!1},publishId:{default:""},publishTitle:{default:""},qrCodeData:{default:""},cancelText:{default:"取消"},objectTitle:{default:""}},emits:["update:modelValue","download","cancel"],setup(h,{emit:v}){const a=h,i=v,u=d({get:()=>a.modelValue,set:e=>{i("update:modelValue",e)}}),c=d(()=>a.publishTitle||"发布二维码"),l=d(()=>a.qrCodeData?a.qrCodeData.startsWith("http")||a.qrCodeData.startsWith("data:image/")?a.qrCodeData:p(a.qrCodeData):p(`publish_${a.publishId}`)),p=e=>`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(e)}&bgcolor=ffffff&color=000000&margin=10`,g=()=>{_.error("二维码加载失败")},r=()=>{u.value=!1,i("cancel")},f=I(!1),b=async()=>{f.value=!0;try{const e=document.createElement("a");if(e.href=l.value,e.download=`${c.value}.png`,l.value.startsWith("http")){const o=await(await fetch(l.value)).blob(),s=window.URL.createObjectURL(o);e.href=s}document.body.appendChild(e),e.click(),document.body.removeChild(e),e.href.startsWith("blob:")&&window.URL.revokeObjectURL(e.href),_.success("二维码下载成功"),i("download",{id:a.publishId,title:c.value,url:l.value})}catch(e){console.error("下载失败:",e),_.error("下载失败，请重试")}finally{f.value=!1}};return W(()=>a.modelValue,e=>{e&&console.log("二维码弹窗打开:",a.publishId,a.publishTitle)}),(e,t)=>{const o=U,s=R;return m(),S(s,{modelValue:u.value,"onUpdate:modelValue":t[0]||(t[0]=y=>u.value=y),title:c.value,width:"560px","align-center":"","close-on-click-modal":!1,"close-on-press-escape":!1,class:"qr-code-dialog",onClose:r},{default:C(()=>[n("div",z,[t[2]||(t[2]=n("div",{class:"tip-info"},[n("p",{class:"text-[#111111] text-[16px] mb-[6px]"},"发布任务生成成功！"),n("p",{class:"text-[#999999] text-[14px] mb-[10px]"},"您可以保存下方二维码，进行视频任务发布")],-1)),n("div",B,[n("div",Q,[n("img",{src:l.value,alt:c.value,class:"qr-code-image",onError:g},null,40,F)])]),n("p",N,w(e.objectTitle),1),n("div",A,[V(o,{class:"cancel-btn",onClick:r},{default:C(()=>[q(w(e.cancelText),1)]),_:1}),V(o,{type:"primary",class:"download-btn",onClick:b,loading:f.value,icon:"Download"},{default:C(()=>t[1]||(t[1]=[q(" 下载二维码 ")])),_:1},8,["loading"])])])]),_:1},8,["modelValue","title"])}}}),Y=T(H,[["__scopeId","data-v-28fbe38e"]]),O={class:"tabs-wrapper-container"},M=["onClick"],G=D({__name:"TabsWrapper",props:{modelValue:{},items:{},styleConfig:{default:()=>({})},fieldConfig:{default:()=>({})}},emits:["update:modelValue","change"],setup(h,{emit:v}){j(t=>({eaf87f18:l.value.underlineWidth,a7b5578e:l.value.underlineHeight,28331023:l.value.activeColor,"7505e2a1":l.value.hoverColor}));const a=h,i=v,u={fontSize:"18px",fontWeight:"bold",color:"#111111",activeColor:"var(--el-color-primary)",hoverColor:"var(--el-color-primary)",width:"140px",height:"42px",gap:"10px",underlineWidth:"32px",underlineHeight:"2px",transition:"all 0.3s"},c={label:"label",value:"value"},l=d(()=>({...u,...a.styleConfig})),p=d(()=>({...c,...a.fieldConfig})),g=t=>t[p.value.label]||"",r=t=>t[p.value.value]||"",f=d(()=>({gap:l.value.gap||""})),b=t=>{const o=l.value,s=a.modelValue===t;return{width:o.width||"",height:o.height||"",fontSize:o.fontSize||"",fontWeight:String(o.fontWeight||""),color:s?o.activeColor||"":o.color||"",transition:o.transition||"",cursor:"pointer",position:"relative"}},e=t=>{i("update:modelValue",t),i("change",t)};return(t,o)=>(m(),x("div",O,[n("div",{class:"tabs-wrapper flex items-center",style:k(f.value)},[(m(!0),x(E,null,L(t.items,s=>(m(),x("div",{class:$(["tab-item flex items-center justify-center",{active:t.modelValue===r(s)}]),key:r(s),style:k(b(r(s))),onClick:y=>e(r(s))},w(g(s)),15,M))),128))],4)]))}}),Z=T(G,[["__scopeId","data-v-d012e269"]]);export{Y as Q,Z as T};
