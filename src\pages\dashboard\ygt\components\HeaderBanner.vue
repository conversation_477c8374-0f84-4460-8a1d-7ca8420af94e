<template>
  <div class="banner-wrapper">
    <img class="bg" src="@/assets/image/dashboard/ygt/banner.png" alt="">
    <div class="banner-content">
      <div class="banner-left">
        <img class="ygt-icon" src="@/assets/image/dashboard/ygt/ygt.png" alt="">
        <h3 class="b-title">企业管理“全域全场景”的智慧协同办公平台</h3>
        <p class="sub-item">
          <img src="@/assets/image/dashboard/ygt/gou.png" alt="">
          <span>集AI老板、人资管理、行政管理、法务管理、财务管理为一体</span>
        </p>
        <p class="sub-item">
          <img src="@/assets/image/dashboard/ygt/gou.png" alt="">
          <span>利用AI人工智能、物联网、大数据、移动互联等先进技术，实现企业云端信息化管理</span>
        </p>
        <p class="sub-item">
          <img src="@/assets/image/dashboard/ygt/gou.png" alt="">
          <span>有效规范流程、降低运营成本，提高企业管理效率</span>
        </p>
      </div>
      <div class="banner-right">
        <div class="video-container">
          <div class="video-header">
            <div class="logo-section">
              <img src="@/assets/image/dashboard/ygt/ygt.png" alt="易管通" class="logo">
              <span class="logo-text">易管通</span>
            </div>
          </div>
          <div class="video-content">
            <h2 class="video-title">一分钟之内带你了解</h2>
            <h2 class="video-subtitle">易管通</h2>
            <div class="video-player" @click="playVideo">
              <img src="@/assets/image/dashboard/ygt/video-cover.png" alt="视频封面" class="video-cover">
              <div class="play-button">
                <img src="@/assets/image/dashboard/ygt/play.png" alt="播放" class="play-icon">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: "HeaderBanner",
  components: {},
  methods: {
    playVideo() {
      // 播放视频的方法
      const videoUrl = "https://zcloud.obs.cn-north-4.myhuaweicloud.com/com_pan/company_80895/20250628/2025062814123150491.mp4";

      // 可以选择在新窗口打开视频
      window.open(videoUrl, '_blank');

      // 或者在当前页面创建视频播放器
      // this.showVideoModal(videoUrl);
    }
  }
};
</script>

<style scoped lang="scss">
.banner-wrapper {
  height: 350px;
  position: relative;
  .bg {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    object-fit: cover;
  }
  .banner-content {
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8%;
    height: 100%;
    .banner-left {
      .ygy-icon {
        width: 266px;
      }
      .b-title {
        font-weight: bold;
        font-size: 36px;
        color: #000000;
        margin-top: 4px;
        margin-bottom: 20px;
      }
      .sub-item {
        display: flex;
        align-items: center;
        height: 32px;
        background: #E3ECFB;
        border-radius: 4px;
        border: 1px solid #F2F7FF;
        padding: 0 10px;
        margin-bottom: 10px;
        img {
          width: 15px;
          margin-right: 6px;
        }
        span {
          font-size: 16px;
          color: #535D6D;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .banner-right {
      .video-container {
        width: 460px;
        height: 280px;
        background: linear-gradient(135deg, #E8F4FD 0%, #F0F8FF 100%);
        border-radius: 16px;
        border: 1px solid #D6E8FF;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;

        .video-header {
          margin-bottom: 16px;

          .logo-section {
            display: flex;
            align-items: center;
            gap: 8px;

            .logo {
              width: 24px;
              height: 24px;
            }

            .logo-text {
              font-size: 16px;
              font-weight: 600;
              color: #2B5CE6;
            }
          }
        }

        .video-content {
          .video-title {
            font-size: 24px;
            font-weight: bold;
            color: #1a1a1a;
            margin: 0 0 4px 0;
            line-height: 1.2;
          }

          .video-subtitle {
            font-size: 24px;
            font-weight: bold;
            color: #2B5CE6;
            margin: 0 0 20px 0;
            line-height: 1.2;
          }

          .video-player {
            position: relative;
            width: 100%;
            height: 160px;
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s ease;

            &:hover {
              transform: scale(1.02);

              .play-button {
                transform: translate(-50%, -50%) scale(1.1);
              }
            }

            .video-cover {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .play-button {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 60px;
              height: 60px;
              background: rgba(255, 255, 255, 0.9);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.3s ease;
              box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);

              .play-icon {
                width: 24px;
                height: 24px;
                margin-left: 2px; // 微调播放图标位置
              }
            }
          }
        }

        // 添加装饰性的3D立体图形背景
        &::before {
          content: '';
          position: absolute;
          top: -20px;
          right: -20px;
          width: 100px;
          height: 100px;
          background: linear-gradient(45deg, #4A90E2, #7BB3F0);
          border-radius: 12px;
          opacity: 0.1;
          transform: rotate(15deg);
        }

        &::after {
          content: '';
          position: absolute;
          bottom: -30px;
          right: 20px;
          width: 80px;
          height: 80px;
          background: linear-gradient(45deg, #5BA7F7, #8FC5F4);
          border-radius: 8px;
          opacity: 0.1;
          transform: rotate(-10deg);
        }
      }
    }
  }
}
</style>