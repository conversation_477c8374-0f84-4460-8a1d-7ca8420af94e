<template>
  <div class="banner-wrapper">
    <img class="bg" src="@/assets/image/dashboard/ygt/banner.png" alt="">
    <div class="banner-content">
      <div class="banner-left">
        <img class="ygt-icon" src="@/assets/image/dashboard/ygt/ygt.png" alt="">
        <h3 class="b-title">企业管理“全域全场景”的智慧协同办公平台</h3>
        <p class="sub-item">
          <img src="@/assets/image/dashboard/ygt/gou.png" alt="">
          <span>集AI老板、人资管理、行政管理、法务管理、财务管理为一体</span>
        </p>
        <p class="sub-item">
          <img src="@/assets/image/dashboard/ygt/gou.png" alt="">
          <span>利用AI人工智能、物联网、大数据、移动互联等先进技术，实现企业云端信息化管理</span>
        </p>
        <p class="sub-item">
          <img src="@/assets/image/dashboard/ygt/gou.png" alt="">
          <span>有效规范流程、降低运营成本，提高企业管理效率</span>
        </p>
      </div>
      <div class="banner-right">
        <div class="video-container">
          <div class="video-header">
            <div class="logo-section">
              <img src="@/assets/image/dashboard/ygt/ygt.png" alt="易管通" class="logo">
              <span class="logo-text">易管通</span>
            </div>
          </div>
          <div class="video-content">
            <h2 class="video-title">一分钟之内带你了解</h2>
            <h2 class="video-subtitle">易管通</h2>
            <div class="video-player" @click="playVideo">
              <img src="@/assets/image/dashboard/ygt/video-cover.png" alt="视频封面" class="video-cover">
              <div class="play-button">
                <img src="@/assets/image/dashboard/ygt/play.png" alt="播放" class="play-icon">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 视频播放弹窗 -->
    <div v-if="showVideoModal" class="video-modal" @click="closeVideoModal">
      <div class="video-modal-content" @click.stop>
        <div class="video-modal-header">
          <h3>一分钟之内带你了解易管通</h3>
          <button class="close-btn" @click="closeVideoModal">×</button>
        </div>
        <div class="video-wrapper">
          <video
            ref="videoPlayer"
            :src="videoUrl"
            controls
            autoplay
            @loadstart="onVideoLoadStart"
            @canplay="onVideoCanPlay"
            @error="onVideoError"
          >
            您的浏览器不支持视频播放。
          </video>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: "HeaderBanner",
  components: {},
  data() {
    return {
      showVideoModal: false,
      videoUrl: "https://zcloud.obs.cn-north-4.myhuaweicloud.com/com_pan/company_80895/20250628/2025062814123150491.mp4"
    };
  },
  methods: {
    playVideo() {
      this.showVideoModal = true;
      // 确保弹窗显示后再播放视频
      this.$nextTick(() => {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.play().catch(err => {
            console.log('视频自动播放失败:', err);
          });
        }
      });
    },
    closeVideoModal() {
      this.showVideoModal = false;
      // 停止视频播放
      if (this.$refs.videoPlayer) {
        this.$refs.videoPlayer.pause();
        this.$refs.videoPlayer.currentTime = 0;
      }
    },
    onVideoLoadStart() {
      console.log('视频开始加载');
    },
    onVideoCanPlay() {
      console.log('视频可以播放');
    },
    onVideoError(error) {
      console.error('视频播放错误:', error);
      alert('视频加载失败，请检查网络连接或稍后重试');
    }
  },
  mounted() {
    // 监听ESC键关闭弹窗
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.showVideoModal) {
        this.closeVideoModal();
      }
    });
  }
};
</script>

<style scoped lang="scss">
.banner-wrapper {
  height: 350px;
  position: relative;
  .bg {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    object-fit: cover;
  }
  .banner-content {
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8%;
    height: 100%;
    .banner-left {
      .ygy-icon {
        width: 266px;
      }
      .b-title {
        font-weight: bold;
        font-size: 36px;
        color: #000000;
        margin-top: 4px;
        margin-bottom: 20px;
      }
      .sub-item {
        display: flex;
        align-items: center;
        height: 32px;
        background: #E3ECFB;
        border-radius: 4px;
        border: 1px solid #F2F7FF;
        padding: 0 10px;
        margin-bottom: 10px;
        img {
          width: 15px;
          margin-right: 6px;
        }
        span {
          font-size: 16px;
          color: #535D6D;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .banner-right {
      .video-container {
        width: 460px;
        height: 280px;
        background: linear-gradient(135deg, #E8F4FD 0%, #F0F8FF 100%);
        border-radius: 16px;
        border: 1px solid #D6E8FF;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;

        .video-header {
          margin-bottom: 16px;

          .logo-section {
            display: flex;
            align-items: center;
            gap: 8px;

            .logo {
              width: 24px;
              height: 24px;
            }

            .logo-text {
              font-size: 16px;
              font-weight: 600;
              color: #2B5CE6;
            }
          }
        }

        .video-content {
          .video-title {
            font-size: 24px;
            font-weight: bold;
            color: #1a1a1a;
            margin: 0 0 4px 0;
            line-height: 1.2;
          }

          .video-subtitle {
            font-size: 24px;
            font-weight: bold;
            color: #2B5CE6;
            margin: 0 0 20px 0;
            line-height: 1.2;
          }

          .video-player {
            position: relative;
            width: 100%;
            height: 160px;
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s ease;

            &:hover {
              transform: scale(1.02);

              .play-button {
                transform: translate(-50%, -50%) scale(1.1);
              }
            }

            .video-cover {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .play-button {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 60px;
              height: 60px;
              background: rgba(255, 255, 255, 0.9);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.3s ease;
              box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);

              .play-icon {
                width: 24px;
                height: 24px;
                margin-left: 2px; // 微调播放图标位置
              }
            }
          }
        }

        // 添加装饰性的3D立体图形背景
        &::before {
          content: '';
          position: absolute;
          top: -20px;
          right: -20px;
          width: 100px;
          height: 100px;
          background: linear-gradient(45deg, #4A90E2, #7BB3F0);
          border-radius: 12px;
          opacity: 0.1;
          transform: rotate(15deg);
        }

        &::after {
          content: '';
          position: absolute;
          bottom: -30px;
          right: 20px;
          width: 80px;
          height: 80px;
          background: linear-gradient(45deg, #5BA7F7, #8FC5F4);
          border-radius: 8px;
          opacity: 0.1;
          transform: rotate(-10deg);
        }
      }
    }
  }
}

// 视频弹窗样式
.video-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease;

  .video-modal-content {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    max-width: 90vw;
    max-height: 90vh;
    width: 800px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;

    .video-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      border-bottom: 1px solid #eee;
      background: #f8f9fa;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        color: #666;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s ease;

        &:hover {
          background: #e9ecef;
          color: #333;
        }
      }
    }

    .video-wrapper {
      position: relative;
      padding-bottom: 56.25%; // 16:9 宽高比
      height: 0;

      video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        outline: none;
      }
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .video-modal {
    .video-modal-content {
      width: 95vw;
      margin: 20px;

      .video-modal-header {
        padding: 16px 20px;

        h3 {
          font-size: 16px;
        }
      }
    }
  }
}
</style>