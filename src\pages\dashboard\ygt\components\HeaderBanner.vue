<template>
  <div class="banner-wrapper">
    <img class="bg" src="@/assets/image/dashboard/ygt/banner.png" alt="">
    <div class="banner-content">
      <div class="banner-left">
        <img class="ygt-icon" src="@/assets/image/dashboard/ygt/ygt.png" alt="">
        <h3 class="b-title">企业管理“全域全场景”的智慧协同办公平台</h3>
        <p class="sub-item">
          <img src="@/assets/image/dashboard/ygt/gou.png" alt="">
          <span>集AI老板、人资管理、行政管理、法务管理、财务管理为一体</span>
        </p>
        <p class="sub-item">
          <img src="@/assets/image/dashboard/ygt/gou.png" alt="">
          <span>利用AI人工智能、物联网、大数据、移动互联等先进技术，实现企业云端信息化管理</span>
        </p>
        <p class="sub-item">
          <img src="@/assets/image/dashboard/ygt/gou.png" alt="">
          <span>有效规范流程、降低运营成本，提高企业管理效率</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: "HeaderBanner",
  components: {},
};
</script>

<style scoped lang="scss">
.banner-wrapper {
  height: 350px;
  position: relative;
  .bg {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    object-fit: cover;
  }
  .banner-content {
    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8%;
    height: 100%;
    .banner-left {
      .ygy-icon {
        width: 266px;
      }
      .b-title {
        font-weight: bold;
        font-size: 36px;
        color: #000000;
        margin-top: 4px;
        margin-bottom: 20px;
      }
      .sub-item {
        display: flex;
        align-items: center;
        height: 32px;
        background: #E3ECFB;
        border-radius: 4px;
        border: 1px solid #F2F7FF;
        padding: 0 10px;
        margin-bottom: 10px;
        img {
          width: 15px;
          margin-right: 6px;
        }
        span {
          font-size: 16px;
          color: #535D6D;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>