var pointerView = {
  props: ['left', 'top', 'isBtn', 'isImg', 'imgWidth', 'data'],
  data() {
    return {
      imgLoaded: false
    }
  },
  watch: { 
    data: {
      handler(newVal) {
        // console.log(newVal)
        this.imgLoaded = false
      },
      deep: true
    }
  },
  methods: {
    handleImageLoad() {
      console.log('图片加载完成')
      this.imgLoaded = true
    }
  },
  template: `
    <div class="pointer-box">
      <template v-if="isBtn">
        <div class="btn" @click="$emit('click')"><slot></slot></div>
      </template>
      <template v-else-if="isImg">
        <img :class="['width' + imgWidth]" :src="data.img" alt="" @load="handleImageLoad" />
      </template>
      <template v-else>
        <slot></slot>
      </template>
      <div v-if="imgLoaded || isBtn" :class="['pointer', 'width87', 'left' + left, 'top' + top]">
        <!-- 文字提示 -->
        <div v-if="data && data.tips" class="pointer-tips animate__animated animate__fadeInDown animate__delay-0.5s">{{ data.tips }}</div>
        <img v-if="isImg" class="animate__animated animate__fadeInUp animate__delay-2s" src="./images/pointer.gif" alt="" srcset="">
        <img v-else src="./images/pointer.gif" alt="" srcset="">
      </div>
      <!-- 点击区域 -->
      <div v-if="imgLoaded && !isBtn" :class="['pointer-area', 'width' + data.areaW, 'height' + data.areaH, 'left' + data.areaL, 'top' + data.areaT]" @click="$emit('click')"></div>
    </div>
  `
}

var tipView = {
  props: ['top'],
  data() {
    return { }
  },
  methods: { },
  template: `
    <div :class="['tips-box', 'w100', (top ? ('top' + top) : '')]">
      <div class="text-box px25 py28"><slot></slot></div>
      <div class="tips-icon width110">
        <img src="./images/long.gif" alt="" />
      </div>
    </div>
  `
}