import{h as t}from"./request-Ciyrqj7N.js";const i=e=>t.post("/completefilm/type",e),s=e=>t.post("/completefilm/update",e),p=e=>t.post("/completefilm/create",e),r=e=>t.post("/completefilm/loglist",e),l=e=>t.post("/completefilm/submit",e),c=e=>t.post("/completefilm/list",e),m=e=>t.post("/completefilm/show",e),n=e=>t.post("/completefilm/logsdelete",e),A=e=>t.post("/completefilm/delete",e),a=e=>t.post("/completefilm/copy",e);export{p as a,r as b,a as c,A as d,l as e,n as f,m as g,c as h,i,s};
