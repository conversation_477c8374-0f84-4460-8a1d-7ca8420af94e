(function () {
  'use strict';
  var global = tinymce.util.Tools.resolve('tinymce.PluginManager');

  function selectLocalImages(editor) {
    let settings = editor.settings;
    if(settings.import_from_bucket){
      settings.import_from_bucket_callback(editor, 2);
    }
  }

  var register$1 = function (editor) {
    editor.ui.registry.getAll().icons.importfrombucketicon || editor.ui.registry.addIcon('importfrombucketicon','<svg t="1663053532235" class="icon" viewBox="0 0 1104 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6689" width="24" height="24"><path d="M919.251863 382.026721q0.284437-7.39535 0.284436-14.733812a367.264465 367.264465 0 1 0-734.585817 0q0 7.281576 0.284437 14.733812a241.259076 241.259076 0 0 0 55.977112 475.919225h65.249743v-75.091248H241.268918a166.167829 166.167829 0 0 1-12.628983-331.880558l39.821116-3.015028-5.68873-39.821116a296.724202 296.724202 0 0 1-2.901253-41.015749 292.230105 292.230105 0 0 1 584.403322 0 296.382878 296.382878 0 0 1-2.901253 41.015749l-5.688731 39.821116 39.821116 3.015028a166.167829 166.167829 0 0 1-12.628982 331.880558h-64.453321v75.091248h64.794645a241.259076 241.259076 0 0 0 55.977112-475.862337z" fill="#222f3e" p-id="6690"></path><path d="M584.6976 556.386323a42.665482 42.665482 0 0 0-64.908419 0l-130.84081 150.92203a45.509847 45.509847 0 0 0-11.377461 30.264048 44.485875 44.485875 0 0 0 43.575678 45.22541 42.836143 42.836143 0 0 0 32.425766-15.018249l54.782478-63.144913v274.196828a43.575678 43.575678 0 1 0 87.151357 0v-274.424377l54.782478 63.144912a42.665482 42.665482 0 0 0 32.368879 15.01825 44.485875 44.485875 0 0 0 43.689453-45.22541 46.306269 46.306269 0 0 0-11.377462-30.264049z" fill="#222f3e" p-id="6691"></path></svg>');

    editor.ui.registry.addButton('importfrombucketVideo', {
      icon: 'importfrombucketicon',
      tooltip: '视频',
      onAction: function () {
        selectLocalImages(editor)
      }
    });
    editor.ui.registry.addMenuItem('importfrombucketVideo', {
      icon: 'importfrombucketicon',
      text: '视频',
      onAction: function () {
        selectLocalImages(editor)
      }
    });
  };
  var Buttons = { register: register$1 };
  function Plugin () {
    global.add('importfrombucketVideo', function (editor) {
      Buttons.register(editor);
    });
  }
  Plugin();
}());
