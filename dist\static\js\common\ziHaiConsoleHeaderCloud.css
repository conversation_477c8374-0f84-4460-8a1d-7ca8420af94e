#zhyConsoleHeader, #zhyConsoleHeader * {
  margin: 0;
  padding: 0;
}

#zhy<PERSON><PERSON>oleHeader,
#zhyConsoleHeader div,
#zhyConsoleHeader dl,
#zhyConsoleHeader dt,
#zhyConsoleHeader dd,
#zhyConsoleHeader ol,
#zhyConsoleHeader ul,
#zhyConsoleHeader li,
#zhyConsoleHeader h1,
#zhyConsoleHeader h2,
#zhyConsoleHeader h3,
#zhyConsoleHeader h4,
#zhyConsoleHeader h5,
#zhyConsoleHeader h6,
#zhyConsoleHeader pre,
#zhyConsoleHeader form,
#zhyConsoleHeader fieldset,
#zhyConsoleHeader input,
#zhyConsoleHeader textarea,
#zhyConsoleHeader p,
#zhyConsoleHeader blockquote,
#zhyConsoleHeader th,
#zhyConsoleHeader td,
#zhyConsoleHeader p {
  margin: 0;
  padding: 0;
  font-size: 14px;
}

#zhyConsoleHeader li {
  list-style: none;
}

#zhyConsoleHeader {
  position: relative;
  width: 100%;
  z-index: 999999;
  background: #333854;
  font-size: 14px;
  color: #333;
  min-width: 1200px;
}

#zhyConsoleHeader .weak-tips {
  position: relative;
}

#zhyConsoleHeader .weak-tips::after {
  position: absolute;
  right: -10px;
  top: 0;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: red;
  content: "";
}

#zhyConsoleHeader .header {
  display: flex;
  align-items: center;
  width: 100%;
  height: 51px;
  background-color: #333854;
}

#zhyConsoleHeader .zhyHomeHeader-title {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}

#zhyConsoleHeader #avatar:hover #seledbox {
  display: block;
}

#zhyConsoleHeader .zhyHomeHeader-title a {
  display: flex;
  flex-direction: row;
  align-items: center;
  text-decoration: none;
}

#zhyConsoleHeader .zhyHomeHeader-title a img:nth-child(1) {
  height: 33px;
  margin-left: 13px;
}

#zhyConsoleHeader .zhyHomeHeader-title a img:nth-child(2) {
  margin-left: 10px;
  height: 25px;
  margin-top: -5px;
}

#zhyConsoleHeader .zhyHomeHeader-title a > span {
  margin-left: 15px;
  color: #5c6076;
  font-size: 20px;
  line-height: 15px;
}

#zhyConsoleHeader .zhyHomeHeader-title .title-company {
  position: relative;
  display: flex;
  align-items: center;
}

#zhyConsoleHeader .zhyHomeHeader-title .title-company span {
  color: #fff;
  font-size: 14px;
}

#zhyConsoleHeader .zhyHomeHeaderPhone {
  width: 83px;
  margin-right: 20px;
  color: rgba(255, 255, 255, 0.6)
}

#zhyConsoleHeader .zhyHomeHeader-title .title-company > a {
  margin-left: 20px;
  padding: 10px 0;
  color: #ffffff;
  font-size: 16px;
  cursor: pointer;
  text-decoration: none;
}

#zhyConsoleHeader #text-div ul li {
  position: relative;
  float: left;
  height: 50px;
  line-height: 50px;
}

#zhyConsoleHeader #text-div ul li > a {
  color: #adb0b8;
  font-size: 14px;
  padding: 0 14px;
  display: block;
  position: relative;
  text-decoration: none;
}

#zhyConsoleHeader #text-div ul li:hover > a {
  color: #ffffff;
  text-decoration: none;
}

#zhyConsoleHeader #text-div ul li:hover a + div {
  display: block;
}

#zhyConsoleHeader .icon-div {
  display: flex;
  align-items: center;
}

#zhyConsoleHeader .icon-div a {
  height: 18px;
  display: inherit;
  text-decoration: none;
}

#zhyConsoleHeader .icon-div a img {
  width: 18px;
  margin-right: 20px;
  cursor: pointer;
}

#zhyConsoleHeader .icon-div a img:hover {
  filter: drop-shadow(#fff 0 0);
}

#zhyConsoleHeader #avatar {
  position: relative;
  cursor: pointer;
}

#zhyConsoleHeader #avatar img {
  margin-right: 12px;
  width: 35px;
  height: 35px;
  cursor: pointer;
  border-radius: 17px;
}

#zhyConsoleHeader .posiabox {
  position: absolute;
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  display: none;
  text-align: center;
  width: 100px;
  background: #ffffff;
  box-shadow: 0 0 8px #999999;
  border-radius: 5px;
  font-size: 14px;
  padding: 5px 0;
  z-index: 100;
}

#zhyConsoleHeader #avatar::after {
  content: "";
  position: absolute;
  display: block;
  top: 38px;
  left: 0;
  width: 100%;
  height: 30%;
}

#zhyConsoleHeader #seledbox {
  left: 82px;
}

#zhyConsoleHeader .posiabox::after {
  position: absolute;
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  left: 0;
  bottom: 10px;
  z-index: -1;
}

#zhyConsoleHeader .posiabox::before {
  position: absolute;
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  background: #fff;
  left: 0;
  right: 0;
  margin: auto;
  top: -5px;
  transform: rotate(45deg);
  z-index: -1;
}

#zhyConsoleHeader .posiabox a {
  display: block;
  color: #444;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  text-decoration: none;
  width: 100px;
  overflow: hidden;
}

#zhyConsoleHeader .posiabox a:last-child {
  border-bottom: 0 solid #DDD;
}

#zhyConsoleHeader .posiabox a:hover {
  color: #409EFF !important;
  background: rgba(64, 158, 255, 0.05);
}

#zhyConsoleHeader .btn-change {
  margin-left: 16px;
  padding: 6px 12px;
  height: 18px;
  background-color: #434f76;
  border-radius: 5px;
  cursor: pointer;
  line-height: 18px;
  box-sizing: content-box;
  display: flex;
  justify-content: center;
  align-items: center;
}

#zhyConsoleHeader .btn-change img {
  margin-right: 4px;
}

#zhyConsoleHeader .jzt-open-box {
  position: fixed;
  width: 100vw;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000000;
  display: none;
}

#zhyConsoleHeader .jzt-open-box .layui-layer-shade {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.2);
}

#zhyConsoleHeader .jzt-open-box .jzt-open-main {
  width: 60vw;
  padding: 20px 30px;
  background: #FFFFFF;
  border-radius: 6px;
  position: absolute;
  left: 0;
  right: 0;
  top: 20%;
  margin: auto;
  z-index: 102;
  box-sizing: border-box;
}

#zhyConsoleHeader .jzt-open-box .close {
  position: absolute;
  top: 6px;
  right: 20px;
  cursor: pointer;
  font-size: 26px;
  color: #909399;
  user-select: none;
  font-style: normal;
}

#zhyConsoleHeader .jzt-open-box .el-dialog__header {
  padding: 10px;
  border-bottom: 1px solid #dcdfe6;
}

#zhyConsoleHeader .el-button--text {
  margin-right: 20px;
  border: 1px solid #20a0ff;
  width: 98px;
  height: 40px;
  font-size: 14px;
  color: #20a0ff;
}

.jzt-open-main .el-button{
  height: auto;
}

.el-button--text .el-button--primary {
  font-size: 14px;
  color: #fff;
  border: 1px solid #20a0ff;
  background: #20a0ff;
}

#zhyConsoleHeader .company-list .loading {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  flex: 1;
}

#zhyConsoleHeader .company-list ul {
  margin-top: 15px;
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 5px;
  display: flex;
  align-items: stretch;
  flex-wrap: wrap;
}

#zhyConsoleHeader .company-list li {
  flex-shrink: 0;
  width: calc(100% / 4 - 10px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 10px;
  cursor: pointer;
  box-sizing: border-box;
  /*  灰色边框*/
  border: 1px solid #E4E7ED;
  border-radius: 4px;
  margin: 5px;
  min-width: 150px;
}

#zhyConsoleHeader .company-list li:hover,
#zhyConsoleHeader .company-list li.active{
  border: 1px solid #409EFF;
  color: #409EFF;
}
#zhyConsoleHeader .company-list li * {
  pointer-events: none;
}
#zhyConsoleHeader .input-box {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
}
#zhyConsoleHeader .input-box input {
  width: 100%;
  height: 40px;
  border: 1px solid #dcdfe6;
  margin-right: 10px;
  border-radius: 4px;
  padding: 0 10px;
  box-sizing: border-box;
}

.zhyContainer {
  height: calc(100% - 51px);
}

#zhyMain {
  width: 100%;
}

.goBack{
  position: fixed;
  right: 20px;
  bottom: 30px;
  z-index: 99;
  cursor: pointer;
}

#zhyConsoleHeader #monica-writing-entry-btn-root{
  display: none;
  width: 0;
}

.el-dropdown-menu__item:focus a, .el-dropdown-menu__item:not(.is-disabled):hover a {
  color: #66b1ff;
}

#zhyConsoleHeader .primary-button{
  margin-left: 12px;
  width: 149px;
  height: 30px;
  background: #4D80FF;
  border-radius: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #FFFFFF;
  cursor: pointer;
  margin-right: 12px;
}

@media (max-width: 700px) {
  .avatar-container, .zhyHomeHeader-menu {
    display: none !important;
  }
}

@media (max-width: 600px) {
  .goBack{
    width: 50px;
    height: 50px;
    bottom: 22%;
    right: 10px;
  }
}
