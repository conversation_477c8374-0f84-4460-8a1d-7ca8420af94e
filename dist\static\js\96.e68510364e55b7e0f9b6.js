webpackJsonp([96],{"6CQw":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("cMGX"),n=a("+PoZ"),s=a("irLX"),r=a("wU6q"),l={name:"index",watch:{$route:"getPath"},components:{Pagination:i.a,CardWrap:r.a},data:function(){return{tableLoading:!1,tableData:[],total:0,queryList:{page:1,limit:10,type:0},selectData:[]}},created:function(){this.getList()},methods:{getPath:function(){"0"===this.$route.query.status?(this.queryList.status=0,this.queryList.type=0,s.a.$emit("left",1)):"1"===this.$route.query.status?(this.queryList.status=1,this.queryList.type=1,s.a.$emit("left",2)):(this.queryList.status=null,this.queryList.type=null),this.getList()},getList:function(){var t=this;this.tableLoading=!0,Object(n.b)(this.queryList).then(function(e){t.tableLoading=!1,e.data.data.forEach(function(t){1===t.type?t.type="产品动态":2===t.type?t.type="服务消息":3===t.type&&(t.type="服务消息")}),t.tableData=e.data.data,t.total=e.data.total}).catch(function(){t.tableLoading=!1})},showDetail:function(t){this.$router.push({path:"/console/order/detail",query:{no:t.no}})},handleSelectionChange:function(t){this.selectData=t},clickType:function(t){this.queryList.type=t,this.getList()},putReadStatus:function(t){var e=this,a=0===t?"未读":"已读";this.$confirm("标记为"+a+", 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var a=e.getTableIds();a&&Object(n.c)({ids:a,status:t}).then(function(t){e.$message.success("操作成功"),e.getList()})})},destory:function(t){var e=this,a=[];if("all"===t)this.tableData.forEach(function(t){a.push(t.id)});else if(!1===(a=this.getTableIds()))return;this.$confirm("删除信息, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){a&&Object(n.a)({ids:a}).then(function(t){e.$message.success("删除成功"),e.getList()})})},getTableIds:function(){if(0===this.selectData.length)return this.$message.error("请选择消息"),!1;var t=[];return this.selectData.forEach(function(e){t.push(e.id)}),t}}},o={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("card-wrap",{attrs:{title:"全部消息"}},[a("div",{staticClass:"container-wrapper"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.queryList,size:"small"}},[a("el-form-item",[a("el-button",{attrs:{type:0===t.queryList.type?"primary":""},on:{click:function(e){return t.clickType(0)}}},[t._v("全部消息类型")]),t._v(" "),a("el-button",{attrs:{type:1===t.queryList.type?"primary":""},on:{click:function(e){return t.clickType(1)}}},[t._v("产品动态")]),t._v(" "),a("el-button",{attrs:{type:2===t.queryList.type?"primary":""},on:{click:function(e){return t.clickType(2)}}},[t._v("服务消息")]),t._v(" "),a("el-button",{attrs:{type:3===t.queryList.type?"primary":""},on:{click:function(e){return t.clickType(3)}}},[t._v("活动消息")])],1)],1),t._v(" "),a("div",{staticClass:"table"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,stripe:"",height:"100%"},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),a("el-table-column",{attrs:{label:"标题内容"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-align-center"},[0===e.row.status?a("span",{staticClass:"read"}):t._e(),t._v(" "),a("span",[t._v(t._s(e.row.title))])])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"created_at",label:"提交时间"}}),t._v(" "),a("el-table-column",{attrs:{prop:"type",label:"类型"}})],1)],1),t._v(" "),a("el-row",{staticStyle:{"margin-top":"20px"}},[a("el-col",[a("el-button",{attrs:{type:"warning",size:"small"},on:{click:function(e){return t.destory()}}},[t._v("删除")]),t._v(" "),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.putReadStatus(1)}}},[t._v("标记已读")]),t._v(" "),a("el-button",{attrs:{type:"success",size:"small"},on:{click:function(e){return t.putReadStatus(0)}}},[t._v("标记未读")]),t._v(" "),a("el-button",{attrs:{type:"danger",size:"small"},on:{click:function(e){return t.destory("all")}}},[t._v("全部删除")])],1)],1),t._v(" "),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"pagation-wrap",staticStyle:{"text-align":"center"},attrs:{total:t.total,page:t.queryList.page,limit:t.queryList.limit},on:{"update:page":function(e){return t.$set(t.queryList,"page",e)},"update:limit":function(e){return t.$set(t.queryList,"limit",e)},pagination:t.getList}})],1)])],1)},staticRenderFns:[]};var c=a("VU/8")(l,o,!1,function(t){a("AMtK")},"data-v-12359b36",null);e.default=c.exports},AMtK:function(t,e){}});