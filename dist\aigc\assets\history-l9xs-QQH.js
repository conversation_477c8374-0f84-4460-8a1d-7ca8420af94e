import{W as E}from"./Writing-CreIJVvw.js";import{U as H,A as p,k as D,l as v,V as B,B as U,c,b as r,q as z,f,a as _,W as I,p as m,w as S,J as Q,F as h,s as g,X as W,o as l,Y as F,n as M,t as C,_ as R}from"./index-BBeD0eDz.js";/* empty css                *//* empty css              */import{g as j}from"./common-DBXWCL9C.js";import{u as q}from"./aiDocument-d1UPkFF7.js";import{g as J,a as O,b as T,e as X,d as Y}from"./index-B2D9HeJa.js";import{E as G}from"./empty-CSpEo1eL.js";/* empty css               *//* empty css                 */import"./aiDocument-BqsEIq7y.js";import"./request-Ciyrqj7N.js";import"./aiVideo-CrvXrva8.js";import"./aiVideo-B86MWzMI.js";const K=H("product",()=>{const y=p([]),a=p(!1),o=p({page:1,perPage:10,name:""}),i=p(0),d=p([]),n=async()=>{a.value=!0;try{const t=await J(o.value);y.value=t.data,i.value=t.total}catch(t){console.error("获取产品列表失败",t)}finally{a.value=!1}};return{pageLoading:a,total:i,listQ:o,productList:y,addProduct:async t=>{try{await T(t),n()}catch(s){console.error("添加产品失败",s)}},editProduct:async t=>{try{await X(t),n()}catch(s){console.error("编辑产品失败",s)}},deleteProduct:async t=>{try{await Y({id:t}),n()}catch(s){console.error("删除产品失败",s)}},fetchProductList:n,fetchProductListNoPage:async()=>{if(!(d.value.length>0))try{const t=await O({});d.value=t}catch(t){console.error("获取产品列表失败",t)}},productListNoPage:d,handleSizeChange:t=>{o.value.perPage=t,n()},handleCurrentChange:t=>{o.value.page=t,n()}}}),Z={class:"history-list h-full flex flex-col"},ee={class:"month-filter flex items-center"},te=["onClick"],se={class:"history-item-content"},oe={class:"history-item-time"},le={class:"flex items-center"},ae={class:"history-item-product flex items-center w-1/2 pr-[10px]"},re={class:"flex-1 truncate"},ne={class:"history-item-keywords flex items-center w-1/2"},ie={class:"flex-1 truncate"},ce=D({__name:"HistoryList",props:{selectedHistory:{}},emits:["select"],setup(y,{emit:a}){const o=q(),i=K(),d=v(()=>i.productListNoPage),n=a,x=t=>{n("select",t)},u=v({get(){return o.listQ},set(t){o.listQ=t}}),P=v(()=>o.historyList);B(()=>o.defaultSelectItem,t=>{x(t)});const L=p(null),b=()=>{console.log("触底了"),o.handleScroll(L.value)},V=v(()=>o.publishScene);return U(()=>{i.fetchProductListNoPage(),o.initHistoryList(),o.getAiDocumentType()}),(t,s)=>{const w=F,k=I,N=W,$=Q;return l(),c("div",Z,[r("div",ee,[_(k,{class:"mr-[10px]",modelValue:u.value.date,"onUpdate:modelValue":s[0]||(s[0]=e=>u.value.date=e),placeholder:"请选择月份",clearable:"",onChange:s[1]||(s[1]=e=>m(o).refreshData())},{default:S(()=>[(l(!0),c(h,null,g(m(j)(),e=>(l(),f(w,{key:e,label:e,value:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),_(k,{class:"mr-[10px]",modelValue:u.value.product,"onUpdate:modelValue":s[2]||(s[2]=e=>u.value.product=e),placeholder:"请选择产品",clearable:"",onChange:s[3]||(s[3]=e=>m(o).refreshData())},{default:S(()=>[(l(!0),c(h,null,g(d.value,e=>(l(),f(w,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),_(k,{class:"mr-[10px]",modelValue:u.value.type,"onUpdate:modelValue":s[4]||(s[4]=e=>u.value.type=e),placeholder:"请选择场景",clearable:"",onChange:s[5]||(s[5]=e=>m(o).refreshData())},{default:S(()=>[(l(!0),c(h,null,g(V.value,e=>(l(),f(w,{key:e.id,label:e.title,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),P.value.length>0?z((l(),c("div",{key:0,ref_key:"historyListRef",ref:L,class:"history-items flex-1 overflow-y-auto",onScroll:b},[(l(!0),c(h,null,g(P.value,e=>{var A;return l(),c("div",{key:e.id,class:M(["history-item",{active:e.id===((A=t.selectedHistory)==null?void 0:A.id)}]),onClick:fe=>x(e)},[r("div",se,[r("div",oe,C(e.created_at),1),r("div",le,[r("div",ae,[s[6]||(s[6]=r("span",{class:"text-[#999999]"},"产品：",-1)),r("span",re,C(e.product),1)]),r("div",ne,[s[7]||(s[7]=r("span",{class:"text-[#999999]"},"场景：",-1)),r("span",ie,C(e.type),1)])])])],10,te)}),128))],32)),[[$,m(o).pageLoading]]):(l(),f(N,{key:1,class:"flex-1",description:"暂无相关记录",image:m(G),"image-size":293},null,8,["image"]))])}}}),de=R(ce,[["__scopeId","data-v-6f2ffedb"]]),ue={class:"history-container flex h-full overflow-hidden"},pe={class:"content-wrapper flex-1 h-full ml-4"},me={key:1,class:"flex h-full items-center justify-center text-gray-400"},Ae=D({__name:"history",setup(y){const a=p(),o=i=>{console.log(i,"历史记录"),a.value=i};return(i,d)=>(l(),c("div",ue,[_(de,{class:"!w-2/5",onSelect:o,"selected-history":a.value},null,8,["selected-history"]),r("div",pe,[a.value?(l(),f(E,{key:0,maxSelectCount:5,data:a.value.copywritinglist,"onUpdate:data":d[0]||(d[0]=n=>a.value.copywritinglist=n)},null,8,["data"])):(l(),c("div",me," 请选择左侧历史记录查看详情 "))])]))}});export{Ae as default};
