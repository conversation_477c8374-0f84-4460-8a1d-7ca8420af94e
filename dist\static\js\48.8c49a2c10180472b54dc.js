webpackJsonp([48],{"+i/E":function(t,i){},"N+uq":function(t,i){},nDe1:function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0});var a=e("mvHQ"),l=e.n(a),s=e("pI5c"),o=e("lbHh"),n=e.n(o),r={name:"UploadImg",props:{name:{type:String,default:function(){return"files[]"}},action:{type:String,default:function(){return"/api/upload"}},url:{type:String,default:function(){return""}},info:{type:Object,default:function(){}},tip:{type:String,default:function(){return"建议尺寸 750 x 750 且不超过2M 的jpg/png图片"}},accept:{type:String,default:function(){return""}},size:{type:Number,default:function(){return 2e3}},image:{type:String,default:function(){return""}},width:{type:String,default:function(){return"178px"}},height:{type:String,default:function(){return"178px"}},headers:{type:Object,default:function(){return{}}},data:{type:Object,default:function(){return{access_token:n.a.get("access_token")}}},multiple:{type:Boolean,default:function(){return!1}},disabled:{type:Boolean,default:function(){return!1}},param:[Object,Boolean,String,Number]},data:function(){return{imageUrl:"",loading:!1}},methods:{handleAvatarSuccess:function(t,i){200===t.code?(this.loading=!1,this.$emit("update:url",t.data.url),this.$emit("update:info",t.data),this.$emit("success",i,t.data,this.param)):this.loading=!1},beforeAvatarUpload:function(t){this.loading=!0;var i=t.size/1024<this.size;return i||(this.loading=!1,this.$message.error("上传图片大小不能超过"+this.size+"KB")),i}}},u={render:function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("el-upload",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"avatar-uploader",attrs:{name:t.name,action:t.action,headers:t.headers,"show-file-list":!1,"on-success":t.handleAvatarSuccess,"before-upload":t.beforeAvatarUpload,accept:t.accept,data:t.data,multiple:t.multiple,disabled:t.disabled}},[t.url?e("img",{staticClass:"avatar",style:{width:t.width,height:t.height},attrs:{src:t.url}}):e("i",{staticClass:"el-icon-plus avatar-uploader-icon",style:{background:"url("+t.image+")",width:t.width,height:t.height,lineHeight:t.height}}),t._v(" "),e("div",{staticStyle:{"margin-top":"-10px",color:"#797979","font-size":"12px"},attrs:{slot:"tip"},slot:"tip"},[t._v(t._s(t.tip))])])},staticRenderFns:[]};var c=e("VU/8")(r,u,!1,function(t){e("+i/E")},null,null).exports,d={name:"detail-thirdSave",props:{dialogVisible:{required:!1,type:Boolean,default:function(){return!1}},selId:{required:!0,type:Number,default:function(){return 1}},formData:{required:!0,type:Object,default:function(){return{}}},selTitle:{required:!0,type:String,default:function(){return""}}},data:function(){return{upload_url:"/api/workorder/upload",form:{},rules:{name:[{required:!0,message:"请输入企业名称",trigger:"blur"}],tax_number:[{required:!0,message:"企业税号不能为空",trigger:"blur"}]},loading:!1,size:2e3,fileList:[]}},computed:{getTypeNoQrCode:function(){return!["mobile_station","baidu_fish_station"].includes(this.formData.type)}},components:{UploadImg:c},watch:{formData:{handler:function(t,i){this.form=JSON.parse(l()(t)),this.form.url?this.fileList=[{name:"",url:this.form.url}]:this.fileList=[]},immediate:!0}},methods:{submit:function(){var t=this,i={type:this.formData.type,value1:this.form.value,value2:this.form.value2};"wechat_offiaccount_station"===this.formData.type&&(i.value1=this.form.url),i.value2=this.form.url,Object(s._23)(i).then(function(){t.loading=!1,t.$message.success("保存成功"),t.close()}).catch(function(){t.loading=!1})},uploadImgSuccess:function(t,i){200===t.code?(this.loading=!1,this.form.url=t.data.url):(this.loading=!1,this.$message.error(t.message))},beforeAvatarUpload:function(t){this.loading=!0;var i=t.size/1024<this.size;return i||(this.loading=!1,this.$message.error("上传图片大小不能超过"+this.size+"KB")),i},handleExceed:function(){this.$message.error("只能上传1张图片，请删除后再添加")},handleRemove:function(){this.form.url=null},close:function(){this.$emit("closepop"),this.form={}},wechatVisible:function(){this.close(),this.$emit("wechatVisible")},douyinVisible:function(){this.close(),this.$emit("douYinVisible")},xiguaVisible:function(){this.close(),this.$emit("xiguaVisible")},kuaishouVisible:function(){this.close(),this.$emit("kuaishouVisible")},bilibiliVisible:function(){this.close(),this.$emit("bilibiliVisible")}}},p={render:function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("el-dialog",{attrs:{title:t.selTitle,visible:t.dialogVisible,width:"600px","before-close":t.close},on:{"update:visible":function(i){t.dialogVisible=i}}},[e("div",[e("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"dataForm",staticClass:"demo-form-inline",attrs:{inline:!0,model:t.formData,rules:t.rules,size:"small","label-width":"150px"}},["mobile_station"===t.formData.type?e("el-form-item",{attrs:{label:"URL链接：",prop:"url"}},[e("el-input",{attrs:{placeholder:"请输入URL链接地址"},model:{value:t.form.value,callback:function(i){t.$set(t.form,"value",i)},expression:"form.value"}}),t._v(" "),e("p",{staticStyle:{"font-size":"12px",color:"#797979"}},[t._v("\n          手机站网址链接，以http://为前缀输入\n        ")])],1):t._e(),t._v(" "),"wechat_mini_program_appid_station"===t.formData.type?e("div",[e("el-form-item",{attrs:{label:"原始ID：",prop:"weichat_appid"}},[e("el-input",{attrs:{placeholder:"请输入小程序原始ID"},model:{value:t.form.value,callback:function(i){t.$set(t.form,"value",i)},expression:"form.value"}}),t._v(" "),e("el-button",{staticStyle:{display:"block"},attrs:{type:"text"},on:{click:t.wechatVisible}},[t._v("如何获取id\n          ")])],1)],1):t._e(),t._v(" "),"weibo_station"===t.formData.type?[e("el-form-item",{attrs:{label:"账号ID："}},[e("div",[e("el-input",{attrs:{placeholder:"请输入账号ID"},model:{value:t.form.value,callback:function(i){t.$set(t.form,"value",i)},expression:"form.value"}})],1)])]:t._e(),t._v(" "),"tik_tok_station"===t.formData.type?[e("el-form-item",{attrs:{label:"账号ID："}},[e("el-input",{attrs:{placeholder:"请输入账号ID"},model:{value:t.form.value,callback:function(i){t.$set(t.form,"value",i)},expression:"form.value"}}),t._v(" "),e("el-button",{staticStyle:{display:"block"},attrs:{type:"text"},on:{click:t.douyinVisible}},[t._v("\n            如何获取抖音id\n          ")])],1)]:t._e(),t._v(" "),"baidu_fish_station"===t.formData.type?[e("el-form-item",{attrs:{label:"URL链接："}},[e("el-input",{attrs:{placeholder:"请输入URL链接地址"},model:{value:t.form.value,callback:function(i){t.$set(t.form,"value",i)},expression:"form.value"}}),t._v(" "),e("p",{staticStyle:{"font-size":"12px",color:"#797979"}},[t._v("百度基木鱼网站链接，以http://为前缀输入")])],1)]:t._e(),t._v(" "),"kuaishou_station"===t.formData.type?[e("el-form-item",{attrs:{label:"账号ID："}},[e("el-input",{attrs:{placeholder:"请输入账号ID"},model:{value:t.form.value,callback:function(i){t.$set(t.form,"value",i)},expression:"form.value"}}),t._v(" "),e("el-button",{staticStyle:{display:"block"},attrs:{type:"text"},on:{click:t.kuaishouVisible}},[t._v("如何获取快手id\n          ")])],1)]:t._e(),t._v(" "),"xigua_station"===t.formData.type?[e("el-form-item",{attrs:{label:"账号ID："}},[e("el-input",{attrs:{placeholder:"请输入账号ID"},model:{value:t.form.value,callback:function(i){t.$set(t.form,"value",i)},expression:"form.value"}}),t._v(" "),e("el-button",{staticStyle:{display:"block"},attrs:{type:"text"},on:{click:t.xiguaVisible}},[t._v("如何获取西瓜id\n          ")])],1)]:t._e(),t._v(" "),"bilibili_station"===t.formData.type?[e("el-form-item",{attrs:{label:"账号ID："}},[e("el-input",{attrs:{placeholder:"请输入账号ID"},model:{value:t.form.value,callback:function(i){t.$set(t.form,"value",i)},expression:"form.value"}}),t._v(" "),e("el-button",{staticStyle:{display:"block"},attrs:{type:"text"},on:{click:t.bilibiliVisible}},[t._v("如何获取B站id\n          ")])],1)]:t._e(),t._v(" "),t.getTypeNoQrCode?[e("el-form-item",{attrs:{label:"二维码图片："}},[e("el-upload",{attrs:{action:t.upload_url,"list-type":"picture-card",limit:1,"on-exceed":t.handleExceed,"file-list":t.fileList,accept:"image/png, image/jpeg","on-remove":t.handleRemove,"before-upload":t.beforeAvatarUpload,"on-success":t.uploadImgSuccess}},[e("i",{staticClass:"el-icon-plus"}),t._v(" "),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("请选择不大于2M的JPG、PNG的文件")])])],1)]:t._e(),t._v(" "),e("div",{staticClass:"view-btn"},[e("el-button",{staticStyle:{"margin-right":"20px"},attrs:{loading:t.loading,type:"primary"},on:{click:t.submit}},[t._v("保存\n        ")]),t._v(" "),e("el-button",{on:{click:t.close}},[t._v("取消")])],1)],2)],1)])},staticRenderFns:[]};var m=e("VU/8")(d,p,!1,function(t){e("raoT")},"data-v-1781ade4",null).exports,f=e("GvAf"),v=e.n(f),g=e("+QfH"),_=e.n(g),b=e("nJ8P"),h=e.n(b),y=e("/ORc"),x=e.n(y),D=e("0U+C"),V=e.n(D),S=e("kDvE"),k=e.n(S),I=e("AjoI"),w=e.n(I),C=e("N2R/"),$=e.n(C),U={name:"thirdSave",components:{detailThirdSave:m,CardWrap:e("wU6q").a},data:function(){return{selId:0,selTitle:"官网手机站",formData:{},dialogWechatVisible:!1,dialogDouYinVisible:!1,dialogXiguaVisible:!1,dialogKuaishouVisible:!1,dialogBilibiliVisible:!1,dialogHudVisible:!1,formLoading:!1,items:[],form:{},xigua1:v.a,xigua2:_.a,xigua3:h.a,kuaishou1:x.a,kuaishou2:V.a,bilibili1:k.a,bilibili2:w.a,bilibili3:$.a}},computed:{getTypeNoQrCode:function(){return!["mobile_station","baidu_fish_station"].includes(this.formData.type)}},created:function(){if(this.$route.query.selId){this.selId=parseInt(this.$route.query.selId);var t=this;this.items.forEach(function(i){i.id===t.selId&&(t.selTitle=i.title)})}this.getData()},methods:{getData:function(){var t=this;this.formLoading=!0,Object(s._22)().then(function(i){if(t.formLoading=!1,t.items=i.data,t.formData.type)for(var e=0;e<i.data.length;e++)i.data[e].type===t.formData.type&&(t.formData=i.data[e],t.formData.value2=i.data[e].url);else t.formData=i.data[0]||[];var a=l()(t.formData);t.form=JSON.parse(a)}).catch(function(){t.formLoading=!1})},closepop:function(){this.dialogHudVisible=!1,this.getData()}}},z={render:function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.formLoading,expression:"formLoading"}],staticClass:"app-container overflow-y-auto"},[e("card-wrap",{staticClass:"auto",attrs:{title:"企业官媒"}},[e("ul",t._l(t.items,function(i,a){return e("li",{key:a,on:{click:function(e){t.selId=a,t.selTitle=i.title,t.formData=i}}},[e("img",{class:a===t.selId?"sel":"",attrs:{src:i.icon}}),t._v(" "),e("p",{class:a===t.selId?"selColor":""},[t._v(t._s(i.title))])])}),0)]),t._v(" "),e("div",{staticClass:"y-gap"}),t._v(" "),e("card-wrap",{staticClass:"auto",attrs:{title:t.selTitle}},[e("el-form",[e("div",{staticClass:"gray-bg"},["mobile_station"===t.formData.type?e("el-form-item",{attrs:{label:"URL链接：","label-width":"100px"}},[e("span",[t._v(t._s(t.formData.value)),e("el-button",{staticClass:"edit",attrs:{type:"text"},on:{click:function(i){t.dialogHudVisible=!0}}},[t._v("修改")])],1)]):t._e(),t._v(" "),"wechat_mini_program_appid_station"===t.formData.type?e("el-form-item",{attrs:{label:"原始ID：","label-width":"100px"}},[e("span",[t._v(t._s(t.formData.value))])]):t._e(),t._v(" "),"weibo_station"===t.formData.type?e("el-form-item",{attrs:{label:"账号ID：","label-width":"100px"}},[e("span",[t._v("\n            "+t._s(t.formData.value)+"\n          ")]),t._v(" "),e("el-button",{staticClass:"edit",attrs:{type:"text"},on:{click:function(i){t.dialogHudVisible=!0}}},[t._v("\n            修改\n          ")])],1):t._e(),t._v(" "),"tik_tok_station"===t.formData.type?e("el-form-item",{attrs:{label:"账号ID：","label-width":"100px"}},[e("span",[t._v(t._s(t.formData.value))]),t._v(" "),e("el-button",{staticClass:"edit",attrs:{type:"text"},on:{click:function(i){t.dialogHudVisible=!0}}},[t._v("修改\n          ")])],1):t._e(),t._v(" "),"baidu_fish_station"===t.formData.type?e("el-form-item",{attrs:{label:"URL链接：","label-width":"100px"}},[e("span",[t._v(t._s(t.formData.value))]),t._v(" "),e("el-button",{staticClass:"edit",attrs:{type:"text"},on:{click:function(i){t.dialogHudVisible=!0}}},[t._v("修改\n          ")])],1):t._e(),t._v(" "),"kuaishou_station"===t.formData.type?e("el-form-item",{attrs:{label:"账号ID：","label-width":"100px"}},[e("span",[t._v(t._s(t.formData.value)+" ")]),t._v(" "),e("el-button",{staticClass:"edit",attrs:{type:"text"},on:{click:function(i){t.dialogHudVisible=!0}}},[t._v("\n            修改\n          ")])],1):t._e(),t._v(" "),"xigua_station"===t.formData.type?e("el-form-item",{attrs:{label:"账号ID：","label-width":"100px"}},[e("span",[t._v(t._s(t.formData.value))]),t._v(" "),e("el-button",{staticClass:"edit",attrs:{type:"text"},on:{click:function(i){t.dialogHudVisible=!0}}},[t._v("\n            修改\n          ")])],1):t._e(),t._v(" "),"bilibili_station"===t.formData.type?e("el-form-item",{attrs:{label:"账号ID：","label-width":"100px"}},[e("span",[t._v(t._s(t.formData.value))]),t._v(" "),e("el-button",{staticClass:"edit",attrs:{type:"text"},on:{click:function(i){t.dialogHudVisible=!0}}},[t._v("\n            修改\n          ")])],1):t._e(),t._v(" "),t.getTypeNoQrCode?e("el-form-item",{attrs:{label:"二维码图片：","label-width":"100px"}},[t.formData.url?e("el-image",{staticStyle:{width:"50px",height:"50px"},attrs:{src:t.formData.url}}):t._e(),t._v(" "),e("el-button",{staticClass:"edit",attrs:{type:"text"},on:{click:function(i){t.dialogHudVisible=!0}}},[t._v("修改\n          ")])],1):t._e()],1)])],1),t._v(" "),e("el-dialog",{attrs:{title:"",visible:t.dialogWechatVisible},on:{"update:visible":function(i){t.dialogWechatVisible=i}}},[e("p",{staticStyle:{"font-size":"16px","font-weight":"bold","margin-bottom":"20px"}},[t._v("\n      微信小程序获取原始ID说明\n    ")]),t._v(" "),e("p"),t._v(" "),e("p",[t._v("\n      一、使用小程序账号登录微信公众平台："),e("a",{attrs:{href:"https://mp.weixin.qq.com",target:"_blank"}},[t._v("https://mp.weixin.qq.com")]),t._v("，点击“设置”=>“基本设置”\n    ")]),t._v(" "),e("el-image",{attrs:{src:"http://images.china9.cn/zihaiyunVue/static/image/console/thirdSave/smallWechat.png"}}),t._v(" "),e("p",{staticStyle:{margin:"20px 0"}},[t._v("二、再下拉，可以看到原始ID：")]),t._v(" "),e("el-image",{attrs:{src:"http://images.china9.cn/zihaiyunVue/static/image/console/thirdSave/smallWechat1.png"}})],1),t._v(" "),e("el-dialog",{attrs:{title:"",visible:t.dialogDouYinVisible},on:{"update:visible":function(i){t.dialogDouYinVisible=i}}},[e("p",{staticStyle:{"font-size":"16px","font-weight":"bold","margin-bottom":"20px"}},[t._v("\n      抖音获取原始ID说明\n    ")]),t._v(" "),e("p"),t._v(" "),e("p",[t._v("\n      手机都分为苹果版和安卓版，抖音APP自然是在这两个系统上都有的，下面我们以苹果系统为例来写出步骤，安卓版类同。首先打开抖音APP。\n    ")]),t._v(" "),e("p",{staticStyle:{margin:"10px 0"}},[t._v("进入后，选择右下角“我”。")]),t._v(" "),e("el-image",{staticStyle:{margin:"20px 0"},attrs:{src:"http://images.china9.cn/zihaiyunVue/static/image/console/thirdSave/douyin01.png"}}),t._v(" "),e("p",[t._v("在个人信息界面，选择右上角“三”符号的按钮。")]),t._v(" "),e("el-image",{staticStyle:{margin:"20px 0"},attrs:{src:"http://images.china9.cn/zihaiyunVue/static/image/console/thirdSave/douyin02.png"}}),t._v(" "),e("p",[t._v("在弹出的列表中，选择“设置”。")]),t._v(" "),e("p",{staticStyle:{margin:"10px 0"}},[t._v("\n      下拉 设置 列表到最下方，有显示抖音版本号的字样。本例中显示“抖音 version\n      13.7.0”。接下来的操作尤为重要，苹果系统下用三根手指，同时点击这一行字五下左右，安卓系统下只需使用一根手机点击五下左右。\n    ")]),t._v(" "),e("el-image",{staticStyle:{margin:"20px 0"},attrs:{src:"http://images.china9.cn/zihaiyunVue/static/image/console/thirdSave/douyin03.png"}}),t._v(" "),e("p",{staticStyle:{"margin-bottom":"20px"}},[t._v("\n      如图所示，按上个步骤所说的方法连续点击，就会显示出UID和DID，再点击一下就会自动复制到UID了，就能到企业官媒——抖音账号ID处粘贴使用了。\n    ")])],1),t._v(" "),e("el-dialog",{attrs:{title:"",visible:t.dialogXiguaVisible},on:{"update:visible":function(i){t.dialogXiguaVisible=i}}},[e("p",{staticStyle:{"font-size":"16px","font-weight":"bold","margin-bottom":"20px"}},[t._v("\n      抖音获取原始ID说明\n    ")]),t._v(" "),e("p"),t._v(" "),e("p",[t._v("打开西瓜APP，点击【我的】，点击【设置】")]),t._v(" "),e("el-image",{staticStyle:{margin:"20px 0"},attrs:{src:t.xigua1}}),t._v(" "),e("p",[t._v("上滑到最底部，点击红框内区域，直到出现UID信息")]),t._v(" "),e("el-image",{staticStyle:{margin:"20px 0"},attrs:{src:t.xigua2}}),t._v(" "),e("p",[t._v("长按复制UID到后台即可")]),t._v(" "),e("el-image",{staticStyle:{margin:"20px 0"},attrs:{src:t.xigua3}})],1),t._v(" "),e("el-dialog",{attrs:{title:"",visible:t.dialogKuaishouVisible},on:{"update:visible":function(i){t.dialogKuaishouVisible=i}}},[e("p",{staticStyle:{"font-size":"16px","font-weight":"bold","margin-bottom":"20px"}},[t._v("\n      打开快手，点击底部下方【我】，点击页面上的【完善资料】\n    ")]),t._v(" "),e("p",[e("el-image",{staticStyle:{margin:"20px 0"},attrs:{src:t.kuaishou1}})],1),t._v(" "),e("p",[t._v("栏目中的【用户ID】，添加到后台。")]),t._v(" "),e("el-image",{staticStyle:{margin:"20px 0"},attrs:{src:t.kuaishou2}})],1),t._v(" "),e("el-dialog",{attrs:{title:"",visible:t.dialogBilibiliVisible},on:{"update:visible":function(i){t.dialogBilibiliVisible=i}}},[e("p",{staticStyle:{"font-size":"16px","font-weight":"bold","margin-bottom":"20px"}},[t._v("\n      打开哔哩哔哩，点击右下【我的】，上拉找到【设置】\n    ")]),t._v(" "),e("p",[e("el-image",{staticStyle:{margin:"20px 0"},attrs:{src:t.bilibili1}})],1),t._v(" "),e("p",[t._v("找到【账号资料】，点击进入")]),t._v(" "),e("el-image",{staticStyle:{margin:"20px 0"},attrs:{src:t.bilibili2}}),t._v(" "),e("p",[t._v("找到UID，复制到后台即可。")]),t._v(" "),e("el-image",{staticStyle:{margin:"20px 0"},attrs:{src:t.bilibili3}})],1),t._v(" "),e("detail-third-save",{attrs:{formData:t.formData,selId:t.selId,selTitle:t.selTitle,dialogVisible:t.dialogHudVisible},on:{closepop:t.closepop,wechatVisible:function(i){t.dialogWechatVisible=!0,t.getData()},douYinVisible:function(i){t.dialogDouYinVisible=!0},xiguaVisible:function(i){t.dialogXiguaVisible=!0},kuaishouVisible:function(i){t.dialogKuaishouVisible=!0},bilibiliVisible:function(i){t.dialogBilibiliVisible=!0}}})],1)},staticRenderFns:[]};var L=e("VU/8")(U,z,!1,function(t){e("N+uq")},"data-v-17294283",null);i.default=L.exports},raoT:function(t,i){}});