<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="./css/index.css">
  <script src="./js/jquery.min.js"></script>
  <script src="./js/jweixin-1.2.0.js"></script>
  <script src="./js/<EMAIL>"></script>
  <script src="./js/w_rem.js"></script>
  <title>资海云产品</title>
</head>
<body>
  <div class="container" id="index-app" v-cloak>
    <div class="banner">
      <img :src="'./images/' + formName + '.png?v=002'" alt="" srcset="">
    </div>
    <div ref="chatBox" class="chat-container">
      <div v-if="customer1" class="chat-item">
        <div class="chat-img">
          <img src="./images/kf.jpg" alt="">
        </div>
        <div class="chat-content">您好！我是资海云-客服小云，在为您定制专属解决方案和优惠套餐前，可以问几个问题吗？</div>
      </div>
      <div v-if="customer2" class="chat-item">
        <div class="chat-img">
          <img src="./images/kf.jpg" alt="">
        </div>
        <div class="chat-content">您放心，不会占用您太多时间的</div>
      </div>
      <div v-if="customer3" class="chat-item">
        <div class="chat-img">
          <img src="./images/kf.jpg" alt="">
        </div>
        <div class="chat-content">
          请问贵公司是属于“<span>哪种类型</span>”的企业呢？
          <div class="tab-box">
            <div v-for="item in cateList" :key="item" :class="['tab-item', {'active': cateName == item}, 'item' + (cateList.length / 2)]" @click="cateClick(item)">{{ item }}</div>
          </div>
        </div>
      </div>
      <div v-if="cateName" class="chat-item user">
        <div class="chat-img">
          <img src="./images/user.jpg" alt="">
        </div>
        <div class="chat-content">{{ cateName }}</div>
      </div>
      <div v-if="customer4" class="chat-item">
        <div class="chat-img">
          <img src="./images/kf.jpg" alt="">
        </div>
        <div class="chat-content">
          请问贵公司是属于“<span>什么行业</span>”呢？<br/>
          我们会有专业人员为您匹配相同案例并提供报价参考
          <div class="tab-box">
            <div v-for="item in industryList" :key="item" :class="['tab-item', {'active': industryName == item}, 'item' + (industryList.length / 2)]" @click="industryClick(item)">{{ item }}</div>
          </div>
        </div>
      </div>
      <div v-if="industryName" class="chat-item user">
        <div class="chat-img">
          <img src="./images/user.jpg" alt="">
        </div>
        <div class="chat-content">{{ industryName }}</div>
      </div>
      <div v-if="customer5" class="chat-item">
        <div class="chat-img">
          <img src="./images/kf.jpg" alt="">
        </div>
        <div class="chat-content">辛苦您填写一下您的联系方式，我这里已经帮您安排好商务定制方案和优惠套餐，运营人员稍后会联系您的，谢谢。</div>
      </div>
    </div>
    <!-- 表单提交 -->
    <div class="form-container" v-if="showForm">
      <div class="form-shake"></div>
      <div class="form-main">
        <p class="f-title">获取定制方案和优惠套餐</p>
        <div class="form-list">
          <div class="form-item">
            <input v-model="form.name" type="text" placeholder="请输入您的姓名" />
          </div>
          <div class="form-item">
            <input v-model="form.phone" type="text" placeholder="请输入您的手机号" />
          </div>
          <div class="form-item">
            <input v-model="form.company" type="text" placeholder="请输入企业名称" />
          </div>
        </div>
        <div class="read">
          <img :src="'./images/check' + (check ? 'ed': '') + '.png' " alt="" @click="check = !check">
          <p class="text">我已阅读并同意<a href="https://account.china9.cn/%E8%B5%84%E6%B5%B7%E4%BA%91%E6%B3%95%E5%BE%8B%E5%A3%B0%E6%98%8E%E5%8F%8A%E9%9A%90%E7%A7%81%E6%9D%83%E6%94%BF%E7%AD%96.html" target="_blank">《个人信息与隐私保护条款》</a></p>
        </div>
        <div class="form-btn" @click="toSubmit">立即获取</div>
      </div>
    </div>
  </div>
  <script src="./js/index.js?v007"></script>
</body>
</html>