.item1 {
  width: calc(100% - 0rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item1:nth-child(1n) {
  margin-right: 0;
}

.item2 {
  width: calc(50% - 0.065rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item2:nth-child(2n) {
  margin-right: 0;
}

.item3 {
  width: calc(33.3333333333% - 0.0866666667rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item3:nth-child(3n) {
  margin-right: 0;
}

.item4 {
  width: calc(25% - 0.0975rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item4:nth-child(4n) {
  margin-right: 0;
}

.item5 {
  width: calc(20% - 0.104rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item5:nth-child(5n) {
  margin-right: 0;
}

.item6 {
  width: calc(16.6666666667% - 0.1083333333rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item6:nth-child(6n) {
  margin-right: 0;
}

.item7 {
  width: calc(14.2857142857% - 0.1114285714rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item7:nth-child(7n) {
  margin-right: 0;
}

.item8 {
  width: calc(12.5% - 0.11375rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item8:nth-child(8n) {
  margin-right: 0;
}

.item9 {
  width: calc(11.1111111111% - 0.1155555556rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item9:nth-child(9n) {
  margin-right: 0;
}

.item10 {
  width: calc(10% - 0.117rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item10:nth-child(10n) {
  margin-right: 0;
}

.item11 {
  width: calc(9.0909090909% - 0.1181818182rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item11:nth-child(11n) {
  margin-right: 0;
}

.item12 {
  width: calc(8.3333333333% - 0.1191666667rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item12:nth-child(12n) {
  margin-right: 0;
}

.item13 {
  width: calc(7.6923076923% - 0.12rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item13:nth-child(13n) {
  margin-right: 0;
}

.item14 {
  width: calc(7.1428571429% - 0.1207142857rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item14:nth-child(14n) {
  margin-right: 0;
}

.item15 {
  width: calc(6.6666666667% - 0.1213333333rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item15:nth-child(15n) {
  margin-right: 0;
}

.item16 {
  width: calc(6.25% - 0.121875rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item16:nth-child(16n) {
  margin-right: 0;
}

.item17 {
  width: calc(5.8823529412% - 0.1223529412rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item17:nth-child(17n) {
  margin-right: 0;
}

.item18 {
  width: calc(5.5555555556% - 0.1227777778rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item18:nth-child(18n) {
  margin-right: 0;
}

.item19 {
  width: calc(5.2631578947% - 0.1231578947rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item19:nth-child(19n) {
  margin-right: 0;
}

.item20 {
  width: calc(5% - 0.1235rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item20:nth-child(20n) {
  margin-right: 0;
}

.item21 {
  width: calc(4.7619047619% - 0.1238095238rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item21:nth-child(21n) {
  margin-right: 0;
}

.item22 {
  width: calc(4.5454545455% - 0.1240909091rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item22:nth-child(22n) {
  margin-right: 0;
}

.item23 {
  width: calc(4.347826087% - 0.1243478261rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item23:nth-child(23n) {
  margin-right: 0;
}

.item24 {
  width: calc(4.1666666667% - 0.1245833333rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item24:nth-child(24n) {
  margin-right: 0;
}

.item25 {
  width: calc(4% - 0.1248rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item25:nth-child(25n) {
  margin-right: 0;
}

.item26 {
  width: calc(3.8461538462% - 0.125rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item26:nth-child(26n) {
  margin-right: 0;
}

.item27 {
  width: calc(3.7037037037% - 0.1251851852rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item27:nth-child(27n) {
  margin-right: 0;
}

.item28 {
  width: calc(3.5714285714% - 0.1253571429rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item28:nth-child(28n) {
  margin-right: 0;
}

.item29 {
  width: calc(3.4482758621% - 0.1255172414rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item29:nth-child(29n) {
  margin-right: 0;
}

.item30 {
  width: calc(3.3333333333% - 0.1256666667rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item30:nth-child(30n) {
  margin-right: 0;
}

.item31 {
  width: calc(3.2258064516% - 0.1258064516rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item31:nth-child(31n) {
  margin-right: 0;
}

.item32 {
  width: calc(3.125% - 0.1259375rem - 0.06rem);
  margin-right: 0.13rem;
  cursor: pointer;
}
.item32:nth-child(32n) {
  margin-right: 0;
}

* {
  margin: 0;
  padding: 0;
}

a {
  text-decoration: none;
}

[v-cloak] {
  display: none;
}

body {
  width: 100vw;
  height: 100vh;
  font-size: 0.16rem;
  background-color: #f5f5f5;
}

img {
  display: inline-block;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.container .banner {
  width: 100%;
}
.container .banner img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.container .chat-container {
  flex-grow: 1;
  padding: 0.3rem;
  overflow: hidden auto;
}
.container .chat-container .chat-item {
  display: flex;
  margin-top: 0.2rem;
  margin-bottom: 0.4rem;
}
.container .chat-container .chat-item .chat-img {
  width: 0.8rem;
  height: 0.8rem;
  background: #69C997;
  border: 1px solid #E0E0E0;
  border-radius: 0.1rem;
  flex-shrink: 0;
  margin-right: 0.21rem;
  overflow: hidden;
}
.container .chat-container .chat-item .chat-img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.container .chat-container .chat-item .chat-content {
  background: #FFFFFF;
  border-radius: 0.1rem;
  font-size: 0.28rem;
  color: #333333;
  line-height: 0.4rem;
  padding: 0.25rem 0.33rem;
}
.container .chat-container .chat-item .chat-content span {
  color: rgb(54, 105, 255);
  font-weight: bold;
}
.container .chat-container .chat-item .chat-content .tab-box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.container .chat-container .chat-item .chat-content .tab-box .tab-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 0.7rem;
  background: #FFFFFF;
  border: 1px solid #3669FF;
  border-radius: 0.04rem;
  font-size: 0.28rem;
  color: #3669FF;
  margin-top: 0.14rem;
  transition: all ease-in-out 0.3s;
}
.container .chat-container .chat-item .chat-content .tab-box .tab-item.active {
  background-color: #3669FF;
  color: #FFFFFF;
}
.container .chat-container .chat-item.user {
  flex-direction: row-reverse;
}
.container .chat-container .chat-item.user .chat-img {
  margin-left: 0.21rem;
  margin-right: 0;
}
.container .chat-container .chat-item.user .chat-content {
  background: #5183F7;
  color: #FFFFFF;
}
.container .form-container {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 1;
  margin: auto;
  width: 100%;
  height: 100%;
  z-index: 100;
}
.container .form-container .form-shake {
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.container .form-container .form-main {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  padding: 0.25rem;
  box-sizing: border-box;
  background: #FFFFFF;
  border-radius: 0.2rem 0.2rem 0rem 0rem;
}
.container .form-container .form-main .f-title {
  font-size: 0.28rem;
  text-align: center;
  font-weight: bold;
  color: #222222;
  line-height: 0.95rem;
  margin-bottom: 0.25rem;
}
.container .form-container .form-main .form-list .form-item {
  height: 0.9rem;
  background: #F5F5F5;
  border-radius: 0.45rem;
  margin-bottom: 0.2rem;
  padding: 0.2rem 0.4rem;
  box-sizing: border-box;
}
.container .form-container .form-main .form-list .form-item input {
  width: 100%;
  height: 100%;
  border: none;
  background: none;
  font-size: 0.28rem;
  font-family: "Microsoft YaHei";
}
.container .form-container .form-main .form-list .form-item input:focus {
  outline: none;
  border-color: rgba(0, 191, 255, 0);
  box-shadow: 0 0 5px rgba(0, 191, 255, 0);
}
.container .form-container .form-main .form-list .form-item:last-child {
  margin-bottom: 0;
}
.container .form-container .form-main .read {
  height: 1rem;
  display: flex;
  align-items: center;
}
.container .form-container .form-main .read img {
  width: 0.36rem;
  height: 0.36rem;
  margin-left: 0.3rem;
  cursor: pointer;
}
.container .form-container .form-main .read .text {
  font-size: 0.28rem;
  color: #999999;
  margin-left: 0.18rem;
}
.container .form-container .form-main .read .text a {
  color: rgb(54, 105, 255);
}
.container .form-container .form-main .form-btn {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 0.9rem;
  background: #3669FF;
  border-radius: 0.45rem;
  font-size: 0.28rem;
  color: #FFFFFF;
  margin-bottom: 0.24rem;
}

@media screen and (min-width: 768px) {
  .item1 {
    width: calc(100% - 0px - 3.6px);
    margin-right: 7.2px;
  }
  .item2 {
    width: calc(50% - 3.6px - 3.6px);
    margin-right: 7.2px;
  }
  .item3 {
    width: calc(33.3333333333% - 4.8px - 3.6px);
    margin-right: 7.2px;
  }
  .item4 {
    width: calc(25% - 5.4px - 3.6px);
    margin-right: 7.2px;
  }
  .item5 {
    width: calc(20% - 5.76px - 3.6px);
    margin-right: 7.2px;
  }
  .item6 {
    width: calc(16.6666666667% - 6px - 3.6px);
    margin-right: 7.2px;
  }
  .item7 {
    width: calc(14.2857142857% - 6.1714285714px - 3.6px);
    margin-right: 7.2px;
  }
  .item8 {
    width: calc(12.5% - 6.3px - 3.6px);
    margin-right: 7.2px;
  }
  .item9 {
    width: calc(11.1111111111% - 6.4px - 3.6px);
    margin-right: 7.2px;
  }
  .item10 {
    width: calc(10% - 6.48px - 3.6px);
    margin-right: 7.2px;
  }
  .item11 {
    width: calc(9.0909090909% - 6.5454545455px - 3.6px);
    margin-right: 7.2px;
  }
  .item12 {
    width: calc(8.3333333333% - 6.6px - 3.6px);
    margin-right: 7.2px;
  }
  .item13 {
    width: calc(7.6923076923% - 6.6461538462px - 3.6px);
    margin-right: 7.2px;
  }
  .item14 {
    width: calc(7.1428571429% - 6.6857142857px - 3.6px);
    margin-right: 7.2px;
  }
  .item15 {
    width: calc(6.6666666667% - 6.72px - 3.6px);
    margin-right: 7.2px;
  }
  .item16 {
    width: calc(6.25% - 6.75px - 3.6px);
    margin-right: 7.2px;
  }
  .item17 {
    width: calc(5.8823529412% - 6.7764705882px - 3.6px);
    margin-right: 7.2px;
  }
  .item18 {
    width: calc(5.5555555556% - 6.8px - 3.6px);
    margin-right: 7.2px;
  }
  .item19 {
    width: calc(5.2631578947% - 6.8210526316px - 3.6px);
    margin-right: 7.2px;
  }
  .item20 {
    width: calc(5% - 6.84px - 3.6px);
    margin-right: 7.2px;
  }
  .item21 {
    width: calc(4.7619047619% - 6.8571428571px - 3.6px);
    margin-right: 7.2px;
  }
  .item22 {
    width: calc(4.5454545455% - 6.8727272727px - 3.6px);
    margin-right: 7.2px;
  }
  .item23 {
    width: calc(4.347826087% - 6.8869565217px - 3.6px);
    margin-right: 7.2px;
  }
  .item24 {
    width: calc(4.1666666667% - 6.9px - 3.6px);
    margin-right: 7.2px;
  }
  .item25 {
    width: calc(4% - 6.912px - 3.6px);
    margin-right: 7.2px;
  }
  .item26 {
    width: calc(3.8461538462% - 6.9230769231px - 3.6px);
    margin-right: 7.2px;
  }
  .item27 {
    width: calc(3.7037037037% - 6.9333333333px - 3.6px);
    margin-right: 7.2px;
  }
  .item28 {
    width: calc(3.5714285714% - 6.9428571429px - 3.6px);
    margin-right: 7.2px;
  }
  .item29 {
    width: calc(3.4482758621% - 6.9517241379px - 3.6px);
    margin-right: 7.2px;
  }
  .item30 {
    width: calc(3.3333333333% - 6.96px - 3.6px);
    margin-right: 7.2px;
  }
  .item31 {
    width: calc(3.2258064516% - 6.9677419355px - 3.6px);
    margin-right: 7.2px;
  }
  .item32 {
    width: calc(3.125% - 6.975px - 3.6px);
    margin-right: 7.2px;
  }
  .container {
    width: 450px;
    margin: auto;
  }
  .container .chat-container {
    padding: 18px;
  }
  .container .chat-container .chat-item {
    margin-top: 12px;
    margin-bottom: 24px;
  }
  .container .chat-container .chat-item .chat-img {
    width: 48px;
    height: 48px;
    border-radius: 6px;
    margin-right: 12px;
  }
  .container .chat-container .chat-item .chat-content {
    border-radius: 6px;
    font-size: 16.8px;
    line-height: 24px;
    padding: 14.4px 19.2px;
  }
  .container .chat-container .chat-item .chat-content .tab-box .tab-item {
    height: 42px;
    border-radius: 2.4px;
    font-size: 16.8px;
    margin-top: 8.4px;
  }
  .container .chat-container .chat-item.user .chat-img {
    margin-left: 12px;
  }
  .container .form-container {
    width: 450px;
  }
  .container .form-container .form-main {
    padding: 14.4px;
    border-radius: 12px 12px 0rem 0rem;
  }
  .container .form-container .form-main .f-title {
    font-size: 16.8px;
    line-height: 50.4px;
    margin-bottom: 14.4px;
  }
  .container .form-container .form-main .form-list .form-item {
    height: 54px;
    border-radius: 28.8px;
    margin-bottom: 12px;
    padding: 12px 24px;
  }
  .container .form-container .form-main .form-list .form-item input {
    font-size: 16.8px;
  }
  .container .form-container .form-main .read {
    height: 60px;
  }
  .container .form-container .form-main .read img {
    width: 21.6px;
    height: 21.6px;
    margin-left: 18px;
  }
  .container .form-container .form-main .read .text {
    font-size: 16.8px;
    margin-left: 10.8px;
  }
  .container .form-container .form-main .form-btn {
    height: 54px;
    border-radius: 28.8px;
    font-size: 16.8px;
    margin-bottom: 14.4px;
  }
}/*# sourceMappingURL=index.css.map */