webpackJsonp([4],{"/XzG":function(e,t){},"/uOQ":function(e,t){},"6/L/":function(e,t){},"64wY":function(e,t){},"7Rdl":function(e,t){},"8kx5":function(e,t){},"9HTw":function(e,t){},AHDM:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a("Aw0F"),s=a("JYzS"),l=a("ev7b"),n={name:"LegalCardWrap",props:{title:{type:String,default:""},desc:{type:String,default:""},icon:{type:String,default:""},showMore:{type:Boolean,default:!1},moreTitle:{type:String,default:"更多"}},methods:{toMore:function(){this.$emit("toMore")}}},r={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"card-wrap"},[a("div",{staticClass:"header-wrap"},[a("div",{staticClass:"title-wrap"},[e.icon?a("div",{staticClass:"icon"},[a("img",{attrs:{src:e.icon,alt:""}})]):e._e(),e._v(" "),e.title?a("div",{staticClass:"title"},[e._v(e._s(e.title))]):e._e(),e._v(" "),e.desc?a("div",{staticClass:"desc"},[e._v(e._s(e.desc))]):e._e()]),e._v(" "),a("div",{staticClass:"right-wrap"},[e._t("right"),e._v(" "),e.showMore?a("div",{staticClass:"more",on:{click:e.toMore}},[a("span",[e._v(e._s(e.moreTitle))]),e._v(" "),a("i",{staticClass:"el-icon-arrow-right"})]):e._e()],2)]),e._v(" "),a("div",{staticClass:"content-wrap"},[e._t("default")],2)])},staticRenderFns:[]};var o=a("VU/8")(n,r,!1,function(e){a("u2EN")},"data-v-249006c8",null).exports,c={name:"AiContractWriting",components:{CardWrap:a("wU6q").a},mixins:[s.a],data:function(){return{addPath:{pathType:"link",path:"https://ihr.china9.cn/human/main/index/v/ygt/m/fw/#/lcontract/contract_draft",roles:["contract_draft"]}}}},u={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("a",{staticClass:"writing-card flex-column flex-center",on:{click:function(t){return e.toPage(e.addPath)}}},[a("div",{staticClass:"icon"}),e._v(" "),a("div",{staticClass:"title"},[e._v("AI智能合同创作")])])},staticRenderFns:[]};var d=a("VU/8")(c,u,!1,function(e){a("y3Ff")},"data-v-30833479",null).exports,p={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"list-wrap"},[a("div",{staticClass:"search-area"},[a("div",{staticClass:"form-group"},[a("el-input",{attrs:{placeholder:"请输入合同名称",clearable:""},model:{value:e.searchForm.contractName,callback:function(t){e.$set(e.searchForm,"contractName",t)},expression:"searchForm.contractName"}},[a("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"suffix"},slot:"suffix"})]),e._v(" "),a("el-input",{attrs:{placeholder:"请输入合同负责人",clearable:""},model:{value:e.searchForm.contractManager,callback:function(t){e.$set(e.searchForm,"contractManager",t)},expression:"searchForm.contractManager"}},[a("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"suffix"},slot:"suffix"})]),e._v(" "),a("el-select",{attrs:{placeholder:"请选择履约状态",clearable:""},model:{value:e.searchForm.contractStatus,callback:function(t){e.$set(e.searchForm,"contractStatus",t)},expression:"searchForm.contractStatus"}},e._l(e.statusOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-button",{staticClass:"search-btn",attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")])],1),e._v(" "),a("div",{staticClass:"table-wrap flex-1"},[a("el-table",{staticStyle:{width:"100%",height:"100%"},attrs:{data:e.tableData,"header-cell-style":e.headerCellStyle,"cell-style":e.cellStyle,border:!1}},[a("el-table-column",{attrs:{prop:"contractName",label:"合同名称","min-width":"180"}}),e._v(" "),a("el-table-column",{attrs:{prop:"contractAmount",label:"合同金额","min-width":"120",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.contractAmount.toFixed(2)))]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"contractManager",label:"合同负责人","min-width":"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"status",label:"履约状态","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"status-tag",class:e.getStatusClass(t.row.status)},[e._v(e._s(e.getStatusText(t.row.status)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"120",fixed:"right",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{staticClass:"operation-link",attrs:{type:"primary"},on:{click:function(a){return e.viewDetail(t.row)}}},[e._v("查看")]),e._v(" "),a("el-link",{staticClass:"operation-link",attrs:{type:"primary"},on:{click:function(a){return e.deleteItem(t.row)}}},[e._v("删除")])]}}])})],1)],1)])},staticRenderFns:[]};var m=a("VU/8")({name:"ContractQuery",data:function(){return{tableData:[{contractName:"建站类产品销售合同",contractAmount:2500,contractManager:"张玉林",status:"pending"},{contractName:"建站类产品销售合同",contractAmount:2500,contractManager:"张玉林",status:"inProgress"},{contractName:"建站类产品销售合同",contractAmount:2500,contractManager:"张玉林",status:"completed"},{contractName:"建站类产品销售合同",contractAmount:2500,contractManager:"张玉林",status:"pending"}],searchForm:{contractName:"",contractManager:"",contractStatus:""},statusOptions:[{value:"pending",label:"待履约"},{value:"inProgress",label:"履约中"},{value:"completed",label:"已完结"}]}},methods:{handleSearch:function(){this.$emit("search",this.searchForm)},viewDetail:function(e){this.$emit("view-detail",e)},deleteItem:function(e){this.$emit("delete-item",e)},getStatusText:function(e){return{pending:"待履约",inProgress:"履约中",completed:"已完结"}[e]||e},getStatusClass:function(e){return{pending:"status-pending",inProgress:"status-in-progress",completed:"status-completed"}[e]||""},headerCellStyle:function(){return{backgroundColor:"#F2F6F9",color:"#3C3C3C",fontWeight:"normal",borderBottom:"1px solid #F1F4F8",padding:"0px 0",height:"40px"}},cellStyle:function(){return{borderBottom:"1px solid #F1F4F8",height:"44px",padding:"0px 0"}}}},p,!1,function(e){a("vC05")},"data-v-494d43e0",null).exports,f={name:"TabBar",props:{tabs:{type:Array,required:!0},borderColor:{type:String,default:"#E4E7ED"},value:{type:[String,Number],required:!0}},methods:{handleClick:function(e){this.$emit("input",e.value),this.$emit("change",e)}}},v={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"tab-container",style:{borderColor:e.borderColor}},[a("div",{staticClass:"tab-wrapper"},e._l(e.tabs,function(t,i){return a("div",{key:i,staticClass:"tab-item",class:{active:e.value===t.value},on:{click:function(a){return e.handleClick(t)}}},[e._v("\n      "+e._s(t.label)+"\n    ")])}),0)])},staticRenderFns:[]};var h=a("VU/8")(f,v,!1,function(e){a("/XzG")},"data-v-0e3f8008",null).exports,g=(a("7Rdl"),{name:"InterestFees",data:function(){return{form:{amount:"",dateRange:"",interestPeriod:"year",interestRate:""},rules:{amount:[{required:!0,message:"请输入本金金额"}],dateRange:[{required:!0,message:"请选择计算日期范围",trigger:"change"}],interestRate:[{required:!0,message:"请输入利率"}]},calculatedResults:{days:0,interest:"0.00",total:"0.00"}}},methods:{calculate:function(){var e=this;e.$refs.formRef.validate(function(t){if(t){console.log(e.form,"form");var a=parseFloat(e.form.amount)||0,i=e.getDaysBetween1(e.form.dateRange[0],e.form.dateRange[1]),s=0;"year"==e.form.interestPeriod?s=a*(e.form.interestRate/100/360)*i:"month"==e.form.interestPeriod?s=a*(e.form.interestRate/100/30)*i:"day"==e.form.interestPeriod&&(s=a*(e.form.interestRate/100)*i),s=parseFloat(s.toFixed(2)),e.calculatedResults={days:i,interest:s,total:a+s}}})},getDaysBetween1:function(e,t){var a=Date.parse(e);return(Date.parse(t)-a)/864e5},reset:function(){this.form={amount:"",dateRange:"",interestPeriod:"year",interestRate:""},this.showResults=!1,this.$refs.formRef.clearValidate()}}}),b={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"interest-fees legal-wrap"},[a("div",{staticClass:"calculator-form"},[a("el-form",{ref:"formRef",attrs:{model:e.form,"label-width":"auto",rules:e.rules}},[a("div",{staticClass:"flex"},[a("el-form-item",{attrs:{label:"本金",prop:"amount"}},[a("el-input",{attrs:{placeholder:"输入金额",type:"number"},model:{value:e.form.amount,callback:function(t){e.$set(e.form,"amount",t)},expression:"form.amount"}})],1),e._v(" "),a("el-form-item",{staticStyle:{"margin-left":"15px"},attrs:{label:"日期",prop:"dateRange"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"起算日期","end-placeholder":"截止日期","value-format":"yyyy-MM-dd"},model:{value:e.form.dateRange,callback:function(t){e.$set(e.form,"dateRange",t)},expression:"form.dateRange"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"利率",prop:"interestRate"}},[a("div",{staticClass:"flex items-center"},[a("el-select",{staticStyle:{width:"200px","margin-right":"15px"},attrs:{placeholder:"请选择结算周期"},model:{value:e.form.interestPeriod,callback:function(t){e.$set(e.form,"interestPeriod",t)},expression:"form.interestPeriod"}},[a("el-option",{attrs:{label:"年利率",value:"year"}}),e._v(" "),a("el-option",{attrs:{label:"月利率",value:"month"}}),e._v(" "),a("el-option",{attrs:{label:"日利率",value:"day"}})],1),e._v(" "),a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"输入利率",type:"number"},scopedSlots:e._u([{key:"append",fn:function(){return[e._v("%")]},proxy:!0}]),model:{value:e.form.interestRate,callback:function(t){e.$set(e.form,"interestRate",t)},expression:"form.interestRate"}})],1)]),e._v(" "),a("el-form-item",{attrs:{label:"计算结果"}},[e.calculatedResults.days||e.calculatedResults.interest||e.calculatedResults.total?a("div",{staticClass:"results-grid flex"},[a("div",{staticClass:"result-box flex"},[a("div",{staticClass:"result-label"},[e._v("间隔")]),e._v(" "),a("div",{staticClass:"result-value"},[e._v(e._s(e.calculatedResults.days)),a("span",{staticClass:"currency"},[e._v("天")])])]),e._v(" "),a("div",{staticClass:"result-box flex"},[a("div",{staticClass:"result-label"},[e._v("利息")]),e._v(" "),a("div",{staticClass:"result-value"},[a("span",{staticClass:"currency"},[e._v("¥")]),e._v(e._s(e.calculatedResults.interest))])]),e._v(" "),a("div",{staticClass:"result-box flex"},[a("div",{staticClass:"result-label"},[e._v("本+利")]),e._v(" "),a("div",{staticClass:"result-value"},[a("span",{staticClass:"currency"},[e._v("¥")]),e._v(e._s(e.calculatedResults.total))])])]):e._e()]),e._v(" "),a("el-form-item",{staticClass:"flex-end",staticStyle:{"margin-top":"15px"}},[a("el-button",{staticClass:"reset-btn",on:{click:e.reset}},[e._v("重置")]),e._v(" "),a("el-button",{staticClass:"calculate-btn",attrs:{type:"primary"},on:{click:e.calculate}},[e._v("计算")])],1)],1)],1)])},staticRenderFns:[]};var y=a("VU/8")(g,b,!1,function(e){a("VPDW")},"data-v-e8d41e7c",null).exports,_=a("zL8q"),C=a("zbWR"),x={name:"AppraisalFees",data:function(){return{form:{type1:"",type2:""},rules:{type1:[{required:!0,message:"请选择鉴定项目"}],type2:[{required:!0,message:"请选择鉴定项目"}]},appraisalOptions:[],tableData:[],dialogVisible:!1,headerCellStyle:function(){return{backgroundColor:"#F2F6F9",color:"#3C3C3C",fontWeight:"normal",borderBottom:"1px solid #F1F4F8",padding:"0px 0",height:"40px"}},cellStyle:function(){return{borderBottom:"1px solid #F1F4F8",height:"44px",padding:"0px 0"}}}},computed:{appraisalOptions2:function(){var e=this;if(e.form.type1){var t=e.appraisalOptions.find(function(t){return t.id===e.form.type1});if(t&&t.type)return t.type}return[]}},created:function(){this.getCalculatorType()},methods:{getCalculatorType:function(){var e=this;Object(C.i)().then(function(t){console.log(t);var a=t.code,i=t.data;if(200===a){var s=i.appraisal;e.appraisalOptions=s}})},toSearch:function(){console.log(this.form);var e=this;e.$refs.formRef.validate(function(t){t&&e.getAppraisalDetail(e.form.type2)})},getAppraisalDetail:function(e){var t=this,a=_.Loading.service({fullscreen:!0,text:"加载中..."});Object(C.f)({id:e}).then(function(e){console.log(e),a.close();var i=e.code,s=e.data;if(200===i){var l=s.type;t.tableData=l,t.dialogVisible=!0}}).catch(function(){a.close()})},reset:function(){this.form={type1:"",type2:""}}}},w={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"appraisal-fees legal-wrap"},[a("div",{staticClass:"calculator-form"},[a("el-form",{ref:"formRef",attrs:{model:e.form,"label-width":"auto",rules:e.rules}},[a("div",{staticClass:"flex"},[a("el-form-item",{staticClass:"flex-1",attrs:{label:"鉴定项目",prop:"type1"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.form.type1,callback:function(t){e.$set(e.form,"type1",t)},expression:"form.type1"}},e._l(e.appraisalOptions,function(e){return a("el-option",{key:e.id,attrs:{label:e.column,value:e.id}})}),1)],1),e._v(" "),e.appraisalOptions2.length>0?a("el-form-item",{staticClass:"no-label",style:"width: 50%;margin-left: 15px;",attrs:{prop:"type2"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.form.type2,callback:function(t){e.$set(e.form,"type2",t)},expression:"form.type2"}},e._l(e.appraisalOptions2,function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}),1)],1):e._e()],1),e._v(" "),a("el-form-item",{staticClass:"flex-end",staticStyle:{"margin-top":"25px"}},[a("el-button",{staticClass:"reset-btn",on:{click:e.reset}},[e._v("重置")]),e._v(" "),a("el-button",{staticClass:"calculate-btn",attrs:{type:"primary"},on:{click:e.toSearch}},[e._v("查询")])],1)],1)],1),e._v(" "),e._m(0),e._v(" "),a("el-dialog",{attrs:{title:"鉴定费用",visible:e.dialogVisible,width:"80%"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,height:"50vh","header-cell-style":e.headerCellStyle,"cell-style":e.cellStyle}},[a("el-table-column",{attrs:{prop:"name",label:"收费项目",width:"180"}}),e._v(" "),a("el-table-column",{attrs:{prop:"unit",label:"单位",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"price",label:"基准价（元）",width:"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"msg",label:"备注"}})],1)],1)],1)},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"calculator-results",staticStyle:{"margin-top":"45px"}},[t("div",{staticClass:"disclaimer"},[this._v("\n      根据《北京市司法鉴定政府指导价项目和收费标准基准价》查询，结果仅供参考\n    ")])])}]};var T={name:"NotaryFees",data:function(){return{form:{type1:"",type2:"",amount:""},rules:{type1:[{required:!0,message:"请选择公证项目"}],type2:[{required:!0,message:"请选择公证项目"}],amount:[{required:!0,message:"请输入标的金额"}]},notaryOptions:[],result:""}},computed:{notaryOptions2:function(){var e=this;if(e.form.type1){var t=e.notaryOptions.find(function(t){return t.column===e.form.type1});return t&&t.type||[]}return[]},showAmount:function(){if(this.form.type1)switch(this.form.type1){case"证明法律行为":case"提存公证":case"赋予债权文书具有强制执行效力":return!0;default:return!1}return!1}},watch:{"form.type1":{handler:function(e){this.form.type2="",this.result="",this.$refs.formRef.clearValidate("type2"),this.$refs.formRef.clearValidate("amount"),this.form.type&&"提存公证"==this.form.type?this.rules.type2=[{required:!1,message:"请选择公证项目"}]:this.rules.type2=[{required:!0,message:"请选择公证项目"}]}}},created:function(){this.getNotaryOptions()},methods:{getNotaryOptions:function(){var e=this;Object(C.i)().then(function(t){var a=t.code,i=t.data;if(200===a){var s=i.project;e.notaryOptions=s}})},calculate:function(){var e=this;e.$refs.formRef.validate(function(t){if(t){var a=e.form,i=a.type1,s=a.type2,l=a.amount;l=parseFloat(l)||0,e.result="证明法律行为"==i&&"证明经济合同、协议"==s?e.resultData1(l):"证明法律行为"==i&&"证明财产继承、赠与和遗赠"==s?e.resultData2(l):"提存公证"==i?.15*l/100<100?100:(.15*l/100).toFixed(2):e.resultData3(s,l)}})},resultData1:function(e){var t=0;return e<=5e5?t=(t=(.3*e/100).toFixed(2))<200?200:t:e>5e5&&e<=5e6?t=(1500+.25*(e-5e5)/100).toFixed(2):e>5e6&&e<=1e7?t=(12750+.2*(e-5e6)/100).toFixed(2):e>1e7&&e<=2e7?t=(22750+.15*(e-1e7)/100).toFixed(2):e>2e7&&e<=5e7?t=(37750+.1*(e-2e7)/100).toFixed(2):e>5e7&&e<=1e8?t=(67750+.05*(e-5e7)/100).toFixed(2):e>1e8&&(t=(92750+.01*(e-1e8)/100).toFixed(2)),t},resultData2:function(e){var t=0;return e<=2e5?t=(t=(.9*e/100).toFixed(2))<200?200:t:e>2e5&&e<=5e5?t=(1800+.7*(e-2e5)/100).toFixed(2):e>5e5&&e<=5e6?t=(3900+.5*(e-5e5)/100).toFixed(2):e>5e6&&e<=1e7?t=(26400+.3*(e-5e6)/100).toFixed(2):e>1e7&&(t=(41400+.1*(e-1e7)/100).toFixed(2)),t},resultData3:function(e,t){var a="";switch(e){case"证明非财产性民事协议":a="200/件";break;case"涉及财产关系的民事协议":a="400/件";break;case"证明收养关系-生父母共同送养的":a="500/件";break;case"证明收养关系-生父母单方送养的":a="800/件";break;case"证明收养关系-其他监护人送养的":case"证明收养关系-事实收养":a="1000/件";break;case"证明出生、生存、死亡、身份、经历、学历、国籍、婚姻状况、亲属关系、未受(受过)刑事处分等":a="80/件";break;case"证明法人和其他组织的资格资信":a="500/件";break;case"证明不可抗力事件":case"制作票据拒绝证书":a="400/件";break;case"证明知识产权的享有、转让和使用许可文书":case"证明法人或其他组织的授权委托书、公司章程、会议决议或其他法律文书":a="500/件";break;case"证明其他有法律意义的文书":a="80/件";break;case"不出具执行证":a=(.08*t/100).toFixed(2);break;case"出具执行证":a=.2*t/100<200?200:(.2*t/100).toFixed(2);break;case"遗嘱公证":a="400/件";break;case"保管遗嘱":a="10/件/月";break;case"清点遗产-公民个人":a="450/件";break;case"清点遗产-法人及其他经济组织":a="2500/件";break;case"确认遗嘱效力":a="500/件";break;case"公民个人":a="450/件";break;case"法人及其他经济组织":a="2500/件";break;case"声明书、委托书等公民个人的单方民事法律行为":a="200/件";break;case"公证抵押登记":a="400/件";break;case"证明行政性合同、协议如：宅基地使用协议等":a="30/件";break;case"无译文的公证副本":a="10/件";break;case"有译文的公证副本":a="20/件";break;case"公证卷宗查档":a="50/卷/次";break;case"未经审查":a="10/件";break;case"已经审查":a="按照该公证事项收费标准的50%收取"}return a},reset:function(){this.form={type1:"",type2:"",amount:""},this.result=""}}},F={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"notary-fees legal-fees-calculator legal-wrap"},[a("div",{staticClass:"calculator-form"},[a("el-form",{ref:"formRef",attrs:{model:e.form,"label-width":"auto",rules:e.rules}},[a("div",{staticClass:"flex"},[a("el-form-item",{staticClass:"flex-1",attrs:{label:"公证项目",prop:"type1"}},[a("div",{staticClass:"flex"},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.form.type1,callback:function(t){e.$set(e.form,"type1",t)},expression:"form.type1"}},e._l(e.notaryOptions,function(e){return a("el-option",{key:e.column,attrs:{label:e.column,value:e.column}})}),1)],1)]),e._v(" "),e.notaryOptions2.length>0?a("el-form-item",{staticClass:"no-label",style:"width: "+(e.showAmount?"30%":"50%")+";margin-left: 15px;",attrs:{prop:"type2"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.form.type2,callback:function(t){e.$set(e.form,"type2",t)},expression:"form.type2"}},e._l(e.notaryOptions2,function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})}),1)],1):e._e(),e._v(" "),e.showAmount?a("el-form-item",{staticStyle:{width:"30%","margin-left":"15px"},attrs:{label:"标的金额",prop:"amount"}},[a("el-input",{attrs:{type:"number",placeholder:"输入金额"},scopedSlots:e._u([{key:"prepend",fn:function(){return[e._v("￥")]},proxy:!0}],null,!1,1264151776),model:{value:e.form.amount,callback:function(t){e.$set(e.form,"amount",t)},expression:"form.amount"}})],1):e._e()],1),e._v(" "),a("div",{staticClass:"flex"},[a("el-form-item",{staticClass:"flex-1",attrs:{label:"计算结果"}},[a("div",{staticClass:"results-grid flex"},[a("div",{staticClass:"result-box flex"},[a("div",{staticClass:"result-label"}),e._v(" "),e.result?a("div",{staticClass:"result-value"},["已经审查"!=e.form.type2?a("span",{staticClass:"currency"},[e._v("¥")]):e._e(),e._v(e._s(e.result))]):e._e()])])])],1),e._v(" "),a("el-form-item",{staticClass:"flex-end"},[a("el-button",{staticClass:"reset-btn",on:{click:e.reset}},[e._v("重置")]),e._v(" "),a("el-button",{staticClass:"calculate-btn",attrs:{type:"primary"},on:{click:e.calculate}},[e._v("计算")])],1)],1)],1),e._v(" "),e._m(0)])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"calculator-results"},[t("div",{staticClass:"disclaimer"},[this._v("\n      根据《北京市实行政府指导价的公证服务项目目录和收费标准》计算，结果仅供参考\n    ")])])}]};var k={name:"LegalFees",data:function(){return{form:{caseType:"",amount:""},rules:{caseType:[{required:!0,message:"请选择案件类型",trigger:"change"}],amount:[{required:!0,message:"请输入诉讼标的金额",trigger:"blur"}]},isAmountRequired:!1,calculatedFees:{acceptFee:0,executionFee:0,securityFee:0},caseTypeOptions:[{value:"property",label:"财产案件"},{value:"nonProperty",label:"普通非财产案件"},{value:"divorce",label:"离婚案件"},{value:"personalRights",label:"人格权案件"},{value:"intellectualProperty",label:"知识产权民事案件"},{value:"laborDispute",label:"劳动争议案件"},{value:"administrative",label:"商标、专利、海事行政案件"},{value:"jurisdiction",label:"管辖权异议不成立案件"}]}},methods:{handleCaseTypeChange:function(){this.form.caseType?this.isAmountRequired=!0:this.isAmountRequired=!1,this.resetCalculation()},calculate:function(){var e=this;e.$refs.formRef.validate(function(t){t&&(e.calculatedFees={acceptFee:e.calculateAcceptFee(),executionFee:e.calculateExecutionFee(),securityFee:e.calculateSecurityFee()})})},calculateAcceptFee:function(){var e=parseFloat(this.form.amount)||0,t=0;switch(this.form.caseType){case"property":t=this.calculatePropertyFee(e);break;case"nonProperty":t="50-100";break;case"divorce":t=this.calculateDivorceFee(e);break;case"personalRights":t=this.calculatePersonalRightsFee(e);break;case"intellectualProperty":t=this.calculateIntellectualPropertyFee(e);break;case"laborDispute":t="10";break;case"administrative":t="100";break;case"jurisdiction":t="50-100"}return t},calculatePropertyFee:function(e){var t=0;return e<1e4?t=50:e>=1e4&&e<1e5?t=50*****(e-1e4)/100:e>=1e5&&e<2e5?t=2300+2*(e-1e5)/100:e>=2e5&&e<5e5?t=4300*****(e-2e5)/100:e>=5e5&&e<1e6?t=8800+1*(e-5e5)/100:e>=1e6&&e<2e6?t=13800+.9*(e-1e6)/100:e>=2e6&&e<5e6?t=22800+.8*(e-2e6)/100:e>=5e6&&e<1e7?t=25200+.7*(e-5e6)/100:e>=1e7&&e<2e7?t=28700+.6*(e-1e7)/100:e>=2e7&&(t=34700+.5*(e-2e7)/100),t.toFixed(2)},calculateDivorceFee:function(e){return e<2e5?"50-300":50+.5*(e-2e5)/100+"-"+(300+.5*(e-2e5)/100)},calculatePersonalRightsFee:function(e){return e<5e4?"100-500":e>=5e4&&e<1e5?100+1*(e-5e4)/100+"-"+(500+1*(e-5e4)/100):600+.5*(e-1e5)/100+"-"+(1e3+.5*(e-1e5)/100)},calculateIntellectualPropertyFee:function(e){var t=0;return 0==e?"500-1000":(e<1e4?t=50:e>=1e4&&e<1e5?t=50*****(e-1e4)/100:e>=1e5&&e<2e5?t=2300+2*(e-1e5)/100:e>=2e5&&e<5e5?t=4300*****(e-2e5)/100:e>=5e5&&e<1e6?t=8800+1*(e-5e5)/100:e>=1e6&&e<2e6?t=13800+.9*(e-1e6)/100:e>=2e6&&e<5e6?t=22800+.8*(e-2e6)/100:e>=5e6&&e<1e7?t=46800+.7*(e-5e6)/100:e>=1e7&&e<2e7?t=81800+.6*(e-1e7)/100:e>=2e7&&(t=141800+.5*(e-2e7)/100),t.toFixed(2))},calculateExecutionFee:function(){var e=parseFloat(this.form.amount)||0,t=0;return 0==e?"50-500":(e<1e4?t=50:e>=1e4&&e<5e5?t=50*****(e-1e4)/100:e>=5e5&&e<5e6?t=7400+1*(e-5e5)/100:e>=5e6&&e<1e7?t=52400+.5*(e-5e6)/100:e>1e7&&(t=77400+.1*(e-1e7)/100),t.toFixed(2))},calculateSecurityFee:function(){var e=parseFloat(this.form.amount)||0,t=0;return e<1e3?t=30:e>=1e3&&e<1e5?t=30+1*(e-1e3)/100:e>=1e5&&(t=1020+.5*(e-1e5)/100),t>5e3&&(t=5e3),t.toFixed(2)},reset:function(){this.form.caseType="",this.form.amount="",this.isAmountRequired=!1,this.resetCalculation()},resetCalculation:function(){this.calculatedFees={acceptFee:0,executionFee:0,securityFee:0}},showLegalDocument:function(){this.$message.info("《诉讼费用缴纳办法》文件查看功能待实现")}}},D={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"legal-fees-calculator legal-wrap"},[a("div",{staticClass:"calculator-form"},[a("el-form",{ref:"formRef",attrs:{model:e.form,"label-width":"auto",rules:e.rules}},[a("div",{staticClass:"flex"},[a("el-form-item",{staticStyle:{"margin-right":"15px"},attrs:{label:"案件类型",prop:"caseType"}},[a("el-select",{attrs:{placeholder:"请选择"},on:{change:e.handleCaseTypeChange},model:{value:e.form.caseType,callback:function(t){e.$set(e.form,"caseType",t)},expression:"form.caseType"}},e._l(e.caseTypeOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{staticClass:"flex-1",attrs:{label:"诉讼标的",prop:"amount"}},[a("el-input",{attrs:{type:"number",placeholder:"输入金额",disabled:!e.isAmountRequired},scopedSlots:e._u([{key:"prepend",fn:function(){return[e._v("￥")]},proxy:!0}]),model:{value:e.form.amount,callback:function(t){e.$set(e.form,"amount",t)},expression:"form.amount"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"计算结果"}},[e.calculatedFees.acceptFee||e.calculatedFees.executionFee||e.calculatedFees.securityFee?a("div",{staticClass:"results-grid flex"},[a("div",{staticClass:"result-box flex"},[a("div",{staticClass:"result-label"},[e._v("受理费")]),e._v(" "),a("div",{staticClass:"result-value"},[a("span",{staticClass:"currency"},[e._v("¥")]),e._v(e._s(e.calculatedFees.acceptFee))])]),e._v(" "),a("div",{staticClass:"result-box flex"},[a("div",{staticClass:"result-label"},[e._v("执行费")]),e._v(" "),a("div",{staticClass:"result-value"},[a("span",{staticClass:"currency"},[e._v("¥")]),e._v(e._s(e.calculatedFees.executionFee))])]),e._v(" "),a("div",{staticClass:"result-box flex"},[a("div",{staticClass:"result-label"},[e._v("保全费")]),e._v(" "),a("div",{staticClass:"result-value"},[a("span",{staticClass:"currency"},[e._v("¥")]),e._v(e._s(e.calculatedFees.securityFee))])])]):e._e()]),e._v(" "),a("el-form-item",{staticClass:"flex-end"},[a("el-button",{staticClass:"reset-btn",on:{click:e.reset}},[e._v("重置")]),e._v(" "),a("el-button",{staticClass:"calculate-btn",attrs:{type:"primary"},on:{click:e.calculate}},[e._v("计算")])],1)],1)],1),e._v(" "),a("div",{staticClass:"calculator-results"},[a("div",{staticClass:"disclaimer"},[e._v("\n      根据 "),a("a",{attrs:{href:"javascript:void(0)"},on:{click:e.showLegalDocument}},[e._v("《诉讼费用缴纳办法》")]),e._v(" 文件计算，结果仅供参考\n    ")])])])},staticRenderFns:[]};var R={name:"LawyerFees",data:function(){return{form:{location:"",price:"",hours:""},rules:{location:[{required:!0,message:"请选择所在地"}],price:[{required:!0,message:"请输入元/小时"}],hours:[{required:!0,message:"请输入小时"}]},result:"",lawyer:[]}},computed:{locationOptions:function(){return this.lawyer.map(function(e){return{value:e.province,label:e.province}})}},created:function(){this.getLawyerFees()},methods:{getLawyerFees:function(){var e=this;Object(C.i)().then(function(t){var a=t.code,i=t.data;if(200===a){var s=i.lawyer;e.lawyer=s}})},calculate:function(){var e=this;e.$refs.formRef&&e.$refs.formRef.validate(function(t){t&&(e.result=e.calculateLawyerFees())})},calculateLawyerFees:function(){var e=this.form.location,t=this.form.price,a=this.form.hours,i=0;return e&&t&&a&&(i=t*a),i.toFixed(2)},reset:function(){this.form={location:"",price:"",hours:""},this.result=""}}},L={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"lawyer-fees legal-fees-calculator legal-wrap"},[a("div",{staticClass:"calculator-form"},[a("el-form",{ref:"formRef",attrs:{model:e.form,"label-width":"auto",rules:e.rules}},[a("div",{staticClass:"flex"},[a("el-form-item",{staticStyle:{"margin-right":"15px"},attrs:{label:"所在地",prop:"location"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{placeholder:"请选择"},model:{value:e.form.location,callback:function(t){e.$set(e.form,"location",t)},expression:"form.location"}},e._l(e.locationOptions,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{staticStyle:{"margin-right":"15px",width:"calc((100% - 100px - 15px -15px) / 2)"},attrs:{label:"元/小时",prop:"price"}},[a("el-input",{attrs:{type:"number",placeholder:"收费标准"},model:{value:e.form.price,callback:function(t){e.$set(e.form,"price",t)},expression:"form.price"}})],1),e._v(" "),a("el-form-item",{staticStyle:{width:"calc((100% - 100px - 15px -15px) / 2)"},attrs:{label:"小时",prop:"hours"}},[a("el-input",{attrs:{type:"number",placeholder:"有效工作时数"},model:{value:e.form.hours,callback:function(t){e.$set(e.form,"hours",t)},expression:"form.hours"}})],1)],1),e._v(" "),e.form.location?a("p",{staticClass:"price-guide"},[e._v(e._s(e.lawyer.find(function(t){return t.province===e.form.location}).guide)+"\n      ")]):e._e(),e._v(" "),a("div",{staticClass:"flex"},[a("el-form-item",{staticClass:"flex-1",attrs:{label:"计算结果"}},[a("div",{staticClass:"results-grid flex"},[a("div",{staticClass:"result-box flex"},[a("div",{staticClass:"result-label"}),e._v(" "),e.result?a("div",{staticClass:"result-value"},[a("span",{staticClass:"currency"},[e._v("¥")]),e._v(e._s(e.result))]):e._e()])])])],1),e._v(" "),a("el-form-item",{staticClass:"flex-end"},[a("el-button",{staticClass:"reset-btn",on:{click:e.reset}},[e._v("重置")]),e._v(" "),a("el-button",{staticClass:"calculate-btn",attrs:{type:"primary"},on:{click:e.calculate}},[e._v("计算")])],1)],1)],1),e._v(" "),e._m(0)])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"calculator-results"},[t("div",{staticClass:"disclaimer"},[this._v("\n      根据各地律师服务计时收费标准计算，结果仅供参考\n    ")])])}]};var $={name:"DismissFees",data:function(){return{form:{startDate:"",endDate:"",salary:""},rules:{startDate:[{required:!0,message:"请选择入职日期"}],endDate:[{required:!0,message:"请选择离职日期"}],salary:[{required:!0,message:"请输入月薪"},{type:"number",message:"月薪必须为数字"}]},calculationResult:null}},methods:{calculate:function(){var e=this;e.$refs.formRef.validate(function(t){if(t){var a=e.getDaysBetween(e.form.startDate,e.form.endDate);if(console.log(a),0==a[0]&&a[1]<6)e.calculationResult=e.form.salary/2;else if(0==a[0]&&a[1]>=6)e.calculationResult=e.form.salary;else{var i=a[0];a[1]>=6?i++:a[1]>0&&a[1]<6&&(i+=.5),e.calculationResult=e.form.salary*i}}})},getDaysBetween:function(e,t){var a=0,i=0,s=e.split("-"),l=t.split("-"),n=parseInt(s[0]),r=parseInt(s[1]),o=parseInt(s[2]),c=parseInt(l[0]),u=parseInt(l[1]),d=parseInt(l[2]);return n==c?u==r?a=.1:(a=u-r,d>o?a+=.1:d<o&&(a=a-1+.1)):(i=c-n,r<u?(a=u-r,d>o?a+=.1:d<o&&(a=a-1+.1)):r>u?(i=i>0?i-1:0,a=12-r+u,d>o?a+=.1:d<o&&(a=a-1+.1)):d>o?a=.1:d<o&&(i=i>0?i-1:0,a=9.1)),[i,a]},reset:function(){this.form={startDate:"",endDate:"",salary:""},this.calculationResult=null}}},S={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dismiss-fees legal-fees-calculator legal-wrap"},[a("div",{staticClass:"calculator-form"},[a("el-form",{ref:"formRef",attrs:{model:e.form,"label-width":"auto",rules:e.rules}},[a("div",{staticClass:"flex",staticStyle:{"align-items":"center"}},[a("el-form-item",{attrs:{label:"日期",prop:"startDate"}},[a("el-date-picker",{staticStyle:{width:"200px"},attrs:{type:"date",placeholder:"入职日期","value-format":"yyyy-MM-dd"},model:{value:e.form.startDate,callback:function(t){e.$set(e.form,"startDate",t)},expression:"form.startDate"}})],1),e._v(" "),a("span",{staticStyle:{margin:"0 10px 16px 10px"}},[e._v("—")]),e._v(" "),a("el-form-item",{staticClass:"no-label",attrs:{label:"",prop:"endDate"}},[a("el-date-picker",{staticStyle:{width:"200px"},attrs:{type:"date",placeholder:"离职日期","value-format":"yyyy-MM-dd"},model:{value:e.form.endDate,callback:function(t){e.$set(e.form,"endDate",t)},expression:"form.endDate"}})],1)],1),e._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"月薪:",prop:"salary"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入您的月薪"},model:{value:e.form.salary,callback:function(t){e.$set(e.form,"salary",e._n(t))},expression:"form.salary"}})],1),e._v(" "),a("el-form-item",{staticClass:"flex-1",staticStyle:{"margin-bottom":"0px"},attrs:{label:"计算结果"}},[a("div",{staticClass:"results-grid flex"},[a("div",{staticClass:"result-box flex"},[a("div",{staticClass:"result-label"},[e._v("您预计可获得经济补偿金")]),e._v(" "),e.calculationResult?a("div",{staticClass:"result-value"},[a("span",{staticClass:"currency"},[e._v("¥")]),e._v(e._s(e.calculationResult)+"\n            ")]):e._e()])])]),e._v(" "),a("el-form-item",{staticClass:"flex-end",staticStyle:{"margin-top":"0px"}},[a("el-button",{staticClass:"reset-btn",on:{click:e.reset}},[e._v("重置")]),e._v(" "),a("el-button",{staticClass:"calculate-btn",attrs:{type:"primary"},on:{click:e.calculate}},[e._v("计算")])],1)],1)],1),e._v(" "),e._m(0)])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"calculator-results",staticStyle:{"margin-top":"0"}},[t("div",{staticClass:"disclaimer",staticStyle:{"margin-top":"0"}},[this._v("\n      根据《劳动合同法》、《劳动合同法实施条例》文件计算，结果仅供参考\n      ")])])}]};var U={name:"Calculator",components:{TabBar:h,InterestFees:y,AppraisalFees:a("VU/8")(x,w,!1,function(e){a("qnuF")},"data-v-aa14bccc",null).exports,NotaryFees:a("VU/8")(T,F,!1,function(e){a("goLn")},"data-v-493d39f4",null).exports,LegalFees:a("VU/8")(k,D,!1,function(e){a("6/L/")},"data-v-7c830fa6",null).exports,LawyerFees:a("VU/8")(R,L,!1,function(e){a("UuEy")},"data-v-422bacbe",null).exports,DismissFees:a("VU/8")($,S,!1,function(e){a("Fbvc")},"data-v-3a9c4dd0",null).exports},data:function(){return{activeTab:"litigation",tabs:[{label:"诉讼费",value:"litigation"},{label:"律师费",value:"lawyer"},{label:"公证费",value:"notary"},{label:"鉴定费",value:"appraisal"},{label:"辞退赔偿",value:"compensation"},{label:"利息计算",value:"interest"}]}},computed:{currentComponent:function(){return{interest:"InterestFees",appraisal:"AppraisalFees",notary:"NotaryFees",litigation:"LegalFees",lawyer:"LawyerFees",compensation:"DismissFees"}[this.activeTab]}},methods:{handleTabChange:function(e){console.log("Tab changed:",e)}}},P={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"calculator"},[a("TabBar",{attrs:{tabs:e.tabs},on:{change:e.handleTabChange},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}}),e._v(" "),a("div",{staticClass:"calculator-content"},[a(e.currentComponent,{tag:"component"})],1)],1)},staticRenderFns:[]};var V=a("VU/8")(U,P,!1,function(e){a("zd9R")},"data-v-f03f234c",null).exports,I=a("Gu7T"),O=a.n(I),A={name:"TempListItem",props:{item:{type:Object,required:!0,default:function(){return{}}}},methods:{handleClick:function(){this.$emit("click",this.item)}}},E={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"temp-list-item",on:{click:this.handleClick}},[t("div",{staticClass:"preview-wrapper"},[t("img",{staticClass:"preview-img",attrs:{src:this.item.DocPic,alt:this.item.DocName}})]),this._v(" "),t("div",{staticClass:"temp-name"},[this._v(this._s(this.item.DocName))])])},staticRenderFns:[]};var q=a("VU/8")(A,E,!1,function(e){a("8kx5")},"data-v-1407c7fd",null).exports,B=a("eVS1"),M={name:"DocumentActionButtons",props:{docUrl:{type:String,default:""},templateDetail:{type:String,default:""},loading:{type:Boolean,default:!1}},methods:{handlePreview:function(){this.$emit("preview")},handleDownload:function(){this.$emit("download")},handlePrint:function(){this.$emit("print")}}},z={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"action-buttons"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-view",disabled:!e.docUrl||e.loading},on:{click:e.handlePreview}},[e._v("\n    预览\n  ")]),e._v(" "),a("el-button",{attrs:{type:"success",icon:"el-icon-download",disabled:!e.docUrl||e.loading},on:{click:e.handleDownload}},[e._v("\n    下载\n  ")]),e._v(" "),a("el-button",{attrs:{type:"warning",icon:"el-icon-printer",disabled:!e.docUrl&&!e.templateDetail||e.loading},on:{click:e.handlePrint}},[e._v("\n    打印\n  ")])],1)},staticRenderFns:[]};var N=a("VU/8")(M,z,!1,function(e){a("iFMK")},"data-v-1cffd1d0",null).exports,j={data:function(){return{docUrl:"",currentData:null}},methods:{handlePreview:function(){if(this.docUrl)try{var e="";e=this.docUrl.toLowerCase().includes(".pdf")?"https://mozilla.github.io/pdf.js/web/viewer.html?file="+encodeURIComponent(this.docUrl):this.docUrl.toLowerCase().includes(".docx")||this.docUrl.toLowerCase().includes(".doc")?"https://view.officeapps.live.com/op/embed.aspx?src="+encodeURIComponent(this.docUrl):"https://docs.google.com/gview?url="+encodeURIComponent(this.docUrl)+"&embedded=true",window.open(e,"_blank")||this.$message.error("无法打开预览窗口，请检查浏览器弹窗设置")}catch(e){console.error("预览失败:",e),this.$message.error("预览失败，请尝试下载文档查看")}else this.$message.warning("暂无可预览的文档")},handleDownload:function(){if(this.docUrl)try{var e=document.createElement("a");e.href=this.docUrl,e.download=this.getDocumentTitle()+".docx",e.target="_blank",document.body.appendChild(e),e.click(),document.body.removeChild(e),this.$message.success("开始下载文档")}catch(e){console.error("下载失败:",e),this.$message.error("下载失败")}else this.$message.warning("暂无可下载的文档")},handlePrint:function(){this.docUrl?this.printDocumentFromUrl():this.templateDetail?this.printHtmlContent():this.$message.warning("暂无可打印的内容")},printDocumentFromUrl:function(){try{var e="";if(e=this.docUrl.toLowerCase().includes(".pdf")?this.docUrl:(this.docUrl.toLowerCase().includes(".docx")||this.docUrl.toLowerCase().includes(".doc"),"https://docs.google.com/gview?url="+encodeURIComponent(this.docUrl)+"&embedded=true"),!window.open(e,"_blank"))return void this.$message.error("无法打开预览窗口，请检查浏览器弹窗设置");this.$message.success("文档已在新标签页打开，您可以使用浏览器的打印功能（Ctrl+P）进行打印")}catch(e){console.error("文档打印失败:",e),this.$message.error("文档打印失败，尝试使用备用打印方式"),this.templateDetail&&this.printHtmlContent()}},printHtmlContent:function(){try{var e=window.open("","_blank");if(!e)return void this.$message.error("无法打开打印窗口，请检查浏览器弹窗设置");var t='\n          <!DOCTYPE html>\n          <html>\n          <head>\n            <meta charset="utf-8">\n            <title>'+this.getDocumentTitle()+'</title>\n            <style>\n              body {\n                font-family: "Microsoft YaHei", Arial, sans-serif;\n                font-size: 14px;\n                line-height: 1.6;\n                color: #333;\n                margin: 20px;\n              }\n              h1, h2, h3, h4, h5, h6 {\n                color: #2c3e50;\n                margin-top: 20px;\n                margin-bottom: 10px;\n              }\n              .print-header {\n                text-align: center;\n                border-bottom: 2px solid #3498db;\n                padding-bottom: 10px;\n                margin-bottom: 20px;\n              }\n              .print-title {\n                font-size: 24px;\n                font-weight: bold;\n                color: #2c3e50;\n                margin: 0;\n              }\n              .print-content {\n                margin-top: 20px;\n              }\n              @media print {\n                body { margin: 0; }\n                .no-print { display: none; }\n              }\n            </style>\n          </head>\n          <body>\n            <div class="print-header">\n              <h1 class="print-title">'+this.getDocumentTitle()+'</h1>\n            </div>\n            <div class="print-content">\n              '+this.templateDetail+"\n            </div>\n          </body>\n          </html>\n        ";e.document.write(t),e.document.close(),e.onload=function(){e.print(),e.close()},this.$message.success("正在准备打印...")}catch(e){console.error("HTML打印失败:",e),this.$message.error("打印失败")}},getDocumentTitle:function(){return this.title||this.dialogTitle||"文档"},clearDocumentData:function(){this.docUrl="",this.currentData=null}}},W={name:"DetailDialog",components:{BaseDialog:B.a,DocumentActionButtons:N},mixins:[j],data:function(){return{dialogVisible:!1,templateId:"",templateDetail:"",dialogTitle:"文书模板",loading:!1}},methods:{open:function(e){this.dialogVisible=!0,this.templateId=e.RecordGuid,this.templateDetail="",this.dialogTitle=e.Title||"文书模板",this.currentData=e,this.clearDocumentData(),this.getTemplateDetail()},handleClose:function(){console.log("handleClose"),this.dialogVisible=!1,this.templateDetail="",this.dialogTitle="文书模板",this.clearDocumentData()},getTemplateDetail:function(){var e=this;this.loading=!0,Object(C.w)({RecordGuid:this.templateId}).then(function(t){console.log(t,"res");var a=t.data;200===t.code&&(e.templateDetail=a.DocDesc||"",e.docUrl=a.DocUrl||"",console.log("DocUrl:",e.docUrl))}).catch(function(t){console.error("获取模板详情失败:",t),e.$message.error("获取模板详情失败")}).finally(function(){e.loading=!1})},getDocumentTitle:function(){return this.dialogTitle}}},Q={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("base-dialog",{attrs:{visible:e.dialogVisible,title:e.dialogTitle,height:"80vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose},scopedSlots:e._u([{key:"title-extra",fn:function(){return[a("document-action-buttons",{attrs:{"doc-url":e.docUrl,"template-detail":e.templateDetail,loading:e.loading},on:{preview:e.handlePreview,download:e.handleDownload,print:e.handlePrint}})]},proxy:!0}])},[e._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"detail-container h100"},[a("div",{staticClass:"detail-content h100",domProps:{innerHTML:e._s(e.templateDetail)}})])])},staticRenderFns:[]};var H={name:"TempList",components:{ListItem:q,DetailDialog:a("VU/8")(W,Q,!1,function(e){a("/uOQ")},"data-v-399ae6a2",null).exports},props:{tempColumn:{type:String,default:"诉讼文书"},isScrollLoad:{type:Boolean,default:!0}},data:function(){return{loaded:!1,loading:!1,list:[],pageIndex:1,total:0,lastPage:0,tempType:"",currentColumns:6,currentRows:2,containerWidth:0,resizeObserver:null}},computed:{columnsForWidth:function(){var e=this.containerWidth;return e>=940?6:e>=768?5:e>=600?4:e>=400?3:2},rowsForWidth:function(){return this.containerWidth>=940?2:3}},mounted:function(){this.initResizeObserver(),this.updateContainerWidth()},beforeDestroy:function(){this.destroyResizeObserver()},methods:{initResizeObserver:function(){var e=this;"undefined"!=typeof ResizeObserver&&(this.resizeObserver=new ResizeObserver(function(t){e.updateContainerWidth()}),this.$refs.tempList&&this.resizeObserver.observe(this.$refs.tempList))},destroyResizeObserver:function(){this.resizeObserver&&(this.resizeObserver.disconnect(),this.resizeObserver=null)},updateContainerWidth:function(){this.$refs.tempList&&(this.containerWidth=this.$refs.tempList.offsetWidth,this.updateSizeSettings())},updateSizeSettings:function(){var e=this.columnsForWidth,t=this.rowsForWidth;this.currentColumns===e&&this.currentRows===t||(this.currentColumns=e,this.currentRows=t,this.tempType&&this.reset(this.tempType))},getList:function(){var e=this;if(!(!this.tempType||this.loading||this.pageIndex>this.lastPage&&0!==this.lastPage)){var t={Column:this.tempColumn,PageIndex:this.pageIndex,PageSize:this.currentColumns*this.currentRows,Type:this.tempType};this.loading=!0,Object(C.x)(t).then(function(t){console.log(t,"模板列表");var a=t.code,i=t.data;if(200==a){var s=i.list,l=i.page;1==e.pageIndex?e.list=s:e.list=[].concat(O()(e.list),O()(s)),e.total=l.count,e.lastPage=l.page}}).finally(function(){e.loading=!1,e.loaded=!0})}},reset:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.pageIndex=1,this.list=[],this.total=0,this.lastPage=0,this.loaded=!1,this.tempType=e,this.getList()},handleClick:function(e){this.$refs.detailDialogRef.open(e)},handleScroll:function(){var e=this.$refs.tempList;e&&this.isScrollLoad&&(e.getBoundingClientRect().bottom<=window.innerHeight+100&&!this.loading&&this.pageIndex<this.lastPage&&(this.pageIndex++,this.getList()))}}},Y={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:!e.loaded,expression:"!loaded"}],ref:"tempList",staticClass:"temp-list",attrs:{"element-loading-background":"rgba(255, 255, 255, 0.4)"},on:{scroll:e.handleScroll}},[a("div",{staticClass:"list-content"},[e.list.length?e._l(e.list,function(t,i){return a("list-item",{key:i,staticClass:"list-item",style:{width:"calc(100% / "+e.currentColumns+" - "+(e.currentColumns-1)+" * 20px / "+e.currentColumns+")",marginRight:((i+1)%e.currentColumns==0?0:20)+"px"},attrs:{item:t},on:{click:function(a){return e.handleClick(t)}}})}):a("div",{staticClass:"empty-tip"},[e._v("\n      暂无相关模板\n    ")])],2),e._v(" "),e.list.length&&e.isScrollLoad?a("div",{staticClass:"load-more"},[e.loading?a("p",[e._v("加载中...")]):e.pageIndex>=e.lastPage?a("p",[e._v("没有更多了")]):a("p",[e._v("滚动加载更多")])]):e._e(),e._v(" "),a("detail-dialog",{ref:"detailDialogRef"})],1)},staticRenderFns:[]};var G={name:"TempLibrary",components:{TabBar:h,List:a("VU/8")(H,Y,!1,function(e){a("rVwj")},"data-v-f2ea366a",null).exports},props:{tempType:{type:Object,default:function(){return{id:1,name:"诉讼文书"}}},column:{type:Number,default:6},row:{type:Number,default:2},isScrollLoad:{type:Boolean,default:!1},tabbarBorderColor:{type:String,default:"#E4E7ED"}},data:function(){return{activeTab:"",tabs:[]}},watch:{tempType:{handler:function(e){var t=this;t.tabs=[],t.activeTab="",t.$nextTick(function(){t.$refs.listRef.list=[]}),t.getTemplateType()},immediate:!0,deep:!0}},methods:{getTemplateType:function(){var e=this,t=e.tempType;Object(C.y)({type:t.id}).then(function(a){console.log(a,"模板类型");var i=a.code,s=a.data;if(200==i){var l=s.list;l.length&&l.forEach(function(t){e.tabs.push({label:t.name,value:t.type})}),e.activeTab=e.tabs[0].value,e.$nextTick(function(){e.$refs.listRef.reset(e.activeTab,t.name)})}})},handleChange:function(){var e=this;e.$nextTick(function(){e.$refs.listRef.reset(e.activeTab,e.tempType.name)})}}},K={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"temp-library"},[a("TabBar",{attrs:{tabs:e.tabs,borderColor:e.tabbarBorderColor},on:{change:e.handleChange},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}}),e._v(" "),a("List",{ref:"listRef",staticClass:"flex-1",attrs:{tempColumn:e.tempType.name,column:e.column,row:e.row,isScrollLoad:e.isScrollLoad}})],1)},staticRenderFns:[]};var J=a("VU/8")(G,K,!1,function(e){a("CwYY")},"data-v-265e0f52",null).exports,X={name:"TempLibrary",components:{TempLibraryList:J},data:function(){return{tempType:{id:1,name:"诉讼文书"},isScrollLoad:!1}}},Z={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"temp-library"},[t("TempLibraryList",{ref:"tempLibraryListRef",attrs:{tempType:this.tempType,isScrollLoad:this.isScrollLoad}})],1)},staticRenderFns:[]};var ee=a("VU/8")(X,Z,!1,function(e){a("gfPs")},"data-v-21edb83a",null).exports,te={name:"AllDialog",components:{BaseDialog:B.a,TempLibraryList:J},data:function(){return{dialogVisible:!1,activeTab:1,tempType:{id:1,name:"诉讼文书"},isScrollLoad:!0}},watch:{activeTab:{handler:function(e){this.tempType={id:e,name:1===e?"诉讼文书":"合同文书"}}}},methods:{open:function(){this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1}}},ae={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("base-dialog",{attrs:{visible:e.dialogVisible,title:"文书模板",height:"80vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("div",{staticClass:"list-container flex-column"},[a("div",{staticClass:"custom-tabs"},[a("div",{staticClass:"tab-item",class:{active:1===e.activeTab},on:{click:function(t){e.activeTab=1}}},[e._v("\n        诉讼文书\n      ")]),e._v(" "),a("div",{staticClass:"tab-item",class:{active:2===e.activeTab},on:{click:function(t){e.activeTab=2}}},[e._v("\n        合同文书\n      ")])]),e._v(" "),a("temp-library-list",{ref:"tempLibraryListRef",staticClass:"temp-list-wrapper flex-1",attrs:{tempType:e.tempType,column:6,row:3,isScrollLoad:e.isScrollLoad,tabbarBorderColor:"#F1F4F8"}})],1)])},staticRenderFns:[]};var ie=a("VU/8")(te,ae,!1,function(e){a("ivDj")},"data-v-71e3f3ab",null).exports,se={name:"LegalConsultation",components:{TabBar:h},data:function(){return{activeTab:"image",tabs:[{label:"图文咨询",value:"image"},{label:"电话咨询",value:"phone"}],selectedType:"",questionContent:"",questionTypes:[{label:"家庭婚姻",value:"family",icon:a("F1hy"),activeIcon:a("h+Ps")},{label:"土地房产",value:"property",icon:a("GfnP"),activeIcon:a("32ue")},{label:"交通事故",value:"traffic",icon:a("6Dwx"),activeIcon:a("nFL7")},{label:"合同欠款",value:"contract",icon:a("ok+L"),activeIcon:a("0dbJ")},{label:"劳动维权",value:"labor",icon:a("8fnJ"),activeIcon:a("9+ed")},{label:"企业创业",value:"business",icon:a("UPua"),activeIcon:a("nosd")},{label:"刑事法律",value:"criminal",icon:a("CPyo"),activeIcon:a("myQ/")},{label:"知识产权",value:"ip",icon:a("/vHh"),activeIcon:a("pdwY")},{label:"其他法律",value:"other",icon:a("4TKF"),activeIcon:a("C85e")}],loading:!1,dialogVisible:!1,iframeUrl:""}},mounted:function(){var e=this;window.addEventListener("message",function(t){"https://longcai.zsgzbj.com"===t.origin&&"closePopup"===t.data.action&&e.closePopup()})},methods:{handleTabChange:function(e){},handleTypeSelect:function(e){this.selectedType=e},handleSubmit:function(){var e=this;if(!e.loading){var t={};e.questionContent&&(t.content=e.questionContent),e.loading=!0,Object(C.k)(t).then(function(t){console.log(t,"法律咨询"),200===t.code&&(e.iframeUrl=t.data.url,e.dialogVisible=!0)}).finally(function(){e.loading=!1})}},closePopup:function(){this.dialogVisible=!1,this.iframeUrl=""}}},le={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"legal-consultation"},[a("TabBar",{attrs:{tabs:e.tabs},on:{change:e.handleTabChange},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}}),e._v(" "),a("div",{staticClass:"consultation-content"},[a("div",{staticClass:"text-consultation"},[a("div",{staticClass:"type-selection"},[a("div",{staticClass:"type-title"},[e._v("选择问题类型")]),e._v(" "),a("div",{staticClass:"type-grid"},e._l(e.questionTypes,function(t){return a("div",{key:t.value,staticClass:"type-item",class:{active:e.selectedType===t.value},on:{click:function(a){return e.handleTypeSelect(t.value)}}},[a("img",{staticClass:"type-icon",attrs:{src:e.selectedType===t.value?t.activeIcon:t.icon,alt:""}}),e._v(" "),a("span",[e._v(e._s(t.label))])])}),0)]),e._v(" "),a("div",{staticClass:"question-input"},["image"==e.activeTab?a("div",{staticClass:"input-area"},[a("p",{staticClass:"input-title"},[e._v(e._s("image"==e.activeTab?"请简要描述您的问题（选填）":"请输入您的手机号（必填）"))]),e._v(" "),a("el-input",{attrs:{type:"image"==e.activeTab?"input":"number",placeholder:"image"==e.activeTab?"请简要描述您的问题（选填）":"请输入您的手机号（必填）"},model:{value:e.questionContent,callback:function(t){e.questionContent=t},expression:"questionContent"}})],1):e._e(),e._v(" "),a("div",{staticClass:"flex-end"},[a("el-button",{staticClass:"submit-btn",attrs:{type:"primary",loading:e.loading},on:{click:e.handleSubmit}},[e._v("立即咨询")])],1)])])]),e._v(" "),e.iframeUrl&&e.dialogVisible?a("div",{attrs:{id:"iframe-wrap"}},[a("iframe",{attrs:{src:e.iframeUrl,frameborder:"0",id:"iframe-content"}})]):e._e()],1)},staticRenderFns:[]};var ne=a("VU/8")(se,le,!1,function(e){a("cgX9")},"data-v-80f8cc02",null).exports,re=a("Xxa5"),oe=a.n(re),ce=a("exGp"),ue=a.n(ce),de={name:"DetailDialog",components:{BaseDialog:B.a,DocumentActionButtons:N},mixins:[j],data:function(){return{dialogVisible:!1,templateId:"",title:"",templateDetail:"",loading:!1,docUrl:""}},methods:{open:function(e){this.dialogVisible=!0,this.templateId=e.RecordId,this.title=e.Title,this.templateDetail="",this.currentData=e,this.clearDocumentData(),this.getTemplateDetail()},handleClose:function(){console.log("handleClose"),this.dialogVisible=!1,this.templateDetail="",this.title="",this.clearDocumentData()},getTemplateDetail:function(){var e=this;this.loading=!0,Object(C.l)({RecordId:this.templateId}).then(function(t){console.log(t,"res");var a=t.data;if(200===t.code&&a&&a.length&&a[0]){var i=a[0];e.templateDetail=i.Content||"",e.docUrl=i.DocUrl||"",console.log("DocUrl:",e.docUrl)}}).catch(function(t){console.error("获取详情失败:",t),e.$message.error("获取详情失败")}).finally(function(){e.loading=!1})},getDocumentTitle:function(){return this.title}}},pe={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("base-dialog",{attrs:{visible:e.dialogVisible,title:e.title,height:"80vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose},scopedSlots:e._u([{key:"title-extra",fn:function(){return[a("document-action-buttons",{attrs:{"doc-url":e.docUrl,"template-detail":e.templateDetail,loading:e.loading},on:{preview:e.handlePreview,download:e.handleDownload,print:e.handlePrint}})]},proxy:!0}])},[e._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"detail-container h100"},[a("div",{staticClass:"detail-content h100",domProps:{innerHTML:e._s(e.templateDetail)}})])])},staticRenderFns:[]};var me={name:"LegalLawQuery",components:{TabBar:h,DetailDialog:a("VU/8")(de,pe,!1,function(e){a("IWRK")},"data-v-046aedb9",null).exports},props:{tempType:{type:Object,default:function(){return{}}}},data:function(){return{activeTab:"",tabs:[],lawList:[],loaded:!1,loading:!1}},methods:{handleTabChange:function(e){this.$emit("tab-change",e),this.loaded=!1,this.getLawList(e.value)},reset:function(){this.activeTab="",this.tabs=[],this.lawList=[],this.loaded=!1,this.loading=!1,this.getLawType()},getLawType:function(){var e=this;return ue()(oe.a.mark(function t(){var a,i,s,l,n,r;return oe.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return a=e,t.prev=1,t.next=4,Object(C.n)();case 4:i=t.sent,s=i.code,l=i.data,console.log(i,"法律法规类型"),200==s&&(n=(n=e.tempType.name||"法律").replace(/库/g,""),(r=l.find(function(e){return e.type==n}))&&r.list&&(a.tabs=r.list.map(function(e){return{label:e.name,value:e.type}})),a.tabs.length>0&&(a.activeTab=a.tabs[0].value,a.getLawList(a.activeTab))),t.next=13;break;case 10:t.prev=10,t.t0=t.catch(1),console.error("获取法律类型失败",t.t0);case 13:case"end":return t.stop()}},t,e,[[1,10]])}))()},getLawList:function(e){var t=this;return ue()(oe.a.mark(function a(){var i,s,l,n,r;return oe.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return i=t,a.prev=1,i.loading=!0,s=(s=t.tempType.name||"法律").replace(/库/g,""),a.next=7,Object(C.m)({Column:s,Type:e});case 7:l=a.sent,n=l.code,r=l.data,console.log(l,"法律法规列表"),200==n&&(i.lawList=r.list),a.next=16;break;case 13:a.prev=13,a.t0=a.catch(1),console.error("获取法律法规列表失败",a.t0);case 16:return a.prev=16,i.loading=!1,i.loaded=!0,a.finish(16);case 20:case"end":return a.stop()}},a,t,[[1,13,16,20]])}))()},handleLawItemClick:function(e){console.log(e,"法律法规详情"),this.$refs.detailDialog.open(e)}},mounted:function(){this.getLawType()}},fe={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"legal-law-query"},[a("div",{staticClass:"tab-wrapper"},[a("TabBar",{attrs:{tabs:e.tabs},on:{change:e.handleTabChange},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}})],1),e._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:!e.loaded,expression:"!loaded"}],staticClass:"flex-1",attrs:{"element-loading-background":"rgba(255, 255, 255, 0.4)"}},[e.lawList.length>0?a("div",{staticClass:"law-list"},e._l(e.lawList,function(t,i){return a("div",{key:i,staticClass:"law-item",on:{click:function(a){return e.handleLawItemClick(t)}}},[a("i",{staticClass:"el-icon-caret-right"}),e._v(" "),a("span",[e._v(e._s(t.Title))])])}),0):a("div",{staticClass:"empty-tip"},[e._v("\n      暂无相关法律法规\n    ")])]),e._v(" "),a("DetailDialog",{ref:"detailDialog"})],1)},staticRenderFns:[]};var ve=a("VU/8")(me,fe,!1,function(e){a("JmKN")},"data-v-6cb769b0",null).exports,he={name:"AllDialog",components:{BaseDialog:B.a,LegalLawQuery:ve},data:function(){return{dialogVisible:!1,activeTab:1,tempType:{id:1,name:"法律库"},isScrollLoad:!0}},watch:{activeTab:{handler:function(e){this.tempType={id:e,name:1===e?"法律":"法规"},this.$refs.legalLawQueryRef.reset()}}},methods:{open:function(){this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1}}},ge={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("base-dialog",{attrs:{visible:e.dialogVisible,title:"法律法规查询",height:"80vh"},on:{"update:visible":function(t){e.dialogVisible=t},close:e.handleClose}},[a("div",{staticClass:"list-container flex-column"},[a("div",{staticClass:"custom-tabs"},[a("div",{staticClass:"tab-item",class:{active:1===e.activeTab},on:{click:function(t){e.activeTab=1}}},[e._v("\n        法律库\n      ")]),e._v(" "),a("div",{staticClass:"tab-item",class:{active:2===e.activeTab},on:{click:function(t){e.activeTab=2}}},[e._v("\n        法规库\n      ")])]),e._v(" "),a("legal-law-query",{ref:"legalLawQueryRef",staticClass:"temp-list-wrapper flex-1",attrs:{tempType:e.tempType,column:6,row:3,isScrollLoad:e.isScrollLoad,tabbarBorderColor:"#F1F4F8"}})],1)])},staticRenderFns:[]};var be=a("VU/8")(he,ge,!1,function(e){a("64wY")},"data-v-52ac01ba",null).exports,ye={name:"Legal",components:{AiToolsCard:l.a,PageWrap:i.a,LegalCardWrap:o,AiContractWriting:d,ContractQuery:m,Calculator:V,TempLibrary:ee,TempLibraryAllDialog:ie,LegalConsultation:ne,LegalLawQuery:ve,LegalLawAllDialog:be},mixins:[s.a],data:function(){return{listPath:{pathType:"link",path:"https://ihr.china9.cn/human/main/index/v/ygt/m/fw/#/lcontract/contract_my",roles:["contract_my"]},legalSearch:"",showTitle:!0}},methods:{handleToAI:function(){window.open("https://china9.cn/#/console/ai-tools-page")},handleSizeChange:function(e){console.log(e)},handleCurrentChange:function(e){console.log(e)},handleToggleCate:function(e){console.log(e),this.activeTab=e.value},handleEdit:function(e,t){console.log(e,t)},toAllDialog:function(){this.$refs.tempLibraryAllDialogRef.open()},toLegalLawAllDialog:function(){this.$refs.legalLawAllDialogRef.open()}},created:function(){this.showTitle=this.$route.meta&&this.$route.meta.showNav}},_e={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("page-wrap",{staticClass:"legal-page",attrs:{"show-title":e.showTitle}},[i("div",{staticClass:"flex box part-one"},[i("legal-card-wrap",{staticClass:"ai-write",attrs:{title:"AI合同撰写"}},[i("ai-contract-writing")],1),e._v(" "),i("div",{staticClass:"x-gap"}),e._v(" "),i("legal-card-wrap",{staticClass:"flex-1",attrs:{title:"合同查询",showMore:!0},on:{toMore:function(t){return e.toPage(e.listPath)}}},[i("contract-query")],1),e._v(" "),i("div",{staticClass:"x-gap"}),e._v(" "),i("ai-tools-card",{staticClass:"ai-tools-card",attrs:{page:e.showTitle?"":"display",colSettings:{lg:12,md:12,sm:12,xl:12,xs:24}},on:{toAiTools:e.handleToAI}})],1),e._v(" "),i("div",{staticClass:"y-gap"}),e._v(" "),i("div",{staticClass:"flex box part-two"},[i("legal-card-wrap",{staticClass:"left-width",attrs:{icon:a("BwCQ"),title:"法律计算器",desc:"快速估算各项法律费用"}},[i("calculator")],1),e._v(" "),i("div",{staticClass:"x-gap"}),e._v(" "),i("legal-card-wrap",{staticClass:"flex-1 right-part",attrs:{title:"文书模版",icon:a("CVUK"),desc:"各类诉讼、文书模板一键使用",showMore:!0},on:{toMore:e.toAllDialog}},[i("temp-library")],1)],1),e._v(" "),i("div",{staticClass:"y-gap"}),e._v(" "),i("div",{staticClass:"flex box part-three"},[i("legal-card-wrap",{staticClass:"left-width",attrs:{title:"法律咨询",icon:a("FYQ0"),desc:"各类法律问题在线咨询"}},[i("legal-consultation")],1),e._v(" "),i("div",{staticClass:"x-gap"}),e._v(" "),i("legal-card-wrap",{staticClass:"flex-1 right-part",attrs:{title:"法律法规查询",icon:a("G3yn"),desc:"一键检索权威法律条文与规定",showMore:!0},on:{toMore:e.toLegalLawAllDialog},scopedSlots:e._u([{key:"right",fn:function(){},proxy:!0}])},[e._v(" "),i("legal-law-query",{ref:"legalLawQueryRef"})],1)],1),e._v(" "),i("temp-library-all-dialog",{ref:"tempLibraryAllDialogRef"}),e._v(" "),i("legal-law-all-dialog",{ref:"legalLawAllDialogRef"})],1)},staticRenderFns:[]};var Ce=a("VU/8")(ye,_e,!1,function(e){a("9HTw")},"data-v-034b32bf",null);t.default=Ce.exports},CwYY:function(e,t){},Fbvc:function(e,t){},IWRK:function(e,t){},JmKN:function(e,t){},UuEy:function(e,t){},VPDW:function(e,t){},cgX9:function(e,t){},gfPs:function(e,t){},goLn:function(e,t){},iFMK:function(e,t){},ivDj:function(e,t){},qnuF:function(e,t){},rVwj:function(e,t){},u2EN:function(e,t){},vC05:function(e,t){},y3Ff:function(e,t){},zd9R:function(e,t){}});