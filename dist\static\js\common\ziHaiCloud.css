#zhyConsoleHeader, #zhyConsoleHeader * {
  margin: 0;
  padding: 0;
}

#zhy<PERSON><PERSON>oleHeader,
#zhyConsoleHeader div,
#zhyConsoleHeader dl,
#zhyConsoleHeader dt,
#zhyConsoleHeader dd,
#zhyConsoleHeader ol,
#zhyConsoleHeader ul,
#zhyConsoleHeader li,
#zhyConsoleHeader h1,
#zhyConsoleHeader h2,
#zhyConsoleHeader h3,
#zhyConsoleHeader h4,
#zhyConsoleHeader h5,
#zhyConsoleHeader h6,
#zhyConsoleHeader pre,
#zhyConsoleHeader form,
#zhyConsoleHeader fieldset,
#zhyConsoleHeader input,
#zhyConsoleHeader textarea,
#zhyConsoleHeader p,
#zhyConsoleHeader blockquote,
#zhyConsoleHeader th,
#zhyConsoleHeader td,
#zhyConsoleHeader p {
  margin: 0;
  padding: 0;
  font-size: 14px;
}

#zhyConsoleHeader li {
  list-style: none;
}

#zhyConsoleHeader {
  position: relative;
  width: 100%;
  z-index: 999999;
  background: #333854;
  font-size: 14px;
  color: #333;
  min-width: 1200px;
}

#zhyConsoleHeader .weak-tips {
  position: relative;
}

#zhyConsoleHeader .weak-tips::after {
  position: absolute;
  right: -10px;
  top: 0;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: red;
  content: "";
}

#zhyConsoleHeader .header {
  display: flex;
  align-items: center;
  width: 100%;
  height: 51px;
  background-color: #333854;
}

#zhyConsoleHeader .zhyHomeHeader-title {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}

#zhyConsoleHeader #avatar:hover #seledbox {
  display: block;
}

#zhyConsoleHeader .zhyHomeHeader-title a {
  display: flex;
  flex-direction: row;
  align-items: center;
  text-decoration: none;
}

#zhyConsoleHeader .zhyHomeHeader-title a img:nth-child(1) {
  height: 33px;
  margin-left: 13px;
}

#zhyConsoleHeader .zhyHomeHeader-title a img:nth-child(2) {
  margin-left: 10px;
  height: 25px;
  margin-top: -5px;
}

#zhyConsoleHeader .zhyHomeHeader-title a > span {
  margin-left: 15px;
  color: #5c6076;
  font-size: 20px;
  line-height: 15px;
}

#zhyConsoleHeader .zhyHomeHeader-title .title-company {
  position: relative;
  display: flex;
  align-items: center;
}

#zhyConsoleHeader .zhyHomeHeader-title .title-company span {
  color: #fff;
  font-size: 14px;
}

#zhyConsoleHeader .zhyHomeHeaderPhone {
  width: 83px;
  margin-right: 20px;
  color: rgba(255, 255, 255, 0.6)
}

#zhyConsoleHeader .zhyHomeHeader-title .title-company > a {
  margin-left: 20px;
  padding: 10px 0;
  color: #ffffff;
  font-size: 16px;
  cursor: pointer;
  text-decoration: none;
}

#zhyConsoleHeader #text-div ul li {
  position: relative;
  float: left;
  height: 50px;
  line-height: 50px;
}

#zhyConsoleHeader #text-div ul li > a {
  color: #adb0b8;
  font-size: 14px;
  padding: 0 14px;
  display: block;
  position: relative;
  text-decoration: none;
}

#zhyConsoleHeader #text-div ul li:hover > a {
  color: #ffffff;
  text-decoration: none;
}

#zhyConsoleHeader #text-div ul li:hover a + div {
  display: block;
}

#zhyConsoleHeader .icon-div {
  display: flex;
  align-items: center;
}

#zhyConsoleHeader .icon-div a {
  height: 18px;
  display: inherit;
  text-decoration: none;
}

#zhyConsoleHeader .icon-div a img {
  width: 18px;
  margin-right: 20px;
  cursor: pointer;
}

#zhyConsoleHeader .icon-div a img:hover {
  filter: drop-shadow(#fff 0 0);
}

#zhyConsoleHeader #avatar {
  position: relative;
  cursor: pointer;
}

#zhyConsoleHeader #avatar img {
  margin-right: 12px;
  width: 35px;
  height: 35px;
  cursor: pointer;
  border-radius: 17px;
}

#zhyConsoleHeader .posiabox {
  position: absolute;
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  display: none;
  text-align: center;
  width: 100px;
  background: #ffffff;
  box-shadow: 0 0 8px #999999;
  border-radius: 5px;
  font-size: 14px;
  padding: 5px 0;
  z-index: 100;
}

#zhyConsoleHeader #avatar::after {
  content: "";
  position: absolute;
  display: block;
  top: 38px;
  left: 0;
  width: 100%;
  height: 30%;
}

#zhyConsoleHeader #seledbox {
  left: 82px;
}

#zhyConsoleHeader .posiabox::after {
  position: absolute;
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  left: 0;
  bottom: 10px;
  z-index: -1;
}

#zhyConsoleHeader .posiabox::before {
  position: absolute;
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  background: #fff;
  left: 0;
  right: 0;
  margin: auto;
  top: -5px;
  transform: rotate(45deg);
  z-index: -1;
}

#zhyConsoleHeader .posiabox a {
  display: block;
  color: #444;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  text-decoration: none;
  width: 100px;
  overflow: hidden;
}

#zhyConsoleHeader .posiabox a:last-child {
  border-bottom: 0 solid #DDD;
}

#zhyConsoleHeader .posiabox a:hover {
  color: #409EFF !important;
  background: rgba(64, 158, 255, 0.05);
}

#zhyConsoleHeader .btn-change {
  margin-left: 16px;
  padding: 6px 12px;
  height: 18px;
  background-color: #434f76;
  border-radius: 5px;
  cursor: pointer;
  line-height: 18px;
  box-sizing: content-box;
  display: flex;
  justify-content: center;
  align-items: center;
}

#zhyConsoleHeader .btn-change img {
  margin-right: 4px;
}

#zhyConsoleHeader .jzt-open-box {
  position: fixed;
  width: 100vw;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000000;
  display: none;
}

#zhyConsoleHeader .jzt-open-box .layui-layer-shade {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.2);
}

#zhyConsoleHeader .jzt-open-box .jzt-open-main {
  width: 600px;
  height: 270px;
  padding: 20px 30px;
  background: #FFFFFF;
  border-radius: 6px;
  position: absolute;
  left: 0;
  right: 0;
  top: 20%;
  margin: auto;
  z-index: 102;
  box-sizing: border-box;
}

#zhyConsoleHeader .jzt-open-box .close {
  position: absolute;
  top: 6px;
  right: 20px;
  cursor: pointer;
  font-size: 26px;
  color: #909399;
  user-select: none;
}

#zhyConsoleHeader .jzt-open-box .el-dialog__header {
  padding: 10px;
  border-bottom: 1px solid #dcdfe6;
}

#zhyConsoleHeader .select-company-form {
  display: flex;
  margin-top: 32px;
  justify-content: center;
  align-items: center;
}

#zhyConsoleHeader .select-company-form .input-box {
  display: inline-block;
  padding: 0px 40px 0px 20px;
  height: 40px;
  line-height: 40px;
  /* width: 300px; */
  flex: 1;
  border-radius: 8px;
  border: 1px solid rgb(234, 234, 234);
  position: relative;
  outline: none;
  font-size: 14px;
  color: #333;
}

#zhyConsoleHeader .select-company-form .input-box span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 20px;
}

#zhyConsoleHeader label {
  margin-right: 20px;
}

#zhyConsoleHeader .select-company-form i {
  position: absolute;
  right: 20px;
  top: 0;
  bottom: 0;
  margin: auto;
  line-height: 42px;
}

#zhyConsoleHeader .el-button--text {
  margin-right: 20px;
  border: 1px solid #20a0ff;
  width: 98px;
  height: 40px;
  font-size: 14px;
  color: #20a0ff;
}

.el-button--text .el-button--primary {
  font-size: 14px;
  color: #fff;
  border: 1px solid #20a0ff;
  background: #20a0ff;
}

#zhyConsoleHeader .company-list {
  width: 100%;
  position: absolute;
  top: 57px;
  left: 0;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  z-index: 9;
  display: none;
  background: #fff;
}

#zhyConsoleHeader .company-list ul {
  overflow: auto;
  max-height: 280px;
  background: #fff;
  padding: 10px 0;
  min-height: 10px;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 10;
}

#zhyConsoleHeader .company-list::after {
  position: absolute;
  top: -19px;
  z-index: -1;
  content: "";
  display: block;
  width: 100%;
  height: 100%;
}

#zhyConsoleHeader .company-list::before {
  position: absolute;
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  background: #fff;
  left: 0;
  right: 0;
  margin: auto;
  top: -5px;
  transform: rotate(45deg);
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

#zhyConsoleHeader .company-list li {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  list-style: none;
  line-height: 36px;
  padding: 0 20px;
  margin: 0;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  outline: 0;
  box-sizing: border-box;
}

.zhyContainer {
  height: calc(100% - 51px);
}

#zhyConsoleLeftSide {
  width: 50px;
  overflow: hidden;
  background-color: #ffffff;
  border-right: 1px #e1e1e1 solid;
  transition: width .3s;
  z-index: 999998;
  position: relative;
}

#zhyMain {
  width: 100%;
}

#zhyConsoleLeftSide ul li {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 50px;
  text-align: center;
}

#zhyConsoleLeftSide ul li a {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px;
  text-decoration: none;
}

#zhyConsoleLeftSide ul li a:hover {
  background-color: #f4f4f4;
}

#zhyConsoleLeftSide ul li a img {
  height: 22px;
  margin-left: 5px;
}

#zhyConsoleLeftSide ul li a p {
  margin-left: 40px;
  color: #333333;
  font-size: 12px;
}

#zhyFooter {
  width: 100%;
  background-color: #202020;
}

#zhyFooter .content {
  position: relative;
  margin: 0 auto;
  padding: 60px 10% 56px;
  width: 80%;
  box-sizing: content-box;
}

#zhyFooter .content .container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  margin: auto;
}

#zhyFooter .content .container p {
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 25px;
}

#zhyFooter .content .container li {
  margin-bottom: 15px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
}

#zhyFooter .content .container li a {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  text-decoration: none;
}

#zhyFooter td {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

#zhyFooter .content .container li:hover {
  color: white;
}

#zhyFooter .content .container .product ul {
  width: 170px;
}

#zhyFooter .content .container .product:first-of-type {
  padding-right: 60px;
  box-sizing: content-box;
}

#zhyFooter .content .container .product:first-of-type ul {
  display: grid;
  grid-auto-flow: column;
  grid-column-gap: 30px;
}

#zhyFooter .content .container .solve {
  width: 240px;
}

#zhyFooter .content .container .serve {
  width: 145px;
}

#zhyFooter .footer-info-wrap.zhyHomeHeaderPhone {
  display: none;
}

#zhyFooter a {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
}

#zhyFooter a:hover {
  color: rgba(255, 255, 255, 1);
  text-decoration: none;
}

#zhyFooter .content .container .address p {
  margin-top: -15px;
  margin-bottom: 14px;
  font-size: 30px;
  color: rgba(255, 255, 255, 0.6);
  font-family: bs;
}

#zhyFooter .content .container .qr {
  width: 120px;
  float: right;
  text-align: center;
  padding-left: 100px;
  box-sizing: content-box;
}

#zhyFooter .content .qr.zhyHomeHeaderPhone {
  display: none;
}

#zhyFooter .content .container .qr .el-image {
  width: 120px;
  height: 120px;
  margin-bottom: 30px;
}

#zhyFooter .content .container .qr p:nth-child(1) {
  font-size: 14px;
  margin-bottom: 9px;
}

#zhyFooter .content .version {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: center;
  position: absolute;
  bottom: 25px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

#zhyFooter .content .version span, #zhyFooter .content .version a, #zhyFooter .content .version span a {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
}

#zhyHomeHeader {
  height: 70px;
  margin: 0;
  background-color: white;
  width: 80%;
  padding: 0 10%;
  z-index: 999999;
  font-size: 14px;
  color: #333;
  position: relative;
  box-sizing: content-box;
}

#zhyHomeHeader > div {
  display: flex;
  align-items: center;
  height: 70px;
  margin: 0 auto;
  background-color: white;
  width: 100%;
  z-index: 999999;
  font-size: 14px;
  color: #333;
}

#zhyHomeHeader .el-menu .el-menu-item.is-active a {
  font-size: 14px;
  color: #333333;
  text-decoration: none;
}

#zhyHomeHeader .zhyHomeHeaderPhone {
  font-size: 14px;
  color: #333333;
  position: static;
}

#zhyHomeHeader .zhyHomeHeader-title {
  display: flex;
  cursor: pointer;
}

#zhyHomeHeader .zhyHomeHeader-title img {
  height: 30px;
  margin-left: 0;
}

#zhyHomeHeader .el-menu.el-menu--horizontal {
  padding: 0 55px;
}

#zhyHomeHeader .el-menu {
  flex: 1;
}

#zhyHomeHeader .el-menu .el-menu-item {
  padding: 0;
  margin-right: 34px;
  line-height: 70px;
}

#zhyHomeHeader .el-menu .el-menu-item a {
  font-size: 14px;
  color: #909399;
  text-decoration: none;
  display: flex;
  align-items: center;
}

#zhyHomeHeader .el-menu .el-menu-item a i.iconNew {
  color: red;
  text-transform: uppercase;
  font-size: 12px;
  transform: scale(0.8);
  transform-origin: center;
  font-weight: bold;
  margin-left: 2px;
  font-style: normal;
}

#zhyHomeHeader .el-menu .el-menu-item::after {
  content: "";
  display: block;
}

#zhyHomeHeader .el-menu .el-menu-item .iconfont {
  display: inline-block;
  float: right;
  transform: scaleX(1.8) rotate(90deg);
  margin-left: 16px;
  color: #222;
  font-size: 12px;
  font-style: normal;
}

#zhyHomeHeader .el-menu .btn_clear {
  position: absolute;
  left: 0;
  width: 42px;
  height: 70px;
}

#zhyHomeHeader .el-menu.el-menu--horizontal {
  height: 100%;
  border-bottom: 0 solid #bc0210;
}

#zhyHomeHeader .el-menu--horizontal > .el-menu-item {
  border-bottom: 0 solid #bc0210;
}

#zhyHomeHeader .el-menu--horizontal > .el-menu-item.is-active {
  height: 100%;
  border-bottom: 4px solid #bc0210;
}

#zhyHomeHeader .el-menu--horizontal > .el-menu-item:hover a {
  color: #333;
}

#zhyHomeHeader .el-menu--horizontal > .el-menu-item.is-active::after, #zhyHomeHeader .el-menu--horizontal > .el-menu-item:hover {
  display: block;
}

#zhyHomeHeader .input {
  display: none;
  position: relative;
  margin-right: 38px;
}

#zhyHomeHeader .input .el-input--suffix .el-input__inner {
  padding-right: 90px !important;
}

#zhyHomeHeader .input .el-input__suffix {
  right: 60px !important;
}

#zhyHomeHeader .input a {
  position: absolute;
  top: 0;
  right: 20px;
  font-size: 15px;
  height: 36px;
  line-height: 36px;
  color: rgba(0, 0, 0, 0.65);
  text-decoration: none;
}

#zhyHomeHeader .icon {
  display: flex;
  cursor: pointer;
  color: #333333;
  height: 100%;
}

#zhyHomeHeader .icon span {
  display: flex;
  align-items: center;
  margin-right: 20px;
  height: 100%;
}

#zhyHomeHeader .icon span i {
  font-size: 18px;
  margin-right: 5px;
}

#zhyHomeHeader .icon span p {
  font-size: 15px;
  height: 100%;
}

#zhyHomeHeader .icon span a {
  display: block;
  height: 100%;
  line-height: 70px;
  font-size: 14px;
  color: #333333;
  text-decoration: none;
}

#zhyHomeHeader .icon span:hover a {
  color: #bc0210;
}

#zhyHomeHeader .icon span:hover i {
  color: #bc0210;
}

#zhyHomeHeader .icon span:hover p {
  color: #bc0210;
}

#zhyHomeHeader .login {
  margin-left: 45px;
  margin-right: 30px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-weight: bold;
}

#zhyHomeHeader .login span {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-weight: bold;
}

#zhyHomeHeader .login:hover {
  color: #bc0210;
}

#zhyHomeHeader .regist {
  margin-right: 60px;
  background-color: #bc0210;
  font-size: 14px;
  font-weight: bold;
  border-radius: 0;
  color: #fff;
}

#zhyHomeHeader .regist span {
  font-size: 14px;
  color: #fff;
}

#zhyHomeHeader .avatar-container .avatar-wrapper .el-image {
  margin-right: 12px;
  width: 35px;
  height: 35px;
  cursor: pointer;
  border-radius: 17px;
}

#zhyHomeHeader .avatar-container .avatar-wrapper .image-slot {
  width: 35px;
  height: 35px;
  cursor: pointer;
}

#zhyHomeHeader .dropdown_menu {
  display: flex;
}

#zhyHomeHeader .dropdown_menu > li > ul {
  width: 120px;
}

#zhyHomeHeader .dropdown_menu > li > ul > li {
  margin-bottom: 15px;
  float: none;
}

#zhyHomeHeader .dropdown_menu > li > ul > li a {
  margin-left: 15px;
  font-weight: normal;
  font-size: 13px;
  color: #666666;
  text-decoration: none;
}

#zhyHomeHeader .dropdown_menu > li > ul > li a:hover {
  color: #bc0210;
}

#zhyHomeHeader .dropdown_menu > li > ul > li:nth-child(1) {
  color: #333333;
  font-weight: bold;
  font-size: 16px;
  display: flex;
  align-items: center;
}

#zhyHomeHeader .dropdown_menu > li > ul > li:nth-child(1) p {
  width: 9px;
  height: 3px;
  margin-right: 6px;
  background-color: #bc0210;
}

#zhyHomeHeader a {
  text-decoration: none;
  display: block;
}

#zhyHomeHeader .el-popover {
  top: 80px;
  display: none;
}

#zhyHomeHeader .el-popover::before {
  position: absolute;
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  background: #fff;
  left: 5px;
  top: -5px;
  transform: rotate(45deg);
  z-index: -1;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

#zhyHomeHeader .el-menu-item::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  background: transparent;
  top: 68px;
  left: 0;
  z-index: 20;
  display: none;
}

#zhyHomeHeader .el-menu-item:hover .el-popover {
  display: block;
}

#zhyHomeHeader .el-dropdown-menu {
  position: absolute;
  margin: auto;
  top: 50px;
  width: 100px;
  display: none;
}

.el-dropdown-menu__item:focus a, .el-dropdown-menu__item:not(.is-disabled):hover a {
  color: #66b1ff;
}

#zhyHomeHeader .el-dropdown-menu a {
  text-decoration: none;
  font-size: 14px;
}

#zhyHomeHeader .avatar-container:hover .el-dropdown-menu {
  display: block;
}

#zhyHomeHeader .avatar-container::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  left: 0;
  top: 40px;
  z-index: 20;
  cursor: none;
}

#zhyHomeHeader .el-dropdown-menu::before {
  position: absolute;
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  background: #fff;
  right: 0;
  left: 0;
  margin: auto;
  top: -5px;
  transform: rotate(45deg);
  z-index: -1;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

#zhyHomeHeader .el-dropdown-menu::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 1;
  background: #fff;
}

#zhyHomeHeader .el-dropdown-menu li {
  position: relative;
  z-index: 2;
  border: none;
}

#zhyHomeHeader .el-dropdown-menu li span {
  font-size: 14px;
}

#zhyFooter a {
  text-decoration: none;
}

#zhyHomeHeader .el-menu .el-menu-item:last-child {
  margin-right: 0;
}

#zhyHomeHeader > .zhyHomeHeaderPhone {
  display: none;
  position: relative;
}

#zhyHomeHeader .zhyHomeHeaderPhone .zhyHomeHeader-menu {
  width: 40px;
  height: 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 20px;
}

#zhyHomeHeader .zhyHomeHeaderPhone .regist {
  margin-left: 0px;
}

#zhyHomeHeader .zhyHomeHeaderPhone .zhyHomeHeader-menu span {
  display: block;
  width: 100%;
  height: 4px;
  border-radius: 4px;
  background: #333;
  pointer-events: none;
  transition: transform .5s;
}

#zhyHomeHeader .zhyHomeHeaderPhone.menu-wrap {
  position: absolute;
  top: 69px;
  left: 0;
  right: 0;
  padding: 40px 10%;
  box-sizing: border-box;
  display: block;
  height: calc(100vh - 69px);
  overflow: auto;
}

#zhyHomeHeader .zhyHomeHeaderPhone .el-menu {
  width: 100%;
  min-height: 100%;
  border-right: 0;
}

#zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item {
  margin-right: 0;
  margin-bottom: 0;
  position: relative;
  height: auto;
  overflow: hidden;
}

#zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item > div {
  display: flex;
  align-items: center;
}

#zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item a {
  flex-grow: 1;
}

#zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item .iconfont {
  display: flex;
  width: 30px;
  transform: scaleY(1.8) rotate(0);
  margin: 0;
  padding: 0 20px;
  justify-content: center;
  align-items: center;
  font-style: normal;
  transition: transform 0.5s;
}

#zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item .iconfont.show {
  transform: scaleX(1.8) rotate(90deg);
}

#zhyHomeHeader .zhyHomeHeaderPhone .dropdown_menu {
  display: block;
  width: 100%;
  box-sizing: border-box;
  padding-left: 20px;
}

#zhyHomeHeader .zhyHomeHeaderPhone .dropdown_menu > li > ul > li a {
  margin-left: 0;
}

#zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item::after {
  display: none;
}

#zhyHomeHeader .zhyHomeHeaderPhone .avatar-container .avatar-wrapper .el-image {
  margin-right: 0;
}

#zhyHomeHeader .zhyHomeHeaderPhone .zhyHomeHeader-menu.open span:nth-child(2) {
  display: none;
}

#zhyHomeHeader .zhyHomeHeaderPhone .zhyHomeHeader-menu.open span:nth-child(1) {
  transform-origin: top left;
  width: 95%;
  transform: rotate(45deg);
}

#zhyHomeHeader .zhyHomeHeaderPhone .zhyHomeHeader-menu.open span:nth-child(3) {
  transform-origin: bottom left;
  width: 95%;
  transform: rotate(-45deg);
}

#zhyFooter .version.zhyHomeHeaderPhone{
  display: none;
}

.goBack{
  position: fixed;
  right: 20px;
  bottom: 30px;
  z-index: 99;
  cursor: pointer;
}

#zhyConsoleHeader #monica-writing-entry-btn-root{
  display: none;
  width: 0;
}

@media (max-width: 1680px) {
  #zhyHomeHeader {
    width: 90%;
    padding: 0 5%;
  }

  #zhyHomeHeader .regist {
    margin-right: 0;
  }

  #zhyFooter .content .version {
    width: 100%;
    position: static;
    margin-top: 30px;
  }

  #zhyFooter .content {
    padding-bottom: 20px;
  }
}

@media (max-width: 1600px) {
  #zhyHomeHeader {
    padding: 0 4%;
    width: 92%;
  }

  #zhyFooter .content {
    padding-left: 4%;
    padding-right: 4%;
    width: 92%;
  }

  #zhyHomeHeader .el-menu.el-menu--horizontal {
    padding: 0 30px;
  }

  #zhyHomeHeader .el-menu .el-menu-item {
    margin-right: 40px;
  }

  #zhyHomeHeader .login {
    margin-left: 35px;
    margin-right: 20px;
  }

  #zhyHomeHeader .el-button {
    padding: 10px 15px;
  }
}

@media (max-width: 1470px) {
  #zhyHomeHeader .el-menu.el-menu--horizontal {
    padding: 0 40px;
  }

  #zhyHomeHeader .el-menu .el-menu-item {
    margin-right: 40px;
  }

  #zhyFooter .content .container .product:first-of-type {
    padding: 0;
    width: 34%;
  }

  #zhyFooter .content .container .product ul {
    margin-right: 10px;
  }

  #zhyHomeHeader .icon svg {
    display: none;
  }

  #zhyHomeHeader .el-menu.el-menu--horizontal {
    padding: 0 30px;
  }

  #zhyHomeHeader .login {
    margin-left: 15px;
  }

  #zhyFooter .content .container .product {
    width: 22%;
  }

  #zhyFooter .content .container .qr {
    flex-grow: 1;
    width: auto;
  }
}

@media (max-width: 1380px) {
  #zhyHomeHeader .login {
    margin: 0 10px 0 0;
  }

  #zhyHomeHeader {
    padding: 0 3%;
    width: 94%;
  }

  #zhyHomeHeader .zhyHomeHeader-title img {
    height: 26px;
  }

  #zhyHomeHeader .el-menu .el-menu-item {
    margin-right: 30px;
  }

  #zhyFooter .content .container {
    flex-wrap: wrap;
  }

  #zhyFooter .content .container .address {
    padding-top: 30px;
    width: 50%;
  }

  #zhyFooter .content .container .address p {
    margin-top: 0;
  }

  #zhyFooter .content {
    padding-left: 7%;
    padding-right: 7%;
    width: 86%;
  }

  #zhyFooter .content .container .qr {
    flex-grow: 0;
    width: 50%;
    box-sizing: border-box;
  }
}

@media (max-width: 1280px) {
  #zhyHomeHeader .icon span {
    margin-right: 10px;
  }

  #zhyHomeHeader .login {
    margin: 0;
  }

  #zhyHomeHeader .el-menu .el-menu-item .iconfont {
    margin-left: 8px;
  }

  #zhyHomeHeader .el-menu .el-menu-item {
    margin-right: 20px;
  }
}

@media (max-width: 1100px) {
  #zhyHomeHeader .el-menu .el-menu-item {
    margin-right: 20px;
  }

  #zhyHomeHeader .zhyHomeHeaderPc {
    display: none
  }

  #zhyHomeHeader {
    position: fixed;
    top: 0;
    box-shadow: 0 0 10px rgba(30, 144, 255, 0.3);
  }

  #zhyHomeHeader + div:not(.clear) {
    margin-top: 70px;
  }

  #zhyHomeHeader .zhyHomeHeaderPhone:not(.menu-wrap) {
    display: flex !important;
    justify-content: space-between;
  }

  #zhyHomeHeader .zhyHomeHeaderPhone:not(.menu-wrap) .zhyHomeHeader-logo-wrap {
    display: flex;
    align-items: center;
  }

  #zhyHomeHeader .zhyHomeHeaderPhone:not(.menu-wrap) .zhyHomeHeader-logo {
    height: 26px !important;
    width: auto;
  }

  #zhyHomeHeader .zhyHomeHeaderPhone:not(.menu-wrap) .zhyHomeHeader-logo + span {
    margin-left: 20px;
    color: #6a6a6a;
  }

  #zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item a, #zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item .iconfont {
    font-size: 2vw;
  }

  #zhyFooter .footer-info-wrap.zhyHomeHeaderPhone {
    display: flex;
    justify-content: flex-start;
  }

  #zhyFooter .content .qr.zhyHomeHeaderPhone {
    display: flex;
    justify-content: center;
    align-items: center;
    float: none;
  }

  #zhyFooter .content .qr.zhyHomeHeaderPhone .el-image {
    width: 120px;
    margin-right: 20px;
  }

  #zhyFooter .content .version {
    margin-top: 30px;
    position: static;
  }

  #zhyFooter .content .container .address p {
    margin-top: 0;
  }

  #zhyHomeHeader .zhyHomeHeaderPhone-button-wrap {
    display: flex;
    align-items: center;
  }
}

@media (max-width: 1000px) {
  #zhyFooter .content .container .address.zhyHomeHeaderPhone {
    display: block;
    flex-grow: 1;
    padding-right: 20px;
    width: calc(50% - 10px);
  }
  #zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item, #zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item > div{
    min-height: 70px;
  }

  #zhyFooter .content .qr.zhyHomeHeaderPhone {
    width: calc(50% - 10px);
  }

  #zhyFooter .content .container {
    min-width: auto;
  }

  #zhyFooter .content .version:not(.zhyHomeHeaderPhone) {
    display: block;
    text-align: center;
  }

  #zhyFooter .content .version span:last-child {
    display: block;
    margin-top: 10px;
  }
}

@media (max-width: 900px) {
  #zhyFooter .content .qr.zhyHomeHeaderPhone {
    margin-top: 30px;
  }
}

@media (max-width: 800px) {
  #zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item a, #zhyHomeHeader .zhyHomeHeaderPhone .el-menu .el-menu-item .iconfont {
    font-size: 2.2vw;
  }
}

@media (max-width: 700px) {
  #zhyFooter .content .version:not(.zhyHomeHeaderPhone){
    display: none;
  }
  #zhyFooter .content .version.zhyHomeHeaderPhone{
    display: block;
  }
  #zhyFooter .content .version.zhyHomeHeaderPhone span,
  #zhyFooter .content .version.zhyHomeHeaderPhone a{
    text-align: center;
  }
  #zhyHomeHeader .zhyHomeHeaderPhone-button-wrap {
    display: none;
  }

  #zhyHomeHeader {
    padding: 0 4%;
    height: 50px;
    width: 94%;
  }

  #zhyHomeHeader > div {
    height: 50px;
  }

  #zhyHomeHeader + div:not(.clear) {
    margin-top: 50px;
  }

  #zhyHomeHeader .zhyHomeHeaderPhone:not(.menu-wrap) {
    justify-content: flex-start;
  }

  #zhyFooter .content .container {
    display: none;
  }

  #zhyFooter .version.zhyHomeHeaderPc{
    display: none;
  }

  #zhyFooter .content .version {
    margin-top: 0;
  }

  #zhyFooter .content .version span, #zhyFooter .content .version a, #zhyFooter .content .version span a {
    font-size: 14px;
  }

  #zhyFooter .content {
    padding: 30px 0;
  }

  #zhyFooter .content .version.zhyHomeHeaderPhone > a,
  #zhyFooter .content .version.zhyHomeHeaderPhone > span{
    display: block;
    line-height: 1;
  }
  #zhyFooter .content .version a{
    margin-top: 10px;
  }

  .avatar-container, .zhyHomeHeader-menu {
    display: none !important;
  }
}

@media (max-width: 600px) {
  .goBack{
    width: 50px;
    height: 50px;
    bottom: 22%;
    right: 10px;
  }
}
