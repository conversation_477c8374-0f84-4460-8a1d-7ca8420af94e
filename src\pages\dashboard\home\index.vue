<template>
  <div class="bg-scend">
    <BossHome />
  </div>
</template>

<script>
import BossH<PERSON> from '@/pages/console/display/components/BossHome.vue'

export default {
  name: 'IndexView',
  components: {
    BossHome,
  },
  data() {
    return { };
  },
};
</script>

<style scoped lang="scss">
.bg-scend {
  background: #f2f7fa;
  overflow-y: auto;
}
/deep/.console-main {
  padding: 16px !important;
  // .card {
  //   box-shadow: 0 0 20px rgba(241, 245, 255, 1);
  // }
}
</style>