import { userHomeCard, weakPassword, logout, getConsoleUserInfo, getNav } from '@/api/console'
import { siteList<PERSON>pi, getUserAuthApi, logoutApi } from '@/api/JztApi/site'
import { getMenuList } from '../api/role'
import { getAuthApi } from '@/api/consoleDisplay'
import { clearStorage } from '@/utils/index'

// 处理动态路由
const handleDynamicRouter = (list) => {
  let navList = JSON.parse(JSON.stringify(list))
  // 替换字段
  navList.forEach((item, index) => {
    item.name = item.statistic
    if (item.icon) item.logo = require(`@/assets${item.icon}`)
    item.url = item.path
    if (!item.children) return
    let children = JSON.parse(JSON.stringify(item.children))
    item.children = children.map((v, i) => {
      v.name = v.statistic
      if (v.icon) v.logo = require(`@/assets${v.icon}`)
      v.url = v.path
      return v
    })
  })
  return navList
}

// 递归展平菜单
const flatMenuList = (list, data) => {
  list.forEach((item, index) => {
    data.push(item)
    if (item.children) {
      flatMenuList(item.children, data)
    }
  })
  return data
}

const actions = {
  loginStatus: ({ commit }, user) => {
    commit('getLoginStatus', user)
  },
  // 获取资海云用户角色
  getUserRole: ({ commit }, user) => {
    return new Promise(async (resolve, reject) => {
      const res = await userHomeCard({})
      if (res.code === 200) {
        commit('setZhUserRole', res.data)
        resolve(res.data)
      }
    })
  },
  // 建站通 获取站点列表
  getSiteList: ({ commit }, showAlert = true) => {
    return new Promise(async (resolve, reject) => {
      const res = await siteListApi({}, showAlert)
      // console.log(res, 'getSiteList')
      if (res.code == 200) {
        commit('setJztSiteList', res.data)
        if (res.data.length > 0) {
          if (res.data[0].id) commit('setJztSiteInfo', res.data[0])
          else if (res.data.length > 1 && res.data[1].id) commit('setJztSiteInfo', res.data[1])
        }
        resolve(res.data)
      }
      reject(res)
    })
  },
  // 获取建站通用户权限
  getJztUserAuth: ({ commit }) => {
    return new Promise(async (resolve, reject) => {
      const res = await getUserAuthApi({})
      if (res.code === 200) {
        commit('setJztUserRole', res.data)
        resolve(res.data)
      }
    })
  },
  // 获取企业购买产品列表及用户权限
  getCompanyProductList: ({ commit }, user) => {
    return new Promise(async (resolve, reject) => {
      const res = await getAuthApi({})
      if (res.code === 200) {
        const { myInfo, power } = res.data
        commit('setZhUserAuth', power) // 设置用户权限
        commit('setBuyGoodsList', myInfo) // 设置购买产品列表
        resolve(res.data)
      }
    })
  },
  //  获取display菜单
  // 获取动态路由
  async getDynamicRouter ({ commit }) {
    return new Promise(async (resolve, reject) => {
      let res = await getMenuList()
      if (res.code == 200) {
        const list = handleDynamicRouter(res.data)
        commit('setMenuList', list)
        // 展平菜单
        let flatList = flatMenuList(list, [])
        commit('setMenuFlatList', flatList)
        resolve(list)
      }
    })
  },
  // 获取弱密码提示
  getWeakPss ({ commit }) {
    return new Promise(async (resolve, reject) => {
      const res = await weakPassword({})
      if (res.code === 200) {
        commit('setWeakPass', res.data == 1 ? true : false)
        resolve(res.data)
      }
    })
  },
  // 退出登录
  userLogout ({ commit, state }) {
    return new Promise(async (resolve, reject) => {
      try {
        const res = await logout()
      } catch (error) {
        console.log(error, 'logout-error')
      } finally {
        clearStorage()
        logoutApi() // 执行建站通退出登录
        window.location.href = `/#/home?type=logout`
      }
    })
  },
  // 获取用户信息
  getUserInfo({ commit }) {
    return new Promise(async (resolve, reject) => {
      const res = await getConsoleUserInfo({type: 'pc'})
      if (res.code === 200) {
        commit('setUserInfo', res.data)
        resolve(res.data)
      }
    })
  },
  // 获取企业设置菜单
  getCompanyNav ({ commit }) {
    return new Promise(async (resolve, reject) => {
      const res = await getNav()
      if (res.code === 200) {
        let list = res.data || []
        list = list.filter(item => item.isshow == 1)
        commit('setCompanyNav', list || [])
        resolve(list)
      }
    })
  },
  // 获取新控制台菜单
  getDashboardMenus ({ commit }) {
    return new Promise(async (resolve, reject) => {
      const res = require('@/database/DashboardMenus.json')
      // console.log(res, 'res')
      if (res.code === 200) {
        let list = JSON.parse(JSON.stringify(res.data))
        list.forEach(item => {
          item.iconA = require(`@/assets/image${item.icon}-active.png`)
          item.icon = require(`@/assets/image${item.icon}.png`)
        })
        commit('setDashboardMenus', list)
        resolve(list)
      }
    })
  }
}
export default actions
