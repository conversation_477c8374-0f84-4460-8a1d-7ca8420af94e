webpackJsonp([35],{"3Mwh":function(e,t){},Lomv:function(e,t){},TrT2:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r("Gu7T"),i=r.n(n),s=r("q1rc"),a=r("ZUny"),o={name:"Document",data:function(){return{init:!1,bread:[{name:"开发文档",url:"/openPlatformHome/document"}]}},components:{Breadcrumb:s.a,tree:a.a},computed:{breadcrumb:function(){return this.$store.state.breadArr},keys:function(){return this.$route.params.id}},methods:{handleNodeClick:function(e,t){this.$router.push({path:"/openPlatformHome/document/"+e.id,query:{type:e.type}});for(var r=0;r<t.length;r++)t[r].url="/openPlatformHome/document/"+t[r].id+"?type="+e.type;if(this.init&&(this.bread=this.bread.splice(0,1),this.init=!this.init),e.child.length){var n={name:e.name,url:e.url,id:e.id};this.$store.commit("setBreadcrumb",[].concat(i()(this.bread),[n,e.child[0]])),this.$router.replace("/openPlatformHome/document/"+e.child[0].id+"?type=detail")}else this.$store.commit("setBreadcrumb",[].concat(i()(this.bread),i()(t)))},toggleHiddenTree:function(){var e=this;window.addEventListener("scroll",function(){document.documentElement.clientHeight||window.innerHeight;var t=document.querySelector("footer").getBoundingClientRect().top;e.$refs.leftTree&&(e.$refs.leftTree.style.maxHeight=t-document.querySelector("footer").offsetHeight+"px")}),this.$once("hook:beforeDestory",function(){window.removeEventListener("scroll")})},initBreadcrumb:function(e){this.init=!0,this.bread.push(e),this.$store.commit("setBreadcrumb",this.bread)}},mounted:function(){this.toggleHiddenTree(),this.$store.commit("setBreadcrumb",this.bread)}},c={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"document",attrs:{id:"document"}},[n("div",{staticClass:"breadcrumb-box"},[n("breadcrumb",{attrs:{breadArr:e.breadcrumb}})],1),e._v(" "),n("div",{staticClass:"tree-box"},[n("div",{staticClass:"tree-title content-container"},[n("img",{attrs:{src:r("LJeB"),alt:""}}),e._v(" "),n("span",[e._v("开发文档")])]),e._v(" "),n("div",{staticClass:"tree-content content-container"},[n("div",{ref:"leftTree",staticClass:"content-left"},[n("tree",{attrs:{keys:e.keys},on:{handleNodeClick:e.handleNodeClick,initBreadcrumb:e.initBreadcrumb}})],1),e._v(" "),n("div",{staticClass:"content-right"},[n("router-view")],1)])])])},staticRenderFns:[]};var d=r("VU/8")(o,c,!1,function(e){r("sfLx")},"data-v-7f6ae692",null);t.default=d.exports},ZUny:function(e,t,r){"use strict";var n=r("Gu7T"),i=r.n(n),s=r("G2Lp"),a={name:"Tree",props:["keys"],data:function(){return{data:[],defaultProps:{children:"child",label:"name"}}},computed:{id:function(){return this.$store.state.breadArr[this.$store.state.breadArr.length-1].id}},methods:{handleNodeClick:function(e){this.$refs.tree.setCheckedNodes([]),this.id!==e.id&&this.$refs.tree.setChecked(e.id,!0),e.id===parseInt(this.$route.params.id)&&this.$refs.tree.setCheckedNodes([e]);var t=[].concat(i()(this.$refs.tree.getHalfCheckedNodes()),i()(this.$refs.tree.getCheckedNodes()));this.$emit("handleNodeClick",e,t)},getCategoryList:function(){var e=this;Object(s.a)().then(function(t){200===t.code&&(e.data=t.data,e.$nextTick(function(){e.$emit("initBreadcrumb",e.$refs.tree.getCurrentNode())}))})}},mounted:function(){this.getCategoryList()}},o={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"tree"},[t("el-tree",{ref:"tree",attrs:{"default-checked-keys":[parseInt(this.keys)],"current-node-key":parseInt(this.keys),"expand-on-click-node":!1,"check-on-click-node":"",data:this.data,props:this.defaultProps,"node-key":"id"},on:{"node-click":this.handleNodeClick}})],1)},staticRenderFns:[]};var c=r("VU/8")(a,o,!1,function(e){r("Lomv")},"data-v-a9c59ab6",null);t.a=c.exports},q1rc:function(e,t,r){"use strict";var n={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"breadcrumb content-container"},[r("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},e._l(e.breadArr,function(t,n){return r("el-breadcrumb-item",{key:n,attrs:{to:{path:t.url}}},[e._v(e._s(t.name))])}),1)],1)},staticRenderFns:[]};var i=r("VU/8")({name:"Breadcrumb",props:["breadArr"]},n,!1,function(e){r("3Mwh")},"data-v-11a465e2",null);t.a=i.exports},sfLx:function(e,t){}});