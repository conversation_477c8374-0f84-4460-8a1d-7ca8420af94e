@charset "UTF-8";
@import url("../font/小米字体MiSans-Heavy-embed.css");
#zhyHomeHeader .el-menu--horizontal>.el-menu-item {
  height: 70px;
  line-height: 70px;
  display: flex !important;
  align-items: center;
}

#zhyFooter .container {
  width: 1320px;
  margin: auto;
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  text-decoration: none;
  list-style: none;
  line-height: 1; }

img {
  display: block; }

.container, .advantages .content-wrap .content, .concat-us .form {
  margin: 0 auto;
  width: 68.75vw; }

.flex {
  display: flex; }

.flex-column, .advantages .content-wrap .overview > li .wrap .box {
  display: flex;
  flex-direction: column; }

.flex-wrap, .concat-us .form {
  display: flex;
  flex-wrap: wrap; }

.flex-center, .advantages .content-wrap .overview > li .wrap .box {
  display: flex;
  justify-content: center;
  align-items: center; }

.flex-between, .concat-us .form {
  display: flex;
  justify-content: space-between; }

.flex-around {
  display: flex;
  justify-content: space-around; }

.flex-align {
  display: flex;
  align-items: center; }

.flex-align-stretch {
  display: flex;
  align-items: stretch; }

.flex-align-start {
  display: flex;
  align-items: start; }

.flex-align-end {
  display: flex;
  align-items: end; }

.flex-align-center {
  display: flex;
  align-items: center; }

.flex-justify {
  display: flex;
  justify-content: center; }

.flex-justify-end {
  display: flex;
  justify-content: flex-end; }

.flex-justify-between {
  display: flex;
  justify-content: space-between; }

.flex-justify-around {
  display: flex;
  justify-content: space-around; }

.flex-justify-start {
  display: flex;
  justify-content: flex-start; }

.flex-justify-center {
  display: flex;
  justify-content: center; }

.flex-1 {
  flex: 1;
  overflow: hidden; }

.flex-x-center {
  align-items: center; }

.relative, .advantages .content-wrap .overview > li .wrap .box, .advantages .content-wrap .content, .advantages .content-wrap .content .wrap, .concat-us .form .input-wrap {
  position: relative; }

.absolute, .advantages .content-wrap .content .wrap {
  position: absolute; }

.img-icon, .product-introduction .content-wrap .img img, .core-function .content-wrap > div .func-item .img img, .advantages .content-wrap .overview > li .wrap .box .img img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain; }

.banner {
  width: 100%;
  height: 34.95vw;
  background: url(../img/banner.png) no-repeat center;
  background-size: 100% 100%; }
  .banner .container, .banner .advantages .content-wrap .content, .advantages .content-wrap .banner .content, .banner .concat-us .form, .concat-us .banner .form {
    width: 73%; }
  .banner .tag {
    display: inline-flex;
    padding: 0.52vw 1.2vw;
    background: #1b499f;
    border-radius: 1.3vw;
    font-size: 1.56vw;
    color: #ffffff;
    font-weight: 400;
    margin-top: 11.09vw;
    letter-spacing: 2px; }
  .banner h2 {
    font-size: 3.44vw;
    color: #ffffff;
    font-weight: 800;
    margin-top: 1.56vw; }
  .banner h3 {
    font-size: 1.56vw;
    color: #ffffff;
    font-weight: 400;
    margin-top: 2.14vw; }

.title {
  padding-top: 4.95vw;
  text-align: center; }
  .title h4 {
    font-size: 2.08vw;
    color: #071b3f;
    font-weight: normal;
    font-weight: bold; }
  .title h5 {
    margin-top: 0.89vw;
    font-size: 0.83vw;
    color: #6e7582;
    font-weight: normal; }
  .title.white h4,
  .title.white h5 {
    color: #fff; }

.fake-ele, .has-before:before, .has-after:after {
  content: "";
  position: absolute; }

.has-before {
  position: relative; }

.has-after {
  position: relative; }

.product-introduction {
  background: #f5f8ff;
  padding-bottom: 5.68vw; }
  .product-introduction .content-wrap {
    margin-top: 3.02vw; }
    .product-introduction .content-wrap .intro {
      width: 53.54vw;
      height: auto;
      background: #fff;
      padding: 6.56vw 10.78vw 4.22vw 2.97vw; }
      .product-introduction .content-wrap .intro .has-before::before {
        top: -1.98vw;
        width: 1.35vw;
        height: 3px;
        background: #1c6bff; }
      .product-introduction .content-wrap .intro p {
        font-size: 0.83vw;
        color: #6e7582;
        font-weight: normal;
        line-height: 1.51; }
    .product-introduction .content-wrap .img {
      top: 0;
      right: 0;
      bottom: 0;
      left: auto;
      width: 21.98vw;
      height: auto;
      background: transparent;
      margin: auto; }

.core-function {
  padding-bottom: 1.67vw; }
  .core-function .content-wrap {
    margin-top: 4.06vw; }
    .core-function .content-wrap > div {
      width: 100%; }
      .core-function .content-wrap > div .func-item {
        margin-bottom: 4.38vw; }
        .core-function .content-wrap > div .func-item .img {
          width: 100%;
          height: 3.44vw;
          background: transparent;
          margin-bottom: 1.51vw; }
        .core-function .content-wrap > div .func-item .func-title {
          font-size: 1.04vw;
          color: #071b3f;
          font-weight: normal; }
        .core-function .content-wrap > div .func-item .func-desc {
          font-size: 0.83vw;
          color: #6e7582;
          font-weight: normal;
          margin-top: 0.94vw; }
    .core-function .content-wrap .phone {
      display: none; }

.advantages {
  padding-bottom: 15.16vw;
  background: url("../img/bg1.png") no-repeat center/cover; }
  .advantages .content-wrap {
    margin-top: 4.01vw; }
    .advantages .content-wrap .overview {
      margin: 0 calc(-2.34vw / 2); }
      .advantages .content-wrap .overview > li {
        width: 33.33%;
        height: auto;
        background: transparent;
        padding: 0 calc(2.34vw / 2);
        box-sizing: content-box; }
        .advantages .content-wrap .overview > li .wrap {
          width: 100%;
          height: 100%;
          background: #fff;
          pointer-events: none;
          padding: 1.67vw 0 3.13vw 0; }
          .advantages .content-wrap .overview > li .wrap .box {
            width: 100%;
            height: 100%;
            background: transparent;
            z-index: 1; }
            .advantages .content-wrap .overview > li .wrap .box .img {
              width: 7.97vw;
              height: 9.01vw;
              background: transparent;
              margin-bottom: 1.35vw; }
            .advantages .content-wrap .overview > li .wrap .box .adv-title {
              font-size: 1.04vw;
              color: #071b3f;
              font-weight: normal; }
              .advantages .content-wrap .overview > li .wrap .box .adv-title br {
                display: none; }
          .advantages .content-wrap .overview > li .wrap::before {
            content: attr(data-index);
            z-index: 0;
            right: 0.6vw;
            bottom: 0.63vw;
            font-size: 6.25vw;
            color: rgba(75, 113, 228, 0.1);
            font-weight: 800;
            font-family: "小米字体MiSans-Heavy"; }
        .advantages .content-wrap .overview > li.active .content {
          display: block; }
    .advantages .content-wrap .content {
      margin-top: 2.14vw; }
      .advantages .content-wrap .content .wrap {
        padding: 1.82vw 2.6vw;
        background: #6b8ffb;
        display: none;
        width: 100%; }
        .advantages .content-wrap .content .wrap::before {
          content: "";
          width: 0.57vw;
          height: 0.57vw;
          background: #6b8ffb;
          z-index: 0;
          top: 0;
          transform: rotate(45deg) translateY(-50%); }
        .advantages .content-wrap .content .wrap ul li {
          list-style-type: square;
          font-size: 0.83vw;
          color: #fff;
          font-weight: normal;
          line-height: 1.5; }
          .advantages .content-wrap .content .wrap ul li + li {
            margin-top: 0.52vw; }
        .advantages .content-wrap .content .wrap:first-child::before {
          left: calc(33.33% / 2 - 1.17vw); }
        .advantages .content-wrap .content .wrap:nth-child(2)::before {
          left: 50%;
          transform: rotate(45deg) translateX(-50%); }
        .advantages .content-wrap .content .wrap:last-child::before {
          right: calc(33.33% / 2 - 1.17vw); }
        .advantages .content-wrap .content .wrap.active {
          display: block; }
  .advantages .phone {
    display: none; }

.concat-us {
  background: url("../img/bg2.png") no-repeat;
  background-size: cover;
  padding: 2.97vw 0 3.28vw;
  text-align: center; }
  .concat-us .form-title {
    font-size: 1.35vw;
    color: #fff;
    font-weight: normal; }
    .concat-us .form-title br {
      display: none; }
  .concat-us .form-en-title {
    font-size: 0.73vw;
    color: rgba(255, 255, 255, 0.5);
    font-weight: normal;
    margin-top: 0.57vw; }
  .concat-us .form {
    margin-top: 2.19vw; }
    .concat-us .form .input-wrap {
      width: 25%;
      padding: 0 0.52vw; }
      .concat-us .form .input-wrap input {
        width: 100%;
        height: 2.08vw;
        background: rgba(255, 255, 255, 0.1);
        border: none;
        outline: transparent;
        border-radius: 2.08vw;
        padding: 0 0.78vw;
        box-sizing: border-box;
        font-size: 0.73vw;
        color: rgba(255, 255, 255, 0.8);
        font-weight: normal; }
        .concat-us .form .input-wrap input::placeholder {
          color: rgba(255, 255, 255, 0.8); }
  .concat-us .btn-wrap {
    margin-top: 1.98vw; }
    .concat-us .btn-wrap .consulting-button {
      width: 15.63vw;
      height: 2.08vw;
      background: #1c6bff;
      border-radius: 2.08vw;
      font-size: 0.73vw;
      color: #fff;
      font-weight: normal;
      cursor: pointer; }

@media only screen and (max-width: 1400px) {
  .container, .advantages .content-wrap .content, .concat-us .form {
    width: 70vw; }

  .title h4 {
    font-size: 2.912vw; }
  .title h5 {
    font-size: 1.162vw; }

  .product-introduction .content-wrap .intro {
    padding-right: 16.78vw;
    width: 60vw; }
    .product-introduction .content-wrap .intro p {
      font-size: 1.162vw; }

  .core-function {
    padding-bottom: 2.338vw; }
    .core-function .content-wrap > div .func-item .img {
      width: 100%;
      height: 4.816vw;
      background: transparent; }
    .core-function .content-wrap > div .func-item .func-title {
      font-size: 1.162vw; }
    .core-function .content-wrap > div .func-item .func-desc {
      font-size: 1.162vw; }

  .advantages .content-wrap .overview > li .wrap .box .adv-title {
    font-size: 1.162vw; }
  .advantages .content-wrap .content .wrap ul li {
    font-size: 1.162vw; }

  .concat-us {
    padding: 4.158vw 4.158vw 4.592vw; }
    .concat-us .form-title {
      font-size: 1.89vw; }
    .concat-us .form-en-title {
      font-size: 1.022vw; }
    .concat-us .form {
      margin-top: 3.066vw; }
      .concat-us .form .input-wrap input {
        width: 100%;
        height: 2.912vw;
        background: rgba(255, 255, 255, 0.1);
        font-size: 1.022vw; }
    .concat-us .btn-wrap {
      margin-top: 2.772vw; }
      .concat-us .btn-wrap .consulting-button {
        font-size: 1.162vw;
        width: 21.882vw;
        height: 2.912vw;
        background: #1c6bff; } }
@media only screen and (max-width: 1260px) {
  .banner {
    height: 48.93vw; }
    .banner .tag {
      font-size: 2.184vw;
      border-radius: 2vw;
      padding: 0.798vw 1.89vw; }

  .container, .advantages .content-wrap .content, .concat-us .form {
    width: 80vw; }

  .title h4 {
    font-size: 2.912vw; }
  .title h5 {
    font-size: 1.162vw; }

  .product-introduction .content-wrap {
    margin-top: 4.228vw; }
    .product-introduction .content-wrap .intro {
      padding-top: 9.184vw;
      width: 67vw;
      padding-right: 14vw; }
      .product-introduction .content-wrap .intro p {
        font-size: 1.162vw; }

  .core-function {
    padding-bottom: 2.338vw; }
    .core-function .content-wrap {
      margin-top: 5.684vw; }
      .core-function .content-wrap > div .func-item .img {
        font-size: 4.816vw;
        margin-bottom: 2.114vw; }
      .core-function .content-wrap > div .func-item .func-title {
        font-size: 1.2782vw; }
      .core-function .content-wrap > div .func-item .func-desc {
        font-size: 1.162vw; }

  .advantages .content-wrap {
    margin-top: 5.614vw; }
    .advantages .content-wrap .overview > li .wrap {
      padding: 2.338vw 0 4.382vw 0; }
      .advantages .content-wrap .overview > li .wrap .box .img {
        width: 11.158vw;
        height: 12.614vw;
        background: transparent; }
      .advantages .content-wrap .overview > li .wrap .box .adv-title {
        font-size: 1.456vw; }
      .advantages .content-wrap .overview > li .wrap::before {
        font-size: 8.75vw; }
    .advantages .content-wrap .content .wrap ul li {
      font-size: 1.162vw; }

  .concat-us {
    padding: 4.158vw 4.158vw 4.592vw; }
    .concat-us .form-title {
      font-size: 1.89vw; }
    .concat-us .form-en-title {
      font-size: 1.022vw; }
    .concat-us .form {
      margin-top: 3.066vw; }
      .concat-us .form .input-wrap input {
        width: 100%;
        height: 2.912vw;
        background: rgba(255, 255, 255, 0.1);
        font-size: 1.022vw; }
    .concat-us .btn-wrap {
      margin-top: 2.772vw; }
      .concat-us .btn-wrap .consulting-button {
        font-size: 1.162vw;
        width: 21.882vw;
        height: 2.912vw;
        background: #1c6bff; } }
@media only screen and (max-width: 1200px) {
  .banner {
    height: 44.736vw; }
    .banner .tag {
      font-size: 2.496vw;
      border-radius: 2vw;
      padding: 0.912vw 2.16vw; }
    .banner h2 {
      margin-top: 3.504vw;
      font-size: 3.504vw; }
    .banner h3 {
      font-size: 2.16vw;
      margin-top: 3.504vw; }

  .title h4 {
    font-size: 3.328vw; }
  .title h5 {
    font-size: 1.328vw; }

  .product-introduction .content-wrap {
    margin-top: 4.832vw;
    background-color: #fff; }
    .product-introduction .content-wrap .intro {
      padding-top: 10.496vw;
      width: 100%;
      padding-right: 14vw; }
      .product-introduction .content-wrap .intro p {
        font-size: 1.328vw;
        line-height: 2; }
      .product-introduction .content-wrap .intro .has-before::before {
        top: -4vw; }
    .product-introduction .content-wrap .img {
      position: static;
      justify-content: end;
      width: 100%;
      padding-right: 1.56vw;
      padding-bottom: 1.56vw; }
      .product-introduction .content-wrap .img img {
        width: 21.98vw; }

  .core-function {
    padding-bottom: 2.672vw; }
    .core-function .content-wrap {
      margin-top: 6.496vw; }
      .core-function .content-wrap > div .func-item .img {
        font-size: 5.504vw;
        margin-bottom: 2.416vw; }
      .core-function .content-wrap > div .func-item .func-title {
        font-size: 1.4608vw; }
      .core-function .content-wrap > div .func-item .func-desc {
        font-size: 1.328vw; }

  .advantages {
    padding-bottom: 20.63vw; }
    .advantages .content-wrap {
      margin-top: 6.416vw; }
      .advantages .content-wrap .overview > li .wrap {
        padding: 2.672vw 0 5.008vw 0; }
        .advantages .content-wrap .overview > li .wrap .box .img {
          width: 12.752vw;
          height: 14.416vw;
          background: transparent;
          margin-bottom: 2vw; }
        .advantages .content-wrap .overview > li .wrap .box .adv-title {
          font-size: 1.664vw; }
        .advantages .content-wrap .overview > li .wrap::before {
          font-size: 10vw; }
      .advantages .content-wrap .content .wrap {
        padding: 2.912vw 4.16vw; }
        .advantages .content-wrap .content .wrap ul li {
          font-size: 1.328vw;
          line-height: 1.8; }
          .advantages .content-wrap .content .wrap ul li + li {
            margin-top: 1.5vw; }
        .advantages .content-wrap .content .wrap::before {
          width: 0.912vw;
          height: 0.912vw;
          background: #6b8ffb; }

  .concat-us {
    padding: 5.7024vw 5.7024vw 6.2976vw; }
    .concat-us .form-title {
      font-size: 2.16vw; }
    .concat-us .form-en-title {
      font-size: 1.168vw; }
    .concat-us .form {
      margin-top: 4.2048vw; }
      .concat-us .form .input-wrap input {
        width: 100%;
        height: 3.9936vw;
        background: rgba(255, 255, 255, 0.1);
        font-size: 1.168vw;
        padding: 0 1.56vw; }
    .concat-us .btn-wrap {
      margin-top: 3.8016vw; }
      .concat-us .btn-wrap .consulting-button {
        font-size: 1.328vw;
        width: 25.008vw;
        height: 3.9936vw;
        background: #1c6bff; } }
@media only screen and (max-width: 1100px) {
  .banner {
    height: 41.94vw;
    padding-top: 70px; }

  .title {
    padding-top: 9.9vw; }
    .title h4 {
      font-size: 3.744vw; }
    .title h5 {
      font-size: 1.66vw; }

  .product-introduction {
    padding-bottom: 11.36vw; }
    .product-introduction .content-wrap {
      margin-top: 6.04vw;
      background-color: #fff; }
      .product-introduction .content-wrap .intro {
        padding-top: 13.12vw;
        width: 100%;
        padding-left: 6vw;
        padding-right: 6vw; }
        .product-introduction .content-wrap .intro p {
          font-size: 1.66vw;
          line-height: 2; }
        .product-introduction .content-wrap .intro .has-before::before {
          top: -4vw; }
      .product-introduction .content-wrap .img {
        position: static;
        justify-content: end;
        width: 100%;
        padding-right: 1.56vw;
        padding-bottom: 1.56vw; }
        .product-introduction .content-wrap .img img {
          width: 30vw; }

  .core-function {
    padding-bottom: 3.34vw; }
    .core-function .content-wrap {
      margin-top: 8.12vw; }
      .core-function .content-wrap > div .func-item {
        margin-bottom: 9.9vw; }
        .core-function .content-wrap > div .func-item .img {
          font-size: 6.88vw;
          margin-bottom: 3.02vw; }
        .core-function .content-wrap > div .func-item .func-title {
          font-size: 1.826vw; }
        .core-function .content-wrap > div .func-item .func-desc {
          font-size: 1.66vw;
          margin-top: 1.14vw; }

  .advantages {
    padding-bottom: 40vw; }
    .advantages .content-wrap {
      margin-top: 8.02vw; }
      .advantages .content-wrap .overview > li .wrap {
        padding: 3.34vw 0 6.26vw 0; }
        .advantages .content-wrap .overview > li .wrap .box .img {
          width: 15.94vw;
          height: 18.02vw;
          background: transparent;
          margin-bottom: 2vw; }
        .advantages .content-wrap .overview > li .wrap .box .adv-title {
          font-size: 2.08vw;
          line-height: 2; }
          .advantages .content-wrap .overview > li .wrap .box .adv-title br {
            display: block; }
        .advantages .content-wrap .overview > li .wrap::before {
          font-size: 12.5vw; }
      .advantages .content-wrap .content .wrap {
        padding: 3.64vw 5.2vw; }
        .advantages .content-wrap .content .wrap ul li {
          font-size: 1.66vw;
          line-height: 1.8; }
          .advantages .content-wrap .content .wrap ul li + li {
            margin-top: 1.5vw; }
        .advantages .content-wrap .content .wrap::before {
          width: 1.14vw;
          height: 1.14vw;
          background: #6b8ffb; }

  .concat-us {
    padding: 7.128vw 7.128vw 7.872vw; }
    .concat-us .form-title {
      font-size: 2.7vw; }
    .concat-us .form-en-title {
      font-size: 1.46vw; }
    .concat-us .form {
      margin-top: 5.256vw; }
      .concat-us .form .input-wrap {
        width: 50%;
        margin-bottom: 2vw; }
        .concat-us .form .input-wrap input {
          width: 100%;
          height: 4.992vw;
          background: rgba(255, 255, 255, 0.1);
          font-size: 1.46vw;
          padding: 0 1.56vw; }
    .concat-us .btn-wrap {
      margin-top: 4.752vw; }
      .concat-us .btn-wrap .consulting-button {
        font-size: 1.66vw;
        width: 31.26vw;
        height: 4.992vw;
        background: #1c6bff; } }
@media only screen and (max-width: 768px) {
  .banner {
    height: 50.328vw; }

  .title {
    padding-top: 11.88vw; }
    .title h4 {
      font-size: 3.9936vw; }
    .title h5 {
      font-size: 1.992vw; }

  .product-introduction {
    padding-bottom: 13.632vw; }
    .product-introduction .content-wrap {
      margin-top: 7.248vw;
      background-color: #fff; }
      .product-introduction .content-wrap .intro {
        padding-top: 15.744vw;
        width: 100%;
        padding-left: 6vw;
        padding-right: 6vw; }
        .product-introduction .content-wrap .intro p {
          font-size: 1.992vw;
          line-height: 2; }
        .product-introduction .content-wrap .intro .has-before::before {
          top: -4vw; }
      .product-introduction .content-wrap .img {
        position: static;
        justify-content: end;
        width: 100%;
        padding-right: 1.56vw;
        padding-bottom: 1.56vw; }
        .product-introduction .content-wrap .img img {
          width: 30vw; }

  .core-function {
    padding-bottom: 4.008vw; }
    .core-function .content-wrap {
      margin-top: 9.744vw; }
      .core-function .content-wrap > div .func-item {
        margin-bottom: 11.88vw; }
        .core-function .content-wrap > div .func-item .img {
          font-size: 8.256vw;
          margin-bottom: 3.624vw; }
        .core-function .content-wrap > div .func-item .func-title {
          font-size: 2.1912vw; }
        .core-function .content-wrap > div .func-item .func-desc {
          font-size: 1.992vw;
          margin-top: 1.368vw; }

  .advantages {
    padding-bottom: 40vw; }
    .advantages .content-wrap {
      margin-top: 9.624vw; }
      .advantages .content-wrap .overview > li .wrap {
        padding: 4.008vw 0 7.512vw 0; }
        .advantages .content-wrap .overview > li .wrap .box .img {
          width: 19.128vw;
          height: 21.624vw;
          background: transparent;
          margin-bottom: 2vw; }
        .advantages .content-wrap .overview > li .wrap .box .adv-title {
          font-size: 2.496vw;
          line-height: 2; }
          .advantages .content-wrap .overview > li .wrap .box .adv-title br {
            display: block; }
        .advantages .content-wrap .overview > li .wrap::before {
          font-size: 15vw; }
      .advantages .content-wrap .content .wrap {
        padding: 4.368vw 6.24vw; }
        .advantages .content-wrap .content .wrap ul li {
          font-size: 1.992vw;
          line-height: 1.8; }
          .advantages .content-wrap .content .wrap ul li + li {
            margin-top: 1.5vw; }
        .advantages .content-wrap .content .wrap::before {
          width: 1.368vw;
          height: 1.368vw;
          background: #6b8ffb; }

  .concat-us {
    padding: 8.5536vw 8.5536vw 9.4464vw; }
    .concat-us .form-title {
      font-size: 3.24vw; }
    .concat-us .form-en-title {
      font-size: 1.752vw; }
    .concat-us .form {
      margin-top: 6.3072vw; }
      .concat-us .form .input-wrap {
        width: 50%;
        margin-bottom: 2vw; }
        .concat-us .form .input-wrap input {
          width: 100%;
          height: 5.9904vw;
          background: rgba(255, 255, 255, 0.1);
          font-size: 1.752vw;
          padding: 0 1.56vw; }
    .concat-us .btn-wrap {
      margin-top: 5.7024vw; }
      .concat-us .btn-wrap .consulting-button {
        font-size: 1.992vw;
        width: 37.512vw;
        height: 5.9904vw;
        background: #1c6bff; } }
@media only screen and (max-width: 700px) {
  .banner {
    padding-top: 50px; } }
@media only screen and (max-width: 500px) {
  .container, .advantages .content-wrap .content, .concat-us .form {
    width: 90vw; }

  .banner {
    height: 69.9vw;
    background-position: 70% bottom;
    background: url(../img/banner.png) no-repeat 60%/cover; }
    .banner .container, .banner .advantages .content-wrap .content, .advantages .content-wrap .banner .content, .banner .concat-us .form, .concat-us .banner .form {
      width: 90vw; }
    .banner .tag {
      font-size: 3.32vw;
      padding: 2vw 4vw;
      border-radius: 4vw;
      margin-top: 20vw; }
    .banner h2 {
      font-size: 5vw;
      margin-top: 8vw; }
    .banner h3 {
      font-size: 3vw;
      margin-top: 5vw; }

  .title {
    padding-top: 11.88vw; }
    .title h4 {
      font-size: 5.6vw; }
    .title h5 {
      font-size: 3vw;
      margin-top: 3vw; }

  .product-introduction .content-wrap .intro {
    padding: 6vw;
    padding-top: 12vw; }
    .product-introduction .content-wrap .intro p {
      font-size: 3.32vw;
      line-height: 2; }
    .product-introduction .content-wrap .intro .has-before::before {
      width: 10px; }
  .product-introduction .content-wrap .img {
    padding-right: 4vw;
    padding-bottom: 4vw; }
    .product-introduction .content-wrap .img img {
      width: 40vw; }

  .core-function .content-wrap {
    margin-top: 16.24vw; }
    .core-function .content-wrap > div .func-item {
      padding: 0 4vw; }
      .core-function .content-wrap > div .func-item .img {
        height: 7vw;
        margin-bottom: 6.04vw; }
      .core-function .content-wrap > div .func-item .func-title {
        font-size: 3.32vw; }
      .core-function .content-wrap > div .func-item .func-desc {
        font-size: 2.988vw;
        margin-top: 2.28vw; }
    .core-function .content-wrap .phone {
      display: flex;
      flex-wrap: wrap;
      justify-content: start; }
    .core-function .content-wrap .pc {
      display: none; }

  .advantages {
    padding-bottom: 20vw; }
    .advantages .pc {
      display: none; }
    .advantages .phone {
      display: block; }
      .advantages .phone > ul > li {
        background-color: #fff;
        margin-bottom: 10vw;
        padding: 10vw;
        position: relative; }
        .advantages .phone > ul > li + li {
          margin-top: 10vw; }
        .advantages .phone > ul > li .adv-title {
          align-items: center; }
          .advantages .phone > ul > li .adv-title p {
            font-size: 4vw;
            color: #6b8ffb;
            font-weight: bold; }
        .advantages .phone > ul > li .desc {
          margin-top: 5vw; }
          .advantages .phone > ul > li .desc img {
            float: right;
            right: 10vw;
            bottom: 10vw;
            width: 40vw;
            z-index: 1;
            margin-top: -30vw;
            margin-right: -15vw; }
          .advantages .phone > ul > li .desc ul {
            margin-bottom: 22.58vw; }
            .advantages .phone > ul > li .desc ul li {
              list-style-type: square;
              margin-top: 2vw;
              font-size: 3.5vw;
              color: #071b3f;
              font-weight: normal;
              line-height: 2; }

  .concat-us {
    padding: 14.256vw 10vw 15.744vw; }
    .concat-us .form-title {
      font-size: 5.4vw;
      line-height: 1.5; }
      .concat-us .form-title br {
        display: block; }
    .concat-us .form-en-title {
      font-size: 2.92vw;
      margin-top: 3vw; }
    .concat-us .form {
      width: 100%;
      margin-top: 10.512vw;
      display: block; }
      .concat-us .form .input-wrap {
        width: 100%;
        margin-bottom: 4vw; }
        .concat-us .form .input-wrap input {
          width: 100%;
          height: 9.984vw;
          background: rgba(255, 255, 255, 0.1);
          font-size: 2.92vw;
          padding: 0 1.56vw; }
    .concat-us .btn-wrap {
      margin-top: 9.504vw; }
      .concat-us .btn-wrap .consulting-button {
        font-size: 3.32vw;
        width: 62.52vw;
        height: 9.984vw;
        background: #1c6bff; } }
.message-box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: black;
  color: #fff;
  padding: 20px 30px;
  border-radius: 4px; }

#message-text {
  color: #fff; }

/*# sourceMappingURL=index.css.map */
