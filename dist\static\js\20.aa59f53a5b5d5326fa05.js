webpackJsonp([20,21],{"+04b":function(t,e){},"+Xza":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("wU6q"),s=a("9FlD"),n=a("44VP"),o=a("LGqE"),r={name:"MyShop",components:{CardWrap:i.a,Tips:s.a,CustomDialog:n.a},mixins:[o.a],data:function(){return{}},created:function(){},methods:{toMore:function(t){console.log("toMore",t),this.$router.push({path:"/console/display/E_commerce/index"})}}},c={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"my-shop-wrapper"},[a("h3",[t._v("我的电商")]),t._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"my-shop-content"},t._l(t.myShopDataList,function(e,i){return a("card-wrap",{key:i,staticClass:"content-item",attrs:{title:e.title,"show-more":!0},on:{toMore:function(a){return t.toMore(e)}}},[a("div",{staticClass:"content-shop-wrapper",class:{"no-data":!e.list||!e.list.length}},[e.list&&e.list.length?[t._l(e.list,function(e,i){return[i<3?a("div",{staticClass:"shop-item flex-x",on:{click:function(a){return t.jump(e,"url")}}},[a("div",{staticClass:"shop-avatar"},[a("img",{attrs:{src:e.icon,alt:""}})]),t._v(" "),a("div",{staticClass:"shop-name flex-1"},[t._v(t._s(e.title))])]):t._e()]})]:[a("div",{staticClass:"add-shop flex",on:{click:function(e){t.isEdit=!0}}},[a("div",{staticClass:"icon-box add"}),t._v(" "),a("div",{staticClass:"tips"},[t._v("请添加电商网站")])])]],2)])}),1),t._v(" "),a("Tips",{attrs:{show:t.show,content:t.content},on:{"update:show":function(e){t.show=e}}}),t._v(" "),a("custom-dialog",{attrs:{show:t.isEdit,"cate-map":t.cateMap,myData:t.myShopData,list:t.allShopList,isFilter:!0},on:{"update:show":function(e){t.isEdit=e},refresh:t.refreshMyShopData}})],1)},staticRenderFns:[]};var l=a("VU/8")(r,c,!1,function(t){a("D+vD")},"data-v-a0eaabc0",null).exports,d={name:"TodoList",components:{CardWrap:i.a},methods:{toMore:function(){console.log("toMore-待办事项"),this.$emit("toMore")}}},u={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"todo-list-wrapper"},[a("card-wrap",{staticClass:"todo-list-card",attrs:{title:"待办事项","show-more":!0},on:{toMore:t.toMore}},[a("div",{staticClass:"todo-list-content"},[a("div",{staticClass:"todo-item"},[a("p",{staticClass:"item-label"},[t._v("待发货订单")]),t._v(" "),a("p",{staticClass:"item-count"},[t._v("28")])]),t._v(" "),a("div",{staticClass:"todo-item"},[a("p",{staticClass:"item-label"},[t._v("待付款订单")]),t._v(" "),a("p",{staticClass:"item-count"},[t._v("60")])]),t._v(" "),a("div",{staticClass:"todo-item"},[a("p",{staticClass:"item-label"},[t._v("待处理退款")]),t._v(" "),a("p",{staticClass:"item-count"},[t._v("4")])]),t._v(" "),a("div",{staticClass:"todo-item"},[a("p",{staticClass:"item-label"},[t._v("待核销订单")]),t._v(" "),a("p",{staticClass:"item-count"},[t._v("117")])])])])],1)},staticRenderFns:[]};var h=a("VU/8")(d,u,!1,function(t){a("UDaK")},"data-v-0c615e28",null).exports,p=a("XLwt"),m={name:"ROIChart",components:{CardWrap:i.a},data:function(){return{roiData:{value:88.6,total:100},chart:null,chartHeight:0}},computed:{chartId:function(){return"roi-chart-"+Math.random().toString(36).substring(2,9)}},mounted:function(){this.initChart()},beforeDestroy:function(){this.chart&&this.chart.dispose()},watch:{roiData:{deep:!0,handler:function(){this.initChart()}}},methods:{initChart:function(){var t=this;this.$nextTick(function(){t.chart&&t.chart.dispose();var e=document.getElementById(t.chartId);if(e){t.chart=p.b(e);var a=e.clientHeight||200,i=e.clientWidth||300,s=Math.min(a,i),n=Math.max(18,Math.min(36,.15*s)),o=Math.max(6,Math.min(15,.04*s)),r=Math.min(.4*i,.8*a),c={series:[{type:"gauge",startAngle:180,endAngle:0,center:["50%","70%"],radius:r,min:0,max:100,splitNumber:8,axisLine:{lineStyle:{width:o,color:[[t.roiData.value/100,"#4D80FF"],[1,"#E6ECF5"]]}},pointer:{show:!1},axisTick:{show:!1},splitLine:{show:!1},axisLabel:{show:!1},detail:{show:!0,offsetCenter:[0,Math.max(-40,.15*-a)],valueAnimation:!0,formatter:"{value}%",fontSize:n,fontWeight:"bold",color:"#333"},data:[{value:t.roiData.value}]},{type:"gauge",startAngle:180,endAngle:0,center:["50%","70%"],radius:r+8,min:0,max:100,axisLine:{lineStyle:{width:1,color:[[1,"#4D80FF"]],type:[5,5]}},pointer:{show:!1},axisTick:{show:!1},splitLine:{show:!1},axisLabel:{show:!1},detail:{show:!1}}]};t.chart.setOption(c);window.addEventListener("resize",function(){t.chart&&t.chart.resize()})}})},updateROI:function(t){this.roiData.value=t}}},v={render:function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"roi-chart-wrapper"},[e("card-wrap",{attrs:{title:"电商投效比"}},[e("div",{ref:"chartContainer",staticClass:"chart-container"},[this.roiData?e("div",{staticClass:"chart",attrs:{id:this.chartId}}):e("el-empty",{attrs:{description:"暂无数据"}})],1)])],1)},staticRenderFns:[]};var f=a("VU/8")(m,v,!1,function(t){a("mrr6")},"data-v-6cea90a9",null).exports,g=a("F6wa"),y={name:"KeyData",components:{CardWrap:i.a,CardList:g.a},data:function(){return{cardList:[{bgColor:"linear-gradient(90deg, #2D6DFF, #4999FF)",logo:a("gaoR"),title:"我的店铺数",desc:"4"},{bgColor:"linear-gradient(90deg, #03C392, #19E498)",logo:a("YI+f"),title:"商品总数",desc:"98",logoStyle:{width:"66px",bottom:"14px"}},{bgColor:"linear-gradient(90deg, #2D6DFF, #4999FF)",logo:a("DJvx"),title:"销售总额",desc:"328901.10",logoStyle:{bottom:"14px"}},{bgColor:"linear-gradient(90deg, #03C392, #19E498)",logo:a("DBS8"),title:"总订单",desc:"360",logoStyle:{width:"53px",bottom:"12px"}},{bgColor:"linear-gradient(90deg, #2D6DFF, #4999FF)",logo:a("RduG"),title:"总利润",desc:"240100.10",logoStyle:{width:"59px",bottom:"14px"}}],titleStyle:{color:"#FFFFFF",fontSize:"14px",marginTop:"0"},descStyle:{color:"#FFFFFF",fontSize:"28px",fontFamily:"Bahnschrift",marginTop:"0"},itemStyle:{position:"relative",height:"94px",padding:"20px 10px 20px 10px",borderRadius:"10px"},logoStyle:{position:"absolute",right:0,bottom:"10px",width:"62px",height:"auto",zIndex:1}}},methods:{}},x={render:function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"key-data-wrapper"},[e("card-wrap",{staticClass:"key-data-card",attrs:{title:"重点数据"}},[e("card-list",{staticClass:"key-data-card-list",attrs:{cardList:this.cardList,itemStyle:this.itemStyle,titleStyle:this.titleStyle,descStyle:this.descStyle,logoStyle:this.logoStyle}})],1)],1)},staticRenderFns:[]};var b=a("VU/8")(y,x,!1,function(t){a("x/yo")},"data-v-8f8becee",null).exports,_={name:"SaleTrend",components:{CardWrap:i.a},data:function(){return{activeTab:"7",timeTabs:[{label:"7天",value:"7"},{label:"30天",value:"30"},{label:"近半年",value:"180"},{label:"全部",value:"all"}],chartData:{xAxis:["06-01","06-02","06-03","06-04","06-05","06-06","06-07"],series:[{name:"淘宝-普智精选商城",data:[85,120,110,100,105,120,85],color:"#24D5AF"},{name:"京东-龙采冰海官方旗舰店",data:[25,110,105,80,75,110,75],color:"#4D80FF"}]},chart:null}},computed:{chartId:function(){return"sale-trend-chart-"+Math.random().toString(36).substring(2,9)}},mounted:function(){this.initChart()},beforeDestroy:function(){this.chart&&this.chart.dispose()},watch:{chartData:{deep:!0,handler:function(){this.initChart()}},activeTab:function(){this.loadData()}},methods:{changeTab:function(t){this.activeTab=t},loadData:function(){var t={7:{xAxis:["06-01","06-02","06-03","06-04","06-05","06-06","06-07"],series:[{name:"淘宝-普智精选商城",data:[85,120,110,100,105,120,85],color:"#24D5AF"},{name:"京东-龙采冰海官方旗舰店",data:[25,110,105,80,75,110,75],color:"#4D80FF"}]},30:{xAxis:["05-08","05-15","05-22","05-29","06-05"],series:[{name:"淘宝-普智精选商城",data:[90,115,105,110,120],color:"#24D5AF"},{name:"京东-龙采冰海官方旗舰店",data:[30,105,100,85,110],color:"#4D80FF"}]},180:{xAxis:["01月","02月","03月","04月","05月","06月"],series:[{name:"淘宝-普智精选商城",data:[95,110,108,115,112,120],color:"#24D5AF"},{name:"京东-龙采冰海官方旗舰店",data:[35,100,98,90,105,110],color:"#4D80FF"}]},all:{xAxis:["2023","2024"],series:[{name:"淘宝-普智精选商城",data:[100,118],color:"#24D5AF"},{name:"京东-龙采冰海官方旗舰店",data:[80,108],color:"#4D80FF"}]}};this.chartData=t[this.activeTab]||t[7]},initChart:function(){var t=this;this.$nextTick(function(){t.chart&&t.chart.dispose();var e=document.getElementById(t.chartId);if(e){t.chart=p.b(e);var a={grid:{top:20,left:0,right:20,bottom:40,containLabel:!0},legend:{bottom:0,left:"center",itemWidth:8,itemHeight:8,itemGap:30,textStyle:{color:"#8A8A8A",fontSize:12},icon:"circle",data:t.chartData.series.map(function(t){return{name:t.name,itemStyle:{color:t.color}}})},tooltip:{trigger:"axis",backgroundColor:"#fff",borderColor:"#E6E6E6",borderWidth:1,textStyle:{color:"#333"},axisPointer:{type:"line",lineStyle:{color:"#E6E6E6",type:"dashed"}},formatter:function(t){var e={"淘宝-普智精选商城":"#24D5AF","京东-龙采冰海官方旗舰店":"#4D80FF"},a='<div style="font-size: 12px; color: #333; padding: 8px;">';return a+='<div style="margin-bottom: 8px; font-weight: 500;">'+t[0].axisValue+"</div>",t.forEach(function(t){var i=e[t.seriesName]||t.color;a+='<div style="display: flex; align-items: center; margin-bottom: 4px;">\n                  <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: '+i+'; margin-right: 8px;"></span>\n                  <span style="margin-right: 20px; color: #666; min-width: 120px;">'+t.seriesName+'</span>\n                  <span style="font-weight: 500; color: #333;">'+t.value+"</span>\n                </div>"}),a+="</div>"}},xAxis:{type:"category",data:t.chartData.xAxis,boundaryGap:!1,axisLine:{lineStyle:{color:"#A7ABBB"}},axisTick:{show:!1},axisLabel:{color:"#666666",fontSize:12}},yAxis:{type:"value",axisLine:{show:!0,lineStyle:{color:"#A7ABBB"}},axisTick:{show:!1},axisLabel:{color:"#666666",fontSize:12},splitLine:{lineStyle:{color:"#F5F5F5",type:"solid"}}},series:t.chartData.series.map(function(e){return{name:e.name,type:"line",data:e.data,smooth:!0,symbol:"circle",symbolSize:8,lineStyle:{width:2,color:e.color},itemStyle:{color:"#fff",borderWidth:3,borderColor:e.color},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:t.hexToRgba(e.color,.3)},{offset:1,color:t.hexToRgba(e.color,.05)}]}}}})};t.chart.setOption(a),window.addEventListener("resize",function(){t.chart&&t.chart.resize()})}})},hexToRgba:function(t,e){return"rgba("+parseInt(t.slice(1,3),16)+", "+parseInt(t.slice(3,5),16)+", "+parseInt(t.slice(5,7),16)+", "+e+")"}},created:function(){this.loadData()}},C={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"sale-trend-wrapper"},[a("card-wrap",{staticClass:"sale-trend-card",attrs:{title:"店铺销售趋势"}},[a("template",{slot:"right"},[a("div",{staticClass:"time-tabs"},t._l(t.timeTabs,function(e,i){return a("div",{key:i,class:["time-tab",{active:t.activeTab===e.value}],on:{click:function(a){return t.changeTab(e.value)}}},[t._v("\n          "+t._s(e.label)+"\n        ")])}),0)]),t._v(" "),a("div",{staticClass:"chart-container"},[t.chartData.xAxis&&t.chartData.xAxis.length?a("div",{staticClass:"chart",attrs:{id:t.chartId}}):a("el-empty",{attrs:{description:"暂无数据"}})],1)],2)],1)},staticRenderFns:[]};var w=a("VU/8")(_,C,!1,function(t){a("423j")},"data-v-1ea01d7e",null).exports,D={name:"ShopOrderComparison",components:{CardWrap:i.a},data:function(){return{activeTab:"近六月",timeTabs:[{label:"7天",value:"7天"},{label:"30天",value:"30天"},{label:"近六月",value:"近六月"},{label:"全年",value:"全年"}],chartData:{xAxis:["1月","2月","3月","4月","5月","6月"],series:[{name:"淘宝-普智精选商城",data:[2500,1800,2200,3500,2800,1200],color:"#4D80FF"},{name:"京东-龙采冰海官方旗舰店",data:[1500,1200,1800,1500,800,3800],color:"#24D5AF"},{name:"拼多多-普智精选商城",data:[1e3,800,1e3,1e3,2200,1e3],color:"#87CEEB"}]},chart:null}},computed:{chartId:function(){return"shop-order-comparison-chart-"+Math.random().toString(36).substring(2,9)}},mounted:function(){this.initChart()},beforeDestroy:function(){this.chart&&this.chart.dispose()},watch:{chartData:{deep:!0,handler:function(){this.initChart()}},activeTab:function(){this.loadData()}},methods:{changeTab:function(t){this.activeTab=t},loadData:function(){var t={"7天":{xAxis:["周一","周二","周三","周四","周五","周六","周日"],series:[{name:"淘宝-普智精选商城",data:[350,280,320,450,380,200,150],color:"#4D80FF"},{name:"京东-龙采冰海官方旗舰店",data:[200,180,250,200,120,480,300],color:"#24D5AF"},{name:"拼多多-普智精选商城",data:[150,120,150,150,280,150,100],color:"#87CEEB"}]},"30天":{xAxis:["第1周","第2周","第3周","第4周"],series:[{name:"淘宝-普智精选商城",data:[2100,1800,2200,2500],color:"#4D80FF"},{name:"京东-龙采冰海官方旗舰店",data:[1400,1200,1600,1800],color:"#24D5AF"},{name:"拼多多-普智精选商城",data:[900,800,1e3,1200],color:"#87CEEB"}]},"近六月":{xAxis:["1月","2月","3月","4月","5月","6月"],series:[{name:"淘宝-普智精选商城",data:[2500,1800,2200,3500,2800,1200],color:"#4D80FF"},{name:"京东-龙采冰海官方旗舰店",data:[1500,1200,1800,1500,800,3800],color:"#24D5AF"},{name:"拼多多-普智精选商城",data:[1e3,800,1e3,1e3,2200,1e3],color:"#87CEEB"}]},"全年":{xAxis:["Q1","Q2","Q3","Q4"],series:[{name:"淘宝-普智精选商城",data:[6500,8500,7200,9800],color:"#4D80FF"},{name:"京东-龙采冰海官方旗舰店",data:[4500,5300,4800,6200],color:"#24D5AF"},{name:"拼多多-普智精选商城",data:[2800,3200,3500,4100],color:"#87CEEB"}]}};this.chartData=t[this.activeTab]||t["近六月"]},initChart:function(){var t=this;this.$nextTick(function(){t.chart&&t.chart.dispose();var e=document.getElementById(t.chartId);if(e){t.chart=p.b(e);var a={grid:{top:20,left:0,right:20,bottom:30,containLabel:!0},legend:{bottom:0,left:"center",itemWidth:8,itemHeight:8,itemGap:30,textStyle:{color:"#8A8A8A",fontSize:12},icon:"circle",data:t.chartData.series.map(function(t){return{name:t.name,itemStyle:{color:t.color}}})},tooltip:{trigger:"axis",backgroundColor:"#fff",borderColor:"#E6E6E6",borderWidth:1,textStyle:{color:"#333"},confineToContainer:!1,appendToBody:!0,axisPointer:{type:"shadow",shadowStyle:{color:"rgba(0,0,0,0.1)"}},formatter:function(t){var e='<div style="font-size: 12px; color: #333; padding: 8px;">';e+='<div style="margin-bottom: 8px; font-weight: 500;">'+t[0].axisValue+"</div>";var a=t.reduce(function(t,e){return t+e.value},0);return t.forEach(function(t){e+='<div style="display: flex; align-items: center; margin-bottom: 4px;">\n                  <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: '+t.color+'; margin-right: 8px;"></span>\n                  <span style="margin-right: 20px; color: #666; min-width: 120px;">'+t.seriesName+'</span>\n                  <span style="font-weight: 500; color: #333;">'+t.value+"</span>\n                </div>"}),e+='<div style="border-top: 1px solid #E6E6E6; margin-top: 8px; padding-top: 8px;">\n                <div style="display: flex; align-items: center;">\n                  <span style="margin-right: 20px; color: #666; min-width: 120px; font-weight: 500;">总计</span>\n                  <span style="font-weight: 600; color: #333;">'+a+"</span>\n                </div>\n              </div>",e+="</div>"}},xAxis:{type:"category",data:t.chartData.xAxis,axisLine:{lineStyle:{color:"#A7ABBB"}},axisTick:{show:!1},axisLabel:{color:"#666666",fontSize:12}},yAxis:{type:"value",axisLine:{show:!0,lineStyle:{color:"#A7ABBB"}},axisTick:{show:!1},axisLabel:{color:"#666666",fontSize:12},splitLine:{lineStyle:{color:"#F5F5F5",type:"solid"}}},series:t.chartData.series.map(function(e,a){return{name:e.name,type:"bar",stack:"total",data:e.data,barWidth:30,itemStyle:{color:e.color,borderRadius:a===t.chartData.series.length-1?[4,4,0,0]:[0,0,0,0]}}})};t.chart.setOption(a),window.addEventListener("resize",function(){t.chart&&t.chart.resize()})}})}},created:function(){this.loadData()}},S={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"shop-order-comparison-wrapper"},[a("card-wrap",{staticClass:"shop-order-comparison-card",attrs:{title:"店铺订单对比"}},[a("template",{slot:"right"},[a("div",{staticClass:"time-tabs"},t._l(t.timeTabs,function(e,i){return a("div",{key:i,class:["time-tab",{active:t.activeTab===e.value}],on:{click:function(a){return t.changeTab(e.value)}}},[t._v("\n          "+t._s(e.label)+"\n        ")])}),0)]),t._v(" "),a("div",{staticClass:"chart-container"},[t.chartData.xAxis&&t.chartData.xAxis.length?a("div",{staticClass:"chart",attrs:{id:t.chartId}}):a("el-empty",{attrs:{description:"暂无数据"}})],1)],2)],1)},staticRenderFns:[]};var F=a("VU/8")(D,S,!1,function(t){a("tU0q")},"data-v-3ea493d9",null).exports,k={name:"ShopSalesRatio",components:{CardWrap:i.a},data:function(){return{activeTab:"近六月",timeTabs:[{label:"7天",value:"7天"},{label:"30天",value:"30天"},{label:"近六月",value:"近六月"},{label:"全年",value:"全年"}],activeDataType:"销售额",dataTypes:[{label:"销售额",value:"销售额"},{label:"订单",value:"订单"}],chartData:[{name:"淘宝-普智精选商城",value:85e3,color:"#24D5AF"},{name:"京东-龙采冰海官方旗舰店",value:68e3,color:"#4D80FF"},{name:"拼多多-普智精选商城",value:45e3,color:"#87CEEB"},{name:"阿里巴巴-龙采冰海官方旗舰店",value:52e3,color:"#FFA500"}],chart:null,centerData:{total:25e4,comparison:25}}},computed:{chartId:function(){return"shop-sales-ratio-chart-"+Math.random().toString(36).substring(2,9)}},mounted:function(){var t=this;this.loadData(),this.$nextTick(function(){t.initChart()})},beforeDestroy:function(){this.chart&&this.chart.dispose()},watch:{chartData:{deep:!0,handler:function(){this.initChart()}},activeTab:function(){this.loadData()},activeDataType:function(){this.loadData()}},methods:{changeTab:function(t){this.activeTab=t},changeDataType:function(t){this.activeDataType=t},loadData:function(){var t={"7天":{"销售额":{data:[{name:"淘宝-普智精选商城",value:3500,color:"#24D5AF"},{name:"京东-龙采冰海官方旗舰店",value:2800,color:"#4D80FF"},{name:"拼多多-普智精选商城",value:1200,color:"#87CEEB"},{name:"阿里巴巴-龙采冰海官方旗舰店",value:2780.8,color:"#FFA500"}],total:10280.8,comparison:25},"订单":{data:[{name:"淘宝-普智精选商城",value:450,color:"#24D5AF"},{name:"京东-龙采冰海官方旗舰店",value:380,color:"#4D80FF"},{name:"拼多多-普智精选商城",value:220,color:"#87CEEB"},{name:"阿里巴巴-龙采冰海官方旗舰店",value:350,color:"#FFA500"}],total:1400,comparison:18}},"30天":{"销售额":{data:[{name:"淘宝-普智精选商城",value:15e3,color:"#24D5AF"},{name:"京东-龙采冰海官方旗舰店",value:12e3,color:"#4D80FF"},{name:"拼多多-普智精选商城",value:8e3,color:"#87CEEB"},{name:"阿里巴巴-龙采冰海官方旗舰店",value:1e4,color:"#FFA500"}],total:45e3,comparison:32},"订单":{data:[{name:"淘宝-普智精选商城",value:1800,color:"#24D5AF"},{name:"京东-龙采冰海官方旗舰店",value:1500,color:"#4D80FF"},{name:"拼多多-普智精选商城",value:1200,color:"#87CEEB"},{name:"阿里巴巴-龙采冰海官方旗舰店",value:1300,color:"#FFA500"}],total:5800,comparison:28}},"近六月":{"销售额":{data:[{name:"淘宝-普智精选商城",value:85e3,color:"#24D5AF"},{name:"京东-龙采冰海官方旗舰店",value:68e3,color:"#4D80FF"},{name:"拼多多-普智精选商城",value:45e3,color:"#87CEEB"},{name:"阿里巴巴-龙采冰海官方旗舰店",value:52e3,color:"#FFA500"}],total:25e4,comparison:25},"订单":{data:[{name:"淘宝-普智精选商城",value:8500,color:"#24D5AF"},{name:"京东-龙采冰海官方旗舰店",value:6800,color:"#4D80FF"},{name:"拼多多-普智精选商城",value:4500,color:"#87CEEB"},{name:"阿里巴巴-龙采冰海官方旗舰店",value:5200,color:"#FFA500"}],total:25e3,comparison:22}},"全年":{"销售额":{data:[{name:"淘宝-普智精选商城",value:32e4,color:"#24D5AF"},{name:"京东-龙采冰海官方旗舰店",value:28e4,color:"#4D80FF"},{name:"拼多多-普智精选商城",value:18e4,color:"#87CEEB"},{name:"阿里巴巴-龙采冰海官方旗舰店",value:22e4,color:"#FFA500"}],total:1e6,comparison:35},"订单":{data:[{name:"淘宝-普智精选商城",value:32e3,color:"#24D5AF"},{name:"京东-龙采冰海官方旗舰店",value:28e3,color:"#4D80FF"},{name:"拼多多-普智精选商城",value:18e3,color:"#87CEEB"},{name:"阿里巴巴-龙采冰海官方旗舰店",value:22e3,color:"#FFA500"}],total:1e5,comparison:30}}},e=t[this.activeTab]||t["近六月"],a=e[this.activeDataType]||e["销售额"];this.chartData=a.data,this.centerData={total:a.total,comparison:a.comparison}},initChart:function(){var t=this;this.$nextTick(function(){t.chart&&(t.chart.dispose(),t.chart=null);var e=document.getElementById(t.chartId);if(e&&t.chartData.length){t.chart=p.b(e);var a={tooltip:{trigger:"item",backgroundColor:"#fff",borderColor:"#E6E6E6",borderWidth:1,textStyle:{color:"#333"},formatter:function(t){return'\n                <div style="font-size: 12px; color: #333; padding: 8px;">\n                  <div style="margin-bottom: 8px; font-weight: 500;">'+t.name+'</div>\n                  <div style="display: flex; align-items: center;">\n                    <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: '+t.color+'; margin-right: 8px;"></span>\n                    <span style="margin-right: 20px; color: #666;">数值:</span>\n                    <span style="font-weight: 500; color: #333;">'+t.value.toLocaleString()+'</span>\n                  </div>\n                  <div style="display: flex; align-items: center; margin-top: 4px;">\n                    <span style="margin-right: 20px; color: #666;">占比:</span>\n                    <span style="font-weight: 500; color: #333;">'+t.percent+"%</span>\n                  </div>\n                </div>\n              "}},legend:{show:!1},series:[{type:"pie",radius:["59%","85%"],center:["50%","50%"],avoidLabelOverlap:!1,label:{show:!1},labelLine:{show:!1},emphasis:{scale:!1},data:t.chartData.map(function(t){return{value:t.value,name:t.name,itemStyle:{color:t.color}}})}],graphic:[{type:"text",left:"center",top:"40%",style:{text:t.centerData.total.toLocaleString(),fontSize:20,fontWeight:"bold",fill:"#000000"}},{type:"text",left:"41%",top:"54%",style:{text:"同比",fontSize:12,fill:"#666"}},{type:"text",left:"49%",top:"54%",style:{text:"↑"+t.centerData.comparison+"%",fontSize:12,fill:"#FF4D4F"}}]};t.chart.setOption(a),window.addEventListener("resize",function(){t.chart&&t.chart.resize()})}})}},created:function(){this.loadData()}},E={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"shop-sales-ratio-wrapper"},[a("card-wrap",{staticClass:"shop-sales-ratio-card",attrs:{title:"店铺销售占比"}},[a("template",{slot:"right"},[a("div",{staticClass:"time-tabs"},t._l(t.timeTabs,function(e,i){return a("div",{key:i,class:["time-tab",{active:t.activeTab===e.value}],on:{click:function(a){return t.changeTab(e.value)}}},[t._v("\n          "+t._s(e.label)+"\n        ")])}),0)]),t._v(" "),a("div",{staticClass:"content-container"},[a("div",{staticClass:"data-type-tabs"},t._l(t.dataTypes,function(e,i){return a("div",{key:i,class:["data-type-tab",{active:t.activeDataType===e.value}],on:{click:function(a){return t.changeDataType(e.value)}}},[t._v("\n          "+t._s(e.label)+"\n        ")])}),0),t._v(" "),a("div",{staticClass:"chart-content"},[a("div",{staticClass:"chart-container"},[t.chartData.length?a("div",{staticClass:"chart",attrs:{id:t.chartId}}):a("el-empty",{attrs:{description:"暂无数据"}})],1),t._v(" "),a("div",{staticClass:"legend-container"},t._l(t.chartData,function(e,i){return a("div",{key:i,staticClass:"legend-item"},[a("span",{staticClass:"legend-dot",style:{backgroundColor:e.color}}),t._v(" "),a("span",{staticClass:"legend-name"},[t._v(t._s(e.name))])])}),0)])])],2)],1)},staticRenderFns:[]};var L=a("VU/8")(k,E,!1,function(t){a("P/Bh")},"data-v-1da38d85",null).exports,z={name:"ShopDashboard",components:{PageWrap:a("Aw0F").a,MyShop:l,ToDoList:h,ROIChart:f,KeyData:b,SaleTrend:w,ShopOrderComparison:F,ShopSalesRatio:L},data:function(){return{name:"dashboard",pageNav:[{name:"dashboard",title:"电商通",path:"/console/display/E_commerce/dashboard",pathType:"page"}]}}},j={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("page-wrap",{attrs:{nav:t.pageNav}},[a("div",{staticClass:"shop-dashboard"},[a("my-shop"),t._v(" "),a("div",{staticClass:"gap-h-20"}),t._v(" "),a("div",{staticClass:"part-two"},[a("h3",{staticClass:"title"},[t._v("电商分析报告")]),t._v(" "),a("div",{staticClass:"part-two-wrapper flex"},[a("div",{staticClass:"todo-list"},[a("to-do-list")],1),t._v(" "),a("div",{staticClass:"roi-chart"},[a("ROIChart")],1),t._v(" "),a("div",{staticClass:"key-data flex-1"},[a("key-data")],1)])]),t._v(" "),a("div",{staticClass:"gap-h-20"}),t._v(" "),a("div",{staticClass:"dashboard-row"},[a("sale-trend")],1),t._v(" "),a("div",{staticClass:"gap-h-20"}),t._v(" "),a("div",{staticClass:"part-four"},[a("div",{staticClass:"shop-order flex-1"},[a("shop-order-comparison")],1),t._v(" "),a("div",{staticClass:"shop-sales"},[a("shop-sales-ratio")],1)]),t._v(" "),a("div",{staticClass:"gap-h-20"})],1)])},staticRenderFns:[]};var A=a("VU/8")(z,j,!1,function(t){a("lOm0")},"data-v-1fd2cd0f",null);e.default=A.exports},"+f9A":function(t,e,a){"use strict";var i=a("Dd8w"),s=a.n(i),n=a("Xxa5"),o=a.n(n),r=a("exGp"),c=a.n(r),l=(a("8PcR"),a("OjgP")),d=a("SgJ7"),u={name:"CateItem",components:{ListItem:l.a},props:{item:{type:Object,default:function(){}},myData:{type:Array,default:function(){}},colSpan:{type:Object,default:function(){return{}}},showBtn:{type:Boolean,default:!0},cateMap:{type:Object,default:function(){return{}}},isFilter:{type:Boolean,default:!1}},data:function(){return{activeCate:0}},methods:{toggleCate:function(t){this.activeCate=t,this.getListByCate(t)},getListByCate:function(t){var e=this;return c()(o.a.mark(function a(){return o.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:e.$emit("getListByCate",t);case 1:case"end":return a.stop()}},a,e)}))()},collect:function(t,e,a){var i=this;Object(d.f)({shopid:t,type:this.cateMap[e]}).then(function(t){200===t.code?(i.$message.success("添加成功"),t.data&&i.$emit("refresh","add",t.data.id,s()({},a,t.data))):i.$emit("refresh","refresh")})},isCollect:function(t,e){return this.myData.some(function(a){return a.shopid===t&&a.type===e})}}},h={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.item.cate&&t.item.cate.length?i("div",{staticClass:"cate-wrap flex-wrap"},t._l(t.item.cate,function(e,a){return i("div",{key:a,class:["cate-item flex-align flex-center",{active:e.id===t.activeCate}],on:{click:function(a){return t.toggleCate(e.id)}}},[t._v(t._s(e.name)+"\r\n    ")])}),0):t._e(),t._v(" "),t.item.list&&t.item.list.length?i("div",{staticClass:"wrap"},[i("list-item",{attrs:{data:t.item.list,activeCate:t.activeCate,"col-span":t.colSpan,isFilter:t.isFilter},scopedSlots:t._u([{key:"default",fn:function(e){var s=e.listItem;return t.showBtn?[t.isCollect(s.id,s.type)?t._e():i("div",{staticClass:"btn-item",on:{click:function(e){return t.collect(s.id,t.item.name,s)}}},[i("img",{attrs:{src:a("X9FQ"),alt:""}})])]:void 0}}],null,!0)})],1):i("el-empty")],1)},staticRenderFns:[]};var p=a("VU/8")(u,h,!1,function(t){a("uqMR")},"data-v-6c8fe556",null);e.a=p.exports},"423j":function(t,e){},"44VP":function(t,e,a){"use strict";var i=a("Xxa5"),s=a.n(i),n=a("exGp"),o=a.n(n),r=(a("8PcR"),a("SgJ7")),c=a("OjgP"),l={name:"CustomDialog",components:{CateItem:a("+f9A").a,ListItem:c.a},props:{cateMap:{type:Object,default:function(){}},show:{type:Boolean,default:!1},myData:{type:Array,default:function(){return[]}},list:{type:Array,default:function(){return[]}},loading:{type:Boolean,default:!1},isFilter:{type:Boolean,default:!1}},data:function(){return{activeCate:0,colSpan:{lg:{span:7},md:4,sm:8,xs:12}}},computed:{_show:{get:function(){return this.show},set:function(t){this.$emit("update:show",t)}},activeTab:{get:function(){return this.list[0].name},set:function(t){}}},methods:{toDel:function(t){var e=this;this.$confirm("是否要将该网站移除收藏？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.$emit("refresh","del",t),Object(r.b)({id:t}).then(function(t){200===t.code&&e.$message.success(t.message)})}).catch(function(t){console.log(t),e.$message({type:"info",message:"已取消删除"})})},getListByCate:function(t){var e=this;return o()(s.a.mark(function a(){return s.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:e.$emit("getListByCate",t);case 1:case"end":return a.stop()}},a,e)}))()},refresh:function(t,e,a){console.log("refresh",t,e,a),this.$emit("refresh",t,e,a)}}},d={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return t._show?i("div",{staticClass:"custom-dialog"},[i("div",{staticClass:"mask"}),t._v(" "),i("div",{staticClass:"dialog flex-column"},[i("div",{staticClass:"title"},[i("h3",[t._v("自定义")]),t._v(" "),i("el-button",{staticClass:"close",attrs:{icon:"el-icon-close",size:"small"},on:{click:function(e){t._show=!1}}})],1),t._v(" "),i("div",{staticClass:"content flex-1"},[i("div",{staticClass:"my"},[i("list-item",{attrs:{data:t.myData,"col-span":t.colSpan},scopedSlots:t._u([{key:"default",fn:function(e){var s=e.listItem;return[i("div",{staticClass:"btn-item",on:{click:function(e){return t.toDel(s.id)}}},[i("img",{attrs:{src:a("GrYv"),alt:""}})])]}}],null,!1,2883166146)})],1),t._v(" "),i("div",{staticClass:"other"},[t.list.length?i("el-tabs",{model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},t._l(t.list,function(e,a){return i("el-tab-pane",{key:a,attrs:{label:e.name,name:e.name}},[[i("cate-item",{attrs:{item:e,"my-data":t.myData,"col-span":t.colSpan,"cate-map":t.cateMap,isFilter:t.isFilter},on:{refresh:t.refresh,getListByCate:t.getListByCate}})]],2)}),1):t._e()],1)])])]):t._e()},staticRenderFns:[]};var u=a("VU/8")(l,d,!1,function(t){a("4cZw")},"data-v-67e1922c",null);e.a=u.exports},"4cZw":function(t,e){},"9FlD":function(t,e,a){"use strict";var i=a("SgJ7"),s={name:"TaoBaoLogin",props:{shopInfo:{type:Object,default:function(){return{}}}},computed:{shopId:function(){return this.shopInfo.shopid}},data:function(){return{passwordVisible:!1,form:{username:"",password:""},rules:{username:[{required:!0,message:"请输入账号名/邮箱/手机号",trigger:"blur"}],password:[{required:!0,message:"请输入登录密码",trigger:"blur"}]}}},methods:{togglePasswordVisibility:function(){this.passwordVisible=!this.passwordVisible},handleLogin:function(){var t=this,e=t.form,a=e.username,s=e.password,n=t.shopId,o=t.shopInfo;t.$refs.formRef.validate(function(e){if(!e)return!1;Object(i.a)({type:n,username:a,password:s,shop_type:o.type}).then(function(e){200===e.code&&(t.$message.success("授权绑定成功"),t.form.username="",t.form.password="",t.$emit("bind-success",e))}).catch(function(e){t.$message.error("授权绑定失败："+e.message)})})}}},n={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formRef",staticClass:"form-container",attrs:{model:t.form,rules:t.rules}},[i("el-form-item",{attrs:{label:"",prop:"username"}},[i("el-input",{attrs:{placeholder:"账号名/邮箱/手机号"},model:{value:t.form.username,callback:function(e){t.$set(t.form,"username",e)},expression:"form.username"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"",prop:"password"}},[i("el-input",{attrs:{placeholder:"请输入登录密码",type:t.passwordVisible?"text":"password"},model:{value:t.form.password,callback:function(e){t.$set(t.form,"password",e)},expression:"form.password"}},[i("img",{staticClass:"eye-icon",attrs:{slot:"suffix",src:a("aB6U")("./"+(t.passwordVisible?"eye":"eye-")+".png")},on:{click:t.togglePasswordVisibility},slot:"suffix"})])],1),t._v(" "),i("el-form-item",{staticStyle:{"margin-bottom":"0"}},[i("div",{staticClass:"login-btn",on:{click:t.handleLogin}},[t._v("授权绑定")])])],1)},staticRenderFns:[]};var o={name:"StoreBinding",components:{TaoBaoLogin:a("VU/8")(s,n,!1,function(t){a("gZ2d")},"data-v-5504ba58",null).exports},props:{shopInfo:{type:Object,default:function(){}}},data:function(){return{shopList:[],isFirstLoad:!0}},computed:{currentShopId:function(){return this.shopInfo.shopid}},created:function(){this.getShopList()},methods:{getShopList:function(){var t=this,e=this.currentShopId,a=this.shopInfo;Object(i.m)({type:e,shop_type:a.type}).then(function(e){200===e.code&&(t.shopList=e.data,t.isFirstLoad=!1)}).catch(function(e){console.error("获取店铺列表失败:",e),t.isFirstLoad=!1})},handleBindSuccess:function(t){this.getShopList()},handleCancelBind:function(t){var e=this;e.$confirm("确定取消授权吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(i.v)({id:t}).then(function(t){200===t.code&&(e.$message.success("取消授权成功"),e.getShopList())})})},handleEnterStore:function(t){this.$message.info("该功能正在开发中，敬请期待！")}}},r={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"store-binding"},[i("h2",{staticClass:"title"},[t._v("绑定店铺")]),t._v(" "),t.shopList.length>0?[i("div",{staticClass:"account-list flex"},[t._l(t.shopList,function(e){return i("div",{key:e.shopId,staticClass:"account-item"},[i("p",{staticClass:"item-title"},[t._v("店铺信息")]),t._v(" "),i("div",{staticClass:"account-wrap flex"},[i("div",{staticClass:"account-avatar"},[e.icon?i("img",{attrs:{src:e.icon,alt:""}}):i("img",{attrs:{src:a("5QbW"),alt:""}})]),t._v(" "),i("div",{staticClass:"account-info"},[i("div",[i("div",{staticClass:"account-name"},[t._v(t._s(e.storename||e.username))]),t._v(" "),1==e.state?i("div",{staticClass:"account-tips"},[i("img",{attrs:{src:a("2dv1"),alt:""}}),t._v(" "),i("span",[t._v("账号不可用")])]):t._e()]),t._v(" "),i("div",{staticClass:"action flex"},[i("div",{staticClass:"cancel-bind",on:{click:function(a){return t.handleCancelBind(e.id)}}},[i("img",{attrs:{src:a("KHy4"),alt:"解除绑定"}}),t._v(" "),i("span",[t._v("取消店铺授权")])]),t._v(" "),i("div",{staticClass:"enter-store",on:{click:function(a){return t.handleEnterStore(e)}}},[t._v("进入店铺")])])])])])}),t._v(" "),i("div",{staticClass:"account-item"},[i("p",{staticClass:"item-title"},[t._v("新增店铺")]),t._v(" "),i("TaoBaoLogin",{attrs:{shopInfo:t.shopInfo},on:{"bind-success":t.handleBindSuccess}})],1)],2)]:[i("p",{staticClass:"tips"},[t._v("您尚未绑定您的店铺，授权绑定后，您可实时查看您的店铺数据并及时进行管理。")]),t._v(" "),i("div",{staticClass:"content"},[i("TaoBaoLogin",{attrs:{shopInfo:t.shopInfo},on:{"bind-success":t.handleBindSuccess}})],1)]],2)},staticRenderFns:[]};var c={name:"tips",components:{StoreBinding:a("VU/8")(o,r,!1,function(t){a("RwPx")},"data-v-a03ac1a0",null).exports},props:{show:{type:Boolean,default:!1},content:{type:Object,default:function(){}}},computed:{_show:{get:function(){return this.show},set:function(t){this.$emit("update:show",t)}},showBtn:function(){return this.content.link},showBindAccount:function(){switch(this.content.title){case"淘宝":case"京东":case"小红书":case"抖音":return!0;default:return!1}}},methods:{toUrl:function(){this._show=!1,this.content.link&&(-1===this.content.link.indexOf("http")&&-1===this.content.link.indexOf("https")&&(this.content.link="http://"+this.content.link),window.open(this.content.link,"_blank"))}}},l={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return t._show?i("div",{attrs:{id:"tips"}},[i("div",{staticClass:"mask",on:{click:function(e){t._show=!1}}}),t._v(" "),i("div",{staticClass:"content-wrap"},[i("div",{staticClass:"content"},[i("div",{staticClass:"top-bg",style:"background-image: url('"+a("Cn2C")+"')"}),t._v(" "),i("div",{staticClass:"close-btn",on:{click:function(e){t._show=!1}}},[i("img",{attrs:{src:a("3C70"),alt:""}})]),t._v(" "),i("div",{staticClass:"content-box flex-column"},[i("div",{staticClass:"title flex"},[i("img",{attrs:{src:t.content.icon,alt:""}}),t._v(" "),i("span",[t._v(t._s(t.content.title))])]),t._v(" "),t.showBindAccount?i("store-binding",{attrs:{shopInfo:t.content}}):t._e(),t._v(" "),i("div",{staticClass:"flex-1"},[i("div",{staticClass:"tips",domProps:{innerHTML:t._s(t.content.content)}})]),t._v(" "),t.showBtn?i("div",{staticClass:"btn-box"},[i("button",{on:{click:t.toUrl}},[t._v("进入")])]):t._e()],1),t._v(" "),i("div",{staticClass:"bottom-bg",style:"background-image: url('"+a("7Wvg")+"')"})])])]):t._e()},staticRenderFns:[]};var d=a("VU/8")(c,l,!1,function(t){a("ARPZ")},"data-v-5fb63673",null);e.a=d.exports},ARPZ:function(t,e){},"D+vD":function(t,e){},FoDU:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("Xxa5"),s=a.n(i),n=a("exGp"),o=a.n(n),r=a("9FlD"),c=a("Aw0F"),l=a("SgJ7"),d=(a("8PcR"),a("JYzS")),u=a("44VP"),h=a("+f9A"),p=a("ev7b"),m=a("+Xza"),v=a("LGqE"),f={"私域电商":1,"传统电商":2,"带货电商":3},g={components:{AiToolsCard:p.a,CateItem:h.a,CustomDialog:u.a,Tips:r.a,PageWrap:c.a,Dashboard:m.default},mixins:[d.a,v.a],data:function(){return{pageNav:[{name:"E_commerce",title:"全域电商",path:"/console/display/E_commerce/index",pathType:"page"}],loading:!1,list:[{title:"我的电商",category:[{title:"私域电商",id:1,list:[]},{title:"传统电商",id:2,list:[]},{title:"带货电商",id:3,list:[]}]}],e_commerce:[{name:"私域电商",list:[]},{name:"传统电商",cate:[],list:[]},{name:"带货电商",list:[]}],activeCate:0,isEdit:!1,content:{},show:!1,isJzt:!1,isJztShop:!1,jztShopUrl:"",jztUrl:"",jzt_yxurl:"",cateMap:f,myData:[]}},mounted:function(){this.getList(),this.getTraditionCate(),this.getJztLink()},watch:{myData:{handler:function(t){var e=this;t.length>0?this.list[0].category.forEach(function(a,i){var s=t.filter(function(t){return t.type===f[a.title]});e.$set(a,"list",s)}):this.list[0].category.forEach(function(t,a){e.$set(t,"list",[])})}}},methods:{getJztLink:function(){var t=this;return o()(s.a.mark(function e(){var a;return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(l.s)();case 2:200===(a=e.sent).code&&a.data&&(t.isJzt=a.data.jzt_ck,t.isJztShop=a.data.jztshop_ck,t.jztShopUrl=a.data.jztshop_url,t.jztUrl=a.data.jzt_url,t.jzt_yxurl=a.data.jzt_yxurl);case 4:case"end":return e.stop()}},e,t)}))()},getTraditionCate:function(){var t=this;return o()(s.a.mark(function e(){var a;return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(l.j)();case 2:200===(a=e.sent).code&&a.data&&(a.data.unshift({id:0,name:"全部"}),t.$set(t.e_commerce[1],"cate",a.data));case 4:case"end":return e.stop()}},e,t)}))()},getListByCate:function(t){var e=this;return o()(s.a.mark(function a(){var i;return s.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return e.$set(e.e_commerce[1],"loading",!0),a.next=3,Object(l.g)({perPage:99999,class_id:t});case 3:200===(i=a.sent).code&&i.data&&e.$set(e.e_commerce[1],"list",i.data.data),e.$set(e.e_commerce[1],"loading",!1);case 6:case"end":return a.stop()}},a,e)}))()},getList:function(t){var e=this;return o()(s.a.mark(function a(){var i,n,o;return s.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return e.activeCate=0,t&&(e.loading=!0),a.next=4,Object(l.r)();case 4:if(200!==(i=a.sent).code||!i.data){a.next=17;break}if("privatelist"in i.data&&e.$set(e.e_commerce,0,i.data.privatelist),!("traditionlist"in i.data)){a.next=15;break}return n=e.e_commerce[1].cate,e.$set(e.e_commerce,1,i.data.traditionlist),e.$set(e.e_commerce[1],"cate",n),a.next=13,Object(l.h)();case 13:200===(o=a.sent).code&&o.data&&e.$set(e.e_commerce[1],"list",o.data);case 15:"takegoodslist"in i.data&&e.$set(e.e_commerce,2,i.data.takegoodslist),"userlist"in i.data&&(e.myData=i.data.userlist.list||[]);case 17:e.loading=!1;case 18:case"end":return a.stop()}},a,e)}))()},jump:function(t){var e=t[arguments.length>1&&void 0!==arguments[1]?arguments[1]:"link"];return t.cktype&&t.cktype.includes("jzt")&&"jzt_shop"!==t.cktype?(this.$store.state.buyGoodsList.includes("jzt")||this.$store.state.buyGoodsList.includes("jztAI")||this.$store.state.buyGoodsList.includes("jztzyb"))&&(e="jzt"===t.cktype?this.jztUrl||e:"jzt_yxxyx"===t.cktype?this.jzt_yxurl||e:"jzt_h5"===t.cktype?t.cklink||e:t.cklink||this.jztUrl||e):"jzt_shop"===t.cktype?this.$store.state.buyGoodsList.includes("jztscb")&&(e=this.jztShopUrl||e):"ymp"===t.cktype&&this.$store.state.buyGoodsList.includes("ymp")&&(t.cklink&&(t.cklink="https://siyuds.zihaiwangluo.com/admin/login?code="+this.$store.state.user.memberInfo.uid),e=t.cklink||e),e||t.content?e&&(e.includes("http://")||e.includes("https://")||(e="http://"+e),!t.content)?window.open(e,"__blank"):(this.content=t,this.content.link=e,void(this.content.content&&(this.show=!0))):this.$message.error("暂无配置链接")},close:function(){this.show=!1},getColSpan:function(t){return t.cate&&t.cate.length?{lg:{span:5},md:{span:8}}:{lg:12,md:24}},refresh:function(t,e,a){switch(t){case"del":this.myData=this.myData.filter(function(t){return t.id!==e});break;case"add":this.myData.push(a);break;case"refresh":this.getList()}}}},y={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("page-wrap",{attrs:{nav:t.pageNav}},[i("ai-tools-card",{attrs:{"display-type":2,"more-text":"查看更多","col-settings":{xl:3,lg:3}}}),t._v(" "),i("div",{staticClass:"gap-h-30"}),t._v(" "),i("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"list-view"},[i("div",{staticClass:"my-wrap"},[t._l(t.list,function(e,s){return[i("div",{staticClass:"list-container"},[i("div",{staticClass:"top-box flex"},[i("div",{staticClass:"list-title"},[t._v(t._s(e.title))]),t._v(" "),0===s?i("div",{class:["edit-box","flex",{show:t.isEdit}],on:{click:function(e){t.isEdit=!t.isEdit}}},[i("img",{attrs:{src:a("cGnt"),alt:""}}),t._v(" "),i("span",[t._v("自定义")])]):t._e()]),t._v(" "),e.category?t._l(e.category,function(e,a){return i("div",{key:a,staticClass:"category-list"},[i("div",{staticClass:"subtitle"},[t._v(t._s(e.title))]),t._v(" "),e.list&&!t.loading?i("div",{staticClass:"list-content"},[e.list.length?t._l(e.list,function(e,a){return i("div",{key:a,staticClass:"list-item"},[i("div",{on:{click:function(a){return t.jump(e,"url")}}},[i("div",{staticClass:"flex"},[i("div",{staticClass:"icon-box"},[e.icon?i("img",{attrs:{src:e.icon,alt:"v.title"}}):i("div",{staticClass:"icon-box-bg"},[t._v("\n                            "+t._s(e.title.substring(0,1))+"\n                          ")])]),t._v(" "),i("div",{staticClass:"info"},[i("div",{class:["name ellipsis",{"edit-width":t.isEdit}]},[t._v("\n                            "+t._s(e.title)+"\n                          ")])])]),t._v(" "),i("div",{staticClass:"desc"},[t._v(t._s(e.description))])])])}):[t.isEdit?i("div",[i("div",{staticClass:"list-item first self-item flex placeholder"})]):i("div",{staticClass:"list-item first self-item flex",on:{click:function(e){t.isEdit=!0}}},[i("div",{staticClass:"icon-box add"}),t._v(" "),i("div",{staticClass:"tips"},[t._v("请从下方添加电商网站")])])]],2):t._e(),t._v(" "),t.loading?i("div",{staticClass:"list-content"},[i("el-skeleton",{staticClass:"list-item",attrs:{animated:"",rows:1,throttle:500}},[i("template",{slot:"template"},[i("el-skeleton-item",{staticStyle:{width:"38px",height:"38px","border-radius":"50%"},attrs:{variant:"image"}}),t._v(" "),i("el-skeleton-item",{staticStyle:{width:"calc(100% - 38px - 14px)","margin-left":"14px"},attrs:{variant:"p"}})],1)],2)],1):t._e()])}):t._e()],2)]})],2),t._v(" "),t.e_commerce.length?i("div",{staticClass:"list-wrap flex-align-stretch"},t._l(t.e_commerce,function(e,a){return i("div",{key:a,staticClass:"list-wrap-box",class:["item-wrap",{"flex-1":e.cate&&e.cate.length}]},[i("div",{staticClass:"top-box flex"},[i("div",{staticClass:"list-title"},[t._v(t._s(e.name))])]),t._v(" "),i("div",{staticClass:"list-item"},[i("cate-item",{attrs:{item:e,"my-data":t.myData,"col-span":t.getColSpan(e),"show-btn":!1,isFilter:!0}})],1)])}),0):t._e()]),t._v(" "),i("tips",{attrs:{show:t.show,content:t.content},on:{"update:show":function(e){t.show=e}}}),t._v(" "),i("custom-dialog",{attrs:{show:t.isEdit,"cate-map":t.cateMap,myData:t.myData,list:t.allShopList,isFilter:!0},on:{"update:show":function(e){t.isEdit=e},refresh:t.refresh}})],1)},staticRenderFns:[]};var x=a("VU/8")(g,y,!1,function(t){a("+04b")},"data-v-7768584c",null);e.default=x.exports},IJSP:function(t,e){},LGqE:function(t,e,a){"use strict";var i=a("Gu7T"),s=a.n(i),n=a("Xxa5"),o=a.n(n),r=a("mvHQ"),c=a.n(r),l=a("exGp"),d=a.n(l),u=a("SgJ7"),h={"私域电商":1,"传统电商":2,"带货电商":3};e.a={data:function(){return{loading:!1,myShopData:[],myShopDataList:function(){var t=[];for(var e in h)t.push({type:h[e],title:e,name:e,list:[]});return t}(),allShopList:function(){var t=[];for(var e in h){var a={type:h[e],title:e,name:e,list:[]};2==h[e]&&(a.cate=[]),t.push(a)}return t}(),show:!1,content:{},isJzt:!1,isJztShop:!1,jztShopUrl:"",jztUrl:"",jzt_yxurl:"",isEdit:!1,cateMap:h}},computed:{},created:function(){this.getJztLink(),this.getMyShopData(),console.log(this.myShopDataList),console.log(this.allShopList)},methods:{getMyShopData:function(){var t=this;return d()(o.a.mark(function e(){var a,i,s,n,r,l,d;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(a=t).loading){e.next=3;break}return e.abrupt("return");case 3:return a.loading=!0,e.prev=4,e.next=7,Object(u.r)();case 7:i=e.sent,console.log(i,"获取我的电商平台数据"),200===i.code&&i.data&&(s=i.data,n=s.privatelist,r=s.takegoodslist,(l=s.userlist)&&l.list&&l.list.length&&(a.myShopData=l.list,a.setMyShopDataList()),(d=JSON.parse(c()(a.allShopList))).forEach(function(t){1==t.type&&(t.list=n&&n.list&&n.list.length?n.list:[]),3==t.type&&(t.list=r&&r.list&&r.list.length?r.list:[])}),a.$set(a,"allShopList",d),a.getTraditionEcommerce(),a.getTraditionEcommerceNew()),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(4),console.log(e.t0,"获取我的电商平台数据");case 15:return e.prev=15,a.loading=!1,e.finish(15);case 18:case"end":return e.stop()}},e,t,[[4,12,15,18]])}))()},setMyShopDataList:function(){var t=this.myShopData,e=JSON.parse(c()(this.myShopDataList));e.forEach(function(e){e.list=t.filter(function(t){return t.type==e.type})}),this.$set(this,"myShopDataList",e)},getTraditionEcommerce:function(){var t=this;return d()(o.a.mark(function e(){var a,i,n;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a=t,e.next=3,Object(u.j)();case 3:200===(i=e.sent).code&&i.data&&((n=JSON.parse(c()(a.allShopList))).forEach(function(t){2==t.type&&(t.cate=[{id:0,name:"全部"}].concat(s()(i.data)))}),a.$set(a,"allShopList",n));case 5:case"end":return e.stop()}},e,t)}))()},getTraditionEcommerceNew:function(){var t=this;return d()(o.a.mark(function e(){var a,i,s;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a=t,e.next=3,Object(u.h)();case 3:200===(i=e.sent).code&&i.data&&((s=JSON.parse(c()(a.allShopList))).forEach(function(t){2==t.type&&(t.list=i.data)}),a.$set(a,"allShopList",s));case 5:case"end":return e.stop()}},e,t)}))()},refreshMyShopData:function(t,e,a){switch(console.log(t,e,a),t){case"add":this.myShopData.push(a),this.setMyShopDataList();break;case"del":this.myShopData=this.myShopData.filter(function(t){return t.id!=e}),this.setMyShopDataList();break;case"refresh":this.getMyShopData()}},getJztLink:function(){var t=this;return d()(o.a.mark(function e(){var a,i;return o.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a=t,e.next=3,Object(u.s)();case 3:200===(i=e.sent).code&&i.data&&(a.isJzt=i.data.jzt_ck,a.isJztShop=i.data.jztshop_ck,a.jztShopUrl=i.data.jztshop_url,a.jztUrl=i.data.jzt_url,a.jzt_yxurl=i.data.jzt_yxurl);case 5:case"end":return e.stop()}},e,t)}))()},jump:function(t){var e=t[arguments.length>1&&void 0!==arguments[1]?arguments[1]:"link"];return console.log(this.$store.state.buyGoodsList),t.cktype&&t.cktype.includes("jzt")&&"jzt_shop"!==t.cktype?(this.$store.state.buyGoodsList.includes("jzt")||this.$store.state.buyGoodsList.includes("jztAI")||this.$store.state.buyGoodsList.includes("jztzyb"))&&(e="jzt"===t.cktype?this.jztUrl||e:"jzt_yxxyx"===t.cktype?this.jzt_yxurl||e:"jzt_h5"===t.cktype?t.cklink||e:t.cklink||this.jztUrl||e):"jzt_shop"===t.cktype?this.$store.state.buyGoodsList.includes("jztscb")&&(e=this.jztShopUrl||e):"ymp"===t.cktype&&this.$store.state.buyGoodsList.includes("ymp")&&(t.cklink&&(t.cklink="https://siyuds.zihaiwangluo.com/admin/login?code="+this.$store.state.user.memberInfo.uid),e=t.cklink||e),e||t.content?e&&(e.includes("http://")||e.includes("https://")||(e="http://"+e),!t.content)?window.open(e,"__blank"):(this.content=t,this.content.link=e,void(this.content.content&&(this.show=!0))):this.$message.error("暂无配置链接")},close:function(){this.show=!1}}}},OjgP:function(t,e,a){"use strict";var i={name:"ListItem",props:{data:{type:Array,default:function(){return[]}},colSpan:{type:Object,default:function(){return{}}},activeCate:{type:Number,default:0},isFilter:{type:Boolean,default:!1}},methods:{isShowItem:function(t){return!this.isFilter||(0===this.activeCate||t.class_id===this.activeCate)}}},s={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-row",{staticClass:"flex-align-stretch flex-wrap",attrs:{gutter:20}},[t._l(t.data,function(e,i){return[t.isShowItem(e)?a("el-col",{attrs:{lg:t.colSpan.lg,md:t.colSpan.md,sm:t.colSpan.sm,xl:t.colSpan.xl,xs:t.colSpan.xs}},[a("div",{staticClass:"flex-align-center item"},[e.icon?a("img",{staticClass:"icon-box-bg",attrs:{src:e.icon,alt:e.title}}):a("div",{staticClass:"icon-box-bg"},[t._v("\n          "+t._s(e.title.substring(0,1))+"\n        ")]),t._v(" "),a("div",{staticClass:"name text-ellipsis",attrs:{title:e.title}},[t._v(t._s(e.title))]),t._v(" "),t._t("default",null,{listItem:e})],2)]):t._e()]})],2)},staticRenderFns:[]};var n=a("VU/8")(i,s,!1,function(t){a("IJSP")},"data-v-024f1f95",null);e.a=n.exports},"P/Bh":function(t,e){},RwPx:function(t,e){},UDaK:function(t,e){},aB6U:function(t,e,a){var i={"./address.png":"jBjN","./eye-.png":"h7ED","./eye.png":"rR46","./no-logo.png":"5QbW","./shop01.png":"gaoR","./shop02.png":"YI+f","./shop03.png":"DJvx","./shop04.png":"DBS8","./shop05.png":"RduG","./tel.png":"U02y","./tips.png":"2dv1","./unbind.png":"KHy4","./warning.png":"AUM0"};function s(t){return a(n(t))}function n(t){var e=i[t];if(!(e+1))throw new Error("Cannot find module '"+t+"'.");return e}s.keys=function(){return Object.keys(i)},s.resolve=n,t.exports=s,s.id="aB6U"},gZ2d:function(t,e){},lOm0:function(t,e){},mrr6:function(t,e){},tU0q:function(t,e){},uqMR:function(t,e){},"x/yo":function(t,e){}});