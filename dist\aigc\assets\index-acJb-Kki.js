import{k as h,A as r,B as C,c as o,q as y,m as p,J as b,F as k,s as P,a as w,P as z,o as s,b as d,t as g,_ as L}from"./index-BBeD0eDz.js";/* empty css                */import{d as B}from"./index-sm6CVty6.js";import"./request-Ciyrqj7N.js";const S={class:"h-full flex flex-col"},N={class:"flex-1 overflow-auto"},V=["onClick"],$={class:"flex-1 flex items-center justify-between overflow-hidden"},j={class:"flex-1 text-[16px] text-[#111111] truncate"},A={class:"text-[16px] text-[#555555] ml-[20px]"},D={key:0,class:"py-8 text-center text-gray-500"},F={key:0,class:"flex items-center justify-center"},H=h({__name:"index",setup(U){const n=r(!1),i=r(0),t=r({perPage:10,page:1}),c=r([]),u=async()=>{n.value=!0;try{const e=await B(t.value);console.log(e,"帮助中心列表"),c.value=e.data,i.value=e.total}catch(e){console.log(e)}finally{n.value=!1}},v=e=>{t.value.perPage=e,u()},_=e=>{t.value.page=e,u()};return C(()=>{u()}),(e,l)=>{const f=z,m=b;return s(),o("div",S,[y((s(),o("div",N,[(s(!0),o(k,null,P(c.value,(a,x)=>(s(),o("div",{key:x,class:"notice-item flex items-center cursor-pointer hover:bg-gray-50 p-4 border-t border-gray-100 last:border-b",onClick:q=>e.$router.push(`/notice/${a.id}`)},[d("div",$,[d("div",j,g(a.title),1),d("div",A,g(a.publish_at),1)])],8,V))),128)),!n.value&&c.value.length===0?(s(),o("div",D," 暂无数据 ")):p("",!0)])),[[m,n.value]]),i.value>0?(s(),o("div",F,[w(f,{"current-page":t.value.page,"onUpdate:currentPage":l[0]||(l[0]=a=>t.value.page=a),"page-size":t.value.perPage,"onUpdate:pageSize":l[1]||(l[1]=a=>t.value.perPage=a),total:i.value,onSizeChange:v,onCurrentChange:_},null,8,["current-page","page-size","total"])])):p("",!0)])}}}),Q=L(H,[["__scopeId","data-v-8c44d149"]]);export{Q as default};
