webpackJsonp([47],{"+LOP":function(e,t){},MnEC:function(e,t){},Ocm9:function(e,t){},oaOg:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=a("ehIy"),r={name:"invoice-add",props:{dialogVisible:{required:!1,type:<PERSON><PERSON>an,default:function(){return!1}}},data:function(){return{formData:{},tableLoading:!1,rules:{type:[{required:!0,message:"请选择发票类型",trigger:"blur"}],name_type:[{required:!0,message:"请选择抬头类型",trigger:"blur"}],name:[{required:!0,message:"请输入发票抬头",trigger:"blur"}]}}},created:function(){},methods:{setFormData:function(e){this.formData=e},store:function(){var e=this;1===this.formData.name_type&&(this.formData.name="个人"),2!==this.formData.name_type&&(this.formData.type=2),this.$refs.dataForm.validate(function(t){if(t){e.tableLoading=!0;var a={type:e.formData.type,name_type:e.formData.name_type,name:e.formData.name,is_default:e.formData.is_default};e.formData.id&&(a.id=e.formData.id),2===e.formData.name_type&&(a.code=e.formData.code,a.bank_name=e.formData.bank_name,a.bank_number=e.formData.bank_number,a.company_addr=e.formData.company_addr,a.company_phone=e.formData.company_phone),Object(o.l)(a).then(function(t){e.tableLoading=!1,e.$emit("getInfoList"),e.close()}).catch(function(){e.tableLoading=!1})}})},close:function(){this.$emit("closepop")}}},n={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.formData.id?"修改发票信息":"新增发票信息",visible:e.dialogVisible,width:"700px","before-close":e.close},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-card",{staticClass:"box-card",attrs:{"v-loading":e.tableLoading}},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],ref:"dataForm",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.formData,rules:e.rules,size:"small"}},[a("div",{staticClass:"container"},[a("el-form-item",{attrs:{label:"抬头类型:",prop:"title"}},[a("el-radio-group",{model:{value:e.formData.name_type,callback:function(t){e.$set(e.formData,"name_type",t)},expression:"formData.name_type"}},[a("el-radio",{attrs:{label:2}},[e._v("企业")]),e._v(" "),a("el-radio",{attrs:{label:1}},[e._v("个人")]),e._v(" "),a("el-radio",{attrs:{label:3}},[e._v("组织")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"发票抬头:",prop:"desc"}},[1===e.formData.name_type?a("span",[e._v("个人")]):2===e.formData.name_type?a("el-input",{attrs:{placeholder:"请输入企业名称"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}}):3===e.formData.name_type?a("el-input",{attrs:{placeholder:"请输入组织名称"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}}):e._e()],1),e._v(" "),2===e.formData.name_type?a("el-form-item",{attrs:{label:"发票类型:"}},[a("el-radio",{attrs:{label:1},model:{value:e.formData.type,callback:function(t){e.$set(e.formData,"type",t)},expression:"formData.type"}},[e._v("增值税普通发票")]),e._v(" "),a("el-radio",{attrs:{label:2},model:{value:e.formData.type,callback:function(t){e.$set(e.formData,"type",t)},expression:"formData.type"}},[e._v("增值税专用发票")])],1):a("el-form-item",{attrs:{label:"发票类型:"}},[a("span",[e._v("增值税普通发票")])]),e._v(" "),2===e.formData.name_type?a("div",[a("el-form-item",{attrs:{label:"税务登记证号/统一社会信用代码:",prop:"code"}},[a("el-input",{attrs:{placeholder:"税务登记证号/统一社会信用代码"},model:{value:e.formData.code,callback:function(t){e.$set(e.formData,"code",t)},expression:"formData.code"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"基本户开户银行:",prop:"bank_name"}},[a("el-input",{attrs:{placeholder:"请输入开户许可证上的开户银行"},model:{value:e.formData.bank_name,callback:function(t){e.$set(e.formData,"bank_name",t)},expression:"formData.bank_name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"基本户开户账号:",prop:"bank_number"}},[a("el-input",{attrs:{placeholder:"请输入开户许可证上的银行账号"},model:{value:e.formData.bank_number,callback:function(t){e.$set(e.formData,"bank_number",t)},expression:"formData.bank_number"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"企业注册地址",prop:"company_addr"}},[a("el-input",{attrs:{placeholder:"请输入营业执照上的注册地址"},model:{value:e.formData.company_addr,callback:function(t){e.$set(e.formData,"company_addr",t)},expression:"formData.company_addr"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"企业注册电话:",prop:"company_phone"}},[a("el-input",{attrs:{placeholder:"请输入企业有效的联系电话"},model:{value:e.formData.company_phone,callback:function(t){e.$set(e.formData,"company_phone",t)},expression:"formData.company_phone"}})],1)],1):e._e(),e._v(" "),a("el-form-item",{staticStyle:{display:"block"},attrs:{label:"",prop:"status"}},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:e.formData.is_default,callback:function(t){e.$set(e.formData,"is_default",t)},expression:"formData.is_default"}},[e._v("设为默认抬头")])],1)],1),e._v(" "),a("div",{staticClass:"view-btn"},[a("el-button",{attrs:{type:"primary"},on:{click:e.store}},[e._v(e._s(e.formData.id?"修改":"新增"))]),e._v(" "),a("el-button",{attrs:{type:"warning"},on:{click:e.close}},[e._v("取消")])],1)])],1)],1)},staticRenderFns:[]};var i=a("VU/8")(r,n,!1,function(e){a("Ocm9")},"data-v-f9b6756a",null).exports,s=a("mvHQ"),l=a.n(s),c=a("pI5c"),d={name:"invoice-address",props:{dialogVisible:{required:!1,type:Boolean,default:function(){return!1}}},data:function(){return{formLoading:!1,formData:{},options:{},props:{value:"id",label:"name",children:"children"},rules:{name:[{required:!0,message:"收件人姓名不能为空",trigger:"blur"}],city:[{required:!0,message:"所在地区不能为空",trigger:"blur"}],address:[{required:!0,message:"详细地址不能为空",trigger:"blur"}],code:[{required:!0,message:"邮政编码不能为空",trigger:"blur"}],phone:[{required:!0,message:"手机号码不能为空",trigger:"blur"}]}}},created:function(){this.getOptions()},methods:{setFormData:function(e){"{}"===l()(e)||e.city[2]||e.city.splice(2,1),this.formData=e},getOptions:function(){var e=this;Object(c.b)().then(function(t){e.options=t.data,e.options.ORGANIZATION_CITY=e.getTreeData(e.options.ORGANIZATION_CITY)})},requestStore:function(){var e=this;this.$refs.formData.validate(function(t){t&&(e.formLoading=!0,Object(o.d)(e.formData).then(function(t){e.formLoading=!1,e.$emit("getAddressList"),e.close()}).catch(function(){e.formLoading=!1}))})},getTreeData:function(e){for(var t=0;t<e.length;t++)e[t].children.length<1?e[t].children=void 0:this.getTreeData(e[t].children);return e},close:function(){this.$emit("closepop")}}},u={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.formData.id?"修改收件地址":"新增收件地址",visible:e.dialogVisible,width:"700px","before-close":e.close},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-card",{staticClass:"box-card"},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.formLoading,expression:"formLoading"}],ref:"formData",staticClass:"demo-form-inline",attrs:{inline:!0,model:e.formData,rules:e.rules,size:"small"}},[a("div",{staticClass:"container"},[a("el-form-item",{attrs:{label:"收件人姓名:",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入收件人姓名"},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"所在地区:",prop:"city"}},[a("el-cascader",{attrs:{options:e.options.ORGANIZATION_CITY,props:e.props},model:{value:e.formData.city,callback:function(t){e.$set(e.formData,"city",t)},expression:"formData.city"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"详细地址:",prop:"address"}},[a("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入有效地址"},model:{value:e.formData.address,callback:function(t){e.$set(e.formData,"address",t)},expression:"formData.address"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"邮政编码:",prop:"code"}},[a("el-input",{attrs:{placeholder:"请输入邮政编码"},model:{value:e.formData.code,callback:function(t){e.$set(e.formData,"code",t)},expression:"formData.code"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"手机号码:",prop:"phone"}},[a("el-input",{attrs:{placeholder:"请输入您的手机号码"},model:{value:e.formData.phone,callback:function(t){e.$set(e.formData,"phone",t)},expression:"formData.phone"}})],1)],1),e._v(" "),a("div",{staticClass:"view-btn"},[a("el-button",{attrs:{type:"primary"},on:{click:e.requestStore}},[e._v(e._s(e.formData.id?"修改":"新增"))]),e._v(" "),a("el-button",{attrs:{type:"warning"},on:{click:e.close}},[e._v("取消")])],1)])],1)],1)},staticRenderFns:[]};var f={name:"applyInvoice",components:{invoiceAdd:i,invoiceAddress:a("VU/8")(d,u,!1,function(e){a("MnEC")},"data-v-17df12f4",null).exports},data:function(){return{routeQuery:this.$G7.decrypt(this.$route.query.info),tableLoading:!1,addressLoading:!1,submitLoading:!1,formQuery:{},tableData:[],addressData:[],invoiceAddVisible:!1,invoiceAddressVisible:!1,amount:0}},created:function(){null!==this.routeQuery&&(this.amount=this.routeQuery.amount),this.getInfoList(),this.getAddressList()},methods:{getInfoList:function(){var e=this;this.tableLoading=!0,Object(o.j)({}).then(function(t){e.tableLoading=!1,e.tableData=t.data}).catch(function(){e.tableLoading=!1})},getAddressList:function(){var e=this;this.addressLoading=!0,Object(o.b)().then(function(t){e.addressData=t.data,e.addressLoading=!1}).catch(function(){e.addressLoading=!1})},setInfoDefault:function(e){var t=this;this.$confirm("设置该发票为默认, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(o.k)({id:e.id}).then(function(e){t.getInfoList()}),t.$message.success("设置成功")}).catch(function(){})},infoAdd:function(){this.$refs.info.setFormData({}),this.invoiceAddVisible=!0},infoEdit:function(e){this.$refs.info.setFormData(e),this.invoiceAddVisible=!0},infoDestory:function(e){var t=this;this.$confirm("此操作将永久删除该发票, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(o.i)({id:e.id}).then(function(e){t.getInfoList()}),t.$message({type:"success",message:"删除成功!"})}).catch(function(){})},setAddressDefault:function(e){var t=this;this.$confirm("设置该地址为默认, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(o.c)({id:e.id}).then(function(e){t.getAddressList()}),t.$message.success("设置成功")}).catch(function(){})},addressAdd:function(){this.$refs.address.setFormData({}),this.invoiceAddressVisible=!0},addressEdit:function(e){this.$refs.address.setFormData(e),this.invoiceAddressVisible=!0},addressDestroy:function(e){var t=this;this.$confirm("此操作将永久删除该地址, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(o.a)({id:e.id}).then(function(e){t.getAddressList()}),t.$message({type:"success",message:"删除成功!"})}).catch(function(){})},handleSelectionChange:function(){},submit:function(){var e=this;return this.formQuery.invoice_id?this.formQuery.address_id?this.routeQuery.ids?(this.formQuery.orders_id=this.routeQuery.ids.split(","),this.submitLoading=!0,void Object(o.e)(this.formQuery).then(function(t){e.submitLoading=!1,e.$message.success(t.message),e.$router.go(-2)}).catch(function(){e.submitLoading=!1})):this.$message.error("没有发票订单"):this.$message.error("请选择一个地址"):this.$message.error("请选择一个发票抬头")},cancel:function(){this.$router.go(-1)}}},m={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-card",{staticClass:"el-box"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("索取发票")])]),e._v(" "),a("el-container",[a("el-main",[a("div",{staticStyle:{"margin-bottom":"30px"}},[a("p",{staticStyle:{color:"red"}},[e._v("1.消费完成的订单请在消费日起30天内索取发票")]),e._v(" "),a("p",{staticStyle:{display:"inline-block"}},[e._v("2.一张发票只能有一个税率。当开具发票的税率有多个时，系统会自动生成多张发票。发票内容和发票税率的详细信息请参见")]),e._v(" "),a("a",[e._v("发票内容和发票税率说明")])]),e._v(" "),a("div",{staticClass:"header-invoice"},[a("h1",[e._v("发票信息")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.infoAdd}},[e._v("新增发票信息")])],1),e._v(" "),a("div",{staticClass:"table",staticStyle:{"margin-top":"20px"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{width:"55"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-radio",{attrs:{label:t.row.id},model:{value:e.formQuery.invoice_id,callback:function(t){e.$set(e.formQuery,"invoice_id",t)},expression:"formQuery.invoice_id"}},[e._v(" ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"发票抬头"}}),e._v(" "),a("el-table-column",{attrs:{label:"抬头类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.type?a("span",[e._v("增值税普通发票")]):e._e(),e._v(" "),2===t.row.type?a("span",[e._v("增值税专用发票")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"type_text",label:"发票类型"}}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[1!==t.row.is_default?a("span",[a("el-button",{staticStyle:{color:"#ff4d51"},attrs:{type:"text"},on:{click:function(a){return e.setInfoDefault(t.row)}}},[e._v("设为默认")]),e._v(" "),a("el-divider",{attrs:{direction:"vertical"}})],1):e._e(),e._v(" "),a("el-button",{staticStyle:{color:"#3a8ee6"},attrs:{type:"text"},on:{click:function(a){return e.infoEdit(t.row)}}},[e._v("修改")]),e._v(" "),a("el-divider",{attrs:{direction:"vertical"}}),e._v(" "),a("el-button",{staticStyle:{color:"#ef3420"},attrs:{type:"text"},on:{click:function(a){return e.infoDestory(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),a("div",{staticClass:"header-address"},[a("div",[e._v("纸质发票")]),e._v(" "),a("div",[a("p",[e._v("申请纸质发票")]),e._v(" "),a("p",{staticStyle:{color:"#cf9236"}},[e._v("如开票金额不足100元,快递费用需要自行承担。")]),e._v(" "),a("p",[e._v("申请成功后,纸质发票预计")]),e._v(" "),a("p",{staticStyle:{color:"#cf9236"}},[e._v("3个工作日内")]),e._v(" "),a("p",[e._v("通过顺丰快递寄出")])]),e._v(" "),a("h1",[e._v("请选择收件地址")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.addressAdd}},[e._v("新增收件地址")])],1),e._v(" "),a("div",{staticClass:"table",staticStyle:{"margin-top":"20px"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.addressLoading,expression:"addressLoading"}],staticStyle:{width:"100%"},attrs:{data:e.addressData,stripe:""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{width:"55"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-radio",{attrs:{label:t.row.id},model:{value:e.formQuery.address_id,callback:function(t){e.$set(e.formQuery,"address_id",t)},expression:"formQuery.address_id"}},[e._v(" ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"姓名"}}),e._v(" "),a("el-table-column",{attrs:{prop:"phone",label:"手机号码"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("phoneEncryption")(t.row.phone)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"收件地址"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.area[0]))]),e._v(" "),a("span",[e._v(e._s(t.row.area[1]))]),e._v(" "),t.row.area.length>2?a("span",[e._v(e._s(t.row.area[2]))]):e._e(),e._v(" "),a("span",[e._v(e._s(t.row.address))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[1!==t.row.is_default?a("span",[a("el-button",{staticStyle:{color:"#ff4d51"},attrs:{type:"text"},on:{click:function(a){return e.setAddressDefault(t.row)}}},[e._v("设为默认")]),e._v(" "),a("el-divider",{attrs:{direction:"vertical"}})],1):e._e(),e._v(" "),a("el-button",{staticStyle:{color:"#3a8ee6"},attrs:{type:"text"},on:{click:function(a){return e.addressEdit(t.row)}}},[e._v("修改")]),e._v(" "),a("el-divider",{attrs:{direction:"vertical"}}),e._v(" "),a("el-button",{staticStyle:{color:"#ef3420"},attrs:{type:"text"},on:{click:function(a){return e.addressDestroy(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),e.routeQuery?a("div",{staticClass:"footer"},[a("p",[e._v("待开票金额:")]),e._v(" "),a("p",[e._v("￥"+e._s(e.amount))]),e._v(" "),a("div",[a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.submit()}}},[e._v("下一步")]),e._v(" "),a("el-button",{attrs:{type:"warning"},on:{click:e.cancel}},[e._v("取消")])],1)]):e._e()])],1)],1),e._v(" "),a("invoiceAdd",{ref:"info",attrs:{dialogVisible:e.invoiceAddVisible},on:{getInfoList:function(t){return e.getInfoList()},closepop:function(t){e.invoiceAddVisible=!1}}}),e._v(" "),a("invoiceAddress",{ref:"address",attrs:{dialogVisible:e.invoiceAddressVisible},on:{getAddressList:function(t){return e.getAddressList()},closepop:function(t){e.invoiceAddressVisible=!1}}})],1)},staticRenderFns:[]};var p=a("VU/8")(f,m,!1,function(e){a("+LOP")},"data-v-3a260d5a",null);t.default=p.exports}});