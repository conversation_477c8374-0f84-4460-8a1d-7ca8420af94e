webpackJsonp([26],{BO1k:function(t,e,a){t.exports={default:a("fxRn"),__esModule:!0}},CNqO:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=a("mvHQ"),i=a.n(n),r=a("BO1k"),o=a.n(r),s=a("Xxa5"),l=a.n(s),c=a("exGp"),u=a.n(c),h=a("Dd8w"),p=a.n(h),d=a("vLgD");function g(t){return Object(d.a)({url:"/api/government/list",method:"POST",data:t})}var f=a("GBNt"),v=a.n(f),y=a("Aw0F"),m=a("kqGT"),x=a("iayf"),w={name:"policy",components:{Home:m.a,PageWrap:y.a},data:function(){return{showTitle:!0,navs:x.a,policyCate:[],address:{},province:{},dialogVisible:!1,city:"",districtList:[],provinceList:[],pinyinEngine:null,selectedCity:{},charteredCities:["110000","120000","310000","500000"],originalData:[],loading:!1,listLoading:!1}},computed:{cityList:function(){return this.provinceList.map(function(t){return t.child.map(function(e){return p()({},e,{parent:t})})}).flat()}},methods:{getCate:function(){var t=this;return u()(l.a.mark(function e(){var a;return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.listLoading=!0,e.next=3,n=void 0,Object(d.a)({url:"/api/government/categories",method:"POST",data:n});case 3:200===(a=e.sent).code&&(t.policyCate=a.data.map(function(t){return p()({},t,{list:[]})}),t.getCurrentPosition());case 5:case"end":return e.stop()}var n},e,t)}))()},getCurrentPosition:function(){var t=this;AMap.plugin("AMap.CitySearch",function(){(new AMap.CitySearch).getLocalCity(function(e,a){"complete"===e&&"OK"===a.info&&(t.address=a,AMap.plugin("AMap.DistrictSearch",function(){var e;new AMap.DistrictSearch({level:"city"}).search(a.province,(e=u()(l.a.mark(function e(a,n){return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("complete"!==a||"OK"!==n.info){e.next=4;break}return t.province=n.districtList[0],e.next=4,t.getPolicyList();case 4:case"end":return e.stop()}},e,this)})),function(t,a){return e.apply(this,arguments)}))}))})})},getPolicyList:function(){var t=this;return u()(l.a.mark(function e(){var a,n,i,r,s,c,u,h;return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:a=!0,n=!1,i=void 0,e.prev=3,r=o()(t.policyCate);case 5:if(a=(s=r.next()).done){e.next=27;break}if((c=s.value).list=[],u={type_id:c.id},!t.charteredCities.includes(t.address.adcode)){e.next=17;break}if(2!==c.id){e.next=14;break}return e.abrupt("continue",24);case 14:3===c.id&&(u.province_id=t.address.adcode);case 15:e.next=19;break;case 17:2===c.id&&(u.province_id=t.province.adcode),3===c.id&&(u.province_id=t.province.adcode,t.province.adcode===t.address.adcode?u.city_id=void 0:u.city_id=t.address.adcode);case 19:return e.next=21,g(u);case 21:h=e.sent,t.listLoading=!1,200===h.code&&t.$set(c,"list",h.data);case 24:a=!0,e.next=5;break;case 27:e.next=33;break;case 29:e.prev=29,e.t0=e.catch(3),n=!0,i=e.t0;case 33:e.prev=33,e.prev=34,!a&&r.return&&r.return();case 36:if(e.prev=36,!n){e.next=39;break}throw i;case 39:return e.finish(36);case 40:return e.finish(33);case 41:case"end":return e.stop()}},e,t,[[3,29,33,41],[34,,36,40]])}))()},getUrl:function(t){return t?0===t.indexOf("http")||0===t.indexOf("https")?t:"http://"+t:""},getCityList:function(){var t=this;return u()(l.a.mark(function e(){var a;return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.loading=!0,e.next=3,n=void 0,Object(d.a)({url:"/api/government/cityindex",method:"POST",data:n});case 3:a=e.sent,t.loading=!1,200===a.code&&(t.provinceList=a.data,t.originalData=JSON.parse(i()(t.provinceList)));case 6:case"end":return e.stop()}var n},e,t)}))()},searchByName:function(){if(!this.city)return this.provinceList=this.originalData;var t=new v.a(this.cityList,["label"]).query(this.city);t.length&&(this.provinceList=t.map(function(t){return p()({},t.parent,{child:[t]})}))},selectCity:function(t,e){this.selectedCity=p()({},t,{province:e}),this.$set(t,"selected",!0)},changeCity:function(){this.selectedCity.value?(this.address={city:this.selectedCity.label,adcode:this.selectedCity.value},this.province=p()({},this.selectedCity.province,{name:this.selectedCity.province.label,adcode:this.selectedCity.province.value}),this.dialogVisible=!1,this.getPolicyList()):this.$message.warning("请选择城市")}},watch:{dialogVisible:function(t){t&&(this.city="",this.selectedCity={},this.getCityList())},$route:{handler:function(){"false"===this.$route.query.showNav?(this.showTitle=!1,this.navs=[{name:"policy",title:"政府政策",path:"/console/display/administration/policy",pathType:"page"}]):(this.showTitle=!0,this.navs=x.a)},immediate:!0}},mounted:function(){this.getCate()}},b={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("page-wrap",{attrs:{nav:t.navs,"show-title":!0}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"policy-container"},[a("h2",{staticClass:"title"},[t._v("政府政策")]),t._v(" "),a("div",{staticClass:"container"},[a("div",{staticClass:"city"},[t._v("\n        当前城市：\n        "),a("span",[t._v(t._s(t.address.city))]),t._v(" "),a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{icon:"el-icon-refresh",size:"small"},on:{click:function(e){t.dialogVisible=!0}}},[t._v("\n          切换城市\n        ")])],1),t._v(" "),a("div",{staticClass:"cate"},t._l(t.policyCate,function(e){return a("div",{key:e.id,staticClass:"cate-item"},[e.list&&e.list.length?[a("div",{staticClass:"cate-title"},[t._v(t._s(e.name))]),t._v(" "),a("div",{staticClass:"list"},[e.list&&e.list.length?a("el-row",{attrs:{gutter:20}},t._l(e.list,function(e){return a("el-col",{key:e.id,attrs:{lg:3,md:4,sm:6,xs:8}},[e.url?a("a",{staticClass:"name",attrs:{href:t.getUrl(e.url),target:"_blank"}},[t._v(t._s(e.name))]):a("div",{staticClass:"name"},[t._v(t._s(e.name))])])}),1):t._e()],1)]:t._e()],2)}),0)]),t._v(" "),a("el-dialog",{attrs:{title:"选择所在城市",visible:t.dialogVisible,width:"1000px"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-row",[a("el-col",{attrs:{lg:12,sm:24}},[a("el-input",{attrs:{placeholder:"请输入城市/拼音"},model:{value:t.city,callback:function(e){t.city=e},expression:"city"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:function(e){return t.searchByName()}},slot:"append"})],1)],1)],1),t._v(" "),a("el-row",{staticClass:"province-wrap"},t._l(t.provinceList,function(e){return a("el-col",{key:e.value,attrs:{span:24}},[a("el-row",{staticClass:"province",attrs:{gutter:20}},[a("el-col",{staticClass:"province-name",attrs:{span:3}},[a("span",{class:{active:t.selectedCity.value===e.value},on:{click:function(a){return t.selectCity(e,e)}}},[t._v(t._s(e.label))])]),t._v(" "),a("el-col",{attrs:{span:21}},[a("el-row",{attrs:{gutter:10}},t._l(e.child,function(n){return a("el-col",{key:n.value,staticClass:"city-name",attrs:{lg:3,md:4,sm:6,xs:8}},[a("span",{class:{active:t.selectedCity.value===n.value},on:{click:function(a){return t.selectCity(n,e)}}},[t._v("\n                  "+t._s(n.label)+"\n                ")])])}),1)],1)],1)],1)}),1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.changeCity}},[t._v("确认切换")]),t._v(" "),a("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")])],1)],1)],1)])},staticRenderFns:[]};var _=a("VU/8")(w,b,!1,function(t){a("SLaa")},"data-v-73706a0f",null);e.default=_.exports},GBNt:function(t,e,a){var n;n=function(){return function(t){function e(n){if(a[n])return a[n].exports;var i=a[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,e),i.l=!0,i.exports}var a={};return e.m=t,e.c=a,e.d=function(t,a,n){e.o(t,a)||Object.defineProperty(t,a,{configurable:!1,enumerable:!0,get:n})},e.n=function(t){var a=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(a,"a",a),a},e.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},e.p="",e(e.s=2)}([function(t,e,a){"use strict";var n=function(){return function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var a=[],n=!0,i=!1,r=void 0;try{for(var o,s=t[Symbol.iterator]();!(n=(o=s.next()).done)&&(a.push(o.value),!e||a.length!==e);n=!0);}catch(t){i=!0,r=t}finally{try{!n&&s.return&&s.return()}finally{if(i)throw r}}return a}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}();t.exports=function(t){var e=n(t,1)[0],a=t[1].split(","),i={};if(!t.length)return i;for(var r=0,o=0,s=e.length;r<s;r++,o++){var l=e[r];if(l<0)o-=l+1;else{var c=String.fromCharCode(o);if("number"==typeof l)i[c]=[a[l]];else{i[c]=[];for(var u=0,h=l.length;u<h;u++)i[c].push(a[l[u]])}}}return i}},function(t,e,a){"use strict";var n=function(){function t(t,e){for(var a=0;a<e.length;a++){var n=e[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,a,n){return a&&t(e.prototype,a),n&&t(e,n),e}}(),i=function(){function t(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")})(this,t),this.indexs=[],this.history={keyword:"",indexs:[],data:[]},this.data=e,this.dict=n,this.prefix=i,a="string"==typeof a?[a]:a;var r=!0,o=!1,s=void 0;try{for(var l,c=e[Symbol.iterator]();!(r=(l=c.next()).done);r=!0){var u=l.value,h="";if("string"==typeof u)h=t.participle(u,n,i);else{var p=!0,d=!1,g=void 0;try{for(var f,v=a[Symbol.iterator]();!(p=(f=v.next()).done);p=!0){var y=u[f.value];y&&(h+=t.participle(y,n,i))}}catch(t){d=!0,g=t}finally{try{!p&&v.return&&v.return()}finally{if(d)throw g}}}this.indexs.push(h.toLowerCase())}}catch(t){o=!0,s=t}finally{try{!r&&c.return&&c.return()}finally{if(o)throw s}}}return n(t,[{key:"query",value:function(t){if(""===(t=t.replace(/\s/g,"").toLowerCase()))return[].concat(function(t){if(Array.isArray(t)){for(var e=0,a=Array(t.length);e<t.length;e++)a[e]=t[e];return a}return Array.from(t)}(this.data));var e=this.indexs,a=this.data,n=this.history,i=[];n.data.length&&0===t.indexOf(n.keyword)&&(e=n.indexs,a=n.data),n.keyword=t,n.indexs=[];for(var r=0;r<e.length;r++)-1!==e[r].indexOf(this.prefix+t)&&(n.indexs.push(e[r]),i.push(a[r]));return i}}],[{key:"participle",value:function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=""+a+(t=t.replace(/\s/g,"")),i=[[],[]],r=!0,o=!1,s=void 0;try{for(var l,c=t[Symbol.iterator]();!(r=(l=c.next()).done);r=!0){var u=e[l.value];u&&(i[0].push(u),t.length>1&&i[1].push(u.map(function(t){return t.charAt(0)})))}}catch(t){o=!0,s=t}finally{try{!r&&c.return&&c.return()}finally{if(o)throw s}}var h=!0,p=!1,d=void 0;try{for(var g,f=i[Symbol.iterator]();!(h=(g=f.next()).done);h=!0){for(var v=g.value,y=v.shift();v.length;){var m=[],x=v.shift(),w=!0,b=!1,_=void 0;try{for(var k,C=y[Symbol.iterator]();!(w=(k=C.next()).done);w=!0){var T=k.value,z=!0,L=!1,j=void 0;try{for(var D,O=x[Symbol.iterator]();!(z=(D=O.next()).done);z=!0){var P=D.value;m.push(T+P)}}catch(t){L=!0,j=t}finally{try{!z&&O.return&&O.return()}finally{if(L)throw j}}}}catch(t){b=!0,_=t}finally{try{!w&&C.return&&C.return()}finally{if(b)throw _}}y=m}y&&(n+=""+a+y.join(""+a))}}catch(t){p=!0,d=t}finally{try{!h&&f.return&&f.return()}finally{if(p)throw d}}return n}}]),t}();t.exports=i},function(t,e,a){"use strict";var n=function(){function t(t,e){for(var a=0;a<e.length;a++){var n=e[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,a,n){return a&&t(e.prototype,a),n&&t(e,n),e}}(),i=a(3),r=a(0),o=a(1),s=r(i),l=function(t){function e(t,a,n){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,a,s,n&&"$"))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,o),n(e,null,[{key:"participle",value:function(t){return o.participle(t,s)}}]),e}();t.exports=l},function(t,e){t.exports=[[-19968,350,65,-1,241,-3,324,372,271,280,332,-1,20,356,-1,87,38,-1,383,[134,246],233,285,-1,250,18,349,47,67,295,35,-3,66,-1,167,346,-1,272,287,-1,91,345,-1,378,-2,82,-1,41,-1,170,-1,62,-1,324,56,326,380,-1,164,134,236,-3,201,-1,133,-1,[321,374],186,350,-1,377,330,369,116,77,[161,358],-1,238,228,245,-1,99,-1,[35,284],350,-2,[193,213],133,241,349,331,334,-4,287,-2,124,-6,182,177,-1,264,-10,243,-7,[161,168],-1,356,376,-1,285,76,39,356,153,-1,359,116,241,330,131,-2,93,-1,345,336,-3,124,-1,325,141,-1,128,106,350,30,113,199,-1,334,131,314,167,-3,247,19,-1,336,-4,259,-4,350,[283,285],259,161,65,365,130,240,38,-1,372,130,129,-1,260,47,-2,179,-1,24,388,285,303,372,85,333,-3,315,259,243,-1,91,-1,55,171,350,-2,271,-1,350,199,188,-3,347,-1,378,233,-1,330,126,125,-3,259,-1,81,-1,79,-1,241,-7,141,350,-2,330,124,85,77,340,-5,378,355,123,[121,150],356,-2,271,326,[41,383],-1,345,-1,280,31,179,24,-2,326,380,-3,19,97,-1,209,-1,7,-1,171,-1,283,-1,295,-1,295,125,-5,62,-2,56,-6,326,61,380,395,355,-1,310,-1,110,-1,321,282,356,350,83,395,96,215,315,209,-2,354,322,243,-1,138,-1,230,-2,160,-2,347,6,-2,125,201,-1,124,-4,312,128,-1,350,285,-3,140,377,-3,36,149,-1,164,-1,285,-1,380,-1,355,-2,198,-2,67,-3,95,-1,350,-2,332,-2,176,-1,128,375,26,245,150,29,-1,217,-1,330,115,-5,247,-9,[14,234],-3,49,74,250,-4,137,-3,391,245,164,354,-5,298,85,-1,164,-1,238,9,[287,356],[241,295],-1,337,-1,356,-2,38,-1,346,[165,167],164,-2,126,340,85,-1,134,-1,226,-4,82,-1,2,-3,13,-13,100,10,-1,287,-2,58,-1,136,-3,306,115,350,-1,310,-2,129,-1,31,-3,146,135,-1,134,243,209,-1,387,329,180,-11,370,-1,377,-1,248,-4,346,-3,125,124,-3,268,-1,326,234,-5,336,-4,395,-1,314,-8,126,-6,389,-9,224,316,-3,[174,176],-2,81,31,153,-4,85,-2,164,-4,8,-21,55,-1,306,-1,17,39,222,-2,51,-5,4,-7,36,277,-19,334,-6,331,-3,168,-11,133,276,-4,128,126,384,-4,298,-1,127,-5,233,-10,131,342,-3,56,-6,264,-14,162,-29,76,330,359,-1,357,339,37,373,-1,333,101,-1,143,-1,191,-3,71,-2,317,295,346,-3,57,-1,68,-5,131,-2,264,-2,252,-2,5,95,172,331,-1,158,95,-1,100,338,18,241,134,62,388,-1,347,126,286,-2,124,30,-3,206,-2,89,255,-2,26,361,-4,185,-2,191,-1,262,-1,336,-1,137,217,-3,100,-1,378,-1,357,195,-6,67,-2,[82,238],18,116,37,136,-1,152,349,163,-3,67,333,169,-2,131,-3,241,-1,386,296,-1,167,-1,63,171,-2,126,-1,48,-9,170,-1,215,-2,124,78,-2,82,-6,85,-1,238,-1,139,120,-2,60,-2,339,-1,317,4,39,124,56,107,-1,364,58,63,-1,259,-2,81,246,350,-1,140,-2,39,327,-2,338,117,-3,358,169,172,365,89,42,-1,39,-2,279,-3,227,-3,229,164,-1,16,-1,131,98,-1,58,-2,148,-2,377,288,252,[28,277],46,143,-1,102,-1,102,139,73,124,310,-6,[245,335,343],-1,156,243,-2,98,126,-2,310,-7,324,-1,124,-2,[279,346],-3,[9,19],-1,134,-1,284,126,-4,85,-2,91,-10,235,-1,[32,128],-1,245,136,-5,233,-7,123,-2,350,-7,164,-1,252,7,95,125,330,182,169,-4,67,380,218,129,251,281,-3,164,130,160,-10,110,285,-3,19,-3,354,-1,191,-1,344,-4,189,-1,161,-3,341,-1,140,-6,199,-4,247,-11,336,-8,9,281,-3,96,330,359,-4,9,47,-1,339,-4,240,-1,229,85,-4,13,117,10,-1,[36,285],-3,360,-2,127,152,-1,332,-2,102,-3,80,-3,153,-10,233,251,350,-2,14,209,-1,285,-1,243,-1,269,-1,284,330,121,7,-3,117,336,-1,10,[49,391],387,-1,56,182,202,-2,19,-1,[19,20],-1,14,20,371,138,175,355,175,-1,98,329,-3,326,-2,377,185,351,326,-1,124,253,177,-1,135,336,-1,130,-4,248,-2,31,-1,74,314,164,-2,164,-1,345,346,282,-7,26,-2,164,-1,115,-2,53,-1,357,-2,334,346,-1,136,[277,332],-1,39,133,-4,295,-7,295,-4,251,-3,333,-1,271,[23,283],-5,355,28,124,355,291,78,-3,77,-2,287,-1,251,286,14,341,-1,227,-3,297,64,-2,147,97,134,171,-1,[58,307],147,377,128,373,5,65,143,304,36,285,355,-1,239,349,109,295,305,-1,161,63,124,-3,[341,356],-1,36,91,-1,348,-1,[91,110],124,63,-1,315,195,115,164,317,334,369,332,-1,176,0,181,-3,137,-1,170,320,351,80,13,-1,247,-2,[84,233],5,72,81,-1,107,314,[108,145],293,241,-1,377,351,-1,330,32,-2,331,43,-1,327,115,-1,330,-1,345,-2,74,-2,55,-1,35,-1,[90,97],85,-4,200,-1,85,350,55,224,164,10,357,104,-1,244,330,-5,205,-1,171,-1,355,-1,379,-8,98,388,326,-1,110,204,332,230,-2,283,116,195,-2,134,-1,360,-1,73,-1,229,-4,[360,365,369],[110,116,123],-1,133,354,85,-1,379,-1,138,97,138,-2,173,67,215,-1,295,-5,101,369,-1,350,169,388,193,190,377,348,124,-1,[91,138,180],-1,362,-1,143,121,-3,333,-2,340,-1,346,-1,350,1,237,283,-1,114,-1,73,322,105,361,-2,226,334,1,93,152,345,54,335,13,358,-1,117,-1,150,73,-1,124,217,198,353,-5,91,[74,223],36,281,164,200,-2,148,335,-3,374,369,-6,20,-1,113,94,-1,91,-1,346,-4,302,44,-1,1,-5,331,306,395,-2,330,-6,181,-4,160,-1,302,364,119,-2,124,-2,82,-1,116,-1,286,326,288,31,-1,164,-3,353,-4,277,-1,321,116,-1,379,-1,144,387,-1,280,-2,170,0,-5,51,-4,307,56,-5,[40,45],-4,80,-2,233,277,156,365,-2,225,-1,274,383,213,-6,19,65,159,335,-2,36,310,-1,133,-1,138,354,326,202,279,-2,156,129,115,107,64,-3,[222,258],-1,351,-2,329,-3,41,121,-2,331,110,-1,153,-7,342,-9,164,-1,[28,369],-1,192,-1,231,-1,153,-1,356,-1,174,148,-5,0,340,-3,298,-2,[1,350],302,-3,143,[54,303],272,34,-1,297,-5,285,91,-1,129,-1,328,-1,295,36,109,302,-1,106,-1,247,213,110,-2,75,-3,1,-1,315,-1,4,-5,297,297,-1,61,241,-6,25,125,-2,235,-1,86,97,-8,[285,341],-2,181,-2,162,69,-3,12,352,-2,190,-4,[40,395],232,-3,380,32,-1,393,-1,295,-2,168,-1,331,-3,111,-12,27,128,349,-5,60,-2,240,136,247,-2,175,-5,223,-1,130,-3,241,74,364,350,285,-4,[136,343],-4,86,-4,270,233,-8,264,109,-7,109,310,-3,[21,28],-15,335,-11,123,-7,256,-4,[128,136],-13,203,-9,203,-2,[54,147],-2,250,295,-1,[126,202],121,337,351,202,318,-1,[72,320],-6,116,-1,357,-2,154,47,-2,326,179,-3,171,97,-2,104,317,355,-3,240,356,-1,357,-1,252,356,248,-17,[119,357],-2,317,-3,284,-4,361,[326,341],91,-1,330,102,233,350,[59,61],-2,375,-5,152,31,241,-2,124,-1,377,-1,7,-4,137,-2,79,-1,11,305,140,118,395,145,-5,150,-2,126,305,164,5,330,81,385,239,-2,154,-1,305,-1,321,88,238,62,-1,209,-1,233,-3,4,-2,199,143,-3,61,33,-5,43,156,173,173,175,-4,338,67,-5,162,86,-7,73,-4,351,77,96,357,64,-1,144,280,-1,74,-1,62,-1,345,149,-3,139,-1,204,-3,357,-9,94,1,-7,182,-2,35,[279,346],-2,169,-1,[20,240],35,-2,285,344,104,-2,210,-1,356,20,-3,233,-8,55,-1,2,-4,377,-3,350,230,124,-2,273,-2,148,-1,306,-3,71,130,-3,232,-1,317,-3,243,-3,73,-3,351,-4,64,-1,115,9,-2,61,-5,140,-5,346,-4,69,-14,163,-7,303,35,-3,298,-2,303,-3,306,-5,[270,274],-6,91,-5,311,357,-17,287,-1,36,183,-1,131,-1,287,-3,354,-8,280,199,-5,244,167,-3,368,341,-8,197,72,-18,124,-4,13,-3,354,-11,110,-3,109,-14,256,-6,285,259,-1,384,-1,284,-2,[143,245],-2,116,-2,350,-8,[78,228,327],-1,39,-2,10,-5,85,-1,332,-4,153,331,323,-2,298,73,-1,349,-2,96,-4,351,123,-1,[54,304],-1,311,304,85,-1,348,347,[11,108],-1,285,-2,316,-2,350,149,[86,125,242],73,-1,152,-4,166,119,-1,346,-2,241,201,82,-1,81,-2,153,390,-1,241,-2,11,350,127,307,363,-1,331,-5,62,-1,282,-2,4,-13,219,218,-1,201,-1,126,303,-3,109,-3,294,264,80,325,-1,384,85,181,-1,259,-2,346,-4,69,124,-2,348,130,-1,192,-4,216,-4,13,356,321,-2,79,330,356,102,-2,209,379,-2,54,-6,187,-1,241,-2,246,-7,199,-3,388,285,-4,129,97,295,338,326,-2,279,237,-1,348,-1,127,287,-5,128,-1,160,-2,350,-3,124,-12,28,-1,351,-3,388,-1,326,-1,322,174,345,257,128,177,238,-2,164,-4,302,-1,326,-4,211,-3,[200,222],-2,135,283,-2,61,-1,74,-3,191,-7,356,322,-1,333,-1,251,-5,31,-3,74,-5,239,-2,324,15,-10,129,-4,122,-7,13,-4,131,-2,158,-9,352,30,283,314,-2,330,-4,341,-18,187,-7,187,357,-14,4,-7,233,331,-1,352,-2,36,-5,96,-2,125,273,-6,124,-2,333,-5,1,-1,237,-1,235,-1,162,-3,372,-3,164,61,-1,346,-2,31,-2,207,-1,197,-5,244,-23,331,-12,13,279,-26,212,-1,352,-2,197,-8,291,-15,[361,388],129,-1,136,146,359,-1,388,52,301,85,10,388,335,-1,189,-2,9,124,97,218,343,-2,106,177,-1,204,-3,287,[23,30],-1,388,-1,85,-4,264,-2,213,-3,215,-1,303,102,370,-1,356,286,2,-1,296,324,-2,114,-3,190,-1,56,-1,389,100,379,65,324,350,9,285,-1,37,283,143,342,285,355,119,-3,333,95,-4,361,-2,106,346,335,125,-1,34,262,-3,151,17,298,-2,124,-1,124,351,190,147,-4,85,-3,187,-1,107,356,-9,247,197,28,-1,98,-2,330,168,-2,370,-5,168,-1,119,-7,52,71,295,344,58,-2,286,-1,82,-2,282,-1,[127,244],-2,[326,356],394,-4,335,-1,281,-2,76,86,126,-1,34,-1,280,-1,86,31,-4,355,-1,355,168,-1,348,-4,86,-4,133,-2,88,-3,285,351,36,142,209,130,326,212,134,233,27,-2,134,-2,251,310,129,330,-2,285,[18,238],124,336,-3,371,-3,74,-4,[287,380],-1,317,176,-1,331,-1,176,134,-8,[320,386],-1,279,-7,350,241,-4,356,-1,300,241,-5,345,-3,124,243,-2,241,-3,28,-1,251,89,333,4,158,58,5,-5,143,96,-5,346,-1,340,125,171,-3,55,-1,358,-1,116,-1,194,2,-4,67,-1,153,-1,185,-2,350,-6,344,-6,67,-6,377,-7,332,-2,[128,245],376,177,-1,74,-1,356,-2,245,-2,82,-10,137,-6,160,157,-2,146,37,-6,241,-5,51,-1,345,-4,136,-2,104,-5,335,-1,346,296,-1,12,-3,371,97,-5,[323,326],-8,361,348,-8,124,-2,284,187,243,-11,262,-2,356,-1,174,-11,296,-1,388,326,-2,53,-4,124,-13,372,-22,170,-3,60,-25,350,-13,62,-7,326,-15,41,379,-2,344,32,-2,95,395,245,134,95,-1,330,-2,[28,29,46],250,-1,124,350,295,5,-2,334,-5,344,130,-2,13,285,20,-1,289,78,-1,285,-3,331,-2,326,372,306,-2,230,225,313,-1,166,377,379,19,377,61,-8,55,375,-5,331,8,-2,38,-5,326,31,-2,365,104,185,-4,190,-1,329,85,-6,120,-7,183,199,-5,372,-2,85,-2,78,[42,384],-15,88,238,210,-1,18,-1,338,-1,348,119,355,355,-1,101,233,-3,384,-1,248,13,-2,42,102,-3,341,175,330,-1,148,352,61,229,62,-1,192,94,-1,85,-1,228,80,334,-4,340,[69,73],395,-5,314,-5,13,-1,2,287,141,354,321,-4,356,-10,166,159,-6,130,4,155,-2,168,-4,30,-12,336,-1,170,-11,346,314,-2,126,-4,210,139,14,350,241,217,-3,350,-1,13,350,-3,285,-1,285,-1,95,-1,351,-1,85,114,-2,36,-3,61,372,-4,190,333,116,-1,218,131,-2,190,-1,324,-1,268,-7,[56,305],[127,244],-1,13,-3,96,-17,102,57,-1,175,318,121,377,-4,350,-3,[236,271],338,-1,315,-1,346,-2,22,15,-1,17,232,-2,372,352,-1,36,-3,228,-1,350,-1,33,13,-3,325,376,49,-1,131,55,-1,344,112,347,118,176,115,-3,341,-1,317,-2,157,-1,59,226,331,-2,31,-4,356,-6,120,-1,344,-2,348,326,-6,376,-1,59,-4,128,121,-5,337,-1,13,350,-2,160,-2,124,259,-1,30,305,308,[308,319],-3,52,377,325,184,-3,311,-2,378,37,-2,330,-2,355,-2,[296,378],150,-1,14,377,-2,34,-3,210,-2,216,-2,337,-1,116,139,81,118,304,296,330,224,31,42,-3,32,-2,395,367,347,-2,218,-1,376,225,20,-2,116,-1,54,166,295,-2,55,350,-3,124,232,338,357,209,99,85,-3,246,-5,39,-5,389,71,-2,350,-1,207,344,285,-7,166,-1,120,-2,146,-1,113,-2,287,-3,347,121,-2,125,-4,121,388,341,-2,219,112,75,143,67,311,95,-1,331,242,-2,144,-2,[74,330],-1,315,346,139,26,204,359,-1,354,-3,154,245,-4,331,-2,310,107,-4,350,-1,121,-1,10,-3,296,252,-1,153,-1,330,355,-2,119,-2,358,-1,214,-2,253,342,243,-1,194,-1,80,10,-1,51,-3,124,-2,338,58,-8,248,38,-3,131,324,-5,123,-3,310,-2,325,-1,116,-1,331,[31,306],-1,326,121,-5,62,134,23,35,-1,10,246,23,56,100,73,-2,334,385,-1,120,-2,258,338,-5,245,38,-4,243,-1,356,356,-3,194,13,350,-5,74,-4,356,-4,88,359,-2,163,81,-1,153,153,-3,298,-19,357,-8,46,-1,[243,246],-1,120,-1,283,-2,282,-3,199,-7,308,-4,183,-4,121,139,-7,326,-4,354,-1,141,-19,16,-2,368,-5,245,-8,71,-9,37,107,241,-2,131,-10,39,-6,107,-3,67,-5,336,-1,4,185,-5,188,158,-1,170,-17,222,-14,189,-9,350,-6,89,-1,91,-1,330,126,341,287,262,331,35,329,129,-2,244,123,244,371,-1,241,125,-3,124,-1,140,124,-1,87,60,-4,129,-1,126,-1,175,-4,45,55,-2,116,-5,116,164,79,302,14,-1,132,-3,279,116,80,-1,286,-1,22,369,-2,240,[5,225],54,260,-3,321,-2,141,-7,147,-2,243,377,-1,155,188,273,347,216,7,33,257,-2,7,-2,85,-2,233,-2,74,-1,373,35,124,-3,32,-4,136,5,-6,350,287,381,-1,316,68,141,374,-1,85,229,-3,318,147,179,244,-1,116,9,-2,232,-2,233,304,-4,9,-3,61,-3,[181,197],-1,34,345,38,-1,194,-2,85,-1,380,56,29,199,210,156,85,-1,7,226,170,-1,99,-1,134,321,5,-1,321,[4,216],134,387,227,373,6,-2,209,-2,173,126,-1,354,158,215,19,365,-2,155,285,129,376,-1,95,-1,252,290,-1,360,142,-4,237,[349,382],285,200,-1,36,98,-4,377,246,2,-4,149,-2,312,-4,322,-3,377,177,-1,[329,381],303,336,204,57,128,376,124,121,-2,1,-1,221,53,-3,375,-2,[269,277,302],-6,350,314,-2,324,-4,330,137,-1,315,154,-2,387,-1,176,5,107,281,213,135,-4,20,-8,160,301,-1,126,119,58,-3,232,-1,169,-3,6,134,-2,1,-4,43,129,-2,200,210,-4,333,-1,62,-4,73,286,63,-2,372,-1,124,307,242,-1,226,-3,349,-1,136,-7,178,-1,305,33,-1,129,-1,146,319,346,53,-1,134,311,243,-1,6,-2,175,[99,104],-2,377,56,-1,30,-1,100,-1,357,-5,356,-1,153,-2,263,-3,390,342,192,310,-1,28,-3,350,-7,2,-1,345,329,-1,40,-5,139,133,-2,129,-4,[64,282,349],-1,357,-1,349,-1,369,-4,158,-1,247,30,91,174,-2,128,-5,40,371,-2,19,39,-2,53,273,-6,126,297,-1,90,-1,294,272,-4,222,-3,306,-1,7,54,-6,243,-5,336,-2,28,-1,91,-1,75,-2,282,287,6,348,17,-1,305,-7,18,-1,289,-3,370,-5,180,-8,51,-1,197,-3,377,-10,197,197,374,-7,168,-1,352,136,-1,236,-9,35,269,-2,295,107,-2,394,-4,384,-5,33,-4,168,-2,245,19,53,-1,383,-4,210,-1,336,175,-1,50,-1,107,-3,88,-1,162,-2,279,-7,25,248,-1,119,-1,247,-4,233,19,-5,297,-3,387,-1,338,-1,21,-25,227,-8,123,-8,[50,362],-5,256,-12,392,-5,136,-2,203,377,-5,[78,228,327],286,-1,355,87,-1,95,-2,79,376,-5,97,-2,335,190,-2,61,-2,194,-1,133,-3,36,4,-2,128,-1,166,-1,13,31,-3,88,271,-2,[71,72],-4,128,131,-3,[287,294],-1,245,-1,376,-2,85,-15,327,-3,370,17,-3,80,7,-1,158,-3,68,-1,168,-1,116,336,-2,375,-1,329,-2,130,36,-1,85,-1,371,-1,387,-1,70,-1,295,337,-8,79,-2,356,285,-3,228,-1,371,185,176,230,-4,342,131,-1,209,391,-2,172,-3,350,241,-8,330,-1,124,-2,261,56,133,377,364,-2,344,341,86,156,88,107,-4,285,152,-2,325,-5,359,-1,3,365,-2,154,-3,109,-1,31,-1,195,122,-3,350,331,337,-3,305,-3,362,-1,338,352,-4,44,-1,187,395,-4,373,-1,285,-1,356,-2,185,209,31,-5,379,-1,333,-2,32,-1,120,-7,130,280,-2,346,-2,278,335,349,359,121,107,-2,324,-4,284,-1,20,-2,330,-1,121,-1,34,-5,240,131,331,-3,248,-1,131,102,-2,377,-3,167,-3,362,-1,342,-2,332,-4,153,-4,287,-4,220,2,-5,195,-9,1,124,-5,199,-5,[9,240],-4,333,-4,320,-26,287,-1,344,348,[9,240],-8,331,-2,203,-6,358,-1,251,349,94,-2,110,-1,25,-2,183,-1,[27,368],310,393,-7,358,355,265,232,-1,85,-2,251,-3,294,375,-1,159,-3,325,-1,[32,373],-1,241,-6,189,-1,199,-1,326,197,11,369,-1,287,-1,380,-2,240,73,-4,124,-2,340,-2,277,-1,360,252,-2,88,-1,28,279,-2,330,-1,164,338,22,52,-1,281,-2,372,-5,69,-1,241,287,89,312,-3,157,-2,347,181,192,-2,108,-1,10,129,-1,90,348,-1,39,-1,225,-4,380,-1,296,7,-1,124,-2,96,-2,233,-1,325,-1,79,-4,331,-4,375,-1,170,266,-1,187,-1,104,377,47,-3,287,364,-1,164,-1,126,35,-1,244,82,-1,335,-1,148,238,-2,377,-1,335,125,125,[96,134],-10,171,18,-10,[6,19],198,88,241,255,263,-3,374,332,355,-1,102,321,[369,395],-1,215,-1,61,-2,28,-3,133,-2,126,-2,143,201,380,-1,172,29,-8,35,-1,285,377,-4,[279,369],-1,15,371,377,173,67,175,-1,[164,358],158,-1,287,-1,290,-2,[241,331],164,-5,98,-3,[128,335],-7,341,380,-7,142,160,-3,347,110,93,-2,91,361,177,-1,129,113,102,307,101,326,152,-1,2,2,135,-1,387,-1,377,-1,315,272,-1,119,129,133,-10,345,257,375,57,241,245,117,[102,121],127,384,-1,302,-8,85,-1,315,136,-9,167,-1,314,-1,187,8,-8,97,-3,388,-3,94,-10,281,-3,189,330,164,-4,302,-1,310,336,-2,287,-1,78,-10,126,-1,171,-6,191,-1,241,-1,103,-4,8,-2,389,-2,124,-1,232,-5,306,-2,61,-10,275,-1,43,163,-3,143,-3,373,100,-1,81,-4,104,-3,350,-5,167,-1,377,[43,385],-1,134,-1,128,-12,69,243,-3,180,-8,321,-2,349,-3,70,-4,[283,375],-3,41,-1,44,-2,[28,369],-17,336,-2,126,-2,39,-2,166,163,-1,202,-2,187,-2,342,-4,124,-2,39,-2,48,-5,139,250,352,-2,174,-3,237,-1,87,-1,158,-1,356,34,176,134,-3,336,-6,159,262,-2,134,-2,375,8,-10,80,369,-2,301,-1,336,-3,51,-2,172,-2,253,-3,303,-5,90,-8,294,-1,43,-1,28,-1,118,-3,90,-6,[126,140],-3,[17,18],380,-12,241,-4,116,-10,25,-1,130,-10,78,-12,39,306,-6,372,-1,197,-6,331,-1,113,-4,244,-1,352,-3,245,-7,394,358,-5,88,-2,245,-8,321,-7,134,35,-1,136,-5,334,-3,380,-11,39,-7,175,-2,357,-3,305,-3,331,-9,247,-1,346,162,-5,19,-8,248,-8,170,-1,28,189,-115,243,46,119,337,356,-2,224,-10,356,-4,331,-1,350,241,-3,151,-4,277,-2,337,336,-1,243,-2,91,-12,[282,331],-8,377,376,46,20,330,241,-2,323,-14,55,-1,295,126,-4,197,49,347,311,-1,55,280,-1,344,287,23,-1,235,-4,359,166,-2,377,-3,56,124,-5,17,-8,350,-8,287,224,70,-1,[346,351],-7,62,-1,121,97,-2,350,-5,330,-1,199,-1,187,-2,69,356,13,13,13,233,-1,13,-1,185,-5,371,-8,199,109,-3,305,-3,51,-1,271,-3,287,-3,126,-7,31,240,175,-5,251,-1,285,61,194,-1,184,241,236,201,-1,58,333,41,81,-3,85,-1,67,248,-1,351,-1,106,347,2,345,143,-3,56,176,248,-1,359,-1,292,-3,354,-4,320,-2,314,377,250,-3,50,121,-1,107,28,-5,331,-3,241,279,-1,107,-3,344,295,264,95,127,36,330,-2,306,-3,190,97,325,-5,304,-1,124,-1,14,-1,327,-2,339,-3,241,81,-2,247,350,329,-1,357,108,-1,[34,283],34,-2,72,-2,241,199,-2,[54,303],191,-4,277,-1,230,-3,96,-1,[187,197],-1,82,224,164,179,24,-1,326,116,197,-1,287,134,-2,321,73,110,-4,80,355,-1,377,373,97,371,346,-4,336,250,-3,252,19,-1,190,-3,161,-2,114,88,77,185,295,-3,78,-2,215,-1,171,229,19,241,-1,209,-2,380,-1,162,342,-2,227,194,304,347,-1,354,-1,12,343,[173,280],175,-1,180,336,239,365,131,-2,129,-2,121,-2,351,-3,347,169,-1,124,-3,269,-4,331,-1,380,127,180,-2,67,-6,130,-1,326,-2,114,341,-2,307,-2,76,379,264,-1,344,-3,119,-1,123,322,242,226,-2,172,-1,125,-1,243,127,128,375,-1,387,26,-1,150,124,172,-1,122,116,217,344,-4,374,[137,344],-1,8,-1,387,-1,331,-2,119,-2,240,-2,109,159,-3,85,-5,356,-2,106,130,-3,187,-5,317,-2,213,-2,335,282,-2,354,-1,333,-2,298,-1,135,-1,310,-5,307,-1,160,157,166,326,329,-1,119,61,-1,267,126,372,274,85,100,-2,290,345,-2,349,-2,107,-2,110,-6,387,62,-3,388,331,335,241,-3,170,306,-4,287,-4,204,-1,307,47,-3,80,296,-1,233,56,-2,356,-1,88,-4,351,51,-1,118,-2,283,-1,44,-3,122,-1,346,-1,311,192,-8,248,-4,357,-1,175,388,69,-1,126,[191,284],-2,356,-1,283,283,-2,380,-2,356,-2,251,69,-1,369,19,329,-3,327,-1,336,-1,326,-1,89,-2,342,-1,143,-3,355,-1,192,-8,226,187,-8,318,191,-4,231,126,-1,116,-1,334,-2,371,-3,120,-11,[128,250],-2,346,-15,324,285,-3,153,-1,126,341,-2,87,-5,306,357,-7,143,-3,172,-2,195,-2,350,-2,240,-1,164,-2,331,-4,298,-1,247,297,-1,340,-1,262,122,-2,209,303,-1,264,-3,39,228,-4,62,-3,388,-3,85,-1,117,-1,388,307,309,-1,13,-2,103,-3,377,346,282,183,352,-1,176,158,177,-1,17,305,-10,61,-4,116,-8,235,-3,241,-2,175,-5,174,-3,164,346,25,-10,197,-3,158,-4,342,350,183,-1,184,-1,[180,303],-1,287,-1,372,-2,119,-7,347,-7,352,335,-3,166,-1,326,-10,227,-3,243,-1,175,-3,120,-3,[160,168],-6,305,32,-3,281,-1,380,-3,279,-1,30,-1,315,-7,35,-3,33,88,-2,295,287,232,-13,158,-4,364,-5,164,-11,4,-2,30,-2,[56,305],-6,124,-1,166,-6,300,-7,157,17,-11,13,-1,109,264,-12,240,387,-33,[9,240],-8,107,352,-7,336,-17,81,-3,358,-18,100,-2,109,-14,5,-12,123,-1,193,-1,60,121,-4,171,364,-1,133,-3,387,-1,361,23,347,-4,102,-3,175,43,-3,346,-3,32,-1,253,141,72,-2,377,-2,326,244,-13,342,134,305,[9,229],132,-1,304,-1,18,-3,380,369,62,-1,285,166,36,-2,116,294,158,314,-4,169,-1,347,-13,114,[160,180],-1,380,-3,346,-4,142,-1,78,281,349,121,-1,306,130,258,-1,331,-7,324,-1,232,-3,82,-11,346,107,-5,330,-2,107,-1,119,188,-1,58,10,81,-11,128,-8,32,346,346,-4,255,-14,70,-4,342,-1,120,-1,126,-13,356,-1,277,-5,187,-1,341,373,326,-5,380,-3,9,116,-4,14,-1,319,-2,279,-6,331,-5,339,-4,344,-4,262,-3,172,331,-5,287,350,-7,[356,359],-3,4,-6,183,-1,280,-3,331,-9,255,-10,168,-5,78,346,-10,356,-4,364,-1,300,-6,336,-10,333,-12,9,-22,136,-10,50,-1,[373,381],-1,225,-3,357,1,-3,136,85,349,5,64,-1,348,-1,291,-1,227,-7,234,7,-3,226,69,-4,64,-3,355,-2,345,-1,216,-1,237,-1,[198,199],-1,199,160,-3,185,199,-1,330,-4,126,97,-2,284,-2,243,-3,308,331,-3,330,97,331,164,-2,124,-5,69,134,-1,126,-1,234,-2,142,-12,127,-12,252,252,-1,78,250,-3,[2,107],-1,384,101,181,355,-7,359,152,216,61,-3,10,-4,229,332,-1,116,-1,80,-4,96,-1,134,-4,215,-1,112,128,-6,262,286,-2,69,332,285,150,376,356,301,356,13,-2,135,164,-1,351,299,159,-4,164,-1,333,-6,209,-3,169,-4,104,-1,190,31,350,-3,189,22,49,282,-2,180,116,-2,326,-3,338,380,185,326,-1,333,-2,204,-2,115,-2,355,187,28,-4,117,357,-13,131,-2,372,-1,4,-4,136,-8,168,-11,336,303,-1,344,-14,119,-5,342,-2,[176,289],-1,356,-1,325,-2,65,-2,124,-4,133,-4,181,-3,194,-2,17,-6,324,-1,187,-2,326,119,333,-1,171,55,-3,62,-2,331,19,-4,239,-1,143,-5,125,194,279,-2,375,-1,136,77,173,-7,95,-4,180,-1,380,-4,76,-1,348,-1,113,-3,7,-4,[121,122],-16,250,-1,159,164,-2,172,345,-4,166,302,-9,134,34,-6,387,-2,116,241,-1,154,-1,241,-1,324,-1,47,-1,346,-2,170,247,233,225,-5,249,-4,185,-19,332,-1,357,-1,204,116,352,356,-1,266,274,-13,306,-2,102,-5,348,1,-6,130,-1,51,47,-1,164,-3,342,-3,372,-2,352,-1,175,-11,120,-1,240,-8,13,23,251,-16,327,-23,362,-9,98,-1,64,-1,116,-1,235,7,256,-1,322,-7,328,224,-4,171,-1,238,46,-7,20,-4,375,-8,189,-1,12,-1,368,-1,233,-4,88,55,283,-1,311,-2,284,-5,284,-2,354,289,-1,85,354,12,-1,215,311,355,125,283,-1,62,-1,202,62,-1,65,117,-2,361,-1,13,-4,31,-2,78,-2,311,129,-1,252,326,-4,227,-4,172,11,375,[39,341],-8,178,241,-3,78,-7,[282,356],-1,38,-3,124,324,-5,124,-3,318,-2,127,-4,233,-3,287,-1,350,-2,65,-1,129,168,-1,91,133,-2,279,-1,221,164,347,-1,355,5,129,-5,350,164,-1,42,82,380,229,233,88,143,46,-2,56,375,-2,309,134,124,-3,125,342,369,18,-1,376,354,131,252,-2,350,-4,347,-1,377,112,345,-1,68,-2,315,-2,233,-3,164,377,53,-1,330,277,160,-1,119,333,-4,305,80,-2,36,-4,13,-2,97,-2,326,356,51,-1,380,-1,56,-4,115,-1,156,-3,356,-4,125,-1,350,174,273,-1,36,-3,327,124,-1,7,-1,172,[29,53],286,-2,54,16,305,-1,15,-2,180,-2,38,372,370,-2,253,-3,197,-1,351,352,120,-2,173,-8,1,7,-6,350,-1,233,-5,62,-1,157,-4,342,-7,62,-3,251,-8,102,-2,60,-1,6,6,-3,364,-1,[59,61],-1,129,120,102,-2,90,-2,128,-2,1,-1,109,-2,324,-2,331,-10,239,-9,233,-2,379,137,-1,52,-10,194,-2,356,-2,378,231,-1,352,-1,350,-2,110,3,371,346,126,110,-1,153,-1,87,58,227,-2,284,-3,189,-5,100,-8,199,65,-1,341,184,-1,377,-3,334,72,-2,227,-1,72,-2,[284,338],-2,191,-2,192,56,187,-1,140,-1,147,-11,350,284,-4,375,191,-1,357,-3,388,-1,369,342,-3,300,-1,190,-5,36,152,135,198,-1,312,-1,346,-3,[373,374,387],376,-1,302,-3,61,-8,157,126,-8,345,131,-5,292,300,69,-1,13,199,-1,209,-2,129,22,-12,69,-3,153,90,266,185,-3,192,38,-6,143,297,332,-2,195,183,-12,235,35,-1,189,-2,236,-1,245,-1,380,60,-1,293,-3,140,-2,315,-1,170,-5,371,-1,97,-1,251,-13,136,-9,39,-3,185,[100,130,247],-5,285,350,-1,377,-1,283,-1,134,-1,128,53,70,1,-4,[56,285],-2,124,-1,88,-4,331,78,152,57,181,277,-6,117,-2,241,140,-3,345,233,-1,346,-1,383,33,72,-1,346,-1,82,77,-1,369,-3,321,-1,61,370,375,-1,80,-2,156,14,-2,232,-3,239,-2,283,360,1,164,173,315,-1,164,-1,39,-4,102,-1,204,-4,91,-1,338,-1,67,-1,331,-2,294,332,245,-5,335,-12,329,172,352,184,253,-8,126,-4,232,-10,65,-1,63,-2,175,1,300,-2,10,-1,71,-3,324,62,-1,10,241,34,-2,64,-1,379,-1,129,-1,14,-1,13,-8,232,126,61,305,28,-4,342,-4,210,-2,46,-3,[8,228],-3,272,162,53,-4,227,-3,374,143,-3,103,-14,197,-3,248,-5,251,-1,60,-2,170,-2,120,-6,128,-3,72,-13,127,-10,189,-5,21,-15,19,-5,285,285,164,-1,282,-1,295,241,-4,333,-1,241,377,-9,85,-2,391,61,-2,395,251,116,380,283,300,46,-1,190,-2,334,-1,312,235,-4,124,-1,375,-7,58,123,-1,241,-5,18,130,-2,175,30,-4,331,-4,85,-10,387,-12,331,-11,256,-5,356,356,164,-1,247,110,-1,340,295,-1,317,-2,88,-2,18,-1,250,-1,378,-3,143,192,-2,13,-2,190,-6,391,-3,197,35,-1,247,347,-1,377,-1,287,-1,388,-1,124,[34,35],-7,129,-2,350,-1,121,-2,331,-1,159,85,-2,176,-4,35,-1,281,292,-5,259,-2,6,-2,377,-3,143,-1,38,-2,298,-15,327,-3,124,-1,375,-1,58,125,124,-1,90,-6,199,-10,274,-5,300,-24,256,-3,343,-1,133,249,331,249,146,-4,41,386,317,-1,246,370,-1,14,-1,348,-4,245,-3,348,377,-2,312,128,42,132,-3,50,329,-1,148,143,-4,153,68,-1,[344,351],-3,356,134,-5,356,-11,173,-11,164,-10,287,-2,371,-4,131,131,372,-2,137,-1,315,296,-6,129,-1,70,-9,380,380,-2,356,-1,88,-3,69,124,-1,5,-1,124,-1,373,301,-3,116,-1,335,-2,13,126,-3,284,-1,61,-2,36,-1,164,-3,312,295,85,-1,11,-1,54,388,61,-1,[365,395],-2,96,-1,125,-4,239,-1,126,-1,173,-1,14,-6,333,-1,249,-1,60,-1,130,252,-2,77,152,380,315,-1,54,-1,26,-1,147,-1,13,278,-1,376,-2,[137,359],-1,225,-11,285,-2,335,281,-2,346,-1,150,-1,38,-1,89,-2,243,-1,126,-4,13,-7,97,-2,131,-3,19,124,-1,299,-4,146,243,-3,100,357,-3,365,246,321,180,56,335,268,126,-3,334,-2,375,-3,380,-8,120,-4,383,234,-4,115,-4,153,-1,174,-5,90,80,-2,96,-3,50,-3,164,13,-3,36,-3,158,-2,164,-5,232,-4,68,-1,193,-8,49,-3,102,298,-2,175,-6,70,-8,62,-6,60,120,-2,362,-13,19,-6,20,379,157,-11,124,-37,190,61,-6,162,333,388,-11,81,-7,5,164,-2,239,-1,49,[210,371],-3,312,164,331,298,-2,388,-1,358,[356,379],-4,81,-3,167,-2,167,23,131,-5,51,-2,170,389,131,-2,[271,283],-3,263,-1,115,341,-1,116,-1,362,46,-7,90,306,250,-1,25,-2,[187,190],-2,364,141,-7,127,-6,222,-11,[124,331],-14,327,-21,298,-1,302,-4,130,-3,388,-3,162,-62,341,-8,377,-46,241,-7,248,-140,190,-5,78,-5,[348,355,379],-58,392,-24,58,-4,133,356,114,379,[243,333],91,358,124,324,152,124,259,326,359,-1,44,233,277,89,200,-1,389,[100,179],81,377,327,79,-2,216,287,333,88,336,85,166,391,283,331,377,378,379,7,85,39,281,350,131,55,8,262,129,148,257,-1,108,121,[92,124],342,127,180,136,128,315,94,335,135,340,-1,300,307,124,310,124,341,171,-1,341,241,80,[32,45],280,103,284,326,191,286,12,38,-1,172,252,[368,389],371,324,176,385,388,143,334,126,191,158,310,192,[124,241],-1,121,295,73,70,14,-1,96,385,119,61,176,14,194,357,130,85,264,375,82,-1,90,30,164,350,126,17,235,183,162,352,[298,302],[192,196,198],273,336,168,279,368,127,243,245,119,128,392,84,-1,89,-1,253,-7,352,-1,248,332,-10,100,325,-2,325,107,-1,180,85,-1,77,-4,97,-1,89,5,-5,346,373,393,-3,377,-2,158,287,-1,233,-4,164,-4,368,-2,124,-8,347,-1,244,-1,187,-5,90,-5,171,-2,61,340,244,-1,333,-2,254,-2,302,-7,129,306,-1,331,-5,162,94,-2,30,356,-1,350,-1,328,-3,36,-4,350,-1,350,-1,171,-5,334,331,-2,245,-6,[61,370],51,80,-3,380,126,-2,234,-4,110,-1,107,4,-1,350,-7,78,350,-3,348,160,-1,142,185,374,241,-4,64,76,288,-2,201,-1,162,-1,388,94,32,109,359,[5,225],-2,295,-3,123,-1,160,-2,306,224,174,-1,127,228,-6,197,-1,76,-1,65,349,54,296,-2,36,-1,56,-1,94,-2,213,56,-2,171,-3,168,173,377,215,-4,104,-1,166,-3,237,-1,134,-14,153,47,-6,4,-13,356,-3,298,350,-1,295,373,-1,263,-1,162,124,-6,120,-2,335,-1,379,-1,69,89,262,88,-1,329,31,97,377,-1,85,80,-3,126,79,386,-1,200,3,144,-1,95,356,-1,348,-2,[243,341],-2,80,-1,131,304,283,378,372,336,283,326,379,-1,56,-5,10,98,304,-7,[227,228],375,-1,395,230,125,284,377,9,-2,116,-2,351,341,-1,173,67,138,175,131,218,346,-1,149,350,101,106,91,67,-1,128,-1,339,-1,2,-1,234,208,-4,377,-3,51,-2,[182,197],124,-2,150,269,363,241,204,190,217,177,-1,19,-1,324,-1,128,-3,53,-13,229,-2,85,-1,321,212,-3,180,-1,166,-5,233,-7,311,-1,131,-1,156,349,[0,346],-3,85,85,-1,80,244,324,-3,389,65,-5,48,-4,338,-1,287,-1,202,-3,74,270,-1,348,126,-2,356,-4,85,333,209,191,322,309,319,8,-1,176,-5,91,-1,19,-4,90,-1,17,-6,15,-2,306,197,331,-5,377,-2,164,-1,232,-1,40,-8,279,-6,352,279,-4,320,166,13,354,-2,350,-3,273,-1,97,-22,34,-3,363,-2,388,-1,213,[38,340],-5,377,377,-6,375,133,-1,356,-1,348,356,37,-1,331,133,356,-5,282,282,-2,285,-1,287,-1,311,-6,41,293,-1,330,379,-1,41,279,350,-4,7,-1,108,79,7,13,-1,378,126,24,-1,380,365,73,19,333,91,41,-1,175,-2,331,-5,281,-2,314,-1,326,-1,189,-3,286,-8,297,-1,25,-4,37,-8,189,-5,93,167,126,-1,274,346,85,-5,350,-2,128,[1,350],201,-2,129,-1,324,-3,190,-1,243,356,-1,281,339,69,-1,241,184,-4,334,[13,233],85,-2,330,377,-1,279,-1,243,-2,147,[87,129],175,-1,124,247,241,[346,357],81,5,266,337,124,117,-1,79,330,-2,377,359,247,-3,345,[80,85],-2,47,-2,14,-2,326,164,-1,74,333,31,24,380,298,-1,357,255,171,304,[281,312],-1,192,248,-2,143,199,-1,9,96,194,350,-1,[134,251],236,268,148,-4,279,-3,11,-1,352,-2,134,-2,88,-1,238,-1,85,-5,387,185,78,[125,246],185,185,5,[46,388],197,-2,36,-1,131,173,-1,212,-2,352,249,-1,195,-2,351,93,[243,331],-10,126,46,-2,184,28,128,-1,85,-1,380,-1,127,121,351,28,-1,262,264,37,-1,315,-3,344,-2,252,-2,131,338,-1,25,-5,259,126,[310,350],120,-1,164,-5,125,257,13,-1,245,121,124,57,-1,262,122,[338,352],180,352,[243,344],130,301,351,182,114,379,348,-7,110,13,-2,61,317,300,-7,164,[85,240],-2,164,-4,[277,302],-3,134,187,-4,[283,337],-2,314,355,-1,[100,324],-1,355,-7,167,[85,235],74,197,-4,140,157,166,285,329,-1,333,123,355,352,352,-1,44,184,-2,324,131,-3,126,-1,97,-2,134,-1,137,-2,110,-4,85,-1,31,-1,296,-3,22,5,-1,317,19,107,-3,331,-3,240,56,-5,97,171,80,-5,346,391,-3,287,-3,241,-1,51,307,-1,13,-4,241,189,238,326,56,-1,[119,385],-6,201,-3,313,180,-6,352,352,352,335,269,-8,342,-6,356,-3,74,[156,180],-8,9,-10,82,-5,380,-1,334,283,91,242,-4,240,-1,67,-5,225,-1,116,363,125,-3,47,-1,326,-1,153,314,-1,331,-1,241,-7,61,-4,30,139,153,-1,127,174,-1,226,-8,159,-1,189,-2,299,-4,8,-13,150,-2,240,-1,294,-3,376,126,124,-2,75,-1,109,-1,375,-2,341,-4,262,328,-2,285,-2,264,302,-1,10,-2,13,-6,158,-1,124,164,-2,356,-1,352,197,-5,232,-3,331,-11,168,-2,341,-12,298,-4,193,-1,[183,324],-3,374,-2,326,-4,49,-1,22,-9,210,287,-10,244,68,166,170,147,1,13,-5,78,-4,344,128,266,-11,251,-2,121,-4,393,-5,266,-3,136,-9,241,-1,359,-4,328,124,114,-2,162,-5,[9,19],109,-1,326,-7,350,-11,343,13,-7,336,-3,114,-1,337,-3,297,287,344,-6,264,-1,304,-7,90,-7,[124,129],-5,24,192,-2,333,-1,224,-6,164,-7,309,-4,78,-17,364,-3,123,-5,113,-11,197,-4,213,-16,251,-1,78,-14,371,-3,209,-17,116,175,221,176,-2,243,-5,341,-3,356,-3,104,-8,37,250,-1,124,-2,285,-7,114,121,189,91,300,332,29,285,350,181,-7,327,266,[8,12],233,-5,351,-1,23,-6,345,109,-5,95,364,-2,129,85,36,68,-1,333,-2,250,355,369,-2,355,110,107,-3,255,-5,380,-3,97,-1,251,[282,350],-1,171,97,56,-2,164,35,251,198,-2,121,-3,347,322,-1,380,-2,155,128,-4,[91,105],-4,249,-3,377,183,-1,374,125,204,295,241,-3,[281,335],354,-4,74,-1,287,-1,82,283,-3,374,330,85,164,-2,39,-4,346,314,-1,319,-1,329,377,-1,80,-1,190,-1,241,-2,156,189,244,-1,331,-3,312,-4,356,-2,233,-2,350,-2,252,-3,248,-2,104,324,-7,352,104,30,-2,143,-1,336,-4,356,-3,120,-1,14,-6,85,-2,355,[250,355],185,-8,85,-1,153,-3,116,-1,64,-4,202,174,44,262,-3,159,228,-1,331,-2,357,-2,297,-1,262,-5,247,-3,306,-7,195,-8,183,-2,[285,374],25,36,-1,4,-3,306,-1,235,-4,180,-2,378,-2,289,-5,181,-3,185,331,-5,372,184,334,-7,232,-4,227,-4,120,-4,121,-3,279,-10,336,-4,30,-4,180,-6,166,-8,189,-1,264,123,-4,193,-5,164,44,-15,135,-6,69,-2,251,-3,343,-3,219,337,-6,[108,338],346,-6,333,-2,129,-1,345,-7,113,251,350,-1,20,-2,15,28,-1,279,34,-1,103,-1,[51,289],-1,200,-4,378,-5,259,247,130,-1,357,187,-1,4,212,-2,125,-2,55,-1,229,-4,305,-3,340,-5,322,-5,227,-1,185,-6,[10,233],-1,331,-3,85,-5,242,-4,91,-4,22,169,-2,384,57,-2,144,-4,35,-5,350,356,-2,250,254,-5,277,-2,166,166,148,126,-2,13,-7,73,15,-1,[31,280],230,-3,180,104,-2,[310,331],-1,134,-3,98,-7,14,-5,110,-1,9,9,-5,10,39,176,-5,54,-3,264,-4,[319,320],36,-4,243,-3,158,-1,374,-10,244,-2,334,-25,13,130,-6,264,-20,227,-3,331,-1,348,-1,[247,305],-2,85,-58,[126,333],100,-1,102,190,285,30,158,136,124,331,61,-1,356,96,130,251,128,-3,136,-3,97,-1,[388,393],-1,280,-4,129,-1,95,39,-4,298,-3,377,-3,116,-12,346,-6,114,-54,388,-9,164,-48,371,-15,356,309,-8,285,-115,126,-30,248,-63,131,-5,233,-52,124,65,85,259,124,129,114,307,256,279,241,-1,344,350,344,124,-1,127,121,224,134,345,[200,205],341,74,179,-1,296,82,282,79,136,376,97,110,238,391,285,-1,369,298,375,61,379,46,251,373,-1,350,350,152,162,285,98,285,129,121,35,380,283,117,56,96,252,102,344,350,376,87,334,28,122,341,-1,129,330,356,245,330,90,355,121,152,[292,294,358],296,-1,248,380,390,222,69,387,80,143,326,356,292,283,[63,312],30,167,386,300,305,-1,350,198,34,64,120,126,336,343,349,326,74,356,342,30,388,2,346,61,190,234,-1,197,57,298,336,348,8,285,243,190,130,183,374,126,196,305,367,245,158,240,136,346,243,371,34,97,-9,123,-4,68,127,-1,36,-2,324,-8,285,-4,320,-6,334,119,-7,109,356,-7,17,-4,377,9,29,-7,63,-2,340,-3,[109,110],197,-1,185,-7,233,-3,197,-132,10,375,85,-1,95,22,365,333,6,372,123,377,78,305,237,14,96,380,100,76,126,13,285,313,102,152,55,185,80,110,350,366,377,[97,125],121,388,170,175,363,388,87,130,250,375,157,282,85,69,124,287,280,46,-2,94,230,56,157,-1,385,85,383,270,365,346,362,-1,368,279,352,88,36,-1,282,202,-3,110,-1,374,-2,390,-2,133,85,373,88,241,-9,34,-2,[134,246],32,-4,358,251,-5,388,-2,169,-10,306,-3,251,-13,362,-1,391,225,9,-2,72,-1,85,-1,346,-1,377,303,-3,358,244,-1,304,-4,5,64,-1,321,125,-1,229,-4,377,85,-2,279,19,-1,134,[164,180],93,-3,333,128,-3,149,-1,102,249,153,-2,175,-3,312,-1,126,-1,245,13,333,73,124,-1,124,-7,343,-3,167,354,-1,38,-2,303,-4,45,-8,118,134,36,-2,310,19,-5,22,389,-1,377,-1,62,377,-1,73,-3,378,-3,40,126,-2,134,-2,336,234,263,-1,310,-2,126,58,53,331,303,-5,213,227,-6,49,-12,12,-2,16,-2,60,27,-1,78,39,-1,72,-1,49,-1,136,-5,240,-2,50,-1,364,-3,380,-1,39,-7,170,169,-3,30,-7,392,-1,336,-12,283,95,-2,251,-2,73,-7,306,-19,326,-151,[33,134],[86,345,369],102,342,-1,259,383,74,179,265,114,97,143,175,379,377,350,116,375,164,348,248,285,361,377,128,-1,252,175,128,374,85,167,210,10,121,103,325,-1,45,388,48,85,124,-1,287,230,357,332,371,175,374,170,337,97,-1,46,[13,233],-3,156,-4,14,14,-1,14,-4,34,264,-7,14,-3,168,54,-2,243,356,-1,241,344,-1,104,182,-5,352,-1,359,130,-1,345,78,330,-2,[106,119],374,-1,130,357,326,166,36,-2,312,-1,350,132,125,-1,55,76,61,239,-1,64,365,-1,287,-2,131,-3,190,12,124,-3,385,-2,319,296,285,307,228,115,209,-2,342,344,20,-1,335,-1,316,380,250,61,-1,317,-1,310,68,-2,315,101,-1,285,35,298,364,254,82,-3,164,-6,119,55,175,-5,153,326,-1,350,-2,180,13,-1,356,-2,72,300,-1,41,-2,356,-5,14,-1,74,332,120,250,58,-3,350,96,-2,172,-6,303,243,-1,348,-2,4,-4,364,374,-5,170,394,-7,134,-1,13,348,-1,336,300,-4,192,-2,156,-5,350,-1,60,-1,354,-1,107,-1,184,-1,249,-1,152,-3,79,338,200,-2,8,-3,336,-1,330,-1,355,107,304,250,-1,233,18,281,10,-1,61,390,349,170,-2,380,-2,356,-2,246,377,-1,[119,344],-2,128,-3,159,125,150,376,-1,359,-3,331,-3,85,90,109,-3,137,352,-3,164,359,20,-2,233,-1,104,-1,305,-4,34,-3,56,-4,[68,69],346,-3,74,-1,135,-20,13,-4,351,-3,346,372,-11,279,-1,239,-7,390,-9,171,-2,82,-2,355,65,250,387,230,379,350,88,-1,133,-4,341,-2,81,-2,359,304,-2,321,[49,395],107,97,298,-3,195,160,-1,38,-1,315,377,333,127,35,-1,317,128,187,148,299,162,-3,346,278,211,-5,230,-1,44,-1,393,-1,49,154,310,-2,116,341,338,-7,190,252,-6,106,306,-6,160,-2,20,128,331,-4,164,134,-4,344,-12,22,-1,355,285,-1,164,[37,378],349,167,-1,130,-10,85,-151,126,-25,249,-31,177,-25,330,-117,362,-43,198,-36,172,-58,4,-11,4,-103,10,-44,337,-26,86,350,375,65,373,239,168,317,243,41,279,-1,78,63,188,219,-1,29,-1,87,20,304,134,72,32,378,200,10,89,7,243,[348,358],247,137,330,96,141,79,123,68,216,5,356,243,376,243,97,19,143,239,20,19,358,392,199,305,125,62,355,313,19,171,294,243,185,-1,285,342,303,13,209,233,73,-1,142,160,76,-2,355,35,125,349,204,-1,[35,57],315,176,63,351,139,369,380,[331,333],314,66,-1,117,252,277,105,348,91,195,376,274,128,350,30,37,306,2,351,264,380,160,240,-1,157,308,166,145,335,302,164,368,39,104,90,74,340,53,178,82,337,172,139,126,266,310,167,247,134,0,244,374,222,53,185,11,-1,59,143,154,-1,331,97,180,43,385,130,-1,333,135,123,230,305,65,126,134,189,388,246,-1,139,244,295,74,28,245,378,70,297,-1,119,1,69,187,174,-1,80,187,197,375,-1,91,213,-1,135,213,200,172,90,8,350,125,17,-1,15,306,183,180,-1,354,131,61,391,342,-1,337,136,168,240,175,71,158,240,50,244,60,123,162,-1,387,166,350,28,15,-2,334,-8,[31,372],-104,188,290,279,346,-1,13,327,42,267,326,333,114,126,194,141,188,369,204,102,327,303,194,176,-1,77,91,110,154,133,358,159,-1,356,346,31,331,327,122,346,346,30,158,251,-1,155,253,110,311,-1,253,[107,140],-1,85,-2,71,-1,243,330,-7,7,-3,265,-2,131,79,347,351,375,129,-4,391,395,62,-1,[0,74],321,-1,10,-1,85,124,175,173,34,338,-1,174,197,[127,334],-2,333,-3,87,279,-5,13,-3,377,-1,68,357,-1,39,-2,213,359,333,230,-1,390,-5,43,-1,233,171,307,333,-13,356,173,-1,326,-2,300,-1,120,-1,300,351,-3,91,-2,[153,326],1,331,-2,372,-10,300,-8,331,-2,121,-2,164,-2,385,-2,301,135,202,-1,253,346,-2,339,345,124,97,-1,377,-2,46,354,134,39,-2,180,-2,63,-10,38,-7,356,356,343,-4,327,-3,164,-2,171,162,-1,9,-4,330,-1,341,124,-2,335,-1,314,375,230,187,-3,123,277,80,-3,209,-2,170,-5,291,-1,332,-11,351,-2,1,-2,333,-1,[174,175],-5,5,233,-4,182,-19,248,[127,167],-2,131,-2,131,-1,62,-2,80,-1,142,190,191,-2,349,-3,91,-9,130,343,-1,5,-5,54,-8,347,-5,336,-1,2,-3,54,245,-1,183,-3,[245,281],-7,134,-2,263,-7,134,-1,14,-1,126,-2,96,-1,10,-49,326,259,-1,107,326,359,307,133,-5,351,-1,359,281,-126,349,65,248,107,334,293,341,341,324,97,72,241,7,296,108,356,175,171,239,131,[129,336],125,-1,[91,110],352,-1,143,350,237,-1,319,107,-1,352,143,310,-1,74,383,346,74,213,183,62,272,109,-1,30,264,237,252,-38,82,-2,15,269,134,-1,297,-2,235,15,15,-3,80,[285,295],-7,301,334,-36,346,-2,23,-29,313,-37,354,307,-15,124,-1,338,320,331,259,356,36,78,351,126,285,9,295,-1,350,76,257,334,-2,128,-1,18,19,-1,74,356,206,-2,122,333,100,28,153,-1,297,30,-1,197,-1,172,340,130,183,271,383,203,286,153,104,334,-11,85,-2,337,-195,181,356,321,344,36,251,-1,19,176,363,285,295,85,134,390,380,321,218,125,350,304,335,181,-1,128,117,180,106,234,-1,164,35,346,-2,137,241,[124,241],143,385,-2,23,234,377,-1,273,330,4,172,243,279,15,180,47,30,379,124,-1,334,97,-7,316,129,-4,61,148,106,-1,115,-1,91,-3,13,143,242,-2,174,-5,151,17,-4,69,-1,300,-4,90,-8,154,-4,185,-4,312,-1,388,-1,255,-9,340,-1,124,-7,389,-4,252,-6,133,-3,17,-11,119,-3,169,-11,31,-2,91,-8,356,102,-4,153,122,5,239,187,-1,346,335,167,-3,325,-1,326,-1,36,-2,197,-295,356,-2,355,-1,175,79,-2,5,238,210,175,-2,85,-1,9,115,-1,304,102,129,-1,326,76,-4,124,128,333,-1,334,344,94,164,166,126,164,285,312,103,277,119,-1,124,-1,248,171,-1,390,80,154,31,97,209,210,63,131,-1,285,388,81,64,-5,270,74,250,85,120,-2,14,273,4,241,303,100,348,-2,161,15,343,16,183,194,354,-2,102,279,170,394,-2,164,-316,212,133,124,357,195,-1,224,345,-1,9,375,97,67,175,345,-1,347,-1,36,251,357,-1,321,295,377,76,98,340,-2,91,177,114,-1,19,164,135,[97,116],74,356,333,310,330,253,192,2,-1,10,232,-1,44,-3,116,-1,74,[97,116],-1,46,187,330,-1,348,-4,126,110,-1,352,374,172,168,128,133,356,175,-2,352,116,-1,100,-10,53,175,-2,124,-4,[137,254],380,-2,190,-6,241,175,-9,282,-1,170,-6,182,-13,251,-3,85,-2,181,-1,[181,186,197,348],121,-5,120,-4,114,-3,287,164,210,-1,111,-2,243,-3,197,-2,55,39,355,-1,350,332,-1,251,-2,248,-1,164,-1,69,23,-4,2,-9,377,-1,85,85,-1,194,-12,357,-1,321,65,-1,201,-2,97,-3,307,-1,233,-6,287,-1,81,-9,355,-2,330,-7,331,-1,346,-1,13,-1,250,107,-5,369,-11,241,124,-45,36,34,-2,134,171,9,312,388,[144,351],356,45,251,329,-12,173,95,140,-3,102,358],"a,ai,an,ang,ao,ba,bai,ban,bang,bao,bei,ben,beng,bi,bian,biao,bie,bin,bing,bo,bu,ca,cai,can,cang,cao,ce,ceng,cha,chai,chan,chang,chao,che,chen,cheng,chi,chong,chou,chu,chuai,chuan,chuang,chui,chun,chuo,ci,cong,cou,cu,cuan,cui,cun,cuo,da,dai,dan,dang,dao,de,deng,di,dian,diao,die,ding,diu,dong,dou,du,duan,dui,dun,duo,e,en,er,fa,fan,fang,fei,fen,feng,fo,fou,fu,ga,gai,gan,gang,gao,ge,gei,gen,geng,gong,gou,gu,gua,guai,guan,guang,gui,gun,guo,ha,hai,han,hang,hao,he,hei,hen,heng,hong,hou,hu,hua,huai,huan,huang,hui,hun,huo,ji,jia,jian,jiang,jiao,jie,jin,jing,jiong,jiu,ju,juan,jue,jun,ka,kai,kan,kang,kao,ke,ken,keng,kong,kou,ku,kua,kuai,kuan,kuang,kui,kun,kuo,la,lai,lan,lang,lao,le,lei,leng,li,lia,lian,liang,liao,lie,lin,ling,liu,long,lou,lu,lv,luan,lue,lun,luo,ma,mai,man,mang,mao,me,mei,men,meng,mi,mian,miao,mie,min,ming,miu,mo,mou,mu,na,nai,nan,nang,nao,ne,nei,nen,neng,ni,nian,niang,niao,nie,nin,ning,niu,nong,nu,nv,nuan,nue,nuo,o,ou,pa,pai,pan,pang,pao,pei,pen,peng,pi,pian,piao,pie,pin,ping,po,pu,qi,qia,qian,qiang,qiao,qie,qin,qing,qiong,qiu,qu,quan,que,qun,ran,rang,rao,re,ren,reng,ri,rong,rou,ru,ruan,rui,run,ruo,sa,sai,san,sang,sao,se,sen,seng,sha,shai,shan,shang,shao,she,shen,sheng,shi,shou,shu,shua,shuai,shuan,shuang,shui,shun,shuo,si,song,sou,su,suan,sui,sun,suo,ta,tai,tan,tang,tao,te,teng,ti,tian,tiao,tie,ting,tong,tou,tu,tuan,tui,tun,tuo,wa,wai,wan,wang,wei,wen,weng,wo,wu,xi,xia,xian,xiang,xiao,xie,xin,xing,xiong,xiu,xu,xuan,xue,xun,ya,yan,yang,yao,ye,yi,yin,ying,yo,yong,you,yu,yuan,yue,yun,za,zai,zan,zang,zao,ze,zei,zen,zeng,zha,zhai,zhan,zhang,zhao,zhe,zhen,zheng,zhi,zhong,zhou,zhu,zhua,zhuai,zhuan,zhuang,zhui,zhun,zhuo,zi,zong,zou,zu,zuan,zui,zun,zuo"]}])},t.exports=n()},SLaa:function(t,e){},fxRn:function(t,e,a){a("+tPU"),a("zQR9"),t.exports=a("g8Ux")},g8Ux:function(t,e,a){var n=a("77Pl"),i=a("3fs2");t.exports=a("FeBl").getIterator=function(t){var e=i(t);if("function"!=typeof e)throw TypeError(t+" is not iterable!");return n(e.call(t))}},iayf:function(t,e,a){"use strict";a.d(e,"a",function(){return n});var n=[{name:"home",title:"首页",path:"/console/display/administration/index",pathType:"page"},{name:"seal",title:"印章管理",path:"https://ihr.china9.cn/human/main/index/v/ygt#/seal/index",pathType:"link",roles:["seal_index"]},{name:"purchase",title:"采购管理",path:"https://ihr.china9.cn/human/main/index/v/ygt#/purchase/index",pathType:"link",roles:["purchase"]},{name:"fixedAssets",title:"固定资产",path:"https://ihr.china9.cn/human/main/index/v/ygt#/assets/index",pathType:"link",roles:["assets"]},{name:"consumables",title:"消耗品管理",path:"https://ihr.china9.cn/human/main/index/v/ygt#/consumable/index",pathType:"link",roles:["consumable"]},{name:"qualifications",title:"资质证照",path:"https://ihr.china9.cn/human/main/index/v/ygt#/UserInfo/base_qualifications",pathType:"link",roles:["qualifications"]},{name:"affairs",title:"行政事务",path:"https://ihr.china9.cn/human/main/index/v/ygt#/printing_file/index",pathType:"link",roles:["printing_file"]},{name:"partyBuilding",title:"智慧党建",path:"https://ihr.china9.cn/human/main/index/v/ygt#/dangjian/news",pathType:"link",roles:["dangjian"]},{name:"camera",title:"视频管家",path:"https://monitor.china9.cn/#/home",pathType:"link",roles:["monitor_home"]},{name:"policy",title:"政府政策",path:"/console/display/administration/policy",pathType:"page"},{name:"logs",title:"操作日志",path:"https://ihr.china9.cn/human/main/index/v/ygt#/UserInfo/base_logs",pathType:"link",roles:["xzgl_logs"]}]},kqGT:function(t,e,a){"use strict";var n=a("Xxa5"),i=a.n(n),r=a("exGp"),o=a.n(r),s=(a("KV03"),a("F6wa")),l=a("OL/w"),c=a("9RLB"),u=a("wU6q"),h=a("RqJO"),p=a("pI5c"),d={name:"News",components:{CardWrap:u.a},data:function(){return{showTitle:!0,activeNews:"4",newsList:[{name:"媒体报道",value:"4",data:[]},{name:"企业新闻",value:"1",data:[]},{name:"企业通知",value:"2",data:[]}],page:1,lastPage:1,loading:!1}},created:function(){this.showTitle=this.$route.meta&&this.$route.meta.showNav},watch:{activeNews:{handler:function(t,e){t&&t!==e&&(this.page=1,this.getNews())},immediate:!0}},methods:{getNews:function(){var t=this;return o()(i.a.mark(function e(){var a,n,r;return i.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t.page>t.lastPage)){e.next=2;break}return e.abrupt("return",t.page=t.lastPage);case 2:return t.loading=!0,e.next=5,Object(p.v)({type:t.activeNews,page:t.page});case 5:200===(a=e.sent).code&&a.data&&a.data.data&&a.data.data.length&&(n=t.newsList.find(function(e){return e.value===t.activeNews}),r=a.data.data,t.lastPage=a.data.last_page,1===t.page?t.$set(n,"data",r):n.data=n.data.concat(r)),t.loading=!1;case 8:case"end":return e.stop()}},e,t)}))()},toMore:function(){this.$router.push("/company/information/list")},toDetail:function(t){this.showTitle?window.open("/console/display/newsDetail/"+t.id):window.open("https://china9.cn/#/console/display/newsDetail/"+t.id)},load:function(){this.page++,this.getNews(),console.log("load")}}},g={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("card-wrap",{style:"height: 370px;",attrs:{title:"企业消息","show-more":!1},on:{toMore:t.toMore}},[a("el-tabs",{model:{value:t.activeNews,callback:function(e){t.activeNews=e},expression:"activeNews"}},t._l(t.newsList,function(e,n){return a("el-tab-pane",{key:n,attrs:{label:e.name,name:e.value}},[a("div",{directives:[{name:"infinite-scroll",rawName:"v-infinite-scroll",value:t.load,expression:"load"}],staticClass:"news-wrap",attrs:{"infinite-scroll-disabled":t.loading}},[e.data.length?t._l(e.data,function(e,n){return a("div",{key:n,staticClass:"news-item",on:{click:function(a){return t.toDetail(e)}}},[a("div",{staticClass:"title text-ellipsis",attrs:{title:e.title}},[t._v(t._s(e.title))]),t._v(" "),a("div",{staticClass:"time"},[t._v(t._s(e.publish_at))])])}):a("div",[a("el-empty")],1)],2)])}),1)],1)},staticRenderFns:[]};var f=a("VU/8")(d,g,!1,function(t){a("mScT")},"data-v-21ea0528",null).exports,v=a("zbWR"),y=a("JYzS"),m=a("bwDj"),x={name:"Administration",components:{AiToolsCard:a("ev7b").a,CardListNew:m.a,CardList:s.a,ImportantData:l.a,ECharts:c.a,CardWrap:u.a,ActionList:h.a,CompanyNews:f},mixins:[y.a],props:{pathType:{type:String,default:"page"}},data:function(){return{showTitle:!0,cardList:[{logo:a("xJ/x"),title:"发布通知",desc:"通知、公告快速发布",bgColor:"#FF9831",pathType:"page",path:"/company/information/list"},{logo:a("PmS9"),title:"政府政策",desc:"企业优惠政策解读",bgColor:"#0880FF",pathType:"page",path:"/console/display/administration/policy"},{logo:a("qc7c"),title:"印章管理",desc:"智慧用印与记录查询",bgColor:"#857BFF",pathType:"link",path:"https://ihr.china9.cn/human/main/index/v/ygt/m/xzgl#/seal/index",roles:["seal_index"]},{logo:a("1mTQ"),title:"行政事务",desc:"各类行政事务一站管理",bgColor:"#52C744",pathType:"link",path:"https://ihr.china9.cn/human/main/index/v/ygt/m/xzgl#/printing_file/index",roles:["printing_file"]},{logo:a("g/sm"),title:"视频管家",desc:"企业安全智能监控管理",bgColor:"#08ABFF",pathType:"link",path:"https://monitor.china9.cn/#/home",roles:["monitor_home"]}],importantData:[{name:"本月发布通知",value:"--",key:"notice"},{name:"今日访客",value:"--",key:"visitor"},{name:"今日邮寄快递",value:"--",key:"express"},{name:"今日报修",value:"--",key:"storehouse"},{name:"固定资产金额",value:"--",key:"assets"}],fixedAssetsData:{x:["使用中","闲置中","已废弃"],y:[],key:["shiyong","xianzhi","baofei"]},consumablesData:{x:["入库","出库","现有","本月消耗"],y:[],key:["ruku","chuku","xianyou","benyue"]},inventoryData:{x:[],y:[],key:[]},chartHeight:106,sealRecord:[],actionData:[{icon:a("J5ef"),title:"文件打印",pathType:"link",path:"https://ihr.china9.cn/human/main/index/v/ygt/m/xzgl#/printing_file/index",roles:["printing_file"]},{icon:a("Ln23"),title:"采购管理",pathType:"link",path:"https://ihr.china9.cn/human/main/index/v/ygt/m/xzgl#/purchase/index",roles:["purchase"]},{icon:a("qaK7"),title:"资产盘点",pathType:"link",path:"https://ihr.china9.cn/human/main/index/v/ygt/m/xzgl#/assets/inventory",roles:["inventory"]},{icon:a("c+Pz"),title:"用印审批",pathType:"link",path:"https://ihr.china9.cn/human/main/index/v/ygt/m/xzgl#/ApprovalFlow/approval_flow_list",roles:["approval_flow_list"]},{icon:a("Ggmo"),title:"消耗品",pathType:"link",path:"https://ihr.china9.cn/human/main/index/v/ygt/m/xzgl#/consumable/index",roles:["consumable"]},{icon:a("Kscf"),title:"访客记录",pathType:"link",path:"https://ihr.china9.cn/human/main/index/v/ygt/m/xzgl#/visitor/index",roles:["visitor"]}],sealPath:{pathType:"link",path:"https://ihr.china9.cn/human/main/index/v/ygt/m/xzgl#/seal/znindex",roles:["znindex"]},pageData:{}}},computed:{newHeight:function(){return 408}},created:function(){if(this.showTitle=this.$route.meta&&this.$route.meta.showNav,!this.showTitle){var t=this.cardList.find(function(t){return"政府政策"===t.title});t&&"page"===t.pathType&&(-1===t.path.indexOf("?")?t.path+="?showNav=false":t.path+="&showNav=false",console.log(t.path))}},mounted:function(){this.getPageData(),this.getSealData()},methods:{handleToAI:function(){window.open("https://china9.cn/#/console/ai-tools-page")},getPageData:function(){var t=this;return o()(i.a.mark(function e(){var a;return i.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(v.a)();case 2:a=e.sent,t.pageData=a.data,t.importantData.forEach(function(t){t.value=a.data[t.key]}),t.fixedAssetsData.y=t.fixedAssetsData.key.map(function(t){return a.data.assetsTable[t]}),t.consumablesData.y=t.consumablesData.key.map(function(t){return a.data.consumable[t]}),a.data.position_list&&a.data.position_list.length&&(t.inventoryData.x=a.data.position_list.map(function(t){return t.title}),t.inventoryData.y=a.data.position_list.map(function(t){return t.value}));case 8:case"end":return e.stop()}},e,t)}))()},getSealData:function(){var t=this;return o()(i.a.mark(function e(){var a;return i.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(v.b)();case 2:a=e.sent,t.sealRecord=a.data.data;case 4:case"end":return e.stop()}},e,t)}))()},jump:function(t){"page"===this.pathType?"components"===t.pathType?this.$emit("jump",t.path):this.toPage(t):this.$emit("jump",t)}}},w={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page-box"},[a("el-row",[a("card-list",{attrs:{cardList:t.cardList,"item-style":{marginBottom:"20px"}},on:{click:t.jump}})],1),t._v(" "),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{lg:15,md:24,sm:24,xs:24}},[a("el-row",[a("important-data",{attrs:{importantData:t.importantData}})],1),t._v(" "),a("div",{staticClass:"gap-h-20"}),t._v(" "),a("el-row",[a("company-news",{staticClass:"flex-1",staticStyle:{height:"438px"}})],1),t._v(" "),a("div",{staticClass:"gap-h-20"}),t._v(" "),a("el-row",[a("card-wrap",{staticStyle:{height:"500px"},attrs:{title:"用印记录","show-more":!0},on:{toMore:function(e){return t.toPage(t.sealPath)}}},[a("div",{staticClass:"table-wrap"},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.sealRecord,height:"350px","header-row-class-name":"table-header","row-class-name":"table-row"}},[a("el-table-column",{attrs:{prop:"title",label:"印章名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"filename",label:"用印文件"}}),t._v(" "),a("el-table-column",{attrs:{prop:"sqr",label:"用印人"}}),t._v(" "),a("el-table-column",{attrs:{prop:"addtime",label:"用印时间"}})],1)],1)])],1)],1),t._v(" "),a("el-col",{attrs:{lg:9,md:24,sm:24,xs:24}},[a("el-row",[a("ai-tools-card",{attrs:{page:t.showTitle?"":"display"},on:{toAiTools:t.handleToAI}})],1),t._v(" "),a("div",{staticClass:"gap-h-20"}),t._v(" "),a("el-row",[a("card-wrap",{staticClass:"common-function",attrs:{title:"常用功能"}},[a("action-list",{attrs:{list:t.actionData},on:{click:t.toPage}})],1)],1),t._v(" "),a("div",{staticClass:"gap-h-20"}),t._v(" "),a("el-row",[a("e-charts",{attrs:{title:"固定资产金额汇总",data:t.fixedAssetsData,height:t.chartHeight,color:"#02A7F0"}})],1),t._v(" "),a("div",{staticClass:"gap-h-20"}),t._v(" "),a("el-row",[a("e-charts",{attrs:{title:"消耗品金额统计",data:t.consumablesData,height:t.chartHeight,color:"#FFA63E"}})],1),t._v(" "),a("div",{staticClass:"gap-h-20"}),t._v(" "),a("el-row",[a("e-charts",{attrs:{title:"库房盘点",data:t.inventoryData,height:t.chartHeight,color:"#24B665"}})],1)],1)],1)],1)},staticRenderFns:[]};var b=a("VU/8")(x,w,!1,function(t){a("s9ly")},"data-v-2b0f6b2e",null);e.a=b.exports},mScT:function(t,e){},s9ly:function(t,e){}});