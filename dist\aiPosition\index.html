<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 问答系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4a6cf7;
            --hover-color: #3655d8;
            --dark-text: #1d2a4d;
            --light-text: #6c7a93;
            --bg-light: #f9fafc;
            --bg-dark: #f1f4f9;
            --border-color: #e6e9f0;
            --panel-bg: #ffffff;
            --shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            --error-color: #ff5470;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', sans-serif;
            background-color: var(--bg-light);
            color: var(--dark-text);
            display: flex;
            flex-direction: column;
            line-height: 1.6;
            height: 100vh;
        }

        header {
            background: var(--panel-bg);
            box-shadow: var(--shadow);
            padding: 15px 0;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
        }

        header h1 {
            font-size: 1.8rem;
            color: var(--dark-text);
            font-weight: 600;
        }

        header p {
            color: var(--light-text);
            font-size: 0.95rem;
            margin-top: 5px;
        }

        .container {
            display: flex;
            flex: 1;
            padding: 30px;
            gap: 30px;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
            max-height: calc(100vh - 107px - 64px);
            min-height: 700px;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                padding: 20px;
            }
        }

        .panel {
            background: var(--panel-bg);
            border-radius: 16px;
            box-shadow: var(--shadow);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .left-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .right-panel {
            flex: 1.2;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            background: var(--primary-color);
            color: white;
            padding: 18px 25px;
            font-size: 1.2rem;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .panel-header i {
            margin-right: 10px;
            font-size: 1.1rem;
        }

        .panel-body {
            padding: 25px;
            flex: 1;
            display: flex;
            flex-direction: column;
            height: calc(100% - 67px);
        }

        .form-group {
            margin-bottom: 25px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        label {
            display: block;
            margin-bottom: 10px;
            font-weight: 500;
            color: var(--dark-text);
            font-size: 1.05rem;
        }

        textarea {
            width: 100%;
            padding: 16px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            resize: vertical;
            min-height: 140px;
            font-size: 15px;
            transition: all 0.3s ease;
            background-color: var(--bg-light);
            color: var(--dark-text);
            font-family: inherit;
            flex: 1;
        }

        textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.15);
        }

        .btn {
            background-color: var(--primary-color);
            color: white;
            padding: 14px 28px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn i {
            margin-right: 8px;
        }

        .btn:hover {
            background-color: var(--hover-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(74, 108, 247, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .result-title {
            font-size: 18px;
            font-weight: 500;
            color: var(--dark-text);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .result-title i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .result-content {
            background-color: var(--bg-dark);
            padding: 20px;
            border-radius: 12px;
            min-height: 250px;
            border: 1px solid var(--border-color);
            white-space: pre-line;
            line-height: 1.7;
            flex: 1;
            overflow-y: auto;
            max-height: calc(100% - 37px - 15px);
        }

        /* 职位信息相关样式 */
        .job-title {
            font-size: 24px;
            font-weight: bold;
            color: var(--dark-text);
            margin-bottom: 20px;
            text-align: center;
        }

        .job-section {
            margin-bottom: 25px;
            background: var(--panel-bg);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
            padding-left: 10px;
        }

        .task-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .task-table th {
            background-color: rgba(74, 108, 247, 0.1);
            color: var(--dark-text);
            padding: 10px;
            text-align: left;
            font-weight: 500;
        }

        .task-table td {
            padding: 10px;
            border-top: 1px solid var(--border-color);
        }

        .task-table tr:hover {
            background-color: rgba(74, 108, 247, 0.05);
        }

        .required::after {
            content: "*";
            color: var(--error-color);
            margin-left: 4px;
        }

        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 50px;
            font-size: 0.85rem;
            background-color: #f3f4f6;
            color: var(--light-text);
            margin-top: 5px;
            margin-left: 15px;
        }

        .status.waiting {
            background-color: #fff8e6;
            color: #e6a23c;
        }

        .status.processing {
            background-color: #ecf5ff;
            color: #409eff;
        }

        .status.completed {
            background-color: #f0f9eb;
            color: #67c23a;
        }

        /* 加载动画样式 */
        .spinner {
            display: none;
            width: 40px;
            height: 40px;
            margin: 30px auto;
            border: 4px solid rgba(74, 108, 247, 0.1);
            border-radius: 50%;
            border-top: 4px solid var(--primary-color);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .btn-loading .spinner-btn {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 8px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 2px solid #fff;
            animation: spin 1s linear infinite;
        }

        footer {
            text-align: center;
            padding: 20px;
            font-size: 0.9rem;
            color: var(--light-text);
            background: var(--panel-bg);
            border-top: 1px solid var(--border-color);
        }

        /* 单选按钮组样式 */
        .radio-group {
            display: flex;
            gap: 20px;
            margin-bottom: 25px;
        }

        .radio-item {
            display: flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
        }

        .radio-item input[type="radio"] {
            margin-right: 8px;
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .radio-item label {
            margin-bottom: 0;
            cursor: pointer;
            font-size: 15px;
        }
    </style>
</head>
<body>
    <header>
        <h1>AI 智能问答系统</h1>
        <p>输入您的信息和问题，获取智能回答</p>
    </header>

    <div class="container">
        <div class="panel left-panel">
            <div class="panel-header">
                <i class="fas fa-edit"></i>数据输入
            </div>
            <div class="panel-body" style="display: flex; flex-direction: column; height: 100%;">
                <form id="inputForm" style="display: flex; flex-direction: column; height: 100%;">
                    <div class="form-group">
                        <label class="required">用户信息</label>
                        <textarea id="userInfo" placeholder="请输入您的详细背景、经验等用户信息..." required></textarea>
                    </div>
                    <div class="form-group">
                        <label class="required">问题要求</label>
                        <textarea id="question" placeholder="请详细描述您的问题或要求..." required></textarea>
                    </div>
                    <div class="radio-group">
                        <div class="radio-item">
                            <input type="radio" id="type1" name="type" value="1" checked>
                            <label for="type1">联网搜索</label>
                        </div>
                        <div class="radio-item">
                            <input type="radio" id="type2" name="type" value="2">
                            <label for="type2">深度思考</label>
                        </div>
                    </div>
                    <button type="submit" class="btn">
                        <i class="fas fa-paper-plane"></i>提交问题
                    </button>
                </form>
            </div>
        </div>
        <div class="panel right-panel">
            <div class="panel-header">
                <i class="fas fa-robot"></i>AI 回答结果
            </div>
            <div class="panel-body">
                <div class="result-title">
                    <i class="fas fa-comment-dots"></i>回答内容
                    <span id="status" class="status waiting">等待提交</span>
                </div>
                <div class="spinner" id="loading"></div>
                <div class="result-content" id="result">
                    您的问题解答将在这里显示...
                </div>
            </div>
        </div>
    </div>

    <footer>
        © 2023 AI 智能问答系统 | 所有权利保留
    </footer>

    <script>
        document.getElementById('inputForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const userInfo = document.getElementById('userInfo').value;
            const question = document.getElementById('question').value;
            const result = document.getElementById('result');
            const status = document.getElementById('status');
            const loading = document.getElementById('loading');
            const submitBtn = this.querySelector('.btn');

            // 检查输入是否为空
            if (!userInfo.trim() || !question.trim()) {
                alert('请填写所有必填项');
                return;
            }

            // 更新状态和显示加载动画
            status.className = 'status processing';
            status.textContent = '处理中...';
            result.style.display = 'none';
            loading.style.display = 'block';

            // 添加按钮加载状态
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-btn"></span>处理中...';

            // 准备请求数据
            const requestData = {
                usercontent: userInfo,
                systemcontent: question,
                type: document.querySelector('input[name="type"]:checked').value
            };

            // 发送API请求
            fetch('https://api.china9.cn/api/questlist/deepseekGangwei', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败，状态码: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                // 处理返回的数据
                console.log('API响应:', data);

                // 隐藏加载动画，显示结果
                loading.style.display = 'none';
                result.style.display = 'block';

                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i>提交问题';

                // 将返回结果显示在结果区域
                if (data && data.code === 200 && data.data) {
                    // 处理特定结构的职位数据
                    const jobData = data.data;
                    if (jobData.job && jobData.job_content) {
                        // 格式化并显示职位信息
                        result.innerHTML = formatJobData(jobData);
                    } else {
                        result.textContent = typeof data.data === 'string' ? data.data : JSON.stringify(data.data, null, 2);
                    }
                } else if (data && data.content) {
                    result.textContent = data.content;
                } else {
                    result.textContent = JSON.stringify(data, null, 2);
                }

                // 更新状态为完成
                status.className = 'status completed';
                status.textContent = '回答完成';
            })
            .catch(error => {
                console.error('请求错误:', error);

                // 隐藏加载动画，显示结果
                loading.style.display = 'none';
                result.style.display = 'block';

                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i>提交问题';

                result.textContent = `请求发生错误：${error.message}`;
                status.className = 'status waiting';
                status.textContent = '请求失败';
            });
        });

        // 格式化职位数据为HTML
        function formatJobData(jobData) {
            let html = `<div class="job-title">${jobData.job}</div>`;

            // 遍历每个工作内容部分
            jobData.job_content.forEach(section => {
                html += `
                <div class="job-section">
                    <div class="section-title">${section.cont}</div>
                    <table class="task-table">
                        <thead>
                            <tr>
                                <th width="30%">工作内容</th>
                                <th width="20%">频率</th>
                                <th width="25%">时间周期</th>
                                <th width="25%">具体时间</th>
                            </tr>
                        </thead>
                        <tbody>
                `;

                // 遍历每个操作
                section.operation.forEach(op => {
                    html += `
                        <tr>
                            <td>${op.title}</td>
                            <td>${op.frequency}</td>
                            <td>${op.frequencytime}</td>
                            <td>${op.time}</td>
                        </tr>
                    `;
                });

                html += `
                        </tbody>
                    </table>
                </div>
                `;
            });

            return html;
        }
    </script>
</body>
</html>