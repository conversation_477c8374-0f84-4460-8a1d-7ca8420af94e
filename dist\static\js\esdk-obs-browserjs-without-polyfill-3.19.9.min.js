"use strict";var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};!function(e){var t,r=e.Base64;if("undefined"!=typeof module&&module.exports)try{t=require("buffer").Buffer}catch(e){}var n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=function(e){for(var t={},r=0,n=e.length;r<n;r++)t[e.charAt(r)]=r;return t}(n),a=String.fromCharCode,i=function(e){if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?a(192|t>>>6)+a(128|63&t):a(224|t>>>12&15)+a(128|t>>>6&63)+a(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return a(240|t>>>18&7)+a(128|t>>>12&63)+a(128|t>>>6&63)+a(128|63&t)},s=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,c=function(e){return e.replace(s,i)},l=function(e){var t=[0,2,1][e.length%3],r=e.charCodeAt(0)<<16|(e.length>1?e.charCodeAt(1):0)<<8|(e.length>2?e.charCodeAt(2):0);return[n.charAt(r>>>18),n.charAt(r>>>12&63),t>=2?"=":n.charAt(r>>>6&63),t>=1?"=":n.charAt(63&r)].join("")},u=e.btoa?function(t){return e.btoa(t)}:function(e){return e.replace(/[\s\S]{1,3}/g,l)},p=t?t.from&&t.from!==Uint8Array.from?function(e){return(e.constructor===t.constructor?e:t.from(e)).toString("base64")}:function(e){return(e.constructor===t.constructor?e:new t(e)).toString("base64")}:function(e){return u(c(e))},d=function(e,t){return t?p(String(e)).replace(/[+\/]/g,function(e){return"+"==e?"-":"_"}).replace(/=/g,""):p(String(e))},h=new RegExp(["[À-ß][-¿]","[à-ï][-¿]{2}","[ð-÷][-¿]{3}"].join("|"),"g"),f=function(e){switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return a(55296+(t>>>10))+a(56320+(1023&t));case 3:return a((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return a((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},m=function(e){return e.replace(h,f)},y=function(e){var t=e.length,r=t%4,n=(t>0?o[e.charAt(0)]<<18:0)|(t>1?o[e.charAt(1)]<<12:0)|(t>2?o[e.charAt(2)]<<6:0)|(t>3?o[e.charAt(3)]:0),i=[a(n>>>16),a(n>>>8&255),a(255&n)];return i.length-=[0,0,2,1][r],i.join("")},g=e.atob?function(t){return e.atob(t)}:function(e){return e.replace(/[\s\S]{1,4}/g,y)},A=t?t.from&&t.from!==Uint8Array.from?function(e){return(e.constructor===t.constructor?e:t.from(e,"base64")).toString()}:function(e){return(e.constructor===t.constructor?e:new t(e,"base64")).toString()}:function(e){return m(g(e))},x=function(e){return A(String(e).replace(/[-_]/g,function(e){return"-"==e?"+":"/"}).replace(/[^A-Za-z0-9\+\/]/g,""))};if(e.Base64={VERSION:"2.3.2",atob:g,btoa:u,fromBase64:x,toBase64:d,utob:c,encode:d,encodeURI:function(e){return d(e,!0)},btou:m,decode:x,noConflict:function(){var t=e.Base64;return e.Base64=r,t}},"function"==typeof Object.defineProperty){var b=function(e){return{value:e,enumerable:!1,writable:!0,configurable:!0}};e.Base64.extendString=function(){Object.defineProperty(String.prototype,"fromBase64",b(function(){return x(this)})),Object.defineProperty(String.prototype,"toBase64",b(function(e){return d(this,e)})),Object.defineProperty(String.prototype,"toBase64URI",b(function(){return d(this,!0)}))}}e.Meteor&&(Base64=e.Base64),"function"==typeof define&&define.amd&&define("Base64",[],function(){return e.Base64})}("undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:void 0),function(e){function t(e,t,r){var l,u,p,d,h,f,m,y,g,A=0,x=[],b=0,w=!1,v=[],C=[],P=!1,S=!1,k=-1;if(r=r||{},l=r.encoding||"UTF8",(g=r.numRounds||1)!==parseInt(g,10)||1>g)throw Error("numRounds must a integer >= 1");if("SHA-1"===e)h=512,f=D,m=L,d=160,y=function(e){return e.slice()};else if(0===e.lastIndexOf("SHA-",0))if(f=function(t,r){return q(t,r,e)},m=function(t,r,n,o){var a,i;if("SHA-224"===e||"SHA-256"===e)a=15+(r+65>>>9<<4),i=16;else{if("SHA-384"!==e&&"SHA-512"!==e)throw Error("Unexpected error in SHA-2 implementation");a=31+(r+129>>>10<<5),i=32}for(;t.length<=a;)t.push(0);for(t[r>>>5]|=128<<24-r%32,r+=n,t[a]=4294967295&r,t[a-1]=r/4294967296|0,n=t.length,r=0;r<n;r+=i)o=q(t.slice(r,r+i),o,e);if("SHA-224"===e)t=[o[0],o[1],o[2],o[3],o[4],o[5],o[6]];else if("SHA-256"===e)t=o;else if("SHA-384"===e)t=[o[0].a,o[0].b,o[1].a,o[1].b,o[2].a,o[2].b,o[3].a,o[3].b,o[4].a,o[4].b,o[5].a,o[5].b];else{if("SHA-512"!==e)throw Error("Unexpected error in SHA-2 implementation");t=[o[0].a,o[0].b,o[1].a,o[1].b,o[2].a,o[2].b,o[3].a,o[3].b,o[4].a,o[4].b,o[5].a,o[5].b,o[6].a,o[6].b,o[7].a,o[7].b]}return t},y=function(e){return e.slice()},"SHA-224"===e)h=512,d=224;else if("SHA-256"===e)h=512,d=256;else if("SHA-384"===e)h=1024,d=384;else{if("SHA-512"!==e)throw Error("Chosen SHA variant is not supported");h=1024,d=512}else{if(0!==e.lastIndexOf("SHA3-",0)&&0!==e.lastIndexOf("SHAKE",0))throw Error("Chosen SHA variant is not supported");var M=6;if(f=j,y=function(e){var t,r=[];for(t=0;5>t;t+=1)r[t]=e[t].slice();return r},k=1,"SHA3-224"===e)h=1152,d=224;else if("SHA3-256"===e)h=1088,d=256;else if("SHA3-384"===e)h=832,d=384;else if("SHA3-512"===e)h=576,d=512;else if("SHAKE128"===e)h=1344,d=-1,M=31,S=!0;else{if("SHAKE256"!==e)throw Error("Chosen SHA variant is not supported");h=1088,d=-1,M=31,S=!0}m=function(e,t,r,n,o){var a,i=M,s=[],c=(r=h)>>>5,l=0,u=t>>>5;for(a=0;a<u&&t>=r;a+=c)n=j(e.slice(a,a+c),n),t-=r;for(e=e.slice(a),t%=r;e.length<c;)e.push(0);for(e[(a=t>>>3)>>2]^=i<<a%4*8,e[c-1]^=2147483648,n=j(e,n);32*s.length<o&&(e=n[l%5][l/5|0],s.push(e.b),!(32*s.length>=o));)s.push(e.a),0==64*(l+=1)%r&&j(null,n);return s}}p=c(t,l,k),u=O(e),this.setHMACKey=function(t,r,n){var o;if(!0===w)throw Error("HMAC key already set");if(!0===P)throw Error("Cannot set HMAC key after calling update");if(!0===S)throw Error("SHAKE is not supported for HMAC");if(l=(n||{}).encoding||"UTF8",r=c(r,l,k)(t),t=r.binLen,r=r.value,o=h>>>3,n=o/4-1,o<t/8){for(r=m(r,t,0,O(e),d);r.length<=n;)r.push(0);r[n]&=4294967040}else if(o>t/8){for(;r.length<=n;)r.push(0);r[n]&=4294967040}for(t=0;t<=n;t+=1)v[t]=909522486^r[t],C[t]=1549556828^r[t];u=f(v,u),A=h,w=!0},this.update=function(e){var t,r,n,o=0,a=h>>>5;for(e=(t=p(e,x,b)).binLen,r=t.value,t=e>>>5,n=0;n<t;n+=a)o+h<=e&&(u=f(r.slice(n,n+a),u),o+=h);A+=o,x=r.slice(o>>>5),b=e%h,P=!0},this.getHash=function(t,r){var c,l,p,h;if(!0===w)throw Error("Cannot call getHash after setting HMAC key");if(p=s(r),!0===S){if(-1===p.shakeLen)throw Error("shakeLen must be specified in options");d=p.shakeLen}switch(t){case"HEX":c=function(e){return n(e,d,k,p)};break;case"B64":c=function(e){return o(e,d,k,p)};break;case"BYTES":c=function(e){return a(e,d,k)};break;case"ARRAYBUFFER":try{l=new ArrayBuffer(0)}catch(e){throw Error("ARRAYBUFFER not supported by this environment")}c=function(e){return i(e,d,k)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER")}for(h=m(x.slice(),b,A,y(u),d),l=1;l<g;l+=1)!0===S&&0!=d%32&&(h[h.length-1]&=16777215>>>24-d%32),h=m(h,d,0,O(e),d);return c(h)},this.getHMAC=function(t,r){var c,l,p,g;if(!1===w)throw Error("Cannot call getHMAC without first setting HMAC key");switch(p=s(r),t){case"HEX":c=function(e){return n(e,d,k,p)};break;case"B64":c=function(e){return o(e,d,k,p)};break;case"BYTES":c=function(e){return a(e,d,k)};break;case"ARRAYBUFFER":try{c=new ArrayBuffer(0)}catch(e){throw Error("ARRAYBUFFER not supported by this environment")}c=function(e){return i(e,d,k)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER")}return l=m(x.slice(),b,A,y(u),d),g=f(C,O(e)),g=m(l,d,h,g,d),c(g)}}function r(e,t){this.a=e,this.b=t}function n(e,t,r,n){var o="";t/=8;var a,i,s;for(s=-1===r?3:0,a=0;a<t;a+=1)i=e[a>>>2]>>>8*(s+a%4*r),o+="0123456789abcdef".charAt(i>>>4&15)+"0123456789abcdef".charAt(15&i);return n.outputUpper?o.toUpperCase():o}function o(e,t,r,n){var o,a,i,s,c="",l=t/8;for(s=-1===r?3:0,o=0;o<l;o+=3)for(a=o+1<l?e[o+1>>>2]:0,i=o+2<l?e[o+2>>>2]:0,i=(e[o>>>2]>>>8*(s+o%4*r)&255)<<16|(a>>>8*(s+(o+1)%4*r)&255)<<8|i>>>8*(s+(o+2)%4*r)&255,a=0;4>a;a+=1)c+=8*o+6*a<=t?"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i>>>6*(3-a)&63):n.b64Pad;return c}function a(e,t,r){var n="";t/=8;var o,a,i;for(i=-1===r?3:0,o=0;o<t;o+=1)a=e[o>>>2]>>>8*(i+o%4*r)&255,n+=String.fromCharCode(a);return n}function i(e,t,r){t/=8;var n,o,a,i=new ArrayBuffer(t);for(a=new Uint8Array(i),o=-1===r?3:0,n=0;n<t;n+=1)a[n]=e[n>>>2]>>>8*(o+n%4*r)&255;return i}function s(e){var t={outputUpper:!1,b64Pad:"=",shakeLen:-1};if(e=e||{},t.outputUpper=e.outputUpper||!1,!0===e.hasOwnProperty("b64Pad")&&(t.b64Pad=e.b64Pad),!0===e.hasOwnProperty("shakeLen")){if(0!=e.shakeLen%8)throw Error("shakeLen must be a multiple of 8");t.shakeLen=e.shakeLen}if("boolean"!=typeof t.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!=typeof t.b64Pad)throw Error("Invalid b64Pad formatting option");return t}function c(e,t,r){switch(t){case"UTF8":case"UTF16BE":case"UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE")}switch(e){case"HEX":e=function(e,t,n){var o,a,i,s,c,l,u=e.length;if(0!=u%2)throw Error("String of HEX type must be in byte increments");for(t=t||[0],c=(n=n||0)>>>3,l=-1===r?3:0,o=0;o<u;o+=2){if(a=parseInt(e.substr(o,2),16),isNaN(a))throw Error("String of HEX type contains invalid characters");for(i=(s=(o>>>1)+c)>>>2;t.length<=i;)t.push(0);t[i]|=a<<8*(l+s%4*r)}return{value:t,binLen:4*u+n}};break;case"TEXT":e=function(e,n,o){var a,i,s,c,l,u,p,d,h=0;if(n=n||[0],o=o||0,l=o>>>3,"UTF8"===t)for(d=-1===r?3:0,s=0;s<e.length;s+=1)for(i=[],128>(a=e.charCodeAt(s))?i.push(a):2048>a?(i.push(192|a>>>6),i.push(128|63&a)):55296>a||57344<=a?i.push(224|a>>>12,128|a>>>6&63,128|63&a):(s+=1,a=65536+((1023&a)<<10|1023&e.charCodeAt(s)),i.push(240|a>>>18,128|a>>>12&63,128|a>>>6&63,128|63&a)),c=0;c<i.length;c+=1){for(u=(p=h+l)>>>2;n.length<=u;)n.push(0);n[u]|=i[c]<<8*(d+p%4*r),h+=1}else if("UTF16BE"===t||"UTF16LE"===t)for(d=-1===r?2:0,i="UTF16LE"===t&&1!==r||"UTF16LE"!==t&&1===r,s=0;s<e.length;s+=1){for(a=e.charCodeAt(s),!0===i&&(c=255&a,a=c<<8|a>>>8),u=(p=h+l)>>>2;n.length<=u;)n.push(0);n[u]|=a<<8*(d+p%4*r),h+=2}return{value:n,binLen:8*h+o}};break;case"B64":e=function(e,t,n){var o,a,i,s,c,l,u,p,d=0;if(-1===e.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");if(a=e.indexOf("="),e=e.replace(/\=/g,""),-1!==a&&a<e.length)throw Error("Invalid '=' found in base-64 string");for(t=t||[0],l=(n=n||0)>>>3,p=-1===r?3:0,a=0;a<e.length;a+=4){for(c=e.substr(a,4),i=s=0;i<c.length;i+=1)s|=(o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(c[i]))<<18-6*i;for(i=0;i<c.length-1;i+=1){for(o=(u=d+l)>>>2;t.length<=o;)t.push(0);t[o]|=(s>>>16-8*i&255)<<8*(p+u%4*r),d+=1}}return{value:t,binLen:8*d+n}};break;case"BYTES":e=function(e,t,n){var o,a,i,s,c,l;for(t=t||[0],i=(n=n||0)>>>3,l=-1===r?3:0,a=0;a<e.length;a+=1)o=e.charCodeAt(a),s=(c=a+i)>>>2,t.length<=s&&t.push(0),t[s]|=o<<8*(l+c%4*r);return{value:t,binLen:8*e.length+n}};break;case"ARRAYBUFFER":try{e=new ArrayBuffer(0)}catch(e){throw Error("ARRAYBUFFER not supported by this environment")}e=function(e,t,n){var o,a,i,s,c,l;for(t=t||[0],a=(n=n||0)>>>3,c=-1===r?3:0,l=new Uint8Array(e),o=0;o<e.byteLength;o+=1)i=(s=o+a)>>>2,t.length<=i&&t.push(0),t[i]|=l[o]<<8*(c+s%4*r);return{value:t,binLen:8*e.byteLength+n}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER")}return e}function l(e,t){return e<<t|e>>>32-t}function u(e,t){return 32<t?(t-=32,new r(e.b<<t|e.a>>>32-t,e.a<<t|e.b>>>32-t)):0!==t?new r(e.a<<t|e.b>>>32-t,e.b<<t|e.a>>>32-t):e}function p(e,t){return e>>>t|e<<32-t}function d(e,t){var n=null,n=new r(e.a,e.b);return n=32>=t?new r(n.a>>>t|n.b<<32-t&4294967295,n.b>>>t|n.a<<32-t&4294967295):new r(n.b>>>t-32|n.a<<64-t&4294967295,n.a>>>t-32|n.b<<64-t&4294967295)}function h(e,t){return 32>=t?new r(e.a>>>t,e.b>>>t|e.a<<32-t&4294967295):new r(0,e.a>>>t-32)}function f(e,t,r){return e&t^~e&r}function m(e,t,n){return new r(e.a&t.a^~e.a&n.a,e.b&t.b^~e.b&n.b)}function y(e,t,r){return e&t^e&r^t&r}function g(e,t,n){return new r(e.a&t.a^e.a&n.a^t.a&n.a,e.b&t.b^e.b&n.b^t.b&n.b)}function A(e){return p(e,2)^p(e,13)^p(e,22)}function x(e){var t=d(e,28),n=d(e,34);return e=d(e,39),new r(t.a^n.a^e.a,t.b^n.b^e.b)}function b(e){return p(e,6)^p(e,11)^p(e,25)}function w(e){var t=d(e,14),n=d(e,18);return e=d(e,41),new r(t.a^n.a^e.a,t.b^n.b^e.b)}function v(e){return p(e,7)^p(e,18)^e>>>3}function C(e){var t=d(e,1),n=d(e,8);return e=h(e,7),new r(t.a^n.a^e.a,t.b^n.b^e.b)}function P(e){return p(e,17)^p(e,19)^e>>>10}function S(e){var t=d(e,19),n=d(e,61);return e=h(e,6),new r(t.a^n.a^e.a,t.b^n.b^e.b)}function k(e,t){var r=(65535&e)+(65535&t);return((e>>>16)+(t>>>16)+(r>>>16)&65535)<<16|65535&r}function M(e,t,r,n){var o=(65535&e)+(65535&t)+(65535&r)+(65535&n);return((e>>>16)+(t>>>16)+(r>>>16)+(n>>>16)+(o>>>16)&65535)<<16|65535&o}function E(e,t,r,n,o){var a=(65535&e)+(65535&t)+(65535&r)+(65535&n)+(65535&o);return((e>>>16)+(t>>>16)+(r>>>16)+(n>>>16)+(o>>>16)+(a>>>16)&65535)<<16|65535&a}function R(e,t){var n,o,a;return n=(65535&e.b)+(65535&t.b),o=(e.b>>>16)+(t.b>>>16)+(n>>>16),a=(65535&o)<<16|65535&n,n=(65535&e.a)+(65535&t.a)+(o>>>16),o=(e.a>>>16)+(t.a>>>16)+(n>>>16),new r((65535&o)<<16|65535&n,a)}function B(e,t,n,o){var a,i,s;return a=(65535&e.b)+(65535&t.b)+(65535&n.b)+(65535&o.b),i=(e.b>>>16)+(t.b>>>16)+(n.b>>>16)+(o.b>>>16)+(a>>>16),s=(65535&i)<<16|65535&a,a=(65535&e.a)+(65535&t.a)+(65535&n.a)+(65535&o.a)+(i>>>16),i=(e.a>>>16)+(t.a>>>16)+(n.a>>>16)+(o.a>>>16)+(a>>>16),new r((65535&i)<<16|65535&a,s)}function I(e,t,n,o,a){var i,s,c;return i=(65535&e.b)+(65535&t.b)+(65535&n.b)+(65535&o.b)+(65535&a.b),s=(e.b>>>16)+(t.b>>>16)+(n.b>>>16)+(o.b>>>16)+(a.b>>>16)+(i>>>16),c=(65535&s)<<16|65535&i,i=(65535&e.a)+(65535&t.a)+(65535&n.a)+(65535&o.a)+(65535&a.a)+(s>>>16),s=(e.a>>>16)+(t.a>>>16)+(n.a>>>16)+(o.a>>>16)+(a.a>>>16)+(i>>>16),new r((65535&s)<<16|65535&i,c)}function T(e,t){return new r(e.a^t.a,e.b^t.b)}function O(e){var t,n=[];if("SHA-1"===e)n=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===e.lastIndexOf("SHA-",0))switch(n=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428],t=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],e){case"SHA-224":break;case"SHA-256":n=t;break;case"SHA-384":n=[new r(3418070365,n[0]),new r(1654270250,n[1]),new r(2438529370,n[2]),new r(355462360,n[3]),new r(1731405415,n[4]),new r(41048885895,n[5]),new r(3675008525,n[6]),new r(1203062813,n[7])];break;case"SHA-512":n=[new r(t[0],4089235720),new r(t[1],2227873595),new r(t[2],4271175723),new r(t[3],1595750129),new r(t[4],2917565137),new r(t[5],725511199),new r(t[6],4215389547),new r(t[7],327033209)];break;default:throw Error("Unknown SHA variant")}else{if(0!==e.lastIndexOf("SHA3-",0)&&0!==e.lastIndexOf("SHAKE",0))throw Error("No SHA variants supported");for(e=0;5>e;e+=1)n[e]=[new r(0,0),new r(0,0),new r(0,0),new r(0,0),new r(0,0)]}return n}function D(e,t){var r,n,o,a,i,s,c,u=[];for(r=t[0],n=t[1],o=t[2],a=t[3],i=t[4],c=0;80>c;c+=1)u[c]=16>c?e[c]:l(u[c-3]^u[c-8]^u[c-14]^u[c-16],1),s=20>c?E(l(r,5),n&o^~n&a,i,1518500249,u[c]):40>c?E(l(r,5),n^o^a,i,1859775393,u[c]):60>c?E(l(r,5),y(n,o,a),i,2400959708,u[c]):E(l(r,5),n^o^a,i,3395469782,u[c]),i=a,a=o,o=l(n,30),n=r,r=s;return t[0]=k(r,t[0]),t[1]=k(n,t[1]),t[2]=k(o,t[2]),t[3]=k(a,t[3]),t[4]=k(i,t[4]),t}function L(e,t,r,n){var o;for(o=15+(t+65>>>9<<4);e.length<=o;)e.push(0);for(e[t>>>5]|=128<<24-t%32,t+=r,e[o]=4294967295&t,e[o-1]=t/4294967296|0,t=e.length,o=0;o<t;o+=16)n=D(e.slice(o,o+16),n);return n}function q(e,t,n){var o,a,i,s,c,l,u,p,d,h,T,O,D,L,q,j,F,_,H,N,G,V,z,Q=[];if("SHA-224"===n||"SHA-256"===n)h=64,O=1,V=Number,D=k,L=M,q=E,j=v,F=P,_=A,H=b,G=y,N=f,z=U;else{if("SHA-384"!==n&&"SHA-512"!==n)throw Error("Unexpected error in SHA-2 implementation");h=80,O=2,V=r,D=R,L=B,q=I,j=C,F=S,_=x,H=w,G=g,N=m,z=K}for(n=t[0],o=t[1],a=t[2],i=t[3],s=t[4],c=t[5],l=t[6],u=t[7],T=0;T<h;T+=1)16>T?(d=T*O,p=e.length<=d?0:e[d],d=e.length<=d+1?0:e[d+1],Q[T]=new V(p,d)):Q[T]=L(F(Q[T-2]),Q[T-7],j(Q[T-15]),Q[T-16]),p=q(u,H(s),N(s,c,l),z[T],Q[T]),d=D(_(n),G(n,o,a)),u=l,l=c,c=s,s=D(i,p),i=a,a=o,o=n,n=D(p,d);return t[0]=D(n,t[0]),t[1]=D(o,t[1]),t[2]=D(a,t[2]),t[3]=D(i,t[3]),t[4]=D(s,t[4]),t[5]=D(c,t[5]),t[6]=D(l,t[6]),t[7]=D(u,t[7]),t}function j(e,t){var n,o,a,i,s=[],c=[];if(null!==e)for(o=0;o<e.length;o+=2)t[(o>>>1)%5][(o>>>1)/5|0]=T(t[(o>>>1)%5][(o>>>1)/5|0],new r(e[o+1],e[o]));for(n=0;24>n;n+=1){for(i=O("SHA3-"),o=0;5>o;o+=1){a=t[o][0];var l=t[o][1],p=t[o][2],d=t[o][3],h=t[o][4];s[o]=new r(a.a^l.a^p.a^d.a^h.a,a.b^l.b^p.b^d.b^h.b)}for(o=0;5>o;o+=1)c[o]=T(s[(o+4)%5],u(s[(o+1)%5],1));for(o=0;5>o;o+=1)for(a=0;5>a;a+=1)t[o][a]=T(t[o][a],c[o]);for(o=0;5>o;o+=1)for(a=0;5>a;a+=1)i[a][(2*o+3*a)%5]=u(t[o][a],F[o][a]);for(o=0;5>o;o+=1)for(a=0;5>a;a+=1)t[o][a]=T(i[o][a],new r(~i[(o+1)%5][a].a&i[(o+2)%5][a].a,~i[(o+1)%5][a].b&i[(o+2)%5][a].b));t[0][0]=T(t[0][0],_[n])}return t}var U,K,F,_;K=[new r((U=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298])[0],3609767458),new r(U[1],602891725),new r(U[2],3964484399),new r(U[3],2173295548),new r(U[4],4081628472),new r(U[5],3053834265),new r(U[6],2937671579),new r(U[7],3664609560),new r(U[8],2734883394),new r(U[9],1164996542),new r(U[10],1323610764),new r(U[11],3590304994),new r(U[12],4068182383),new r(U[13],991336113),new r(U[14],633803317),new r(U[15],3479774868),new r(U[16],2666613458),new r(U[17],944711139),new r(U[18],2341262773),new r(U[19],2007800933),new r(U[20],1495990901),new r(U[21],1856431235),new r(U[22],3175218132),new r(U[23],2198950837),new r(U[24],3999719339),new r(U[25],766784016),new r(U[26],2566594879),new r(U[27],3203337956),new r(U[28],1034457026),new r(U[29],2466948901),new r(U[30],3758326383),new r(U[31],168717936),new r(U[32],1188179964),new r(U[33],1546045734),new r(U[34],1522805485),new r(U[35],2643833823),new r(U[36],2343527390),new r(U[37],1014477480),new r(U[38],1206759142),new r(U[39],344077627),new r(U[40],1290863460),new r(U[41],3158454273),new r(U[42],3505952657),new r(U[43],106217008),new r(U[44],3606008344),new r(U[45],1432725776),new r(U[46],1467031594),new r(U[47],851169720),new r(U[48],3100823752),new r(U[49],1363258195),new r(U[50],3750685593),new r(U[51],3785050280),new r(U[52],3318307427),new r(U[53],3812723403),new r(U[54],2003034995),new r(U[55],3602036899),new r(U[56],1575990012),new r(U[57],1125592928),new r(U[58],2716904306),new r(U[59],442776044),new r(U[60],593698344),new r(U[61],3733110249),new r(U[62],2999351573),new r(U[63],3815920427),new r(3391569614,3928383900),new r(3515267271,566280711),new r(3940187606,3454069534),new r(4118630271,4000239992),new r(116418474,1914138554),new r(174292421,2731055270),new r(289380356,3203993006),new r(460393269,320620315),new r(685471733,587496836),new r(852142971,1086792851),new r(1017036298,365543100),new r(1126000580,2618297676),new r(1288033470,3409855158),new r(1501505948,4234509866),new r(1607167915,987167468),new r(1816402316,1246189591)],_=[new r(0,1),new r(0,32898),new r(2147483648,32906),new r(2147483648,2147516416),new r(0,32907),new r(0,2147483649),new r(2147483648,2147516545),new r(2147483648,32777),new r(0,138),new r(0,136),new r(0,2147516425),new r(0,2147483658),new r(0,2147516555),new r(2147483648,139),new r(2147483648,32905),new r(2147483648,32771),new r(2147483648,32770),new r(2147483648,128),new r(0,32778),new r(2147483648,2147483658),new r(2147483648,2147516545),new r(2147483648,32896),new r(0,2147483649),new r(2147483648,2147516424)],F=[[0,36,3,41,18],[1,44,10,45,2],[62,6,43,15,61],[28,55,25,21,56],[27,20,39,8,14]],"function"==typeof define&&define.amd?define("jsSHA",[],function(){return t}):e.jsSHA=t}(window),function(e,t){"function"==typeof define&&define.amd?define("URI",[],t):e.URI=t(e.punycode,e.IPv6,e.SecondLevelDomains,e)}(window,function(e,t,r,n){function o(e,t){var r=arguments.length>=1,n=arguments.length>=2;if(!(this instanceof o))return r?n?new o(e,t):new o(e):new o;if(void 0===e){if(r)throw new TypeError("undefined is not a valid argument for URI");e="undefined"!=typeof location?location.href+"":""}if(null===e&&r)throw new TypeError("null is not a valid argument for URI");return this.href(e),void 0!==t?this.absoluteTo(t):this}function a(e){return/^[0-9]+$/.test(e)}function i(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function s(e){return void 0===e?"Undefined":String(Object.prototype.toString.call(e)).slice(8,-1)}function c(e){return"Array"===s(e)}function l(e,t){var r,n,o={};if("RegExp"===s(t))o=null;else if(c(t))for(r=0,n=t.length;r<n;r++)o[t[r]]=!0;else o[t]=!0;for(r=0,n=e.length;r<n;r++)(o&&void 0!==o[e[r]]||!o&&t.test(e[r]))&&(e.splice(r,1),n--,r--);return e}function u(e,t){var r,n;if(c(t)){for(r=0,n=t.length;r<n;r++)if(!u(e,t[r]))return!1;return!0}var o=s(t);for(r=0,n=e.length;r<n;r++)if("RegExp"===o){if("string"==typeof e[r]&&e[r].match(t))return!0}else if(e[r]===t)return!0;return!1}function p(e,t){if(!c(e)||!c(t))return!1;if(e.length!==t.length)return!1;e.sort(),t.sort();for(var r=0,n=e.length;r<n;r++)if(e[r]!==t[r])return!1;return!0}function d(e){var t=/^\/+|\/+$/g;return e.replace(t,"")}function h(e){return escape(e)}function f(e){return encodeURIComponent(e).replace(/[!'()*]/g,h).replace(/\*/g,"%2A")}function m(e){return function(t,r){return void 0===t?this._parts[e]||"":(this._parts[e]=t||null,this.build(!r),this)}}function y(e,t){return function(r,n){return void 0===r?this._parts[e]||"":(null!==r&&(r+="").charAt(0)===t&&(r=r.substring(1)),this._parts[e]=r,this.build(!n),this)}}var g=n&&n.URI;o.version="1.19.1";var A=o.prototype,x=Object.prototype.hasOwnProperty;o._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:o.preventInvalidHostname,duplicateQueryParameters:o.duplicateQueryParameters,escapeQuerySpace:o.escapeQuerySpace}},o.preventInvalidHostname=!1,o.duplicateQueryParameters=!1,o.escapeQuerySpace=!0,o.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,o.idn_expression=/[^a-z0-9\._-]/i,o.punycode_expression=/(xn--)/i,o.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,o.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,o.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/gi,o.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},o.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},o.hostProtocols=["http","https"],o.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,o.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},o.getDomAttribute=function(e){if(e&&e.nodeName){var t=e.nodeName.toLowerCase();if("input"!==t||"image"===e.type)return o.domAttributes[t]}},o.encode=f,o.decode=decodeURIComponent,o.iso8859=function(){o.encode=escape,o.decode=unescape},o.unicode=function(){o.encode=f,o.decode=decodeURIComponent},o.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/gi,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/gi,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/gi,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},o.encodeQuery=function(e,t){var r=o.encode(e+"");return void 0===t&&(t=o.escapeQuerySpace),t?r.replace(/%20/g,"+"):r},o.decodeQuery=function(e,t){e+="",void 0===t&&(t=o.escapeQuerySpace);try{return o.decode(t?e.replace(/\+/g,"%20"):e)}catch(t){return e}};var b,w={encode:"encode",decode:"decode"},v=function(e,t){return function(r){try{return o[t](r+"").replace(o.characters[e][t].expression,function(r){return o.characters[e][t].map[r]})}catch(e){return r}}};for(b in w)o[b+"PathSegment"]=v("pathname",w[b]),o[b+"UrnPathSegment"]=v("urnpath",w[b]);var C=function(e,t,r){return function(n){var a;a=r?function(e){return o[t](o[r](e))}:o[t];for(var i=(n+"").split(e),s=0,c=i.length;s<c;s++)i[s]=a(i[s]);return i.join(e)}};o.decodePath=C("/","decodePathSegment"),o.decodeUrnPath=C(":","decodeUrnPathSegment"),o.recodePath=C("/","encodePathSegment","decode"),o.recodeUrnPath=C(":","encodeUrnPathSegment","decode"),o.encodeReserved=v("reserved","encode"),o.parse=function(e,t){var r;return t||(t={preventInvalidHostname:o.preventInvalidHostname}),(r=e.indexOf("#"))>-1&&(t.fragment=e.substring(r+1)||null,e=e.substring(0,r)),(r=e.indexOf("?"))>-1&&(t.query=e.substring(r+1)||null,e=e.substring(0,r)),"//"===e.substring(0,2)?(t.protocol=null,e=e.substring(2),e=o.parseAuthority(e,t)):(r=e.indexOf(":"))>-1&&(t.protocol=e.substring(0,r)||null,t.protocol&&!t.protocol.match(o.protocol_expression)?t.protocol=void 0:"//"===e.substring(r+1,r+3)?(e=e.substring(r+3),e=o.parseAuthority(e,t)):(e=e.substring(r+1),t.urn=!0)),t.path=e,t},o.parseHost=function(e,t){e||(e="");var r,n,a=(e=e.replace(/\\/g,"/")).indexOf("/");if(-1===a&&(a=e.length),"["===e.charAt(0))r=e.indexOf("]"),t.hostname=e.substring(1,r)||null,t.port=e.substring(r+2,a)||null,"/"===t.port&&(t.port=null);else{var i=e.indexOf(":"),s=e.indexOf("/"),c=e.indexOf(":",i+1);-1!==c&&(-1===s||c<s)?(t.hostname=e.substring(0,a)||null,t.port=null):(n=e.substring(0,a).split(":"),t.hostname=n[0]||null,t.port=n[1]||null)}return t.hostname&&"/"!==e.substring(a).charAt(0)&&(a++,e="/"+e),t.preventInvalidHostname&&o.ensureValidHostname(t.hostname,t.protocol),t.port&&o.ensureValidPort(t.port),e.substring(a)||"/"},o.parseAuthority=function(e,t){return e=o.parseUserinfo(e,t),o.parseHost(e,t)},o.parseUserinfo=function(e,t){var r,n=e.indexOf("/"),a=e.lastIndexOf("@",n>-1?n:e.length-1);return a>-1&&(-1===n||a<n)?(r=e.substring(0,a).split(":"),t.username=r[0]?o.decode(r[0]):null,r.shift(),t.password=r[0]?o.decode(r.join(":")):null,e=e.substring(a+1)):(t.username=null,t.password=null),e},o.parseQuery=function(e,t){if(!e)return{};if(!(e=e.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,"")))return{};for(var r,n,a,i={},s=e.split("&"),c=s.length,l=0;l<c;l++)r=s[l].split("="),n=o.decodeQuery(r.shift(),t),a=r.length?o.decodeQuery(r.join("="),t):null,x.call(i,n)?("string"!=typeof i[n]&&null!==i[n]||(i[n]=[i[n]]),i[n].push(a)):i[n]=a;return i},o.build=function(e){var t="";return e.protocol&&(t+=e.protocol+":"),e.urn||!t&&!e.hostname||(t+="//"),t+=o.buildAuthority(e)||"","string"==typeof e.path&&("/"!==e.path.charAt(0)&&"string"==typeof e.hostname&&(t+="/"),t+=e.path),"string"==typeof e.query&&e.query&&(t+="?"+e.query),"string"==typeof e.fragment&&e.fragment&&(t+="#"+e.fragment),t},o.buildHost=function(e){var t="";return e.hostname?(o.ip6_expression.test(e.hostname)?t+="["+e.hostname+"]":t+=e.hostname,e.port&&(t+=":"+e.port),t):""},o.buildAuthority=function(e){return o.buildUserinfo(e)+o.buildHost(e)},o.buildUserinfo=function(e){var t="";return e.username&&(t+=o.encode(e.username)),e.password&&(t+=":"+o.encode(e.password)),t&&(t+="@"),t},o.buildQuery=function(e,t,r){var n,a,i,s,l="";for(a in e)if(x.call(e,a)&&a)if(c(e[a]))for(n={},i=0,s=e[a].length;i<s;i++)void 0!==e[a][i]&&void 0===n[e[a][i]+""]&&(l+="&"+o.buildQueryParameter(a,e[a][i],r),!0!==t&&(n[e[a][i]+""]=!0));else void 0!==e[a]&&(l+="&"+o.buildQueryParameter(a,e[a],r));return l.substring(1)},o.buildQueryParameter=function(e,t,r){return o.encodeQuery(e,r)+(null!==t?"="+o.encodeQuery(t,r):"")},o.addQuery=function(e,t,r){if("object"===(void 0===t?"undefined":_typeof(t)))for(var n in t)x.call(t,n)&&o.addQuery(e,n,t[n]);else{if("string"!=typeof t)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");if(void 0===e[t])return void(e[t]=r);"string"==typeof e[t]&&(e[t]=[e[t]]),c(r)||(r=[r]),e[t]=(e[t]||[]).concat(r)}},o.setQuery=function(e,t,r){if("object"===(void 0===t?"undefined":_typeof(t)))for(var n in t)x.call(t,n)&&o.setQuery(e,n,t[n]);else{if("string"!=typeof t)throw new TypeError("URI.setQuery() accepts an object, string as the name parameter");e[t]=void 0===r?null:r}},o.removeQuery=function(e,t,r){var n,a,i;if(c(t))for(n=0,a=t.length;n<a;n++)e[t[n]]=void 0;else if("RegExp"===s(t))for(i in e)t.test(i)&&(e[i]=void 0);else if("object"===(void 0===t?"undefined":_typeof(t)))for(i in t)x.call(t,i)&&o.removeQuery(e,i,t[i]);else{if("string"!=typeof t)throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter");void 0!==r?"RegExp"===s(r)?!c(e[t])&&r.test(e[t])?e[t]=void 0:e[t]=l(e[t],r):e[t]!==String(r)||c(r)&&1!==r.length?c(e[t])&&(e[t]=l(e[t],r)):e[t]=void 0:e[t]=void 0}},o.hasQuery=function(e,t,r,n){switch(s(t)){case"String":break;case"RegExp":for(var a in e)if(x.call(e,a)&&t.test(a)&&(void 0===r||o.hasQuery(e,a,r)))return!0;return!1;case"Object":for(var i in t)if(x.call(t,i)&&!o.hasQuery(e,i,t[i]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(s(r)){case"Undefined":return t in e;case"Boolean":return r===Boolean(c(e[t])?e[t].length:e[t]);case"Function":return!!r(e[t],t,e);case"Array":return!!c(e[t])&&(n?u:p)(e[t],r);case"RegExp":return c(e[t])?!!n&&u(e[t],r):Boolean(e[t]&&e[t].match(r));case"Number":r=String(r);case"String":return c(e[t])?!!n&&u(e[t],r):e[t]===r;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},o.joinPaths=function(){for(var e=[],t=[],r=0,n=0;n<arguments.length;n++){var a=new o(arguments[n]);e.push(a);for(var i=a.segment(),s=0;s<i.length;s++)"string"==typeof i[s]&&t.push(i[s]),i[s]&&r++}if(!t.length||!r)return new o("");var c=new o("").segment(t);return""!==e[0].path()&&"/"!==e[0].path().slice(0,1)||c.path("/"+c.path()),c.normalize()},o.commonPath=function(e,t){var r,n=Math.min(e.length,t.length);for(r=0;r<n;r++)if(e.charAt(r)!==t.charAt(r)){r--;break}return r<1?e.charAt(0)===t.charAt(0)&&"/"===e.charAt(0)?"/":"":("/"===e.charAt(r)&&"/"===t.charAt(r)||(r=e.substring(0,r).lastIndexOf("/")),e.substring(0,r+1))},o.withinString=function(e,t,r){r||(r={});var n=r.start||o.findUri.start,a=r.end||o.findUri.end,i=r.trim||o.findUri.trim,s=r.parens||o.findUri.parens,c=/[a-z0-9-]=["']?$/i;for(n.lastIndex=0;;){var l=n.exec(e);if(!l)break;var u=l.index;if(r.ignoreHtml){var p=e.slice(Math.max(u-3,0),u);if(p&&c.test(p))continue}for(var d=u+e.slice(u).search(a),h=e.slice(u,d),f=-1;;){var m=s.exec(h);if(!m)break;var y=m.index+m[0].length;f=Math.max(f,y)}if(!((h=f>-1?h.slice(0,f)+h.slice(f).replace(i,""):h.replace(i,"")).length<=l[0].length||r.ignore&&r.ignore.test(h))){var g=t(h,u,d=u+h.length,e);void 0!==g?(g=String(g),e=e.slice(0,u)+g+e.slice(d),n.lastIndex=u+g.length):n.lastIndex=d}}return n.lastIndex=0,e},o.ensureValidHostname=function(t,r){var n=!!t,a=!1;if(!!r&&(a=u(o.hostProtocols,r)),a&&!n)throw new TypeError("Hostname cannot be empty, if protocol is "+r);if(t&&t.match(o.invalid_hostname_characters)){if(!e)throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(e.toASCII(t).match(o.invalid_hostname_characters))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-:_]')}},o.ensureValidPort=function(e){if(e){var t=Number(e);if(!(a(t)&&t>0&&t<65536))throw new TypeError('Port "'+e+'" is not a valid port')}},o.noConflict=function(e){if(e){var t={URI:this.noConflict()};return n.URITemplate&&"function"==typeof n.URITemplate.noConflict&&(t.URITemplate=n.URITemplate.noConflict()),n.IPv6&&"function"==typeof n.IPv6.noConflict&&(t.IPv6=n.IPv6.noConflict()),n.SecondLevelDomains&&"function"==typeof n.SecondLevelDomains.noConflict&&(t.SecondLevelDomains=n.SecondLevelDomains.noConflict()),t}return n.URI===this&&(n.URI=g),this},A.build=function(e){return!0===e?this._deferred_build=!0:(void 0===e||this._deferred_build)&&(this._string=o.build(this._parts),this._deferred_build=!1),this},A.clone=function(){return new o(this)},A.valueOf=A.toString=function(){return this.build(!1)._string},A.protocol=m("protocol"),A.username=m("username"),A.password=m("password"),A.hostname=m("hostname"),A.port=m("port"),A.query=y("query","?"),A.fragment=y("fragment","#"),A.search=function(e,t){var r=this.query(e,t);return"string"==typeof r&&r.length?"?"+r:r},A.hash=function(e,t){var r=this.fragment(e,t);return"string"==typeof r&&r.length?"#"+r:r},A.pathname=function(e,t){if(void 0===e||!0===e){var r=this._parts.path||(this._parts.hostname?"/":"");return e?(this._parts.urn?o.decodeUrnPath:o.decodePath)(r):r}return this._parts.urn?this._parts.path=e?o.recodeUrnPath(e):"":this._parts.path=e?o.recodePath(e):"/",this.build(!t),this},A.path=A.pathname,A.href=function(e,t){var r;if(void 0===e)return this.toString();this._string="",this._parts=o._parts();var n=e instanceof o,a="object"===(void 0===e?"undefined":_typeof(e))&&(e.hostname||e.path||e.pathname);if(e.nodeName&&(e=e[o.getDomAttribute(e)]||"",a=!1),!n&&a&&void 0!==e.pathname&&(e=e.toString()),"string"==typeof e||e instanceof String)this._parts=o.parse(String(e),this._parts);else{if(!n&&!a)throw new TypeError("invalid input");var i=n?e._parts:e;for(r in i)"query"!==r&&x.call(this._parts,r)&&(this._parts[r]=i[r]);i.query&&this.query(i.query,!1)}return this.build(!t),this},A.is=function(e){var t=!1,n=!1,a=!1,i=!1,s=!1,c=!1,l=!1,u=!this._parts.urn;switch(this._parts.hostname&&(u=!1,n=o.ip4_expression.test(this._parts.hostname),a=o.ip6_expression.test(this._parts.hostname),s=(i=!(t=n||a))&&r&&r.has(this._parts.hostname),c=i&&o.idn_expression.test(this._parts.hostname),l=i&&o.punycode_expression.test(this._parts.hostname)),e.toLowerCase()){case"relative":return u;case"absolute":return!u;case"domain":case"name":return i;case"sld":return s;case"ip":return t;case"ip4":case"ipv4":case"inet4":return n;case"ip6":case"ipv6":case"inet6":return a;case"idn":return c;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return l}return null};var P=A.protocol,S=A.port,k=A.hostname;A.protocol=function(e,t){if(e&&!(e=e.replace(/:(\/\/)?$/,"")).match(o.protocol_expression))throw new TypeError('Protocol "'+e+"\" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]");return P.call(this,e,t)},A.scheme=A.protocol,A.port=function(e,t){return this._parts.urn?void 0===e?"":this:(void 0!==e&&(0===e&&(e=null),e&&(":"===(e+="").charAt(0)&&(e=e.substring(1)),o.ensureValidPort(e))),S.call(this,e,t))},A.hostname=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0!==e){var r={preventInvalidHostname:this._parts.preventInvalidHostname};if("/"!==o.parseHost(e,r))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');e=r.hostname,this._parts.preventInvalidHostname&&o.ensureValidHostname(e,this._parts.protocol)}return k.call(this,e,t)},A.origin=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){var r=this.protocol();return this.authority()?(r?r+"://":"")+this.authority():""}var n=o(e);return this.protocol(n.protocol()).authority(n.authority()).build(!t),this},A.host=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e)return this._parts.hostname?o.buildHost(this._parts):"";if("/"!==o.parseHost(e,this._parts))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');return this.build(!t),this},A.authority=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e)return this._parts.hostname?o.buildAuthority(this._parts):"";if("/"!==o.parseAuthority(e,this._parts))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-]');return this.build(!t),this},A.userinfo=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){var r=o.buildUserinfo(this._parts);return r?r.substring(0,r.length-1):r}return"@"!==e[e.length-1]&&(e+="@"),o.parseUserinfo(e,this._parts),this.build(!t),this},A.resource=function(e,t){var r;return void 0===e?this.path()+this.search()+this.hash():(r=o.parse(e),this._parts.path=r.path,this._parts.query=r.query,this._parts.fragment=r.fragment,this.build(!t),this)},A.subdomain=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,r)||""}var n=this._parts.hostname.length-this.domain().length,a=this._parts.hostname.substring(0,n),s=new RegExp("^"+i(a));if(e&&"."!==e.charAt(e.length-1)&&(e+="."),-1!==e.indexOf(":"))throw new TypeError("Domains cannot contain colons");return e&&o.ensureValidHostname(e,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(s,e),this.build(!t),this},A.domain=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("boolean"==typeof e&&(t=e,e=void 0),void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.match(/\./g);if(r&&r.length<2)return this._parts.hostname;var n=this._parts.hostname.length-this.tld(t).length-1;return n=this._parts.hostname.lastIndexOf(".",n-1)+1,this._parts.hostname.substring(n)||""}if(!e)throw new TypeError("cannot set domain empty");if(-1!==e.indexOf(":"))throw new TypeError("Domains cannot contain colons");if(o.ensureValidHostname(e,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=e;else{var a=new RegExp(i(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(a,e)}return this.build(!t),this},A.tld=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("boolean"==typeof e&&(t=e,e=void 0),void 0===e){if(!this._parts.hostname||this.is("IP"))return"";var n=this._parts.hostname.lastIndexOf("."),o=this._parts.hostname.substring(n+1);return!0!==t&&r&&r.list[o.toLowerCase()]?r.get(this._parts.hostname)||o:o}var a;if(!e)throw new TypeError("cannot set TLD empty");if(e.match(/[^a-zA-Z0-9-]/)){if(!r||!r.is(e))throw new TypeError('TLD "'+e+'" contains characters other than [A-Z0-9]');a=new RegExp(i(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(a,e)}else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");a=new RegExp(i(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(a,e)}return this.build(!t),this},A.directory=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path&&!this._parts.hostname)return"";if("/"===this._parts.path)return"/";var r=this._parts.path.length-this.filename().length-1,n=this._parts.path.substring(0,r)||(this._parts.hostname?"/":"");return e?o.decodePath(n):n}var a=this._parts.path.length-this.filename().length,s=this._parts.path.substring(0,a),c=new RegExp("^"+i(s));return this.is("relative")||(e||(e="/"),"/"!==e.charAt(0)&&(e="/"+e)),e&&"/"!==e.charAt(e.length-1)&&(e+="/"),e=o.recodePath(e),this._parts.path=this._parts.path.replace(c,e),this.build(!t),this},A.filename=function(e,t){if(this._parts.urn)return void 0===e?"":this;if("string"!=typeof e){if(!this._parts.path||"/"===this._parts.path)return"";var r=this._parts.path.lastIndexOf("/"),n=this._parts.path.substring(r+1);return e?o.decodePathSegment(n):n}var a=!1;"/"===e.charAt(0)&&(e=e.substring(1)),e.match(/\.?\//)&&(a=!0);var s=new RegExp(i(this.filename())+"$");return e=o.recodePath(e),this._parts.path=this._parts.path.replace(s,e),a?this.normalizePath(t):this.build(!t),this},A.suffix=function(e,t){if(this._parts.urn)return void 0===e?"":this;if(void 0===e||!0===e){if(!this._parts.path||"/"===this._parts.path)return"";var r,n,a=this.filename(),s=a.lastIndexOf(".");return-1===s?"":(r=a.substring(s+1),n=/^[a-z0-9%]+$/i.test(r)?r:"",e?o.decodePathSegment(n):n)}"."===e.charAt(0)&&(e=e.substring(1));var c,l=this.suffix();if(l)c=e?new RegExp(i(l)+"$"):new RegExp(i("."+l)+"$");else{if(!e)return this;this._parts.path+="."+o.recodePath(e)}return c&&(e=o.recodePath(e),this._parts.path=this._parts.path.replace(c,e)),this.build(!t),this},A.segment=function(e,t,r){var n=this._parts.urn?":":"/",o=this.path(),a="/"===o.substring(0,1),i=o.split(n);if(void 0!==e&&"number"!=typeof e&&(r=t,t=e,e=void 0),void 0!==e&&"number"!=typeof e)throw new Error('Bad segment "'+e+'", must be 0-based integer');if(a&&i.shift(),e<0&&(e=Math.max(i.length+e,0)),void 0===t)return void 0===e?i:i[e];if(null===e||void 0===i[e])if(c(t)){i=[];for(var s=0,l=t.length;s<l;s++)(t[s].length||i.length&&i[i.length-1].length)&&(i.length&&!i[i.length-1].length&&i.pop(),i.push(d(t[s])))}else(t||"string"==typeof t)&&(t=d(t),""===i[i.length-1]?i[i.length-1]=t:i.push(t));else t?i[e]=d(t):i.splice(e,1);return a&&i.unshift(""),this.path(i.join(n),r)},A.segmentCoded=function(e,t,r){var n,a,i;if("number"!=typeof e&&(r=t,t=e,e=void 0),void 0===t){if(n=this.segment(e,t,r),c(n))for(a=0,i=n.length;a<i;a++)n[a]=o.decode(n[a]);else n=void 0!==n?o.decode(n):void 0;return n}if(c(t))for(a=0,i=t.length;a<i;a++)t[a]=o.encode(t[a]);else t="string"==typeof t||t instanceof String?o.encode(t):t;return this.segment(e,t,r)};var M=A.query;return A.query=function(e,t){if(!0===e)return o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("function"==typeof e){var r=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace),n=e.call(this,r);return this._parts.query=o.buildQuery(n||r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!t),this}return void 0!==e&&"string"!=typeof e?(this._parts.query=o.buildQuery(e,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!t),this):M.call(this,e,t)},A.setQuery=function(e,t,r){var n=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("string"==typeof e||e instanceof String)n[e]=void 0!==t?t:null;else{if("object"!==(void 0===e?"undefined":_typeof(e)))throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");for(var a in e)x.call(e,a)&&(n[a]=e[a])}return this._parts.query=o.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},A.addQuery=function(e,t,r){var n=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return o.addQuery(n,e,void 0===t?null:t),this._parts.query=o.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},A.removeQuery=function(e,t,r){var n=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return o.removeQuery(n,e,t),this._parts.query=o.buildQuery(n,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof e&&(r=t),this.build(!r),this},A.hasQuery=function(e,t,r){var n=o.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return o.hasQuery(n,e,t,r)},A.setSearch=A.setQuery,A.addSearch=A.addQuery,A.removeSearch=A.removeQuery,A.hasSearch=A.hasQuery,A.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},A.normalizeProtocol=function(e){return"string"==typeof this._parts.protocol&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!e)),this},A.normalizeHostname=function(r){return this._parts.hostname&&(this.is("IDN")&&e?this._parts.hostname=e.toASCII(this._parts.hostname):this.is("IPv6")&&t&&(this._parts.hostname=t.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!r)),this},A.normalizePort=function(e){return"string"==typeof this._parts.protocol&&this._parts.port===o.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!e)),this},A.normalizePath=function(e){var t=this._parts.path;if(!t)return this;if(this._parts.urn)return this._parts.path=o.recodeUrnPath(this._parts.path),this.build(!e),this;if("/"===this._parts.path)return this;var r,n,a,i="";for("/"!==(t=o.recodePath(t)).charAt(0)&&(r=!0,t="/"+t),"/.."!==t.slice(-3)&&"/."!==t.slice(-2)||(t+="/"),t=t.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),r&&(i=t.substring(1).match(/^(\.\.\/)+/)||"")&&(i=i[0]);;){if(-1===(n=t.search(/\/\.\.(\/|$)/)))break;0!==n?(-1===(a=t.substring(0,n).lastIndexOf("/"))&&(a=n),t=t.substring(0,a)+t.substring(n+3)):t=t.substring(3)}return r&&this.is("relative")&&(t=i+t.substring(1)),this._parts.path=t,this.build(!e),this},A.normalizePathname=A.normalizePath,A.normalizeQuery=function(e){return"string"==typeof this._parts.query&&(this._parts.query.length?this.query(o.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!e)),this},A.normalizeFragment=function(e){return this._parts.fragment||(this._parts.fragment=null,this.build(!e)),this},A.normalizeSearch=A.normalizeQuery,A.normalizeHash=A.normalizeFragment,A.iso8859=function(){var e=o.encode,t=o.decode;o.encode=escape,o.decode=decodeURIComponent;try{this.normalize()}finally{o.encode=e,o.decode=t}return this},A.unicode=function(){var e=o.encode,t=o.decode;o.encode=f,o.decode=unescape;try{this.normalize()}finally{o.encode=e,o.decode=t}return this},A.readable=function(){var t=this.clone();t.username("").password("").normalize();var r="";if(t._parts.protocol&&(r+=t._parts.protocol+"://"),t._parts.hostname&&(t.is("punycode")&&e?(r+=e.toUnicode(t._parts.hostname),t._parts.port&&(r+=":"+t._parts.port)):r+=t.host()),t._parts.hostname&&t._parts.path&&"/"!==t._parts.path.charAt(0)&&(r+="/"),r+=t.path(!0),t._parts.query){for(var n="",a=0,i=t._parts.query.split("&"),s=i.length;a<s;a++){var c=(i[a]||"").split("=");n+="&"+o.decodeQuery(c[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),void 0!==c[1]&&(n+="="+o.decodeQuery(c[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}r+="?"+n.substring(1)}return r+=o.decodeQuery(t.hash(),!0)},A.absoluteTo=function(e){var t,r,n,a=this.clone(),i=["protocol","username","password","hostname","port"];if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(e instanceof o||(e=new o(e)),a._parts.protocol)return a;if(a._parts.protocol=e._parts.protocol,this._parts.hostname)return a;for(r=0;n=i[r];r++)a._parts[n]=e._parts[n];return a._parts.path?(".."===a._parts.path.substring(-2)&&(a._parts.path+="/"),"/"!==a.path().charAt(0)&&(t=(t=e.directory())||(0===e.path().indexOf("/")?"/":""),a._parts.path=(t?t+"/":"")+a._parts.path,a.normalizePath())):(a._parts.path=e._parts.path,a._parts.query||(a._parts.query=e._parts.query)),a.build(),a},A.relativeTo=function(e){var t,r,n,a,i,s=this.clone().normalize();if(s._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(e=new o(e).normalize(),t=s._parts,r=e._parts,a=s.path(),i=e.path(),"/"!==a.charAt(0))throw new Error("URI is already relative");if("/"!==i.charAt(0))throw new Error("Cannot calculate a URI relative to another relative URI");if(t.protocol===r.protocol&&(t.protocol=null),t.username!==r.username||t.password!==r.password)return s.build();if(null!==t.protocol||null!==t.username||null!==t.password)return s.build();if(t.hostname!==r.hostname||t.port!==r.port)return s.build();if(t.hostname=null,t.port=null,a===i)return t.path="",s.build();if(!(n=o.commonPath(a,i)))return s.build();var c=r.path.substring(n.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return t.path=c+t.path.substring(n.length)||"./",s.build()},A.equals=function(e){var t,r,n,a=this.clone(),i=new o(e),s={},l={},u={};if(a.normalize(),i.normalize(),a.toString()===i.toString())return!0;if(t=a.query(),r=i.query(),a.query(""),i.query(""),a.toString()!==i.toString())return!1;if(t.length!==r.length)return!1;s=o.parseQuery(t,this._parts.escapeQuerySpace),l=o.parseQuery(r,this._parts.escapeQuerySpace);for(n in s)if(x.call(s,n)){if(c(s[n])){if(!p(s[n],l[n]))return!1}else if(s[n]!==l[n])return!1;u[n]=!0}for(n in l)if(x.call(l,n)&&!u[n])return!1;return!0},A.preventInvalidHostname=function(e){return this._parts.preventInvalidHostname=!!e,this},A.duplicateQueryParameters=function(e){return this._parts.duplicateQueryParameters=!!e,this},A.escapeQuerySpace=function(e){return this._parts.escapeQuerySpace=!!e,this},o}),function(e,t){"function"==typeof define&&define.amd?define("md5",[],t):e.md5=t()}(window,function(){function e(e,t){var r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}function t(e,t){return e<<t|e>>>32-t}function r(r,n,o,a,i,s){return e(t(e(e(n,r),e(a,s)),i),o)}function n(e,t,n,o,a,i,s){return r(t&n|~t&o,e,t,a,i,s)}function o(e,t,n,o,a,i,s){return r(t&o|n&~o,e,t,a,i,s)}function a(e,t,n,o,a,i,s){return r(t^n^o,e,t,a,i,s)}function i(e,t,n,o,a,i,s){return r(n^(t|~o),e,t,a,i,s)}function s(t,r){t[r>>5]|=128<<r%32,t[14+(r+64>>>9<<4)]=r;var s,c,l,u,p,d=1732584193,h=-271733879,f=-1732584194,m=271733878;for(s=0;s<t.length;s+=16)c=d,l=h,u=f,p=m,h=i(h=i(h=i(h=i(h=a(h=a(h=a(h=a(h=o(h=o(h=o(h=o(h=n(h=n(h=n(h=n(h,f=n(f,m=n(m,d=n(d,h,f,m,t[s],7,-680876936),h,f,t[s+1],12,-389564586),d,h,t[s+2],17,606105819),m,d,t[s+3],22,-1044525330),f=n(f,m=n(m,d=n(d,h,f,m,t[s+4],7,-176418897),h,f,t[s+5],12,1200080426),d,h,t[s+6],17,-1473231341),m,d,t[s+7],22,-45705983),f=n(f,m=n(m,d=n(d,h,f,m,t[s+8],7,1770035416),h,f,t[s+9],12,-1958414417),d,h,t[s+10],17,-42063),m,d,t[s+11],22,-1990404162),f=n(f,m=n(m,d=n(d,h,f,m,t[s+12],7,1804603682),h,f,t[s+13],12,-40341101),d,h,t[s+14],17,-1502002290),m,d,t[s+15],22,1236535329),f=o(f,m=o(m,d=o(d,h,f,m,t[s+1],5,-165796510),h,f,t[s+6],9,-1069501632),d,h,t[s+11],14,643717713),m,d,t[s],20,-373897302),f=o(f,m=o(m,d=o(d,h,f,m,t[s+5],5,-701558691),h,f,t[s+10],9,38016083),d,h,t[s+15],14,-660478335),m,d,t[s+4],20,-405537848),f=o(f,m=o(m,d=o(d,h,f,m,t[s+9],5,568446438),h,f,t[s+14],9,-1019803690),d,h,t[s+3],14,-187363961),m,d,t[s+8],20,1163531501),f=o(f,m=o(m,d=o(d,h,f,m,t[s+13],5,-1444681467),h,f,t[s+2],9,-51403784),d,h,t[s+7],14,1735328473),m,d,t[s+12],20,-1926607734),f=a(f,m=a(m,d=a(d,h,f,m,t[s+5],4,-378558),h,f,t[s+8],11,-2022574463),d,h,t[s+11],16,1839030562),m,d,t[s+14],23,-35309556),f=a(f,m=a(m,d=a(d,h,f,m,t[s+1],4,-1530992060),h,f,t[s+4],11,1272893353),d,h,t[s+7],16,-155497632),m,d,t[s+10],23,-1094730640),f=a(f,m=a(m,d=a(d,h,f,m,t[s+13],4,681279174),h,f,t[s],11,-358537222),d,h,t[s+3],16,-722521979),m,d,t[s+6],23,76029189),f=a(f,m=a(m,d=a(d,h,f,m,t[s+9],4,-640364487),h,f,t[s+12],11,-421815835),d,h,t[s+15],16,530742520),m,d,t[s+2],23,-995338651),f=i(f,m=i(m,d=i(d,h,f,m,t[s],6,-198630844),h,f,t[s+7],10,1126891415),d,h,t[s+14],15,-1416354905),m,d,t[s+5],21,-57434055),f=i(f,m=i(m,d=i(d,h,f,m,t[s+12],6,1700485571),h,f,t[s+3],10,-1894986606),d,h,t[s+10],15,-1051523),m,d,t[s+1],21,-2054922799),f=i(f,m=i(m,d=i(d,h,f,m,t[s+8],6,1873313359),h,f,t[s+15],10,-30611744),d,h,t[s+6],15,-1560198380),m,d,t[s+13],21,1309151649),f=i(f,m=i(m,d=i(d,h,f,m,t[s+4],6,-145523070),h,f,t[s+11],10,-1120210379),d,h,t[s+2],15,718787259),m,d,t[s+9],21,-343485551),d=e(d,c),h=e(h,l),f=e(f,u),m=e(m,p);return[d,h,f,m]}function c(e){var t,r="",n=32*e.length;for(t=0;t<n;t+=8)r+=String.fromCharCode(e[t>>5]>>>t%32&255);return r}function l(e){var t,r=[];for(r[(e.length>>2)-1]=void 0,t=0;t<r.length;t+=1)r[t]=0;var n=8*e.length;for(t=0;t<n;t+=8)r[t>>5]|=(255&e.charCodeAt(t/8))<<t%32;return r}function u(e){return c(s(l(e),8*e.length))}function p(e,t){var r,n,o=l(e),a=[],i=[];for(a[15]=i[15]=void 0,o.length>16&&(o=s(o,8*e.length)),r=0;r<16;r+=1)a[r]=909522486^o[r],i[r]=1549556828^o[r];return n=s(a.concat(l(t)),512+8*t.length),c(s(i.concat(n),640))}function d(e){var t,r,n="";for(r=0;r<e.length;r+=1)t=e.charCodeAt(r),n+="0123456789abcdef".charAt(t>>>4&15)+"0123456789abcdef".charAt(15&t);return n}function h(e){return window.unescape?window.unescape(encodeURIComponent(e)):e}function f(e){return u(h(e))}function m(e){return d(f(e))}function y(e,t){return p(h(e),h(t))}function g(e,t){return d(y(e,t))}return{MD5:function(e,t,r){return t?r?y(t,e):g(t,e):r?f(e):m(e)},RawMD5:u}}),function(e,t){"function"==typeof define&&define.amd?define("axios",[],t):e.axios=t()}(window,function(){return function(e){function t(n){if(r[n])return r[n].exports;var o=r[n]={exports:{},id:n,loaded:!1};return e[n].call(o.exports,o,o.exports,t),o.loaded=!0,o.exports}var r={};return t.m=e,t.c=r,t.p="",t(0)}([function(e,t,r){e.exports=r(1)},function(e,t,r){function n(e){var t=new i(e),r=a(i.prototype.request,t);return o.extend(r,i.prototype,t),o.extend(r,t),r}var o=r(2),a=r(3),i=r(5),s=r(22),c=n(r(11));c.Axios=i,c.create=function(e){return n(s(c.defaults,e))},c.Cancel=r(23),c.CancelToken=r(24),c.isCancel=r(10),c.all=function(e){return Promise.all(e)},c.spread=r(25),e.exports=c,e.exports.default=c},function(e,t,r){function n(e){return"[object Array]"===p.call(e)}function o(e){return null!==e&&"object"===(void 0===e?"undefined":_typeof(e))}function a(e){return"[object Function]"===p.call(e)}function i(e,t){if(null!==e&&void 0!==e)if("object"!==(void 0===e?"undefined":_typeof(e))&&(e=[e]),n(e))for(var r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.call(null,e[a],a,e)}function s(){for(var e={},t=0,r=arguments.length;t<r;t++)i(arguments[t],function(t,r){"object"===_typeof(e[r])&&"object"===(void 0===t?"undefined":_typeof(t))?e[r]=s(e[r],t):e[r]=t});return e}function c(){for(var e={},t=0,r=arguments.length;t<r;t++)i(arguments[t],function(t,r){"object"===_typeof(e[r])&&"object"===(void 0===t?"undefined":_typeof(t))?e[r]=c(e[r],t):"object"===(void 0===t?"undefined":_typeof(t))?e[r]=c({},t):e[r]=t});return e}var l=r(3),u=r(4),p=Object.prototype.toString;e.exports={isArray:n,isArrayBuffer:function(e){return"[object ArrayBuffer]"===p.call(e)},isBuffer:u,isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:o,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===p.call(e)},isFile:function(e){return"[object File]"===p.call(e)},isBlob:function(e){return"[object Blob]"===p.call(e)},isFunction:a,isStream:function(e){return o(e)&&a(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:i,merge:s,deepMerge:c,extend:function(e,t,r){return i(t,function(t,n){e[n]=r&&"function"==typeof t?l(t,r):t}),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}}},function(e,t){e.exports=function(e,t){return function(){for(var r=new Array(arguments.length),n=0;n<r.length;n++)r[n]=arguments[n];return e.apply(t,r)}}},function(e,t){e.exports=function(e){return null!=e&&null!=e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}},function(e,t,r){function n(e){this.defaults=e,this.interceptors={request:new i,response:new i}}var o=r(2),a=r(6),i=r(7),s=r(8),c=r(22);n.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=c(this.defaults,e)).method=e.method?e.method.toLowerCase():"get";var t=[s,void 0],r=Promise.resolve(e);for(this.interceptors.request.forEach(function(e){t.unshift(e.fulfilled,e.rejected)}),this.interceptors.response.forEach(function(e){t.push(e.fulfilled,e.rejected)});t.length;)r=r.then(t.shift(),t.shift());return r},n.prototype.getUri=function(e){return e=c(this.defaults,e),a(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},o.forEach(["delete","get","head","options"],function(e){n.prototype[e]=function(t,r){return this.request(o.merge(r||{},{method:e,url:t}))}}),o.forEach(["post","put","patch"],function(e){n.prototype[e]=function(t,r,n){return this.request(o.merge(n||{},{method:e,url:t,data:r}))}}),e.exports=n},function(e,t,r){function n(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var o=r(2);e.exports=function(e,t,r){if(!t)return e;var a;if(r)a=r(t);else if(o.isURLSearchParams(t))a=t.toString();else{var i=[];o.forEach(t,function(e,t){null!==e&&void 0!==e&&(o.isArray(e)?t+="[]":e=[e],o.forEach(e,function(e){o.isDate(e)?e=e.toISOString():o.isObject(e)&&(e=JSON.stringify(e)),i.push(n(t)+"="+n(e))}))}),a=i.join("&")}if(a){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+a}return e}},function(e,t,r){function n(){this.handlers=[]}var o=r(2);n.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},n.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},n.prototype.forEach=function(e){o.forEach(this.handlers,function(t){null!==t&&e(t)})},e.exports=n},function(e,t,r){function n(e){e.cancelToken&&e.cancelToken.throwIfRequested()}var o=r(2),a=r(9),i=r(10),s=r(11),c=r(20),l=r(21);e.exports=function(e){return n(e),e.baseURL&&!c(e.url)&&(e.url=l(e.baseURL,e.url)),e.headers=e.headers||{},e.data=a(e.data,e.headers,e.transformRequest),e.headers=o.merge(e.headers.common||{},e.headers[e.method]||{},e.headers||{}),o.forEach(["delete","get","head","post","put","patch","common"],function(t){delete e.headers[t]}),(e.adapter||s.adapter)(e).then(function(t){return n(e),t.data=a(t.data,t.headers,e.transformResponse),t},function(t){return i(t)||(n(e),t&&t.response&&(t.response.data=a(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)})}},function(e,t,r){var n=r(2);e.exports=function(e,t,r){return n.forEach(r,function(r){e=r(e,t)}),e}},function(e,t){e.exports=function(e){return!(!e||!e.__CANCEL__)}},function(e,t,r){function n(e,t){!o.isUndefined(e)&&o.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var o=r(2),a=r(12),i={"Content-Type":"application/x-www-form-urlencoded"},s={adapter:function(){var e;return"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process)?e=r(13):"undefined"!=typeof XMLHttpRequest&&(e=r(13)),e}(),transformRequest:[function(e,t){return a(t,"Accept"),a(t,"Content-Type"),o.isFormData(e)||o.isArrayBuffer(e)||o.isBuffer(e)||o.isStream(e)||o.isFile(e)||o.isBlob(e)?e:o.isArrayBufferView(e)?e.buffer:o.isURLSearchParams(e)?(n(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):o.isObject(e)?(n(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300}};s.headers={common:{Accept:"application/json, text/plain, */*"}},o.forEach(["delete","get","head"],function(e){s.headers[e]={}}),o.forEach(["post","put","patch"],function(e){s.headers[e]=o.merge(i)}),e.exports=s},function(e,t,r){var n=r(2);e.exports=function(e,t){n.forEach(e,function(r,n){n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,delete e[n])})}},function(e,t,r){var n=r(2),o=r(14),a=r(6),i=r(17),s=r(18),c=r(15);e.exports=function(e){return new Promise(function(t,l){var u=e.data,p=e.headers;n.isFormData(u)&&delete p["Content-Type"];var d=new XMLHttpRequest;if(e.auth){var h=e.auth.username||"",f=e.auth.password||"";p.Authorization="Basic "+btoa(h+":"+f)}if(d.open(e.method.toUpperCase(),a(e.url,e.params,e.paramsSerializer),!0),d.timeout=e.timeout,d.onreadystatechange=function(){if(d&&4===d.readyState&&(0!==d.status||d.responseURL&&0===d.responseURL.indexOf("file:"))){var r="getAllResponseHeaders"in d?i(d.getAllResponseHeaders()):null,n={data:e.responseType&&"text"!==e.responseType?d.response:d.responseText,status:d.status,statusText:d.statusText,headers:r,config:e,request:d};o(t,l,n),d=null}},d.onabort=function(){d&&(l(c("Request aborted",e,"ECONNABORTED",d)),d=null)},d.onerror=function(){l(c("Network Error",e,null,d)),d=null},d.ontimeout=function(){l(c("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",d)),d=null},n.isStandardBrowserEnv()){var m=r(19),y=(e.withCredentials||s(e.url))&&e.xsrfCookieName?m.read(e.xsrfCookieName):void 0;y&&(p[e.xsrfHeaderName]=y)}if("setRequestHeader"in d&&n.forEach(p,function(e,t){void 0===u&&"content-type"===t.toLowerCase()?delete p[t]:d.setRequestHeader(t,e)}),e.withCredentials&&(d.withCredentials=!0),e.responseType)try{d.responseType=e.responseType}catch(t){if("json"!==e.responseType)throw t}"function"==typeof e.onDownloadProgress&&d.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&d.upload&&d.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then(function(e){d&&(d.abort(),l(e),d=null)}),void 0===u&&(u=null),d.send(u)})}},function(e,t,r){var n=r(15);e.exports=function(e,t,r){var o=r.config.validateStatus;!o||o(r.status)?e(r):t(n("Request failed with status code "+r.status,r.config,null,r.request,r))}},function(e,t,r){var n=r(16);e.exports=function(e,t,r,o,a){var i=new Error(e);return n(i,t,r,o,a)}},function(e,t){e.exports=function(e,t,r,n,o){return e.config=t,r&&(e.code=r),e.request=n,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}},function(e,t,r){var n=r(2),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,r,a,i={};return e?(n.forEach(e.split("\n"),function(e){if(a=e.indexOf(":"),t=n.trim(e.substr(0,a)).toLowerCase(),r=n.trim(e.substr(a+1)),t){if(i[t]&&o.indexOf(t)>=0)return;i[t]="set-cookie"===t?(i[t]?i[t]:[]).concat([r]):i[t]?i[t]+", "+r:r}}),i):i}},function(e,t,r){var n=r(2);e.exports=n.isStandardBrowserEnv()?function(){function e(e){var t=e;return r&&(o.setAttribute("href",t),t=o.href),o.setAttribute("href",t),{href:o.href,protocol:o.protocol?o.protocol.replace(/:$/,""):"",host:o.host,search:o.search?o.search.replace(/^\?/,""):"",hash:o.hash?o.hash.replace(/^#/,""):"",hostname:o.hostname,port:o.port,pathname:"/"===o.pathname.charAt(0)?o.pathname:"/"+o.pathname}}var t,r=/(msie|trident)/i.test(navigator.userAgent),o=document.createElement("a");return t=e(window.location.href),function(r){var o=n.isString(r)?e(r):r;return o.protocol===t.protocol&&o.host===t.host}}():function(){return!0}},function(e,t,r){var n=r(2);e.exports=n.isStandardBrowserEnv()?{write:function(e,t,r,o,a,i){var s=[];s.push(e+"="+encodeURIComponent(t)),n.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),n.isString(o)&&s.push("path="+o),n.isString(a)&&s.push("domain="+a),!0===i&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},function(e,t){e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},function(e,t){e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},function(e,t,r){var n=r(2);e.exports=function(e,t){t=t||{};var r={};return n.forEach(["url","method","params","data"],function(e){void 0!==t[e]&&(r[e]=t[e])}),n.forEach(["headers","auth","proxy"],function(o){n.isObject(t[o])?r[o]=n.deepMerge(e[o],t[o]):void 0!==t[o]?r[o]=t[o]:n.isObject(e[o])?r[o]=n.deepMerge(e[o]):void 0!==e[o]&&(r[o]=e[o])}),n.forEach(["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","maxContentLength","validateStatus","maxRedirects","httpAgent","httpsAgent","cancelToken","socketPath"],function(n){void 0!==t[n]?r[n]=t[n]:void 0!==e[n]&&(r[n]=e[n])}),r}},function(e,t){function r(e){this.message=e}r.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},r.prototype.__CANCEL__=!0,e.exports=r},function(e,t,r){function n(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise(function(e){t=e});var r=this;e(function(e){r.reason||(r.reason=new o(e),t(r.reason))})}var o=r(23);n.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},n.source=function(){var e;return{token:new n(function(t){e=t}),cancel:e}},e.exports=n},function(e,t){e.exports=function(e){return function(t){return e.apply(null,t)}}}])}),function(e,t){"function"==typeof define&&define.amd?define("log",[],t):e.log=t()}(window,function(){function e(){this.consoleLog=window.console,this._level=t}var t=Number.MAX_VALUE;return e.prototype.setLevel=function(e){e&&(e="info"===(e=String(e).toLowerCase())?20:"warn"===e?30:"error"===e?40:"debug"===e?10:t,this._level=e)},e.prototype.runLog=function(e,t,r){if(e){var n=[(new Date).toLocaleString(),e.toLowerCase(),t,r].join("|");"debug"===e.toLowerCase()&&this._level<=10?this.consoleLog.debug(n):"info"===e.toLowerCase()&&this._level<=20?this.consoleLog.info(n):"warn"===e.toLowerCase()&&this._level<=30?this.consoleLog.warn(n):"error"===e.toLowerCase()&&this._level<=40&&this.consoleLog.error(n)}},e}),function(e,t){"function"==typeof define&&define.amd?define("v2Model",[],t):e.v2Model=t()}(window,function(){var e={type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"},Name:{sentAs:"DisplayName"}}},t={type:"object",location:"xml",sentAs:"Rule",parameters:{ApplyServerSideEncryptionByDefault:{type:"object",sentAs:"ApplyServerSideEncryptionByDefault",parameters:{SSEAlgorithm:{sentAs:"SSEAlgorithm"},KMSMasterKeyID:{sentAs:"KMSMasterKeyID"}}}}},r={type:"object",location:"xml",sentAs:"InventoryConfiguration",parameters:{Id:{sentAs:"Id"},IsEnabled:{sentAs:"IsEnabled"},Filter:{type:"object",sentAs:"Filter",parameters:{Prefix:{sentAs:"Prefix"}}},Destination:{type:"object",sentAs:"Destination",parameters:{Format:{sentAs:"Format"},Bucket:{sentAs:"Bucket"},Prefix:{sentAs:"Prefix"},Encryption:{type:"object",sentAs:"Encryption",parameters:{"SSE-KMS":{type:"object",sentAs:"SSE-KMS",parameters:{KeyId:{sentAs:"KeyId",type:"adapter"}}}}}}},Schedule:{type:"object",sentAs:"Schedule",parameters:{Frequency:{sentAs:"Frequency"}}},IncludedObjectVersions:{sentAs:"IncludedObjectVersions"},OptionalFields:{type:"object",location:"xml",sentAs:"OptionalFields",parameters:{Field:{type:"array",sentAs:"Field",items:{type:"string"}}}}}},n={type:"object",location:"xml",sentAs:"Initiator",parameters:{ID:{sentAs:"ID"},Name:{sentAs:"DisplayName"}}},o={type:"array",location:"xml",sentAs:"CommonPrefixes",items:{type:"object",parameters:{Prefix:{sentAs:"Prefix"}}}},a={type:"array",location:"xml",wrapper:"AccessControlList",sentAs:"Grant",items:{type:"object",parameters:{Grantee:{data:{xsiNamespace:"http://www.w3.org/2001/XMLSchema-instance",xsiType:"Type"},type:"object",sentAs:"Grantee",parameters:{Type:{type:"ignore"},ID:{sentAs:"ID"},Name:{sentAs:"DisplayName"},URI:{sentAs:"URI",type:"adapter"}}},Permission:{sentAs:"Permission"}}}},i={type:"object",location:"xml",sentAs:"LoggingEnabled",parameters:{TargetBucket:{sentAs:"TargetBucket"},TargetPrefix:{sentAs:"TargetPrefix"},TargetGrants:{type:"array",wrapper:"TargetGrants",sentAs:"Grant",items:{type:"object",parameters:{Grantee:{data:{xsiNamespace:"http://www.w3.org/2001/XMLSchema-instance",xsiType:"Type"},type:"object",sentAs:"Grantee",parameters:{Type:{type:"ignore"},ID:{sentAs:"ID"},Name:{sentAs:"DisplayName"},URI:{sentAs:"URI",type:"adapter"}}},Permission:{sentAs:"Permission"}}}}}},s={required:!0,type:"array",location:"xml",sentAs:"Rule",items:{type:"object",parameters:{ID:{sentAs:"ID"},Prefix:{sentAs:"Prefix"},Status:{sentAs:"Status"},Transitions:{type:"array",sentAs:"Transition",items:{type:"object",parameters:{StorageClass:{sentAs:"StorageClass",type:"adapter"},Date:{sentAs:"Date"},Days:{type:"number",sentAs:"Days"}}}},Expiration:{type:"object",sentAs:"Expiration",parameters:{Date:{sentAs:"Date"},Days:{type:"number",sentAs:"Days"}}},NoncurrentVersionTransitions:{type:"array",sentAs:"NoncurrentVersionTransition",items:{type:"object",parameters:{StorageClass:{sentAs:"StorageClass",type:"adapter"},NoncurrentDays:{type:"number",sentAs:"NoncurrentDays"}}}},NoncurrentVersionExpiration:{type:"object",sentAs:"NoncurrentVersionExpiration",parameters:{NoncurrentDays:{type:"number",sentAs:"NoncurrentDays"}}}}}},c={type:"object",location:"xml",sentAs:"RedirectAllRequestsTo",parameters:{HostName:{sentAs:"HostName"},Protocol:{sentAs:"Protocol"}}},l={type:"array",wrapper:"RoutingRules",location:"xml",sentAs:"RoutingRule",items:{type:"object",parameters:{Condition:{type:"object",sentAs:"Condition",parameters:{HttpErrorCodeReturnedEquals:{sentAs:"HttpErrorCodeReturnedEquals"},KeyPrefixEquals:{sentAs:"KeyPrefixEquals"}}},Redirect:{type:"object",sentAs:"Redirect",parameters:{HostName:{sentAs:"HostName"},HttpRedirectCode:{sentAs:"HttpRedirectCode"},Protocol:{sentAs:"Protocol"},ReplaceKeyPrefixWith:{sentAs:"ReplaceKeyPrefixWith"},ReplaceKeyWith:{sentAs:"ReplaceKeyWith"}}}}}},u={type:"object",location:"xml",sentAs:"IndexDocument",parameters:{Suffix:{sentAs:"Suffix"}}},p={type:"object",location:"xml",sentAs:"ErrorDocument",parameters:{Key:{sentAs:"Key"}}},d={required:!0,type:"array",location:"xml",sentAs:"CORSRule",items:{type:"object",parameters:{ID:{sentAs:"ID"},AllowedMethod:{type:"array",sentAs:"AllowedMethod",items:{type:"string"}},AllowedOrigin:{type:"array",sentAs:"AllowedOrigin",items:{type:"string"}},AllowedHeader:{type:"array",sentAs:"AllowedHeader",items:{type:"string"}},MaxAgeSeconds:{type:"number",sentAs:"MaxAgeSeconds"},ExposeHeader:{type:"array",sentAs:"ExposeHeader",items:{type:"string"}}}}},h={type:"array",location:"xml",sentAs:"FunctionGraphConfiguration",items:{type:"object",location:"xml",parameters:{ID:{sentAs:"Id"},Filter:{type:"object",parameters:{FilterRules:{wrapper:"S3Key",type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{},Value:{}}}}}},FunctionGraph:{},Event:{type:"array",items:{type:"adapter"}}}}},f={type:"array",location:"xml",sentAs:"TopicConfiguration",items:{type:"object",location:"xml",parameters:{ID:{sentAs:"Id"},Filter:{type:"object",parameters:{FilterRules:{wrapper:"S3Key",type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{},Value:{}}}}}},Topic:{},Event:{type:"array",items:{type:"adapter"}}}}},m={required:!0,type:"array",location:"xml",wrapper:"TagSet",sentAs:"Tag",items:{type:"object",parameters:{Key:{sentAs:"Key"},Value:{sentAs:"Value"}}}},y={required:!0,type:"array",location:"xml",sentAs:"Rule",items:{type:"object",parameters:{ID:{sentAs:"ID"},Prefix:{sentAs:"Prefix"},Status:{sentAs:"Status"},Destination:{type:"object",sentAs:"Destination",parameters:{Bucket:{sentAs:"Bucket",type:"adapter"},StorageClass:{sentAs:"StorageClass",type:"adapter"}}}}}};return{HeadBucket:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"}}},HeadApiVersion:{httpMethod:"HEAD",urlPath:"apiversion",parameters:{Bucket:{location:"uri"}}},HeadApiVersionOutput:{parameters:{ApiVersion:{location:"header",sentAs:"x-obs-api"}}},CreateBucket:{httpMethod:"PUT",data:{xmlRoot:"CreateBucketConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0},StorageType:{location:"header",sentAs:"x-default-storage-class"},FileInterface:{location:"header",sentAs:"fs-file-interface",withPrefix:!0},MultiAz:{location:"header",sentAs:"x-obs-az-redundancy"},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadACP:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWrite:{location:"header",sentAs:"grant-write",withPrefix:!0},GrantWriteACP:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},ClusterType:{location:"header",sentAs:"cluster-type",withPrefix:!0},MultiEnterprise:{location:"header",sentAs:"epid",withPrefix:!0},Location:{location:"xml",sentAs:"LocationConstraint"}}},GetBucketMetadata:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"}}},GetBucketMetadataOutput:{parameters:{StorageClass:{location:"header",sentAs:"x-default-storage-class"},ObsVersion:{location:"header",sentAs:"x-obs-version"},Location:{location:"header",sentAs:"bucket-region",withPrefix:!0},FileInterface:{location:"header",sentAs:"fs-file-interface",withPrefix:!0},MultiAz:{location:"header",sentAs:"x-obs-az-redundancy"},MultiEnterprise:{location:"header",sentAs:"epid",withPrefix:!0},ClusterType:{location:"header",sentAs:"cluster-type",withPrefix:!0},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"}}},DeleteBucket:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"}}},ListBuckets:{httpMethod:"GET",parameters:{Type:{sentAs:"x-obs-bucket-type",location:"header"},Location:{sentAs:"location",location:"header",withPrefix:!0}}},ListBucketsOutput:{data:{type:"xml",xmlRoot:"ListAllMyBucketsResult"},parameters:{Buckets:{location:"xml",sentAs:"Bucket",type:"array",wrapper:"Buckets",items:{type:"object",location:"xml",sentAs:"Bucket",parameters:{Name:{sentAs:"Name"},CreationDate:{sentAs:"CreationDate"},Location:{sentAs:"Location"},ClusterType:{sentAs:"ClusterType"}}}},Owner:{type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"},DisplayName:{sentAs:"DisplayName"}}}}},ListObjects:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Prefix:{location:"urlPath",sentAs:"prefix"},Marker:{location:"urlPath",sentAs:"marker"},MaxKeys:{type:"number",location:"urlPath",sentAs:"max-keys"},Delimiter:{location:"urlPath",sentAs:"delimiter"}}},ListObjectsOutput:{data:{type:"xml",xmlRoot:"ListBucketResult"},parameters:{Location:{location:"header",sentAs:"bucket-region",withPrefix:!0},Bucket:{location:"xml",sentAs:"Name"},Delimiter:{location:"xml",sentAs:"Delimiter"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},Prefix:{location:"xml",sentAs:"Prefix"},Marker:{location:"xml",sentAs:"Marker"},NextMarker:{location:"xml",sentAs:"NextMarker"},MaxKeys:{location:"xml",sentAs:"MaxKeys"},Contents:{type:"array",location:"xml",sentAs:"Contents",items:{type:"object",parameters:{Key:{sentAs:"Key"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"},Type:{sentAs:"Type"},StorageClass:{sentAs:"StorageClass"},Owner:e}}},CommonPrefixes:o}},ListVersions:{httpMethod:"GET",urlPath:"versions",parameters:{Bucket:{required:!0,location:"uri"},Prefix:{location:"urlPath",sentAs:"prefix"},KeyMarker:{location:"urlPath",sentAs:"key-marker"},MaxKeys:{type:"number",location:"urlPath",sentAs:"max-keys"},Delimiter:{location:"urlPath",sentAs:"delimiter"},VersionIdMarker:{location:"urlPath",sentAs:"version-id-marker"}}},ListVersionsOutput:{data:{type:"xml",xmlRoot:"ListVersionsResult"},parameters:{Location:{location:"header",sentAs:"bucket-region",withPrefix:!0},Bucket:{location:"xml",sentAs:"Name"},Prefix:{location:"xml",sentAs:"Prefix"},Delimiter:{location:"xml",sentAs:"Delimiter"},KeyMarker:{location:"xml",sentAs:"KeyMarker"},VersionIdMarker:{location:"xml",sentAs:"VersionIdMarker"},NextKeyMarker:{location:"xml",sentAs:"NextKeyMarker"},NextVersionIdMarker:{location:"xml",sentAs:"NextVersionIdMarker"},MaxKeys:{location:"xml",sentAs:"MaxKeys"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},Versions:{type:"array",location:"xml",sentAs:"Version",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"},IsLatest:{sentAs:"IsLatest"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"},Type:{sentAs:"Type"},Owner:e,StorageClass:{sentAs:"StorageClass"}}}},DeleteMarkers:{type:"array",location:"xml",sentAs:"DeleteMarker",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"},IsLatest:{sentAs:"IsLatest"},LastModified:{sentAs:"LastModified"},Owner:e}}},CommonPrefixes:o}},GetBucketLocation:{httpMethod:"GET",urlPath:"location",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLocationOutput:{data:{type:"xml",xmlRoot:"CreateBucketConfiguration"},parameters:{Location:{location:"xml",sentAs:"LocationConstraint"}}},GetBucketStorageInfo:{httpMethod:"GET",urlPath:"storageinfo",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketStorageInfoOutput:{data:{type:"xml",xmlRoot:"GetBucketStorageInfoResult"},parameters:{Size:{location:"xml",sentAs:"Size"},ObjectNumber:{location:"xml",sentAs:"ObjectNumber"}}},SetBucketQuota:{httpMethod:"PUT",urlPath:"quota",data:{xmlRoot:"Quota"},parameters:{Bucket:{required:!0,location:"uri"},StorageQuota:{required:!0,location:"xml",sentAs:"StorageQuota"}}},GetBucketQuota:{httpMethod:"GET",urlPath:"quota",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketQuotaOutput:{data:{type:"xml",xmlRoot:"Quota"},parameters:{StorageQuota:{location:"xml",sentAs:"StorageQuota"}}},SetBucketAcl:{httpMethod:"PUT",urlPath:"acl",data:{xmlRoot:"AccessControlPolicy"},parameters:{Bucket:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},Owner:e,Grants:a}},GetBucketAcl:{httpMethod:"GET",urlPath:"acl",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketAclOutput:{data:{type:"xml",xmlRoot:"AccessControlPolicy"},parameters:{Owner:e,Grants:a}},SetBucketLoggingConfiguration:{httpMethod:"PUT",urlPath:"logging",data:{xmlRoot:"BucketLoggingStatus",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},LoggingEnabled:i}},GetBucketLoggingConfiguration:{httpMethod:"GET",urlPath:"logging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLoggingConfigurationOutput:{data:{type:"xml",xmlRoot:"BucketLoggingStatus"},parameters:{LoggingEnabled:i}},SetBucketPolicy:{httpMethod:"PUT",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"},Policy:{required:!0,location:"body"}}},GetBucketPolicy:{httpMethod:"GET",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketPolicyOutput:{data:{type:"body"},parameters:{Policy:{location:"body"}}},DeleteBucketPolicy:{httpMethod:"DELETE",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketDisPolicy:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},OEFMarker:{location:"header",sentAs:"x-obs-oef-marker"},Rules:{required:!0,location:"body"}}},GetBucketDisPolicy:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},OEFMarker:{location:"header",sentAs:"x-obs-oef-marker"}}},GetBucketDisPolicyOutput:{data:{type:"body"},parameters:{Rules:{location:"body"}}},DeleteBucketDisPolicy:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},OEFMarker:{location:"header",sentAs:"x-obs-oef-marker"}}},SetBucketLifecycleConfiguration:{httpMethod:"PUT",urlPath:"lifecycle",data:{xmlRoot:"LifecycleConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Rules:s}},GetBucketLifecycleConfiguration:{httpMethod:"GET",urlPath:"lifecycle",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLifecycleConfigurationOutput:{data:{type:"xml",xmlRoot:"LifecycleConfiguration"},parameters:{Rules:s}},DeleteBucketLifecycleConfiguration:{httpMethod:"DELETE",urlPath:"lifecycle",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketWebsiteConfiguration:{httpMethod:"PUT",urlPath:"website",data:{xmlRoot:"WebsiteConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},RedirectAllRequestsTo:c,IndexDocument:u,ErrorDocument:p,RoutingRules:l}},GetBucketWebsiteConfiguration:{httpMethod:"GET",urlPath:"website",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketWebsiteConfigurationOutput:{data:{type:"xml",xmlRoot:"WebsiteConfiguration"},parameters:{RedirectAllRequestsTo:c,IndexDocument:u,ErrorDocument:p,RoutingRules:l}},DeleteBucketWebsiteConfiguration:{httpMethod:"DELETE",urlPath:"website",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketVersioningConfiguration:{httpMethod:"PUT",urlPath:"versioning",data:{xmlRoot:"VersioningConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},VersionStatus:{required:!0,location:"xml",sentAs:"Status"}}},GetBucketVersioningConfiguration:{httpMethod:"GET",urlPath:"versioning",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketVersioningConfigurationOutput:{data:{type:"xml",xmlRoot:"VersioningConfiguration"},parameters:{VersionStatus:{location:"xml",sentAs:"Status"}}},SetBucketCors:{httpMethod:"PUT",urlPath:"cors",data:{xmlRoot:"CORSConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},CorsRules:d}},GetBucketCors:{httpMethod:"GET",urlPath:"cors",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketCorsOutput:{data:{type:"xml",xmlRoot:"CORSConfiguration"},parameters:{CorsRules:d}},DeleteBucketCors:{httpMethod:"DELETE",urlPath:"cors",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketNotification:{httpMethod:"PUT",urlPath:"notification",data:{xmlRoot:"NotificationConfiguration",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},TopicConfigurations:f,FunctionGraphConfigurations:h}},GetBucketNotification:{httpMethod:"GET",urlPath:"notification",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketNotificationOutput:{data:{type:"xml",xmlRoot:"NotificationConfiguration"},parameters:{TopicConfigurations:f,FunctionGraphConfigurations:h}},SetBucketTagging:{httpMethod:"PUT",urlPath:"tagging",data:{xmlRoot:"Tagging",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Tags:m}},DeleteBucketTagging:{httpMethod:"DELETE",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketTagging:{httpMethod:"GET",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketTaggingOutput:{data:{type:"xml",xmlRoot:"Tagging"},parameters:{Tags:m}},SetBucketStoragePolicy:{httpMethod:"PUT",urlPath:"storagePolicy",data:{xmlRoot:"StoragePolicy"},parameters:{Bucket:{required:!0,location:"uri"},StorageClass:{required:!0,location:"xml",type:"adapter",sentAs:"DefaultStorageClass"}}},GetBucketStoragePolicy:{httpMethod:"GET",urlPath:"storagePolicy",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketStoragePolicyOutput:{data:{type:"xml",xmlRoot:"StoragePolicy"},parameters:{StorageClass:{location:"xml",type:"string",sentAs:"DefaultStorageClass"}}},SetBucketReplication:{httpMethod:"PUT",urlPath:"replication",data:{xmlRoot:"ReplicationConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Agency:{location:"xml",sentAs:"Agency"},Rules:y}},GetBucketReplication:{httpMethod:"GET",urlPath:"replication",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketReplicationOutput:{data:{type:"xml",xmlRoot:"ReplicationConfiguration"},parameters:{Agency:{location:"xml",sentAs:"Agency"},Rules:y}},DeleteBucketReplication:{httpMethod:"DELETE",urlPath:"replication",parameters:{Bucket:{required:!0,location:"uri"}}},PutObject:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ContentMD5:{location:"header",sentAs:"Content-MD5"},ContentType:{location:"header",sentAs:"Content-Type"},Offset:{type:"plain"},ContentLength:{location:"header",sentAs:"Content-Length",type:"plain"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Expires:{location:"header",sentAs:"x-obs-expires",type:"number"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},Body:{location:"body"},SourceFile:{type:"srcFile"},ProgressCallback:{type:"plain"}}},PutObjectOutput:{parameters:{ETag:{location:"header",sentAs:"etag"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},AppendObject:{httpMethod:"POST",urlPath:"append",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},Position:{location:"urlPath",sentAs:"position",type:"number"},ContentMD5:{location:"header",sentAs:"Content-MD5"},Offset:{type:"plain"},ContentType:{location:"header",sentAs:"Content-Type"},ContentLength:{location:"header",sentAs:"Content-Length",type:"plain"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Expires:{location:"header",sentAs:"x-obs-expires",type:"number"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},Body:{location:"body"},SourceFile:{type:"srcFile"},ProgressCallback:{type:"plain"}}},AppendObjectOutput:{parameters:{ETag:{location:"header",sentAs:"etag"},NextPosition:{location:"header",sentAs:"x-obs-next-append-position"},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},GetObject:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ResponseCacheControl:{location:"urlPath",sentAs:"response-cache-control"},ResponseContentDisposition:{location:"urlPath",sentAs:"response-content-disposition"},ResponseContentEncoding:{location:"urlPath",sentAs:"response-content-encoding"},ResponseContentLanguage:{location:"urlPath",sentAs:"response-content-language"},ResponseContentType:{location:"urlPath",sentAs:"response-content-type"},ResponseExpires:{location:"urlPath",sentAs:"response-expires"},VersionId:{location:"urlPath",sentAs:"versionId"},ImageProcess:{location:"urlPath",sentAs:"x-image-process"},IfMatch:{location:"header",sentAs:"If-Match"},IfModifiedSince:{location:"header",sentAs:"If-Modified-Since"},IfNoneMatch:{location:"header",sentAs:"If-None-Match"},IfUnmodifiedSince:{location:"header",sentAs:"If-Unmodified-Since"},Range:{location:"header",sentAs:"Range"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},SaveByType:{type:"dstFile"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},ProgressCallback:{type:"plain"}}},GetObjectOutput:{data:{type:"body"},parameters:{Content:{location:"body"},Expiration:{location:"header",sentAs:"expiration",withPrefix:!0},ETag:{location:"header",sentAs:"etag"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentType:{location:"header",sentAs:"Content-Type"},Expires:{location:"header",sentAs:"Expires"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},ContentLength:{location:"header",sentAs:"Content-Length"},DeleteMarker:{location:"header",sentAs:"delete-marker",withPrefix:!0},LastModified:{location:"header",sentAs:"Last-Modified"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},Restore:{location:"header",sentAs:"restore",withPrefix:!0},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0},Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0}}},CopyObject:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},CopySource:{required:!0,location:"header",sentAs:"copy-source",withPrefix:!0,skipEncoding:!0},CopySourceIfMatch:{location:"header",sentAs:"copy-source-if-match",withPrefix:!0},CopySourceIfModifiedSince:{location:"header",sentAs:"copy-source-if-modified-since",withPrefix:!0},CopySourceIfNoneMatch:{location:"header",sentAs:"copy-source-if-none-match",withPrefix:!0},CopySourceIfUnmodifiedSince:{location:"header",sentAs:"copy-source-if-unmodified-since",withPrefix:!0},ContentType:{location:"header",sentAs:"Content-Type"},ContentEncoding:{location:"header",sentAs:"content-encoding"},ContentLanguage:{location:"header",sentAs:"content-language"},ContentDisposition:{location:"header",sentAs:"content-disposition"},CacheControl:{location:"header",sentAs:"cache-control"},Expires:{location:"header",sentAs:"expires"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},MetadataDirective:{location:"header",sentAs:"metadata-directive",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},CopySourceSseC:{location:"header",sentAs:"copy-source-server-side-encryption-customer-algorithm",withPrefix:!0},CopySourceSseCKey:{location:"header",sentAs:"copy-source-server-side-encryption-customer-key",type:"password",withPrefix:!0}}},CopyObjectOutput:{data:{type:"xml",xmlRoot:"CopyObjectResult"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},CopySourceVersionId:{location:"header",sentAs:"copy-source-version-id",withPrefix:!0},ETag:{location:"xml",sentAs:"ETag"},LastModified:{location:"xml",sentAs:"LastModified"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},RestoreObject:{httpMethod:"POST",urlPath:"restore",data:{xmlRoot:"RestoreRequest",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Days:{location:"xml",sentAs:"Days"},Tier:{wrapper:"GlacierJobParameters",location:"xml",sentAs:"Tier"}}},GetObjectMetadata:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0}}},GetObjectMetadataOutput:{parameters:{Expiration:{location:"header",sentAs:"expiration",withPrefix:!0},LastModified:{location:"header",sentAs:"Last-Modified"},ContentLength:{location:"header",sentAs:"Content-Length"},ContentType:{location:"header",sentAs:"Content-Type"},ETag:{location:"header",sentAs:"etag"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},Restore:{location:"header",sentAs:"restore",withPrefix:!0},ObjectType:{location:"header",sentAs:"x-obs-object-type"},NextPosition:{location:"header",sentAs:"x-obs-next-append-position"},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0},Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0},ContentLanguage:{location:"header",sentAs:"Content-Language"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},Expires:{location:"header",sentAs:"Expires"}}},SetObjectMetadata:{httpMethod:"PUT",urlPath:"metadata",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},ContentType:{location:"header",sentAs:"Content-Type"},Expires:{location:"header",sentAs:"Expires"},Metadata:{shape:"Sy",location:"header",sentAs:"meta-",withPrefix:!0},MetadataDirective:{location:"header",sentAs:"metadata-directive",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0}}},SetObjectMetadataOutput:{parameters:{Expires:{location:"header",sentAs:"Expires"},ContentLength:{location:"header",sentAs:"Content-Length"},ContentType:{location:"header",sentAs:"Content-Type"},ContentLanguage:{location:"header",sentAs:"Content-Language"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0}}},SetObjectAcl:{httpMethod:"PUT",urlPath:"acl",data:{xmlRoot:"AccessControlPolicy"},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},Owner:e,Grants:a}},SetObjectAclOutput:{parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0}}},GetObjectAcl:{httpMethod:"GET",urlPath:"acl",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},GetObjectAclOutput:{data:{type:"xml",xmlRoot:"AccessControlPolicy"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},Owner:e,Grants:a}},DeleteObject:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},DeleteObjectOutput:{parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},DeleteMarker:{location:"header",sentAs:"delete-marker",withPrefix:!0}}},DeleteObjects:{httpMethod:"POST",urlPath:"delete",data:{xmlRoot:"Delete",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Quiet:{location:"xml",sentAs:"Quiet"},Objects:{required:!0,type:"array",location:"xml",sentAs:"Object",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"}}}}}},DeleteObjectsOutput:{data:{type:"xml",xmlRoot:"DeleteResult"},parameters:{Deleteds:{type:"array",location:"xml",sentAs:"Deleted",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"},DeleteMarker:{sentAs:"DeleteMarker"},DeleteMarkerVersionId:{sentAs:"DeleteMarkerVersionId"}}}},Errors:{type:"array",location:"xml",sentAs:"Error",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"},Code:{sentAs:"Code"},Message:{sentAs:"Message"}}}}}},InitiateMultipartUpload:{httpMethod:"POST",urlPath:"uploads",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Expires:{location:"header",sentAs:"x-obs-expires",type:"number"},ContentType:{location:"header",sentAs:"Content-Type"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0}}},InitiateMultipartUploadOutput:{data:{type:"xml",xmlRoot:"InitiateMultipartUploadResult"},parameters:{Bucket:{location:"xml",sentAs:"Bucket"},Key:{location:"xml",sentAs:"Key"},UploadId:{location:"xml",sentAs:"UploadId"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},ListMultipartUploads:{httpMethod:"GET",urlPath:"uploads",parameters:{Bucket:{required:!0,location:"uri"},Delimiter:{location:"urlPath",sentAs:"delimiter"},KeyMarker:{location:"urlPath",sentAs:"key-marker"},MaxUploads:{type:"number",location:"urlPath",sentAs:"max-uploads"},Prefix:{location:"urlPath",sentAs:"prefix"},UploadIdMarker:{location:"urlPath",sentAs:"upload-id-marker"}}},ListMultipartUploadsOutput:{data:{type:"xml",xmlRoot:"ListMultipartUploadsResult"},parameters:{Bucket:{location:"xml",sentAs:"Bucket"},KeyMarker:{location:"xml",sentAs:"KeyMarker"},UploadIdMarker:{location:"xml",sentAs:"UploadIdMarker"},NextKeyMarker:{location:"xml",sentAs:"NextKeyMarker"},Prefix:{location:"xml",sentAs:"Prefix"},Delimiter:{location:"xml",sentAs:"Delimiter"},NextUploadIdMarker:{location:"xml",sentAs:"NextUploadIdMarker"},MaxUploads:{location:"xml",sentAs:"MaxUploads"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},Uploads:{type:"array",location:"xml",sentAs:"Upload",items:{type:"object",parameters:{UploadId:{sentAs:"UploadId"},Key:{sentAs:"Key"},Initiated:{sentAs:"Initiated"},StorageClass:{sentAs:"StorageClass"},Owner:e,Initiator:n}}},CommonPrefixes:o}},UploadPart:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},PartNumber:{required:!0,type:"number",location:"urlPath",sentAs:"partNumber"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},ContentMD5:{location:"header",sentAs:"Content-MD5"},Body:{location:"body"},SourceFile:{type:"srcFile"},Offset:{type:"plain"},PartSize:{type:"plain"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},ProgressCallback:{type:"plain"}}},UploadPartOutput:{parameters:{ETag:{location:"header",sentAs:"etag"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},ListParts:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},MaxParts:{type:"number",location:"urlPath",sentAs:"max-parts"},PartNumberMarker:{type:"number",location:"urlPath",sentAs:"part-number-marker"}}},ListPartsOutput:{data:{type:"xml",xmlRoot:"ListPartsResult"},parameters:{Bucket:{location:"xml",sentAs:"Bucket"},Key:{location:"xml",sentAs:"Key"},UploadId:{location:"xml",sentAs:"UploadId"},PartNumberMarker:{location:"xml",sentAs:"PartNumberMarker"},NextPartNumberMarker:{location:"xml",sentAs:"NextPartNumberMarker"},MaxParts:{location:"xml",sentAs:"MaxParts"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},StorageClass:{location:"xml",sentAs:"StorageClass"},Initiator:n,Owner:e,Parts:{type:"array",location:"xml",sentAs:"Part",items:{type:"object",parameters:{PartNumber:{sentAs:"PartNumber"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"}}}}}},CopyPart:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},PartNumber:{required:!0,location:"urlPath",sentAs:"partNumber",type:"number"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},CopySource:{required:!0,location:"header",sentAs:"copy-source",skipEncoding:!0,withPrefix:!0},CopySourceRange:{location:"header",sentAs:"copy-source-range",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},CopySourceSseC:{location:"header",sentAs:"copy-source-server-side-encryption-customer-algorithm",withPrefix:!0},CopySourceSseCKey:{location:"header",sentAs:"copy-source-server-side-encryption-customer-key",type:"password",withPrefix:!0}}},CopyPartOutput:{data:{type:"xml",xmlRoot:"CopyPartResult"},parameters:{LastModified:{location:"xml",sentAs:"LastModified"},ETag:{location:"xml",sentAs:"ETag"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},AbortMultipartUpload:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"}}},CompleteMultipartUpload:{httpMethod:"POST",data:{xmlRoot:"CompleteMultipartUpload"},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},Parts:{required:!0,type:"array",location:"xml",sentAs:"Part",items:{type:"object",parameters:{PartNumber:{sentAs:"PartNumber"},ETag:{sentAs:"ETag"}}}}}},CompleteMultipartUploadOutput:{data:{type:"xml",xmlRoot:"CompleteMultipartUploadResult"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},Location:{location:"xml",sentAs:"Location"},Bucket:{location:"xml",sentAs:"Bucket"},Key:{location:"xml",sentAs:"Key"},ETag:{location:"xml",sentAs:"ETag"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-aws-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},GetBucketInventory:{httpMethod:"GET",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketInventoryOutput:{data:{type:"xml",xmlRoot:"ListInventoryConfiguration"},parameters:{Rules:{type:"array",location:"xml",sentAs:"InventoryConfiguration",items:{type:"object",parameters:{Id:{sentAs:"Id"},IsEnabled:{sentAs:"IsEnabled"},Filter:{type:"object",sentAs:"Filter",parameters:{Prefix:{sentAs:"Prefix"}}},Destination:{type:"object",sentAs:"Destination",parameters:{Format:{sentAs:"Format"},Bucket:{sentAs:"Bucket"},Prefix:{sentAs:"Prefix"}}},Schedule:{type:"object",sentAs:"Schedule",parameters:{Frequency:{sentAs:"Frequency"}}},IncludedObjectVersions:{sentAs:"IncludedObjectVersions"},OptionalFields:{type:"object",location:"xml",sentAs:"OptionalFields",parameters:{Field:{type:"array",sentAs:"Field",items:{type:"string"}}}},LastExportTime:{sentAs:"LastExportTime"}}}}}},SetBucketInventory:{httpMethod:"PUT",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"},Id:{location:"urlPath",sentAs:"id"},InventoryConfiguration:r}},SetBucketInventoryOutput:{data:{type:"xml",xmlRoot:"InventoryConfiguration"},parameters:{InventoryConfiguration:r}},DeleteInventory:{httpMethod:"DELETE",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"},Id:{location:"urlPath",sentAs:"id"}}},DeleteInventoryOutput:{data:{type:"xml",xmlRoot:"InventoryConfiguration"},parameters:{InventoryConfiguration:r}},GetBucketEncryption:{httpMethod:"GET",urlPath:"encryption",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:{type:"object",location:"xml",sentAs:"Rule",parameters:{ApplyServerSideEncryptionByDefault:{type:"object",sentAs:"ApplyServerSideEncryptionByDefault",parameters:{SSEAlgorithm:{sentAs:"SSEAlgorithm"},KMSMasterKeyID:{sentAs:"KMSMasterKeyID"}}}}}}},SetBucketEncryption:{httpMethod:"PUT",urlPath:"encryption",data:{xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},Rule:t}},SetBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:t}},DeleteBucketEncryption:{httpMethod:"DELETE",urlPath:"encryption",parameters:{Bucket:{required:!0,location:"uri"}}},DeleteBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:t}}}}),function(e,t){"function"==typeof define&&define.amd?define("obsModel",[],t):e.obsModel=t()}(window,function(){var e={type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"}}},t={type:"object",location:"xml",sentAs:"Initiator",parameters:{ID:{sentAs:"ID"}}},r={type:"array",location:"xml",sentAs:"CommonPrefixes",items:{type:"object",parameters:{Prefix:{sentAs:"Prefix"}}}},n={type:"array",location:"xml",wrapper:"AccessControlList",sentAs:"Grant",items:{type:"object",parameters:{Grantee:{type:"object",sentAs:"Grantee",parameters:{Type:{type:"ignore"},ID:{sentAs:"ID"},URI:{sentAs:"Canned",type:"adapter"}}},Permission:{sentAs:"Permission"},Delivered:{sentAs:"Delivered"}}}},o={type:"object",location:"xml",sentAs:"LoggingEnabled",parameters:{TargetBucket:{sentAs:"TargetBucket"},TargetPrefix:{sentAs:"TargetPrefix"},TargetGrants:{type:"array",wrapper:"TargetGrants",sentAs:"Grant",items:{type:"object",parameters:{Grantee:{type:"object",sentAs:"Grantee",parameters:{Type:{type:"ignore"},ID:{sentAs:"ID"},URI:{sentAs:"Canned",type:"adapter"}}},Permission:{sentAs:"Permission"}}}}}},a={required:!0,type:"array",location:"xml",sentAs:"Rule",items:{type:"object",parameters:{ID:{sentAs:"ID"},Prefix:{sentAs:"Prefix"},Status:{sentAs:"Status"},Transitions:{type:"array",sentAs:"Transition",items:{type:"object",parameters:{StorageClass:{sentAs:"StorageClass",type:"adapter"},Date:{sentAs:"Date"},Days:{type:"number",sentAs:"Days"}}}},Expiration:{type:"object",sentAs:"Expiration",parameters:{Date:{sentAs:"Date"},Days:{type:"number",sentAs:"Days"}}},NoncurrentVersionTransitions:{type:"array",sentAs:"NoncurrentVersionTransition",items:{type:"object",parameters:{StorageClass:{sentAs:"StorageClass",type:"adapter"},NoncurrentDays:{type:"number",sentAs:"NoncurrentDays"}}}},NoncurrentVersionExpiration:{type:"object",sentAs:"NoncurrentVersionExpiration",parameters:{NoncurrentDays:{type:"number",sentAs:"NoncurrentDays"}}}}}},i={type:"object",location:"xml",sentAs:"RedirectAllRequestsTo",parameters:{HostName:{sentAs:"HostName"},Protocol:{sentAs:"Protocol"}}},s={type:"array",wrapper:"RoutingRules",location:"xml",sentAs:"RoutingRule",items:{type:"object",parameters:{Condition:{type:"object",sentAs:"Condition",parameters:{HttpErrorCodeReturnedEquals:{sentAs:"HttpErrorCodeReturnedEquals"},KeyPrefixEquals:{sentAs:"KeyPrefixEquals"}}},Redirect:{type:"object",sentAs:"Redirect",parameters:{HostName:{sentAs:"HostName"},HttpRedirectCode:{sentAs:"HttpRedirectCode"},Protocol:{sentAs:"Protocol"},ReplaceKeyPrefixWith:{sentAs:"ReplaceKeyPrefixWith"},ReplaceKeyWith:{sentAs:"ReplaceKeyWith"}}}}}},c={type:"object",location:"xml",sentAs:"IndexDocument",parameters:{Suffix:{sentAs:"Suffix"}}},l={type:"object",location:"xml",sentAs:"ErrorDocument",parameters:{Key:{sentAs:"Key"}}},u={required:!0,type:"array",location:"xml",sentAs:"CORSRule",items:{type:"object",parameters:{ID:{sentAs:"ID"},AllowedMethod:{type:"array",sentAs:"AllowedMethod",items:{type:"string"}},AllowedOrigin:{type:"array",sentAs:"AllowedOrigin",items:{type:"string"}},AllowedHeader:{type:"array",sentAs:"AllowedHeader",items:{type:"string"}},MaxAgeSeconds:{type:"number",sentAs:"MaxAgeSeconds"},ExposeHeader:{type:"array",sentAs:"ExposeHeader",items:{type:"string"}}}}},p={type:"array",location:"xml",sentAs:"FunctionGraphConfiguration",items:{type:"object",location:"xml",parameters:{ID:{sentAs:"Id"},Filter:{type:"object",parameters:{FilterRules:{wrapper:"Object",type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{},Value:{}}}}}},FunctionGraph:{},Event:{type:"array",items:{type:"adapter"}}}}},d={type:"array",location:"xml",sentAs:"TopicConfiguration",items:{type:"object",location:"xml",parameters:{ID:{sentAs:"Id"},Filter:{type:"object",parameters:{FilterRules:{wrapper:"Object",type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{},Value:{}}}}}},Topic:{},Event:{type:"array",items:{type:"adapter"}}}}},h={required:!0,type:"array",location:"xml",wrapper:"TagSet",sentAs:"Tag",items:{type:"object",parameters:{Key:{sentAs:"Key"},Value:{sentAs:"Value"}}}},f={required:!0,type:"array",location:"xml",sentAs:"Rule",items:{type:"object",parameters:{ID:{sentAs:"ID"},Prefix:{sentAs:"Prefix"},Status:{sentAs:"Status"},Destination:{type:"object",sentAs:"Destination",parameters:{Bucket:{sentAs:"Bucket",type:"adapter"},StorageClass:{sentAs:"StorageClass",type:"adapter"}}}}}},m={type:"object",location:"xml",sentAs:"Rule",parameters:{ApplyServerSideEncryptionByDefault:{type:"object",sentAs:"ApplyServerSideEncryptionByDefault",parameters:{SSEAlgorithm:{sentAs:"SSEAlgorithm"},KMSMasterKeyID:{sentAs:"KMSMasterKeyID"}}}}},y={type:"object",location:"xml",sentAs:"InventoryConfiguration",parameters:{Id:{sentAs:"Id"},IsEnabled:{sentAs:"IsEnabled"},Filter:{type:"object",sentAs:"Filter",parameters:{Prefix:{sentAs:"Prefix"}}},Destination:{type:"object",sentAs:"Destination",parameters:{Format:{sentAs:"Format"},Bucket:{sentAs:"Bucket"},Prefix:{sentAs:"Prefix"},Encryption:{type:"object",sentAs:"Encryption",parameters:{"SSE-KMS":{type:"object",sentAs:"SSE-KMS",parameters:{KeyId:{sentAs:"KeyId",type:"adapter"}}}}}}},Schedule:{type:"object",sentAs:"Schedule",parameters:{Frequency:{sentAs:"Frequency"}}},IncludedObjectVersions:{sentAs:"IncludedObjectVersions"},OptionalFields:{type:"object",location:"xml",sentAs:"OptionalFields",parameters:{Field:{type:"array",sentAs:"Field",items:{type:"string"}}}}}};return{HeadBucket:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"}}},HeadApiVersion:{httpMethod:"HEAD",urlPath:"apiversion",parameters:{Bucket:{location:"uri"}}},HeadApiVersionOutput:{parameters:{ApiVersion:{location:"header",sentAs:"x-obs-api"}}},CreateBucket:{httpMethod:"PUT",data:{xmlRoot:"CreateBucketConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0},StorageType:{location:"header",sentAs:"storage-class",withPrefix:!0},FileInterface:{location:"header",sentAs:"fs-file-interface",withPrefix:!0},MultiAz:{location:"header",sentAs:"az-redundancy",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadACP:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWrite:{location:"header",sentAs:"grant-write",withPrefix:!0},GrantWriteACP:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},ClusterType:{location:"header",sentAs:"cluster-type",withPrefix:!0},MultiEnterprise:{location:"header",sentAs:"epid",withPrefix:!0},Location:{location:"xml",sentAs:"Location"}}},GetBucketMetadata:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"}}},GetBucketMetadataOutput:{parameters:{StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},ObsVersion:{location:"header",sentAs:"version",withPrefix:!0},Location:{location:"header",sentAs:"bucket-location",withPrefix:!0},FileInterface:{location:"header",sentAs:"fs-file-interface",withPrefix:!0},MultiAz:{location:"header",sentAs:"az-redundancy",withPrefix:!0},MultiEnterprise:{location:"header",sentAs:"epid",withPrefix:!0},ClusterType:{location:"header",sentAs:"cluster-type",withPrefix:!0},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"}}},DeleteBucket:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"}}},ListBuckets:{httpMethod:"GET",parameters:{Type:{sentAs:"x-obs-bucket-type",location:"header"},Location:{sentAs:"location",location:"header",withPrefix:!0}}},ListBucketsOutput:{data:{type:"xml",xmlRoot:"ListAllMyBucketsResult"},parameters:{Buckets:{location:"xml",sentAs:"Bucket",type:"array",wrapper:"Buckets",items:{type:"object",location:"xml",sentAs:"Bucket",parameters:{Name:{sentAs:"Name"},CreationDate:{sentAs:"CreationDate"},Location:{sentAs:"Location"},ClusterType:{sentAs:"ClusterType"}}}},Owner:{type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"},DisplayName:{sentAs:"DisplayName"}}}}},ListObjects:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Prefix:{location:"urlPath",sentAs:"prefix"},Marker:{location:"urlPath",sentAs:"marker"},MaxKeys:{type:"number",location:"urlPath",sentAs:"max-keys"},Delimiter:{location:"urlPath",sentAs:"delimiter"}}},ListObjectsOutput:{data:{type:"xml",xmlRoot:"ListBucketResult"},parameters:{Location:{location:"header",sentAs:"bucket-location",withPrefix:!0},Bucket:{location:"xml",sentAs:"Name"},Delimiter:{location:"xml",sentAs:"Delimiter"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},Prefix:{location:"xml",sentAs:"Prefix"},Marker:{location:"xml",sentAs:"Marker"},NextMarker:{location:"xml",sentAs:"NextMarker"},MaxKeys:{location:"xml",sentAs:"MaxKeys"},Contents:{type:"array",location:"xml",sentAs:"Contents",items:{type:"object",parameters:{Key:{sentAs:"Key"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"},Type:{sentAs:"Type"},StorageClass:{sentAs:"StorageClass"},Owner:e}}},CommonPrefixes:r}},ListVersions:{httpMethod:"GET",urlPath:"versions",parameters:{Bucket:{required:!0,location:"uri"},Prefix:{location:"urlPath",sentAs:"prefix"},KeyMarker:{location:"urlPath",sentAs:"key-marker"},MaxKeys:{type:"number",location:"urlPath",sentAs:"max-keys"},Delimiter:{location:"urlPath",sentAs:"delimiter"},VersionIdMarker:{location:"urlPath",sentAs:"version-id-marker"}}},ListVersionsOutput:{data:{type:"xml",xmlRoot:"ListVersionsResult"},parameters:{Location:{location:"header",sentAs:"bucket-location",withPrefix:!0},Bucket:{location:"xml",sentAs:"Name"},Prefix:{location:"xml",sentAs:"Prefix"},Delimiter:{location:"xml",sentAs:"Delimiter"},KeyMarker:{location:"xml",sentAs:"KeyMarker"},VersionIdMarker:{location:"xml",sentAs:"VersionIdMarker"},NextKeyMarker:{location:"xml",sentAs:"NextKeyMarker"},NextVersionIdMarker:{location:"xml",sentAs:"NextVersionIdMarker"},MaxKeys:{location:"xml",sentAs:"MaxKeys"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},Versions:{type:"array",location:"xml",sentAs:"Version",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"},IsLatest:{sentAs:"IsLatest"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"},Type:{sentAs:"Type"},Owner:e,StorageClass:{sentAs:"StorageClass"}}}},DeleteMarkers:{type:"array",location:"xml",sentAs:"DeleteMarker",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"},IsLatest:{sentAs:"IsLatest"},LastModified:{sentAs:"LastModified"},Owner:e}}},CommonPrefixes:r}},GetBucketLocation:{httpMethod:"GET",urlPath:"location",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLocationOutput:{data:{type:"xml"},parameters:{Location:{location:"xml",sentAs:"Location"}}},GetBucketStorageInfo:{httpMethod:"GET",urlPath:"storageinfo",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketStorageInfoOutput:{data:{type:"xml",xmlRoot:"GetBucketStorageInfoResult"},parameters:{Size:{location:"xml",sentAs:"Size"},ObjectNumber:{location:"xml",sentAs:"ObjectNumber"}}},SetBucketQuota:{httpMethod:"PUT",urlPath:"quota",data:{xmlRoot:"Quota"},parameters:{Bucket:{required:!0,location:"uri"},StorageQuota:{required:!0,location:"xml",sentAs:"StorageQuota"}}},GetBucketQuota:{httpMethod:"GET",urlPath:"quota",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketQuotaOutput:{data:{type:"xml",xmlRoot:"Quota"},parameters:{StorageQuota:{location:"xml",sentAs:"StorageQuota"}}},SetBucketAcl:{httpMethod:"PUT",urlPath:"acl",data:{xmlRoot:"AccessControlPolicy"},parameters:{Bucket:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},Owner:e,Grants:n}},GetBucketInventory:{httpMethod:"GET",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketInventoryOutput:{data:{type:"xml",xmlRoot:"ListInventoryConfiguration"},parameters:{Rules:{type:"array",location:"xml",sentAs:"InventoryConfiguration",items:{type:"object",parameters:{Id:{sentAs:"Id"},IsEnabled:{sentAs:"IsEnabled"},Filter:{type:"object",sentAs:"Filter",parameters:{Prefix:{sentAs:"Prefix"}}},Destination:{type:"object",sentAs:"Destination",parameters:{Format:{sentAs:"Format"},Bucket:{sentAs:"Bucket"},Prefix:{sentAs:"Prefix"}}},Schedule:{type:"object",sentAs:"Schedule",parameters:{Frequency:{sentAs:"Frequency"}}},IncludedObjectVersions:{sentAs:"IncludedObjectVersions"},OptionalFields:{type:"object",location:"xml",sentAs:"OptionalFields",parameters:{Field:{type:"array",sentAs:"Field",items:{type:"string"}}}},LastExportTime:{sentAs:"LastExportTime"}}}}}},SetBucketInventory:{httpMethod:"PUT",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"},Id:{location:"urlPath",sentAs:"id"},InventoryConfiguration:y}},SetBucketInventoryOutput:{data:{type:"xml",xmlRoot:"InventoryConfiguration"},parameters:{InventoryConfiguration:y}},DeleteBucketInventory:{httpMethod:"DELETE",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"},Id:{location:"urlPath",sentAs:"id"}}},DeleteBucketInventoryOutput:{data:{type:"xml",xmlRoot:"InventoryConfiguration"},parameters:{InventoryConfiguration:y}},GetBucketAcl:{httpMethod:"GET",urlPath:"acl",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketAclOutput:{data:{type:"xml",xmlRoot:"AccessControlPolicy"},parameters:{Owner:e,Grants:n}},SetBucketLoggingConfiguration:{httpMethod:"PUT",urlPath:"logging",data:{xmlRoot:"BucketLoggingStatus",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},Agency:{location:"xml",sentAs:"Agency"},LoggingEnabled:o}},GetBucketLoggingConfiguration:{httpMethod:"GET",urlPath:"logging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLoggingConfigurationOutput:{data:{type:"xml",xmlRoot:"BucketLoggingStatus"},parameters:{Agency:{location:"xml",sentAs:"Agency"},LoggingEnabled:o}},SetBucketPolicy:{httpMethod:"PUT",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"},Policy:{required:!0,location:"body"}}},GetBucketPolicy:{httpMethod:"GET",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketPolicyOutput:{data:{type:"body"},parameters:{Policy:{location:"body"}}},DeleteBucketPolicy:{httpMethod:"DELETE",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketDisPolicy:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},OEFMarker:{location:"header",sentAs:"x-obs-oef-marker"},Rules:{required:!0,location:"body"}}},GetBucketDisPolicy:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},OEFMarker:{location:"header",sentAs:"x-obs-oef-marker"}}},GetBucketDisPolicyOutput:{data:{type:"body"},parameters:{Rules:{location:"body"}}},DeleteBucketDisPolicy:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},OEFMarker:{location:"header",sentAs:"x-obs-oef-marker"}}},SetBucketLifecycleConfiguration:{httpMethod:"PUT",urlPath:"lifecycle",data:{xmlRoot:"LifecycleConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Rules:a}},GetBucketLifecycleConfiguration:{httpMethod:"GET",urlPath:"lifecycle",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLifecycleConfigurationOutput:{data:{type:"xml",xmlRoot:"LifecycleConfiguration"},parameters:{Rules:a}},DeleteBucketLifecycleConfiguration:{httpMethod:"DELETE",urlPath:"lifecycle",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketWebsiteConfiguration:{httpMethod:"PUT",urlPath:"website",data:{xmlRoot:"WebsiteConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},RedirectAllRequestsTo:i,IndexDocument:c,ErrorDocument:l,RoutingRules:s}},GetBucketWebsiteConfiguration:{httpMethod:"GET",urlPath:"website",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketWebsiteConfigurationOutput:{data:{type:"xml",xmlRoot:"WebsiteConfiguration"},parameters:{RedirectAllRequestsTo:i,IndexDocument:c,ErrorDocument:l,RoutingRules:s}},DeleteBucketWebsiteConfiguration:{httpMethod:"DELETE",urlPath:"website",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketVersioningConfiguration:{httpMethod:"PUT",urlPath:"versioning",data:{xmlRoot:"VersioningConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},VersionStatus:{required:!0,location:"xml",sentAs:"Status"}}},GetBucketVersioningConfiguration:{httpMethod:"GET",urlPath:"versioning",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketVersioningConfigurationOutput:{data:{type:"xml",xmlRoot:"VersioningConfiguration"},parameters:{VersionStatus:{location:"xml",sentAs:"Status"}}},SetBucketCors:{httpMethod:"PUT",urlPath:"cors",data:{xmlRoot:"CORSConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},CorsRules:u}},GetBucketCors:{httpMethod:"GET",urlPath:"cors",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketCorsOutput:{data:{type:"xml",xmlRoot:"CORSConfiguration"},parameters:{CorsRules:u}},DeleteBucketCors:{httpMethod:"DELETE",urlPath:"cors",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketNotification:{httpMethod:"PUT",urlPath:"notification",data:{xmlRoot:"NotificationConfiguration",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},TopicConfigurations:d,FunctionGraphConfigurations:p}},GetBucketNotification:{httpMethod:"GET",urlPath:"notification",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketNotificationOutput:{data:{type:"xml",xmlRoot:"NotificationConfiguration"},parameters:{RequestId:{location:"header",sentAs:"x-obs-request-id"},TopicConfigurations:d,FunctionGraphConfigurations:p}},SetBucketTagging:{httpMethod:"PUT",urlPath:"tagging",data:{xmlRoot:"Tagging",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Tags:h}},DeleteBucketTagging:{httpMethod:"DELETE",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketTagging:{httpMethod:"GET",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketTaggingOutput:{data:{type:"xml",xmlRoot:"Tagging"},parameters:{Tags:h}},SetBucketStoragePolicy:{httpMethod:"PUT",urlPath:"storageClass",parameters:{Bucket:{required:!0,location:"uri"},StorageClass:{required:!0,location:"xml",type:"adapter",sentAs:"StorageClass"}}},GetBucketStoragePolicy:{httpMethod:"GET",urlPath:"storageClass",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketStoragePolicyOutput:{data:{type:"xml"},parameters:{StorageClass:{location:"xml",type:"string",sentAs:"StorageClass"}}},SetBucketReplication:{httpMethod:"PUT",urlPath:"replication",data:{xmlRoot:"ReplicationConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Agency:{location:"xml",sentAs:"Agency"},Rules:f}},GetBucketReplication:{httpMethod:"GET",urlPath:"replication",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketReplicationOutput:{data:{type:"xml",xmlRoot:"ReplicationConfiguration"},parameters:{Agency:{location:"xml",sentAs:"Agency"},Rules:f}},DeleteBucketReplication:{httpMethod:"DELETE",urlPath:"replication",parameters:{Bucket:{required:!0,location:"uri"}}},PutObject:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ContentMD5:{location:"header",sentAs:"Content-MD5"},ContentType:{location:"header",sentAs:"Content-Type"},Offset:{type:"plain"},ContentLength:{location:"header",sentAs:"Content-Length",type:"plain"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Expires:{location:"header",sentAs:"expires",type:"number",withPrefix:!0},SuccessActionRedirect:{location:"header",sentAs:"success-action-redirect"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},Body:{location:"body"},SourceFile:{type:"srcFile"},ProgressCallback:{type:"plain"}}},PutObjectOutput:{parameters:{ETag:{location:"header",sentAs:"etag"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},AppendObject:{httpMethod:"POST",urlPath:"append",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},Position:{location:"urlPath",sentAs:"position",type:"number"},ContentMD5:{location:"header",sentAs:"Content-MD5"},ContentType:{location:"header",sentAs:"Content-Type"},Offset:{type:"plain"},ContentLength:{location:"header",sentAs:"Content-Length",type:"plain"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Expires:{location:"header",sentAs:"expires",type:"number",withPrefix:!0},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},Body:{location:"body"},SourceFile:{type:"srcFile"},ProgressCallback:{type:"plain"}}},AppendObjectOutput:{parameters:{ETag:{location:"header",sentAs:"etag"},NextPosition:{location:"header",sentAs:"next-append-position",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},GetObject:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ResponseCacheControl:{location:"urlPath",sentAs:"response-cache-control"},ResponseContentDisposition:{location:"urlPath",sentAs:"response-content-disposition"},ResponseContentEncoding:{location:"urlPath",sentAs:"response-content-encoding"},ResponseContentLanguage:{location:"urlPath",sentAs:"response-content-language"},ResponseContentType:{location:"urlPath",sentAs:"response-content-type"},ResponseExpires:{location:"urlPath",sentAs:"response-expires"},VersionId:{location:"urlPath",sentAs:"versionId"},ImageProcess:{location:"urlPath",sentAs:"x-image-process"},IfMatch:{location:"header",sentAs:"If-Match"},IfModifiedSince:{location:"header",sentAs:"If-Modified-Since"},IfNoneMatch:{location:"header",sentAs:"If-None-Match"},IfUnmodifiedSince:{location:"header",sentAs:"If-Unmodified-Since"},Range:{location:"header",sentAs:"Range"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},SaveByType:{type:"dstFile"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},ProgressCallback:{type:"plain"}}},GetObjectOutput:{data:{type:"body"},parameters:{Content:{location:"body"},Expiration:{location:"header",sentAs:"expiration",withPrefix:!0},ETag:{location:"header",sentAs:"etag"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentType:{location:"header",sentAs:"Content-Type"},Expires:{location:"header",sentAs:"Expires"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},ContentLength:{location:"header",sentAs:"Content-Length"},DeleteMarker:{location:"header",sentAs:"delete-marker",withPrefix:!0},LastModified:{location:"header",sentAs:"Last-Modified"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},Restore:{location:"header",sentAs:"restore",withPrefix:!0},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0},Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0}}},CopyObject:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},CopySource:{required:!0,location:"header",sentAs:"copy-source",withPrefix:!0,skipEncoding:!0},CopySourceIfMatch:{location:"header",sentAs:"copy-source-if-match",withPrefix:!0},CopySourceIfModifiedSince:{location:"header",sentAs:"copy-source-if-modified-since",withPrefix:!0},CopySourceIfNoneMatch:{location:"header",sentAs:"copy-source-if-none-match",withPrefix:!0},CopySourceIfUnmodifiedSince:{location:"header",sentAs:"copy-source-if-unmodified-since",withPrefix:!0},ContentType:{location:"header",sentAs:"Content-Type"},ContentEncoding:{location:"header",sentAs:"content-encoding"},ContentLanguage:{location:"header",sentAs:"content-language"},ContentDisposition:{location:"header",sentAs:"content-disposition"},CacheControl:{location:"header",sentAs:"cache-control"},Expires:{location:"header",sentAs:"expires"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},MetadataDirective:{location:"header",sentAs:"metadata-directive",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},SuccessActionRedirect:{location:"header",sentAs:"success-action-redirect"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},CopySourceSseC:{location:"header",sentAs:"copy-source-server-side-encryption-customer-algorithm",withPrefix:!0},CopySourceSseCKey:{location:"header",sentAs:"copy-source-server-side-encryption-customer-key",type:"password",withPrefix:!0}}},CopyObjectOutput:{data:{type:"xml",xmlRoot:"CopyObjectResult"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},CopySourceVersionId:{location:"header",sentAs:"copy-source-version-id",withPrefix:!0},ETag:{location:"xml",sentAs:"ETag"},LastModified:{location:"xml",sentAs:"LastModified"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},RestoreObject:{httpMethod:"POST",urlPath:"restore",data:{xmlRoot:"RestoreRequest",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Days:{location:"xml",sentAs:"Days"},Tier:{wrapper:"RestoreJob",location:"xml",sentAs:"Tier"}}},GetObjectMetadata:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0}}},GetObjectMetadataOutput:{parameters:{Expiration:{location:"header",sentAs:"expiration",withPrefix:!0},LastModified:{location:"header",sentAs:"Last-Modified"},ContentLength:{location:"header",sentAs:"Content-Length"},ContentType:{location:"header",sentAs:"Content-Type"},ETag:{location:"header",sentAs:"etag"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},Restore:{location:"header",sentAs:"restore",withPrefix:!0},ObjectType:{location:"header",sentAs:"x-obs-object-type"},NextPosition:{location:"header",sentAs:"x-obs-next-append-position"},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},SseKms:{location:"header",sentAs:"x-obs-server-side-encryption"},SseKmsKey:{location:"header",sentAs:"x-obs-server-side-encryption-kms-key-id"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0},Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0},ContentLanguage:{location:"header",sentAs:"Content-Language"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},Expires:{location:"header",sentAs:"Expires"}}},SetObjectMetadata:{httpMethod:"PUT",urlPath:"metadata",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},ContentType:{location:"header",sentAs:"Content-Type"},Expires:{location:"header",sentAs:"Expires"},Metadata:{shape:"Sy",location:"header",sentAs:"meta-",withPrefix:!0},MetadataDirective:{location:"header",sentAs:"metadata-directive",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0}}},SetObjectMetadataOutput:{parameters:{Expires:{location:"header",sentAs:"Expires"},ContentLength:{location:"header",sentAs:"Content-Length"},ContentType:{location:"header",sentAs:"Content-Type"},ContentLanguage:{location:"header",sentAs:"Content-Language"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0}}},SetObjectAcl:{httpMethod:"PUT",urlPath:"acl",data:{xmlRoot:"AccessControlPolicy"},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},Delivered:{location:"xml",sentAs:"Delivered"},Owner:e,Grants:n}},SetObjectAclOutput:{parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0}}},GetObjectAcl:{httpMethod:"GET",urlPath:"acl",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},GetObjectAclOutput:{data:{type:"xml",xmlRoot:"AccessControlPolicy"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},Delivered:{location:"xml",sentAs:"Delivered"},Owner:e,Grants:n}},DeleteObject:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},DeleteObjectOutput:{parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},DeleteMarker:{location:"header",sentAs:"delete-marker",withPrefix:!0}}},DeleteObjects:{httpMethod:"POST",urlPath:"delete",data:{xmlRoot:"Delete",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Quiet:{location:"xml",sentAs:"Quiet"},Objects:{required:!0,type:"array",location:"xml",sentAs:"Object",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"}}}}}},DeleteObjectsOutput:{data:{type:"xml",xmlRoot:"DeleteResult"},parameters:{Deleteds:{type:"array",location:"xml",sentAs:"Deleted",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"},DeleteMarker:{sentAs:"DeleteMarker"},DeleteMarkerVersionId:{sentAs:"DeleteMarkerVersionId"}}}},Errors:{type:"array",location:"xml",sentAs:"Error",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"},Code:{sentAs:"Code"},Message:{sentAs:"Message"}}}}}},InitiateMultipartUpload:{httpMethod:"POST",urlPath:"uploads",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Expires:{location:"header",sentAs:"expires",type:"number",withPrefix:!0},ContentType:{location:"header",sentAs:"Content-Type"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0}}},InitiateMultipartUploadOutput:{data:{type:"xml",xmlRoot:"InitiateMultipartUploadResult"},parameters:{Bucket:{location:"xml",sentAs:"Bucket"},Key:{location:"xml",sentAs:"Key"},UploadId:{location:"xml",sentAs:"UploadId"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},ListMultipartUploads:{httpMethod:"GET",urlPath:"uploads",parameters:{Bucket:{required:!0,location:"uri"},Delimiter:{location:"urlPath",sentAs:"delimiter"},KeyMarker:{location:"urlPath",sentAs:"key-marker"},MaxUploads:{type:"number",location:"urlPath",sentAs:"max-uploads"},Prefix:{location:"urlPath",sentAs:"prefix"},UploadIdMarker:{location:"urlPath",sentAs:"upload-id-marker"}}},ListMultipartUploadsOutput:{data:{type:"xml",xmlRoot:"ListMultipartUploadsResult"},parameters:{Bucket:{location:"xml",sentAs:"Bucket"},KeyMarker:{location:"xml",sentAs:"KeyMarker"},UploadIdMarker:{location:"xml",sentAs:"UploadIdMarker"},NextKeyMarker:{location:"xml",sentAs:"NextKeyMarker"},Prefix:{location:"xml",sentAs:"Prefix"},Delimiter:{location:"xml",sentAs:"Delimiter"},NextUploadIdMarker:{location:"xml",sentAs:"NextUploadIdMarker"},MaxUploads:{location:"xml",sentAs:"MaxUploads"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},Uploads:{type:"array",location:"xml",sentAs:"Upload",items:{type:"object",parameters:{UploadId:{sentAs:"UploadId"},Key:{sentAs:"Key"},Initiated:{sentAs:"Initiated"},StorageClass:{sentAs:"StorageClass"},Owner:e,Initiator:t}}},CommonPrefixes:r}},UploadPart:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},PartNumber:{required:!0,type:"number",location:"urlPath",sentAs:"partNumber"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},ContentMD5:{location:"header",sentAs:"Content-MD5"},Body:{location:"body"},SourceFile:{type:"srcFile"},Offset:{type:"plain"},PartSize:{type:"plain"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},ProgressCallback:{type:"plain"}}},UploadPartOutput:{parameters:{ETag:{location:"header",sentAs:"etag"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},ListParts:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},MaxParts:{type:"number",location:"urlPath",sentAs:"max-parts"},PartNumberMarker:{type:"number",location:"urlPath",sentAs:"part-number-marker"}}},ListPartsOutput:{data:{type:"xml",xmlRoot:"ListPartsResult"},parameters:{Bucket:{location:"xml",sentAs:"Bucket"},Key:{location:"xml",sentAs:"Key"},UploadId:{location:"xml",sentAs:"UploadId"},PartNumberMarker:{location:"xml",sentAs:"PartNumberMarker"},NextPartNumberMarker:{location:"xml",sentAs:"NextPartNumberMarker"},MaxParts:{location:"xml",sentAs:"MaxParts"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},StorageClass:{location:"xml",sentAs:"StorageClass"},Initiator:t,Owner:e,Parts:{type:"array",location:"xml",sentAs:"Part",items:{type:"object",parameters:{PartNumber:{sentAs:"PartNumber"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"}}}}}},CopyPart:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},PartNumber:{required:!0,location:"urlPath",sentAs:"partNumber",type:"number"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},CopySource:{required:!0,location:"header",sentAs:"copy-source",skipEncoding:!0,withPrefix:!0},CopySourceRange:{location:"header",sentAs:"copy-source-range",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},CopySourceSseC:{location:"header",sentAs:"copy-source-server-side-encryption-customer-algorithm",withPrefix:!0},CopySourceSseCKey:{location:"header",sentAs:"copy-source-server-side-encryption-customer-key",type:"password",withPrefix:!0}}},CopyPartOutput:{data:{type:"xml",xmlRoot:"CopyPartResult"},parameters:{LastModified:{location:"xml",sentAs:"LastModified"},ETag:{location:"xml",sentAs:"ETag"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},AbortMultipartUpload:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"}}},CompleteMultipartUpload:{httpMethod:"POST",data:{xmlRoot:"CompleteMultipartUpload"},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},Parts:{required:!0,type:"array",location:"xml",sentAs:"Part",items:{type:"object",parameters:{PartNumber:{sentAs:"PartNumber"},ETag:{sentAs:"ETag"}}}}}},CompleteMultipartUploadOutput:{data:{type:"xml",xmlRoot:"CompleteMultipartUploadResult"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},Location:{location:"xml",sentAs:"Location"},Bucket:{location:"xml",sentAs:"Bucket"},Key:{location:"xml",sentAs:"Key"},ETag:{location:"xml",sentAs:"ETag"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}}},GetBucketEncryption:{httpMethod:"GET",urlPath:"encryption",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:{type:"object",location:"xml",sentAs:"Rule",parameters:{ApplyServerSideEncryptionByDefault:{type:"object",sentAs:"ApplyServerSideEncryptionByDefault",parameters:{SSEAlgorithm:{sentAs:"SSEAlgorithm"},KMSMasterKeyID:{sentAs:"KMSMasterKeyID"}}}}}}},SetBucketEncryption:{httpMethod:"PUT",urlPath:"encryption",data:{xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},Rule:m}},SetBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:m}},DeleteBucketEncryption:{httpMethod:"DELETE",urlPath:"encryption",parameters:{Bucket:{required:!0,location:"uri"}}},DeleteBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:m}}}}),function(e,t){"function"==typeof define&&define.amd?define("xml2js",[],t):e.xml2js=t()}(window,function(){return new function(){var e=function(e){var t=JSON.stringify(e,void 0,2).replace(/(\\t|\\r|\\n)/g,"").replace(/"",[\n\t\r\s]+""[,]*/g,"").replace(/(\n[\t\s\r]*\n)/g,"").replace(/[\s\t]{2,}""[,]{0,1}/g,"").replace(/"[\s\t]{1,}"[,]{0,1}/g,"");return-1===t.indexOf('"parsererror": {')?t:"Invalid XML format"},t=function e(t){var r={};if(1===t.nodeType){if(t.attributes.length>0){r["@attributes"]={};for(var n=0;n<t.attributes.length;n++){var o=t.attributes.item(n);r["@attributes"][o.nodeName]=o.value}}}else 3===t.nodeType&&(r=t.nodeValue);if(t.hasChildNodes())for(var a=0;a<t.childNodes.length;a++){var i=t.childNodes.item(a),s=i.nodeName;if(void 0===r[s])r[s]=e(i);else{if(void 0===r[s].push){var c=r[s];r[s]=[],r[s].push(c)}r[s].push(e(i))}}return r};this.parseString=function(r,n){var o;window.DOMParser?o=(new window.DOMParser).parseFromString(r,"text/xml"):(o=new window.ActiveXObject("Microsoft.XMLDOM")).async="false";var a=e(t(o));return void 0===n?JSON.parse(a):a}}}),function(e,t){"function"==typeof define&&define.amd?define("utils",["URI","axios","jsSHA","Base64","md5","xml2js","obsModel","v2Model"],t):e.utils=t(e.URI,e.axios,e.jsSHA,e.Base64,e.md5,e.xml2js,e.obsModel,e.v2Model)}(window,function(e,t,r,n,o,a,i,s){function c(e,t,r){if(0===(e=String(e)).length)return"";if(r)return e;var n=void 0;if(t){n=[];for(var o=0;o<e.length;o++)n.push(t.indexOf(e[o])>=0?e[o]:encodeURIComponent(e[o]));n=n.join("")}else n=encodeURIComponent(e);return n.replace(/!/g,"%21").replace(/\*/g,"%2A").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29")}function l(e){return JSON?JSON.stringify(e):""}function u(e,t){var r={};for(var n in t){var o=String(n).toLowerCase();0===o.indexOf(e)&&(r[o.slice(e.length)]=t[n])}return r}function p(e){return"[object Array]"===Object.prototype.toString.call(e)}function d(e){return"[object Function]"===Object.prototype.toString.call(e)}function h(e){return"[object Object]"===Object.prototype.toString.call(e)}function f(e,t){if("object"===(void 0===e?"undefined":_typeof(e)))return t(null,e);try{return t(null,a.parseString(e))}catch(e){return t(e,null)}}function m(e){var t=new Date(Date.parse(e)),r=t.getUTCHours(),n=t.getUTCMinutes(),o=t.getUTCSeconds(),a=t.getUTCDate(),i=t.getUTCMonth()+1,s="";return s+=t.getUTCFullYear()+"-",i<10&&(s+="0"),s+=i+"-",a<10&&(s+="0"),s+=a+"T",r<10&&(s+="0"),s+=r+":",n<10&&(s+="0"),s+=n+":",o<10&&(s+="0"),s+=o+"Z"}function y(e){var t=new Date(Date.parse(e)),r=t.getUTCHours(),n=t.getUTCMinutes(),o=t.getUTCSeconds(),a=t.getUTCDate(),i=t.getUTCMonth()+1,s="",c="";return s+=t.getUTCFullYear(),i<10&&(s+="0"),s+=i,a<10&&(s+="0"),s+=a,c+=s+"T",r<10&&(c+="0"),c+=r,n<10&&(c+="0"),c+=n,o<10&&(c+="0"),c+=o+"Z",[s,c]}function g(e){var t=[],r={};for(var n in e)t.push(n.toLowerCase()),r[n.toLowerCase()]=e[n];t=t.sort();for(var o="",a="",i=0;i<t.length;i++)0!==i&&(o+=";"),o+=t[i],a+=t[i]+":"+r[t[i]]+"\n";return[o,a]}function A(e,t,r,n){var o=w.createHmac("sha256","AWS4"+t).update(e).digest(),a=w.createHmac("sha256",o).update(r).digest(),i=w.createHmac("sha256",a).update("s3").digest(),s=w.createHmac("sha256",i).update("aws4_request").digest();return w.createHmac("sha256",s).update(n).digest("hex")}function x(e,t,r,n,o){var a="AWS4-HMAC-SHA256\n";return a+=t+"\n",a+=e+"/"+n+"/s3/aws4_request"+"\n",a+=w.createHash("sha256").update(o).digest("hex"),A(e,r,n,a)}function b(e){this.log=e,this.ak=null,this.sk=null,this.securityToken=null,this.isSecure=!0,this.server=null,this.pathStyle=!1,this.signatureContext=null,this.isSignatureNegotiation=!0,this.bucketSignatureCache={},this.region="region",this.port=null,this.timeout=300,this.obsSdkVersion=P,this.isCname=!1,this.bucketEventEmitters={},this.useRawXhr=!1}var w={createHmac:function(e,t){var n=void 0,o=new r(n="sha1"===e?"SHA-1":"sha512"===e?"SHA-512":"SHA-256","TEXT");return o.setHMACKey(t,t instanceof ArrayBuffer?"ARRAYBUFFER":"TEXT"),{update:function(e){return o.update(e),this},digest:function(e){return"hex"===e?o.getHMAC("HEX"):"base64"===e?o.getHMAC("B64"):o.getHMAC("ARRAYBUFFER")}}},createHash:function(e){if("md5"===e)return{update:function(e){return this.message?this.message+=e:this.message=e,this},digest:function(e){return"hex"===e?o.MD5(this.message):"base64"===e?(window.btoa?window.btoa:n.encode)(o.MD5(this.message,!1,!0)):"rawbase64"===e?(window.btoa?window.btoa:n.encode)(o.RawMD5(this.message,!1,!0)):o.MD5(this.message,!1,!0)}};var t=void 0,a=new r(t="sha1"===e?"SHA-1":"sha512"===e?"SHA-512":"SHA-256","TEXT");return{update:function(e){return a.update(e),this},digest:function(e){return"hex"===e?a.getHash("HEX"):"base64"===e?a.getHash("B64"):a.getHash("ARRAYBUFFER")}}}},v={parse:function(t){var r=e.parse(t);return{hostname:r.hostname,port:r.port,host:r.hostname,protocol:r.protocol?r.protocol+":":"",query:r.query,path:r.path+(r.query?"?"+r.query:""),pathname:r.path,search:r.query?"?"+r.query:""}}},C="e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",P="3.19.9",S={"7z":"application/x-7z-compressed",aac:"audio/x-aac",ai:"application/postscript",aif:"audio/x-aiff",asc:"text/plain",asf:"video/x-ms-asf",atom:"application/atom+xml",avi:"video/x-msvideo",bmp:"image/bmp",bz2:"application/x-bzip2",cer:"application/pkix-cert",crl:"application/pkix-crl",crt:"application/x-x509-ca-cert",css:"text/css",csv:"text/csv",cu:"application/cu-seeme",deb:"application/x-debian-package",doc:"application/msword",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",dvi:"application/x-dvi",eot:"application/vnd.ms-fontobject",eps:"application/postscript",epub:"application/epub+zip",etx:"text/x-setext",flac:"audio/flac",flv:"video/x-flv",gif:"image/gif",gz:"application/gzip",htm:"text/html",html:"text/html",ico:"image/x-icon",ics:"text/calendar",ini:"text/plain",iso:"application/x-iso9660-image",jar:"application/java-archive",jpe:"image/jpeg",jpeg:"image/jpeg",jpg:"image/jpeg",js:"text/javascript",json:"application/json",latex:"application/x-latex",log:"text/plain",m4a:"audio/mp4",m4v:"video/mp4",mid:"audio/midi",midi:"audio/midi",mov:"video/quicktime",mp3:"audio/mpeg",mp4:"video/mp4",mp4a:"audio/mp4",mp4v:"video/mp4",mpe:"video/mpeg",mpeg:"video/mpeg",mpg:"video/mpeg",mpg4:"video/mp4",oga:"audio/ogg",ogg:"audio/ogg",ogv:"video/ogg",ogx:"application/ogg",pbm:"image/x-portable-bitmap",pdf:"application/pdf",pgm:"image/x-portable-graymap",png:"image/png",pnm:"image/x-portable-anymap",ppm:"image/x-portable-pixmap",ppt:"application/vnd.ms-powerpoint",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",ps:"application/postscript",qt:"video/quicktime",rar:"application/x-rar-compressed",ras:"image/x-cmu-raster",rss:"application/rss+xml",rtf:"application/rtf",sgm:"text/sgml",sgml:"text/sgml",svg:"image/svg+xml",swf:"application/x-shockwave-flash",tar:"application/x-tar",tif:"image/tiff",tiff:"image/tiff",torrent:"application/x-bittorrent",ttf:"application/x-font-ttf",txt:"text/plain",wav:"audio/x-wav",webm:"video/webm",wma:"audio/x-ms-wma",wmv:"video/x-ms-wmv",woff:"application/x-font-woff",wsdl:"application/wsdl+xml",xbm:"image/x-xbitmap",xls:"application/vnd.ms-excel",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xml:"application/xml",xpm:"image/x-xpixmap",xwd:"image/x-xwindowdump",yaml:"text/yaml",yml:"text/yaml",zip:"application/zip"},k=["inventory","acl","backtosource","policy","torrent","logging","location","storageinfo","quota","storageclass","storagepolicy","requestpayment","versions","versioning","versionid","uploads","uploadid","partnumber","website","notification","replication","lifecycle","deletebucket","delete","cors","restore","tagging","append","position","response-content-type","response-content-language","response-expires","response-cache-control","response-content-disposition","response-content-encoding","x-image-process","x-oss-process","encryption"],M=["content-type","content-md5","content-length","content-language","expires","origin","cache-control","content-disposition","content-encoding","x-default-storage-class","location","date","etag","host","last-modified","content-range","x-reserved","access-control-allow-origin","access-control-allow-headers","access-control-max-age","access-control-allow-methods","access-control-expose-headers","connection"],E={"content-length":"ContentLength",date:"Date","x-reserved":"Reserved"},R=["STANDARD","WARM","COLD"],B=["STANDARD","STANDARD_IA","GLACIER"],I=["private","public-read","public-read-write","public-read-delivered","public-read-write-delivered"],T=["private","public-read","public-read-write","authenticated-read","bucket-owner-read","bucket-owner-full-control","log-delivery-write"],O=["Everyone"],D=["http://acs.amazonaws.com/groups/global/AllUsers","http://acs.amazonaws.com/groups/global/AuthenticatedUsers","http://acs.amazonaws.com/groups/s3/LogDelivery"],L=["ObjectCreated:*","ObjectCreated:Put","ObjectCreated:Post","ObjectCreated:Copy","ObjectCreated:CompleteMultipartUpload","ObjectRemoved:*","ObjectRemoved:Delete","ObjectRemoved:DeleteMarkerCreated"],q=["s3:ObjectCreated:*","s3:ObjectCreated:Put","s3:ObjectCreated:Post","s3:ObjectCreated:Copy","s3:ObjectCreated:CompleteMultipartUpload","s3:ObjectRemoved:*","s3:ObjectRemoved:Delete","s3:ObjectRemoved:DeleteMarkerCreated"],j={signature:"obs",headerPrefix:"x-obs-",headerMetaPrefix:"x-obs-meta-",authPrefix:"OBS"},U={signature:"v2",headerPrefix:"x-amz-",headerMetaPrefix:"x-amz-meta-",authPrefix:"AWS"};return b.prototype.encodeURIWithSafe=c,b.prototype.mimeTypes=S,b.prototype.refresh=function(e,t,r){this.ak=e?String(e).trim():null,this.sk=t?String(t).trim():null,this.securityToken=r?String(r).trim():null},b.prototype.initFactory=function(e,t,r,n,o,a,i,s,c,l,u,p,d,h,f,m){if(this.refresh(e,t,l),this.urlPrefix=d||"",this.regionDomains=h||null,this.setRequestHeaderHook=f||null,!n)throw new Error("Server is not set");0===(n=String(n).trim()).indexOf("https://")?(n=n.slice("https://".length),r=!0):0===n.indexOf("http://")&&(n=n.slice("http://".length),r=!1);for(var y=n.lastIndexOf("/");y>=0;)y=(n=n.slice(0,y)).lastIndexOf("/");(y=n.indexOf(":"))>=0&&(s=n.slice(y+1),n=n.slice(0,y)),this.server=n,/^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$/.test(this.server)&&(o=!0),void 0!==r&&(this.isSecure=Boolean(r)),void 0!==o&&(this.pathStyle=Boolean(o)),a=void 0!==a?String(a).trim().toLowerCase():"obs",void 0!==u&&(this.isSignatureNegotiation=Boolean(u)),this.isCname=p,(this.pathStyle||this.isCname)&&(this.isSignatureNegotiation=!1,"obs"===a&&(a="v2")),this.signatureContext="obs"===a?j:U,void 0!==i&&(this.region=String(i)),this.port=s?parseInt(s):this.isSecure?443:80,void 0!==c&&(this.timeout=parseInt(c)),void 0!==m&&(this.useRawXhr=m)},b.prototype.SseKmsAdapter=function(e,t){e=e||"";var r=(e=String(e)).indexOf("aws:");return"obs"===t.signature?0===r?e.slice(4):e:0===r?e:"aws:"+e},b.prototype.BucketAdapter=function(e,t){e=e||"";var r=(e=String(e)).indexOf("arn:aws:s3:::");return"obs"===t.signature?0===r?e.slice("arn:aws:s3:::".length):e:0===r?e:"arn:aws:s3:::"+e},b.prototype.EventAdapter=function(e,t){return e=e||"",e=String(e),"obs"===t.signature?L.indexOf(e)>=0?e:q.indexOf(e)>=0?e.substring(3):"":q.indexOf(e)>=0?e:L.indexOf(e)>=0?"s3:"+e:""},b.prototype.URIAdapter=function(e,t){return e=e||"",e=String(e),"obs"===t.signature?O.indexOf(e)>=0?e:"AllUsers"===e||"http://acs.amazonaws.com/groups/global/AllUsers"===e?"Everyone":"":D.indexOf(e)>=0?e:"Everyone"===e||"AllUsers"===e?"http://acs.amazonaws.com/groups/global/AllUsers":"AuthenticatedUsers"===e?"http://acs.amazonaws.com/groups/global/AuthenticatedUsers":"LogDelivery"===e?"http://acs.amazonaws.com/groups/s3/LogDelivery":""},b.prototype.StorageClassAdapter=function(e,t){return e=e||"",e=String(e).toUpperCase(),"obs"===t.signature?R.indexOf(e)>=0?e:"STANDARD_IA"===e?"WARM":"GLACIER"===e?"COLD":"":B.indexOf(e)>=0?e:"WARM"===e?"STANDARD_IA":"COLD"===e?"GLACIER":""},b.prototype.ACLAdapter=function(e,t){return e=e||"",e=String(e).toLowerCase(),"obs"===t.signature?I.indexOf(e)>=0?e:"":("public-read-delivered"===e?e="public-read":"public-read-write-delivered"===e&&(e="public-read-write"),T.indexOf(e)>=0?e:"")},b.prototype.doExec=function(e,t,r){var n=this.makeParam(e,t);if("err"in n)return r(n.err,null);this.sendRequest(e,n,r)},b.prototype.doNegotiation=function(e,t,r,n,o,a){var i=null,s=this;if(o&&t.Bucket){var c=this.bucketSignatureCache[t.Bucket];if(c&&c.signatureContext&&c.expireTime>(new Date).getTime()){t.signatureContext=c.signatureContext;var l=this.makeParam(e,t);return"err"in l?r(l.err,null):(l.signatureContext=c.signatureContext,this.sendRequest(e,l,r))}if((i=this.bucketEventEmitters[t.Bucket])||(i={s:0,n:function(){for(;this.e&&this.e.length>0;)this.e.shift()()}},this.bucketEventEmitters[t.Bucket]=i),i.s)return void i.e.push(function(){s.doNegotiation(e,t,r,n,o,a)});i.e=[],i.s=1}this.doExec("HeadApiVersion",n?{Bucket:t.Bucket}:{},function(o,c){if(o)return r(o,null),void(i&&(i.s=0,i.n()));if(n&&404===c.CommonMsg.Status||c.CommonMsg.Status>=500)return r(o,c),void(i&&(i.s=0,i.n()));var l=U;c.CommonMsg.Status<300&&c.InterfaceResult&&c.InterfaceResult.ApiVersion>="3.0"&&(l=j),a&&(s.bucketSignatureCache[t.Bucket]={signatureContext:l,expireTime:(new Date).getTime()+15+60*Math.ceil(5*Math.random())*1e3}),i&&(i.s=0,i.n()),t.signatureContext=l;var u=s.makeParam(e,t);if("err"in u)return r(u.err,null);u.signatureContext=l,s.sendRequest(e,u,r)})},b.prototype.exec=function(e,t,r){var n=this;if(n.isSignatureNegotiation&&"HeadApiVersion"!==e)if("ListBuckets"===e)n.doNegotiation(e,t,r,!1,!1,!1);else if("CreateBucket"===e){n.doNegotiation(e,t,function(o,a){if(!o&&400===a.CommonMsg.Status&&"Unsupported Authorization Type"===a.CommonMsg.Message&&t.signatureContext&&"v2"===t.signatureContext.signature){t.signatureContext=U;var i=n.makeParam(e,t);return"err"in i?r(i.err,null):(i.signatureContext=t.signatureContext,void n.sendRequest(e,i,r))}r(o,a)},!1,!0,!1)}else n.doNegotiation(e,t,r,!0,!0,!0);else n.doExec(e,t,r)},b.prototype.sliceBlob=function(e,t,r,n){return n=n||e.type,e.mozSlice?e.mozSlice(t,r,n):e.webkitSlice?e.webkitSlice(t,r,n):e.slice(t,r,n)},b.prototype.toXml=function(e,t,r,n,o){var a="";if(null!==r)return a+=this.buildXml(e,t,r,n,o);for(var i in t)if(i in e){var s=t[i].sentAs||i;a+=this.buildXml(e,t[i],i,s,o)}return a},b.prototype.buildXml=function(e,t,r,n,o){var a="",i=t.type;if("array"===i)for(var s=0;s<e[r].length;s++)if("object"===t.items.type){var c=this.toXml(e[r][s],t.items.parameters,null,null,o);""!==c&&(a+="<"+n+">"+c+"</"+n+">")}else"adapter"===t.items.type?a+="<"+n+">"+String(this[r+"Adapter"](e[r][s],o)).replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;")+"</"+n+">":"array"!==t.items.type&&(a+="<"+n+">"+String(e[r][s]).replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;")+"</"+n+">");else if("object"===i){var l=this.toXml(e[r],t.parameters,null,null,o);""!==l&&(a+="<"+n,"data"in t&&("xsiNamespace"in t.data&&(a+=' xmlns:xsi="'+t.data.xsiNamespace+'"'),"xsiType"in t.data&&(a+=' xsi:type="'+e[r][t.data.xsiType]+'"')),a+=">",a+=l+"</"+n+">")}else"adapter"===i?a+="<"+n+">"+String(this[r+"Adapter"](e[r],o)).replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;")+"</"+n+">":"ignore"!==i&&(a+="<"+n+">"+String(e[r]).replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;")+"</"+n+">");if(a&&t.wrapper){var u=t.wrapper;a="<"+u+">"+a+"</"+u+">"}return a},b.prototype.jsonToObject=function(e,t,r){var n={};if(null!==r)this.buildObject(e,t,r,n);else for(var o in e)this.buildObject(e,t,o,n);return n},b.prototype.buildObject=function(e,t,r,n){if(h(t)){var o=!0,a=e[r].wrapper;if(a&&a in t&&(o=h(t=t[a])),o){var i=e[r].sentAs||r;if(i in t)if("object"===e[r].type)n[r]=this.jsonToObject(e[r].parameters,t[i],null);else if("array"===e[r].type){var s=[];if(p(t[i]))for(var c=0;c<t[i].length;c++)s[c]="object"===e[r].items.type?this.jsonToObject(e[r].items.parameters,t[i][c],null):t[i][c]["#text"];else s[0]="object"===e[r].items.type?this.jsonToObject(e[r].items.parameters,t[i],null):t[i]["#text"]||"";n[r]=s}else n[r]=t[i]["#text"]}}void 0===n[r]&&("object"===e[r].type?n[r]=e[r].parameters?this.jsonToObject(e[r].parameters,null,null):{}:"array"===e[r].type?n[r]=[]:n[r]="")},b.prototype.makeParam=function(e,t){var r=t.signatureContext||this.signatureContext,o="obs"===r.signature?i[e]:s[e],a=o.httpMethod,l="/",u="",p="",d={},h={};h.$requestParam=t,"urlPath"in o&&(u+="?",u+=o.urlPath);for(var f in o.parameters){var m=o.parameters[f];if("Bucket"!==f||!this.isCname){var y=t[f];if(m.required&&(null===y||void 0===y||"[object String]"===Object.prototype.toString.call(y)&&""===y))return h.err=f+" is a required element!",this.log.runLog("error",e,h.err),h;if(null!==y&&void 0!==y){if("srcFile"===m.type||"dstFile"===m.type){h[m.type]=y;continue}"plain"===m.type&&(h[f]=y);var g=m.sentAs||f;if(m.withPrefix&&(g=r.headerPrefix+g),"uri"===m.location)"/"!==l&&(l+="/"),l+=y;else if("header"===m.location){var A=m.encodingSafe||" ;/?:@&=+$,";if("object"===m.type){if(r.headerMetaPrefix===g)for(var x in y){var b=y[x];d[0===(x=String(x).trim().toLowerCase()).indexOf(g)?x:g+x]=c(b,A)}}else if("array"===m.type){var w=[];for(var v in y)w[v]=c(y[v],A);d[g]=w}else if("password"===m.type){var C=window.btoa?window.btoa:n.encode;d[g]=C(y),d[m.pwdSentAs||g+"-MD5"]=this.rawBufMD5(y)}else if("number"===m.type&&Number(y))d[g]=c(String(y),A);else if("boolean"===m.type)d[g]=c(y?"true":"false",A);else if("adapter"===m.type){var P=this[f+"Adapter"](y,r);P&&(d[g]=c(String(P),A))}else d[g]=c(String(y),A,m.skipEncoding)}else if("urlPath"===m.location){var S=""===u?"?":"&",k=y;("number"!==m.type||"number"===m.type&&Number(k)>=0)&&(u+=S+c(g,"/")+"="+c(String(k),"/"))}else if("xml"===m.location){var M=this.toXml(t,m,f,g,r);M&&(p+=M)}else"body"===m.location&&(p=y)}}}var E="file"===h.dstFile;if("Content-Type"in d||E||(d["Content-Type"]="binary/octet-stream"),"data"in o&&"xmlRoot"in o.data&&(p||o.data.xmlAllowEmpty)){var R=o.data.xmlRoot;p="<"+R+">"+p+"</"+R+">",d["Content-Type"]="application/xml"}if(E&&(h.rawUri=l),d.Host=this.server+(80===this.port||443===this.port?"":":"+this.port),!this.pathStyle&&!this.isCname){var B=l.split("/");if(B.length>=2&&B[1]){d.Host=B[1]+"."+d.Host;var I=l.replace(B[1],"");0===I.indexOf("//")&&(I=I.slice(1)),"v4"===r.signature?l=I:"/"===I&&(l+="/"),h.requestUri=c(I,"/")}}if(h.method=a,h.uri=c(l,"/"),h.urlPath=u,p&&(o.data&&o.data.md5&&(d["Content-MD5"]=this.bufMD5(p),d["Content-Length"]=0===p.length?"0":String(p.length)),h.xml=p,this.log.runLog("debug",e,"request content:"+p)),h.headers=d,"srcFile"in h&&(h.srcFile instanceof window.File||h.srcFile instanceof window.Blob)){var T=h.srcFile.size;if("Content-Length"in h.headers||"PartSize"in h||"Offset"in h){var O=h.Offset;O=O&&O>=0&&O<T?O:0;var D=void 0;D=(D="PartSize"in h?h.PartSize:"Content-Length"in h.headers?parseInt(h.headers["Content-Length"]):T)&&D>0&&D<=T-O?D:T-O,h.PartSize=D,h.Offset=O,h.headers["Content-Length"]=String(h.PartSize)}}return h},b.prototype.parseCommonHeaders=function(e,t,r){for(var n in E)e.InterfaceResult[E[n]]=t[n];e.InterfaceResult.RequestId=t[r.headerPrefix+"request-id"],e.InterfaceResult.Id2=t[r.headerPrefix+"id-2"],e.CommonMsg.RequestId=e.InterfaceResult.RequestId,e.CommonMsg.Id2=e.InterfaceResult.Id2},b.prototype.contrustCommonMsg=function(e,t,r,n){e.InterfaceResult={},this.parseCommonHeaders(e,r,n);for(var o in t)if("header"===t[o].location){var a=t[o].sentAs||o;if(t[o].withPrefix&&(a=n.headerPrefix+a),"object"===t[o].type)e.InterfaceResult[o]=u(a,r);else{var i=null;a in r?i=r[a]:a.toLowerCase()in r&&(i=r[a.toLowerCase()]),null!==i&&(e.InterfaceResult[o]=i)}}},b.prototype.getRequest=function(e,t,r,n,o,a){var c=this.regionDomains,u={},p=this.log,m="obs"===r.signature?i[e+"Output"]:s[e+"Output"],y=(m=m||{}).parameters||{};u.CommonMsg={Status:t.status,Code:"",Message:"",HostId:"",RequestId:"",InterfaceResult:null};var g=t.headers,A=l(g);p.runLog("info",e,"get response start, statusCode:"+t.status),p.runLog("debug",e,"response msg :statusCode:"+t.status+", headers:"+A);var x=function(){var t="Status:"+u.CommonMsg.Status+", Code:"+u.CommonMsg.Code+", Message:"+u.CommonMsg.Message;p.runLog("debug",e,"exec interface "+e+" finish, "+t),a(null,u)};if(t.status>=300&&t.status<400&&304!==t.status&&n<=5){var b=g.location||g.Location;if(b){var w="http code is 3xx, need to redirect to "+b;p.runLog("warn",e,w);var v=new Error("redirect");return v.location=b,a(v)}var C=g["x-amz-bucket-region"]||g["x-obs-bucket-location"];if(C&&c[C]){var P=(this.isSecure?"https://":"http://")+c[C];d(this.setRequestHeaderHook)&&this.setRequestHeaderHook(g,o,e,c[C]);var S="get redirect code 3xx, need to redirect to"+P;p.runLog("error",e,S);var k=new Error("redirect");return k.location=P,a(k)}p.runLog("error",e,"get redirect code 3xx, but no location in headers")}if(t.status<300){var M=t.data;this.contrustCommonMsg(u,y,g,r);var E="Status: "+u.CommonMsg.Status+", headers: "+A;if(M&&(E+="body length: "+M.length,p.runLog("debug",e,"response body length:"+M.length)),p.runLog("debug",e,E),M&&"data"in m){if("xml"===m.data.type){var R=this;return f(M,function(t,r){if(t)return p.runLog("error",e,"change xml to json err ["+l(t)+"]"),a(t,null);var n=r;if(m.data.xmlRoot&&m.data.xmlRoot in n&&(n=r[m.data.xmlRoot]),h(n))for(var o in y)"xml"===y[o].location&&(u.InterfaceResult[o]=R.jsonToObject(y,n,o)[o]);x()})}if("body"===m.data.type)for(var B in y)if("body"===y[B].location){u.InterfaceResult[B]=M;break}}return x()}var I=t.data,T="Status: "+u.CommonMsg.Status+", headers: "+A;return""!==I&&(T+="body: "+I,p.runLog("debug",e,"response body :"+I)),u.CommonMsg.RequestId=g[r.headerPrefix+"request-id"],u.CommonMsg.Id2=g[r.headerPrefix+"id2"],u.CommonMsg.Indicator=g["x-reserved-indicator"],p.runLog("info",e,"request finished with request id:"+u.CommonMsg.RequestId),p.runLog("debug",e,T),I?f(I,function(t,r){if(t)p.runLog("error",e,"change xml to json err ["+l(t)+"]"),u.CommonMsg.Message=t.message;else if(r){if("Error"in r){var n=r.Error;"Code"in n&&(u.CommonMsg.Code=n.Code["#text"]),"Message"in n&&(u.CommonMsg.Message=n.Message["#text"]),"HostId"in n&&(u.CommonMsg.HostId=n.HostId["#text"]),"RequestId"in n&&n.RequestId["#text"]&&(u.CommonMsg.RequestId=n.RequestId["#text"])}else{var o=r;"code"in o&&(u.CommonMsg.Code=o.code),"message"in o&&(u.CommonMsg.Message=o.message),"hostId"in o&&(u.CommonMsg.HostId=o.hostId),"request_id"in o&&o.request_id&&(u.CommonMsg.RequestId=o.request_id)}p.runLog("error",e,"request error with error code:"+u.CommonMsg.Code+", error message:"+u.CommonMsg.Message+", request id:"+u.CommonMsg.RequestId)}x()}):x()},b.prototype.makeRequest=function(e,r,n,o){var a=this.log,c=(this.server,r.xml||null),u=r.signatureContext||this.signatureContext;if(delete r.headers.Authorization,"file"===r.dstFile){var p={};if(r.urlPath)for(var h=r.urlPath.slice(1).split("&"),f=0;f<h.length;f++)if(-1===h[f].indexOf("="))p[h[f]]="";else{var m=h[f].split("=");p[m[0]]=m[1]}var g=r.rawUri.split("/")[1],A=r.rawUri.slice(("/"+g+"/").length),x={};x.CommonMsg={Status:0,Code:"",Message:"",HostId:""},x.InterfaceResult={};var b=("obs"===u.signature?i[e+"Output"]:s[e+"Output"]).parameters;for(var w in b)if("body"===b[w].location){x.InterfaceResult[w]=this.createSignedUrlSync({Method:r.method,Bucket:g,Key:A,Expires:3600,Headers:r.headers,QueryParams:p,signatureContext:u});break}return o(null,x)}var v,C=r.$requestParam.RequestDate,P=Object.prototype.toString.call(C);if("[object Date]"===P)v=C;else if("[object String]"===P)try{(v=new Date).setTime(Date.parse(C))}catch(e){}v||(v=new Date);var S=v.toUTCString(),k="v4"===u.signature.toLowerCase();r.headers[u.headerPrefix+"date"]=k?y(S)[1]:S;var M=(r.requestUri?r.requestUri:r.uri)+r.urlPath;this.ak&&this.sk&&"HeadApiVersion"!==e&&(this.securityToken&&(r.headers[u.headerPrefix+"security-token"]=this.securityToken),k?this.v4Auth(r,e,u):this.doAuth(r,e,u));var E=r.headers;d(this.setRequestHeaderHook)&&this.setRequestHeaderHook(E,r.$requestParam,e);var R=E.Host,B=r.method,I={};for(var T in E)I[T]=E[T];I.Authorization="****";var O="method:"+B+", path:"+M+"headers:"+l(I);c&&(O+="body:"+c),a.runLog("info",e,"prepare request parameters ok,then Send request to service start"),a.runLog("debug",e,"request msg:"+O);var D=r.protocol?0===r.protocol.toLowerCase().indexOf("https"):this.isSecure,L=r.port||this.port;delete E.Host,delete E["Content-Length"];var q="text";!r.dstFile||"file"===r.dstFile||"arraybuffer"!==r.dstFile&&"blob"!==r.dstFile||(q=String(r.dstFile));var j=v.getTime(),U=this;if(this.useRawXhr){var K=null;try{K=new XMLHttpRequest}catch(e){try{K=new ActiveXObject("Msxml2.XMLHTTP")}catch(e){try{K=new ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}}if(null===K)return o(new Error("XHR is not available"),null);if(r.srcFile){if(!(r.srcFile instanceof window.File||r.srcFile instanceof window.Blob))return o(new Error("source file must be an instance of window.File or window.Blob"),null);try{var F=r.srcFile;if(r.Offset>=0&&r.PartSize>0)F=this.sliceBlob(F,r.Offset,r.Offset+r.PartSize);else if("ContentLength"in r){var _=parseInt(r.ContentLength);_>0&&(F=this.sliceBlob(F,0,_))}}catch(e){return o(e)}c=srcFile}K.open(B,(D?"https://"+this.urlPrefix+R:"http://"+this.urlPrefix+R)+M),K.withCredentials=!1;for(var H in E)K.setRequestHeader(H,E[H]);K.timeout=1e3*U.timeout,K.responseType=q,r.$requestParam.cancelHook=function(){K.abort()},K.onreadystatechange=function(){if(4===K.readyState&&K.status>=200){a.runLog("info",e,"http cost "+((new Date).getTime()-j)+" ms");for(var t=K.getAllResponseHeaders().trim().split(/[\r\n]+/),i={},s=0;s<t.length;s++){var c=t[s].split(": "),l=c.shift(),p=c.join(": ");i[l.toLowerCase()]=p}var d=K.response;d||""!==q&&"text"!==q||(d=K.responseText);var h={status:K.status,headers:i,data:d};U.getRequest(e,h,u,n,r.$requestParam,o)}};var N=!1,G=function(t){if(!N){N=!0;var r=l(t);a.runLog("error",e,"Send request to service error ["+r+"]"),a.runLog("info",e,"http cost "+((new Date).getTime()-j)+" ms"),o(t,null)}};K.ontimeout=function(){G(new Error("timeout of "+K.timeout+"ms exceed"))},K.onerror=function(){G(new Error("Network Error"))},K.onabort=function(){G(new Error("Cancel"))},K.upload&&(K.upload.ontimeout=function(){G(new Error("timeout of "+K.timeout+"ms exceed"))},K.upload.onerror=function(){G(new Error("Network Error"))},K.upload.onabort=function(e){G(new Error("Cancel"))}),d(r.ProgressCallback)&&("GET"!==B&&K.upload?"PUT"!==B&&"POST"!==B||(K.upload.onprogress=function(e){e.lengthComputable&&r.ProgressCallback(e.loaded,e.total,((new Date).getTime()-j)/1e3)}):K.onprogress=function(e){e.lengthComputable&&r.ProgressCallback(e.loaded,e.total,((new Date).getTime()-j)/1e3)}),K.send(c)}else{var V=null,z=null;if(d(r.ProgressCallback)){var Q=function(e){e.lengthComputable&&r.ProgressCallback(e.loaded,e.total,((new Date).getTime()-j)/1e3)};"GET"===B?z=Q:"PUT"!==B&&"POST"!==B||(V=Q)}var W=":"+L;R.indexOf(":")>=0&&(W="");var X={method:B,url:(D?"https://"+this.urlPrefix+R+W:"http://"+this.urlPrefix+R+W)+M,withCredentials:!1,headers:E,validateStatus:function(e){return e>=200},maxRedirects:0,responseType:q,data:c,timeout:1e3*this.timeout,onUploadProgress:V,onDownloadProgress:z,cancelToken:new t.CancelToken(function(e){r.$requestParam.cancelHook=e})};if(r.srcFile){if(!(r.srcFile instanceof window.File||r.srcFile instanceof window.Blob))return o(new Error("source file must be an instance of window.File or window.Blob"),null);var $=r.srcFile;try{if(r.Offset>=0&&r.PartSize>0)$=this.sliceBlob($,r.Offset,r.Offset+r.PartSize);else if("ContentLength"in r){var Y=parseInt(r.ContentLength);Y>0&&($=this.sliceBlob($,0,Y))}}catch(e){return o(e)}X.data=$,t.request(X).then(function(t){a.runLog("info",e,"http cost "+((new Date).getTime()-j)+" ms"),U.getRequest(e,t,u,n,r.$requestParam,o)}).catch(function(t){var r=l(t);a.runLog("error",e,"Send request to service error ["+r+"]"),a.runLog("info",e,"http cost "+((new Date).getTime()-j)+" ms"),o(t,null)})}else t.request(X).then(function(t){a.runLog("info",e,"http cost "+((new Date).getTime()-j)+" ms"),U.getRequest(e,t,u,n,r.$requestParam,o)}).catch(function(t){var r=l(t);a.runLog("error",e,"Send request to service error ["+r+"]"),a.runLog("info",e,"http cost "+((new Date).getTime()-j)+" ms"),o(t,null)})}},b.prototype.sendRequest=function(e,t,r,n){void 0===n&&(n=1);var o=this;o.makeRequest(e,t,n,function(a,i){if(a&&"redirect"===a.message){var s=v.parse(a.location);t.headers.Host=s.hostname,t.protocol=s.protocol,t.port=s.port||(t.protocol&&0===t.protocol.toLowerCase().indexOf("https")?443:80),o.sendRequest(e,t,r,n+1)}else r(a,i)})},b.prototype.doAuth=function(e,t,r){for(var n=["Content-MD5","Content-Type"],o=e.method+"\n",a=0;a<n.length;a++)n[a]in e.headers&&(o+=e.headers[n[a]]),o+="\n";r.headerPrefix+"date"in e.headers||(o+=e.headers.Date),o+="\n";var i=[];for(var s in e.headers){var c=s.toLowerCase();0===c.indexOf(r.headerPrefix)&&i.push({key:c,value:e.headers[s]})}i=i.sort(function(e,t){return e.key<t.key?-1:e.key>t.key?1:0});for(var l=0;l<i.length;l++){var u=i[l].key;o+=u+":"+(0===u.indexOf(r.headerMetaPrefix)?i[l].value.trim():i[l].value)+"\n"}var p=e.uri;if(this.isCname&&("/"===p?p+=e.headers.Host+"/":0===p.indexOf("/")&&(p="/"+e.headers.Host+p)),e.urlPath){for(var d=e.urlPath.slice(1).split("&").sort(),h="",f=0;f<d.length;f++){var m=d[f].split("="),y=decodeURIComponent(m[0]);k.indexOf(y.toLowerCase())>=0&&(h+=""===h?"?":"&",h+=y,2===m.length&&m[1]&&(h+="="+decodeURIComponent(m[1])))}p+=h}o+=p,this.log.runLog("debug",t,"stringToSign:"+o),e.headers.Authorization=r.authPrefix+" "+this.ak+":"+w.createHmac("sha1",this.sk).update(o).digest("base64")},b.prototype.v4Auth=function(e,t,r){e.headers[r.headerPrefix+"content-sha256"]=C;var n=e.headers,o=this.log,a=null,i=null;if(r.headerPrefix+"date"in n)a=(i=n[r.headerPrefix+"date"]).slice(0,i.indexOf("T"));else{var s=y(n.Date);a=s[0],i=s[1]}var c=this.ak+"/"+a+"/"+this.region+"/s3/aws4_request",l=g(n),u=l[0],p=l[1],d="";if(e.urlPath){var h=e.urlPath.slice(1).split("&");h=h.sort();for(var f=0;f<h.length;f++)d+=h[f],-1===h[f].indexOf("=")&&(d+="="),f!==h.length-1&&(d+="&")}var m=e.method+"\n";m+=e.uri+"\n",m+=d+"\n",m+=p+"\n",m+=u+"\n",m+=C,o.runLog("debug",t,"canonicalRequest:"+m);var A=x(a,i,this.sk,this.region,m);e.headers.Authorization="AWS4-HMAC-SHA256 Credential="+c+",SignedHeaders="+u+",Signature="+A},b.prototype.bufMD5=function(e){return w.createHash("md5").update(e).digest("base64")},b.prototype.rawBufMD5=function(e){return w.createHash("md5").update(e).digest("rawbase64")},b.prototype.createSignedUrlSync=function(e){return"v4"===(e.signatureContext||this.signatureContext).signature.toLowerCase()?this.createV4SignedUrlSync(e):this.createV2SignedUrlSync(e)},b.prototype.createV2SignedUrlSync=function(e){var t=(e=e||{}).signatureContext||this.signatureContext,r=e.Method?String(e.Method):"GET",n=e.Bucket?String(e.Bucket):null,o=e.Key?String(e.Key):null,a=e.SpecialParam?String(e.SpecialParam):null;"obs"===t.signature.toLowerCase()&&"storagePolicy"===a?a="storageClass":"v2"===t.signature.toLowerCase()&&"storageClass"===a&&(a="storagePolicy");var i=e.Expires?parseInt(e.Expires):300,s={};if(e.Headers&&e.Headers instanceof Object&&!(e.Headers instanceof Array))for(var l in e.Headers)s[l]=e.Headers[l];var u={};if(e.QueryParams&&e.QueryParams instanceof Object&&!(e.QueryParams instanceof Array))for(var p in e.QueryParams)u[p]=e.QueryParams[p];this.securityToken&&!u[t.headerPrefix+"security-token"]&&(u[t.headerPrefix+"security-token"]=this.securityToken);var d="",h="",f=this.server;this.isCname?h+="/"+f+"/":n&&(h+="/"+n,this.pathStyle?d+="/"+n:(f=n+"."+f,h+="/")),o&&(d+="/"+(o=c(o,"/")),h.lastIndexOf("/")!==h.length-1&&(h+="/"),h+=o),""===h&&(h="/"),d+="?",a&&(u[a]=""),"v2"===t.signature.toLowerCase()?u.AWSAccessKeyId=this.ak:u.AccessKeyId=this.ak,i<0&&(i=300),i=parseInt((new Date).getTime()/1e3)+i,u.Expires=String(i);var m={};for(var y in s){var g=String(y).toLowerCase();("content-type"===g||"content-md5"===g||g.length>t.headerPrefix.length&&g.slice(0,t.headerPrefix.length)===t.headerPrefix)&&(m[g]=s[y])}var A=[];for(var x in u)A.push(x);A.sort();for(var b=!1,v=[],C=0;C<A.length;C++){var P=A[C],S=u[P];if(P=c(P,"/"),S=c(S,"/"),d+=P,S&&(d+="="+S),k.indexOf(P.toLowerCase())>=0||0===P.toLowerCase().indexOf(t.headerPrefix)){b=!0;var M=S?P+"="+decodeURIComponent(S):P;v.push(M)}d+="&",0}v=v.join("&"),b&&(v="?"+v),h+=v;var E=[r];E.push("\n"),"content-md5"in m&&E.push(m["content-md5"]),E.push("\n"),"content-type"in m&&E.push(m["content-type"]),E.push("\n"),E.push(String(i)),E.push("\n");var R=[],B=0;for(var I in m)I.length>t.headerPrefix.length&&I.slice(0,t.headerPrefix.length)===t.headerPrefix&&(R[B++]=I);R=R.sort();for(var T=0;T<R.length;T++)E.push(R[T]),E.push(":"),E.push(m[R[T]]),E.push("\n");E.push(h);var O=w.createHmac("sha1",this.sk);return O.update(E.join("")),d+="Signature="+c(O.digest("base64"),"/"),{ActualSignedRequestHeaders:s,SignedUrl:(this.isSecure?"https":"http")+"://"+f+":"+this.port+d}},b.prototype.createV4SignedUrlSync=function(e){var t=(e=e||{}).signatureContext||this.signatureContext,r=e.Method?String(e.Method):"GET",n=e.Bucket?String(e.Bucket):null,o=e.Key?String(e.Key):null,a=e.SpecialParam?String(e.SpecialParam):null;"storageClass"===a&&(a="storagePolicy");var i=e.Expires?parseInt(e.Expires):300,s={};if(e.Headers&&e.Headers instanceof Object&&!(e.Headers instanceof Array))for(var l in e.Headers)s[l]=e.Headers[l];var u={};if(e.QueryParams&&e.QueryParams instanceof Object&&!(e.QueryParams instanceof Array))for(var p in e.QueryParams)u[p]=e.QueryParams[p];this.securityToken&&!u[t.headerPrefix+"security-token"]&&(u[t.headerPrefix+"security-token"]=this.securityToken);var d="",h="",f=this.server;n&&(this.pathStyle?(d+="/"+n,h+="/"+n):f=n+"."+f),o&&(d+="/"+(o=c(o,"/")),h+="/"+o),""===h&&(h="/"),d+="?",a&&(u[a]=""),i<0&&(i=300);var m=y(s.date||s.Date||(new Date).toUTCString()),A=m[0],b=m[1];s.Host=f+(80===this.port||443===this.port?"":":"+this.port),u["X-Amz-Algorithm"]="AWS4-HMAC-SHA256",u["X-Amz-Credential"]=this.ak+"/"+A+"/"+this.region+"/s3/aws4_request",u["X-Amz-Date"]=b,u["X-Amz-Expires"]=String(i);var w=g(s);u["X-Amz-SignedHeaders"]=w[0];var v={},C=[];for(var P in u){var S=u[P];P=c(P,"/"),S=c(S),v[P]=S,C.push(P),d+=P,S&&(d+="="+S),d+="&"}var k="";C.sort();for(var M=0;M<C.length;)k+=C[M]+"="+v[C[M]],++M!==C.length&&(k+="&");var E=r+"\n";return E+=h+"\n",E+=k+"\n",E+=w[1]+"\n",E+=w[0]+"\n",E+="UNSIGNED-PAYLOAD",d+="X-Amz-Signature="+c(x(A,b,this.sk,this.region,E)),{ActualSignedRequestHeaders:s,SignedUrl:(this.isSecure?"https":"http")+"://"+f+":"+this.port+d}},b.prototype.createPostSignatureSync=function(e){var t=e.signatureContext||this.signatureContext;if("v4"===t.signature)return this.createV4PostSignatureSync(e);var r=(e=e||{}).Bucket?String(e.Bucket):null,o=e.Key?String(e.Key):null,a=e.Expires?parseInt(e.Expires):300,i={};if(e.FormParams&&e.FormParams instanceof Object&&!(e.FormParams instanceof Array))for(var s in e.FormParams)i[s]=e.FormParams[s];this.securityToken&&!i[t.headerPrefix+"security-token"]&&(i[t.headerPrefix+"security-token"]=this.securityToken);var c=new Date;c.setTime(parseInt((new Date).getTime())+1e3*a),c=m(c.toUTCString()),r&&(i.bucket=r),o&&(i.key=o);var l=[];l.push('{"expiration":"'),l.push(c),l.push('", "conditions":[');var u=!0,p=!0,d=["acl","bucket","key","success_action_redirect","redirect","success_action_status"];for(var h in i)if(h){var f=i[h];"bucket"===(h=String(h).toLowerCase())?u=!1:"key"===h&&(p=!1),M.indexOf(h)<0&&d.indexOf(h)<0&&0!==h.indexOf(t.headerPrefix)||(l.push('{"'),l.push(h),l.push('":"'),l.push(null!==f?String(f):""),l.push('"},'))}u&&l.push('["starts-with", "$bucket", ""],'),p&&l.push('["starts-with", "$key", ""],'),l.push("]}");var y=l.join("");l=window.btoa?window.btoa(y):n.encode(y);var g=w.createHmac("sha1",this.sk).update(l).digest("base64");return{OriginPolicy:y,Policy:l,Signature:g,Token:this.ak+":"+g+":"+l}},b.prototype.createV4PostSignatureSync=function(e){var t=(e=e||{}).signatureContext||this.signatureContext,r=e.Bucket?String(e.Bucket):null,o=e.Key?String(e.Key):null,a=e.Expires?parseInt(e.Expires):300,i={};if(e.FormParams&&e.FormParams instanceof Object&&!(e.FormParams instanceof Array))for(var s in e.FormParams)i[s]=e.FormParams[s];this.securityToken&&!i[t.headerPrefix+"security-token"]&&(i[t.headerPrefix+"security-token"]=this.securityToken);var c=y((new Date).toUTCString()),l=c[0],u=c[1],p=this.ak+"/"+l+"/"+this.region+"/s3/aws4_request",d=new Date;d.setTime(parseInt((new Date).getTime())+1e3*a),d=m(d.toUTCString()),i["X-Amz-Algorithm"]="AWS4-HMAC-SHA256",i["X-Amz-Date"]=u,i["X-Amz-Credential"]=p,r&&(i.bucket=r),o&&(i.key=o);var h=[];h.push('{"expiration":"'),h.push(d),h.push('", "conditions":[');var f=!0,g=!0,x=["acl","bucket","key","success_action_redirect","redirect","success_action_status"];for(var b in i)if(b){var w=i[b];"bucket"===(b=String(b).toLowerCase())?f=!1:"key"===b&&(g=!1),M.indexOf(b)<0&&x.indexOf(b)<0&&0!==b.indexOf(t.headerPrefix)||(h.push('{"'),h.push(b),h.push('":"'),h.push(null!==w?String(w):""),h.push('"},'))}f&&h.push('["starts-with", "$bucket", ""],'),g&&h.push('["starts-with", "$key", ""],'),h.push("]}");var v=h.join("");h=window.btoa?window.btoa(v):n.encode(v);var C=A(l,this.sk,this.region,h);return{OriginPolicy:v,Policy:h,Algorithm:i["X-Amz-Algorithm"],Credential:i["X-Amz-Credential"],Date:i["X-Amz-Date"],Signature:C}},b}),function(e,t){"function"==typeof define&&define.amd?define("enums",[],t):e.enums=t()}(window,function(){var e={};return e.AclPrivate="private",e.AclPublicRead="public-read",e.AclPublicReadWrite="public-read-write",e.AclPublicReadDelivered="public-read-delivered",e.AclPublicReadWriteDelivered="public-read-write-delivered",e.AclAuthenticatedRead="authenticated-read",e.AclBucketOwnerRead="bucket-owner-read",e.AclBucketOwnerFullControl="bucket-owner-full-control",e.AclLogDeliveryWrite="log-delivery-write",e.StorageClassStandard="STANDARD",e.StorageClassWarm="WARM",e.StorageClassCold="COLD",e.PermissionRead="READ",e.PermissionWrite="WRITE",e.PermissionReadAcp="READ_ACP",e.PermissionWriteAcp="WRITE_ACP",e.PermissionFullControl="FULL_CONTROL",e.GroupAllUsers="AllUsers",e.GroupAuthenticatedUsers="AuthenticatedUsers",e.GroupLogDelivery="LogDelivery",e.RestoreTierExpedited="Expedited",e.RestoreTierStandard="Standard",e.RestoreTierBulk="Bulk",e.GranteeGroup="Group",e.GranteeUser="CanonicalUser",e.CopyMetadata="COPY",e.ReplaceMetadata="REPLACE",e.EventObjectCreatedAll="ObjectCreated:*",e.EventObjectCreatedPut="ObjectCreated:Put",e.EventObjectCreatedPost="ObjectCreated:Post",e.EventObjectCreatedCopy="ObjectCreated:Copy",e.EventObjectCreatedCompleteMultipartUpload="ObjectCreated:CompleteMultipartUpload",e.EventObjectRemovedAll="ObjectRemoved:*",e.EventObjectRemovedDelete="ObjectRemoved:Delete",e.EventObjectRemovedDeleteMarkerCreated="ObjectRemoved:DeleteMarkerCreated",e}),function(e,t){"function"==typeof define&&define.amd?define("posix",[],t):e.posix=t()}(window,function(){var e=function(e,t,r){e=e||function(){};var n=(new Date).getTime();return function(o,a){return t.runLog("info",r,"ObsClient cost "+((new Date).getTime()-n)+" ms"),"[object String]"===Object.prototype.toString.call(o)?e(new Error(o),a):e(o,a)}},t=function(e){return e=e||function(){},function(t,r,n){if("[object Error]"===Object.prototype.toString.call(n))return e(t,r,n);if("[object String]"===Object.prototype.toString.call(n))return e(t,r,new Error(n));if(n)return n.CommonMsg.Status>300?e(t,r,new Error("status:"+n.CommonMsg.Status+", code:"+n.CommonMsg.Code+", message:"+n.CommonMsg.Message)):void e(t,r,n)}},r=function(e){return"[object String]"===Object.prototype.toString.call(e)&&e.lastIndexOf("/")!==e.length-1&&(e+="/"),e},n={};return n.extend=function(n){n.prototype.dropFile=function(e,t){this.deleteObject(e,t)},n.prototype.dropFolder=function(n,o){var a=this;n=n||{};var i=e(o,a.log,"dropFolder"),s=t(n.EventCallback),c=n.TaskNum||1,l=0,u=[],p=function(){for(;l<c&&u.length>0;)u.shift()()},d=function(e,t,r,n){if(n)return t.finished++,void r(t);var o=function(){l++,a.dropFile({Bucket:t.bucket,Key:e},function(n,o){l--,t.finished++,p(),n?(s("dropFileFailed",e,n),t.subDeleted=!1):o.CommonMsg.Status>=300?(s("dropFileFailed",e,o),t.subDeleted=!1):s("dropFileSucceed",e,o),r(t)})};l<c?o():u.push(o)},h=function(e,t,r){return function(n){!n.isTruncated&&n.finished===n.total&&n.subDeleted&&d(e,t,r,!1)}},f=r(n.Prefix);!function e(t,n,o,s,p){l++,a.listObjects({Bucket:n,Prefix:o,Delimiter:"/",Marker:s},function(a,s){if(l--,a)return i(a);if(s.CommonMsg.Status>=300)return i(null,s);if(t.total+=s.InterfaceResult.Contents.length,t.total+=s.InterfaceResult.CommonPrefixes.length,0!==t.total){t.isTruncated="true"===s.InterfaceResult.IsTruncated;for(var f=0;f<s.InterfaceResult.CommonPrefixes.length;f++){var m=r(s.InterfaceResult.CommonPrefixes[f].Prefix);l<c?e({total:0,finished:0,isTruncated:!1,bucket:n,subDeleted:!0},n,m,null,h(m,t,p)):u.push(function(r){return function(){e({total:0,finished:0,isTruncated:!1,bucket:n,subDeleted:!0},n,r,null,h(r,t,p))}}(m))}for(var y=0;y<s.InterfaceResult.Contents.length;y++){var g=s.InterfaceResult.Contents[y].Key;d(g,t,p,g.lastIndexOf("/")===g.length-1)}t.isTruncated&&(l<c?e(t,n,o,s.InterfaceResult.NextMarker,p):u.push(function(){e(t,n,o,s.InterfaceResult.NextMarker,p)}))}else p(t)})}({total:0,finished:0,isTruncated:!1,bucket:n.Bucket,subDeleted:!0},n.Bucket,f,null,function(e){if(!e.isTruncated&&e.finished===e.total)if(e.subDeleted)a.dropFile({Bucket:e.bucket,Key:f},function(e,t){return e?(s("dropFileFailed",f,e),i(e)):t.CommonMsg.Status>=300?(s("dropFileFailed",f,t),i(null,t)):(s("dropFileSucceed",f,t),i(null,t))});else{var t="drop folder "+f+" failed due to child file deletion failed";s("dropFileFailed",f,new Error(t)),i(t)}})}},n}),function(e,t){"function"==typeof define&&define.amd?define("resumable",["md5","Base64"],t):e.resumable=t(e.md5,e.Base64)}(window,function(e,t){var r=function(e,t,r){e=e||function(){};var n=(new Date).getTime();return function(o,a){return t.runLog("info",r,"ObsClient cost "+((new Date).getTime()-n)+" ms"),"[object String]"===Object.prototype.toString.call(o)?e(new Error(o),a):e(o,a)}},n=function(e){return"[object Function]"===Object.prototype.toString.call(e)},o=function(e){return e=e||function(){},function(t,r,n){if("[object Error]"===Object.prototype.toString.call(n))return e(t,r,n);if("[object String]"===Object.prototype.toString.call(n))return e(t,r,new Error(n));if(n)return n.CommonMsg.Status>300?e(t,r,new Error("status:"+n.CommonMsg.Status+", code:"+n.CommonMsg.Code+", message:"+n.CommonMsg.Message)):void e(t,r,n)}},a=function(t){var r=[];if(r.push(t.bucket),r.push(t.key),r.push(t.sourceFile.name),r.push(String(t.partSize)),r.push(String(t.partCount)),r.push(String(t.fileStat.fileSize)),r.push(String(t.fileStat.lastModified)),t.uploadId&&r.push(t.uploadId),t.sseC&&r.push(t.sseC),t.sseCKey&&r.push(t.sseCKey),t.parts)for(var n=0;n<t.parts.length;n++){var o=t.parts[n];o&&(r.push(String(o.partNumber)),r.push(String(o.offset)),r.push(String(o.partSize)),r.push(String(o.isCompleted)),o.etag&&r.push(String(o.etag)))}return window.btoa(e.MD5(r.join(""),!1,!0))},i=function(e,t,r){e&&e.uploadId&&r.abortMultipartUpload({Bucket:e.bucket,Key:e.key,RequestDate:e.requestDate,UploadId:e.uploadId},function(n,o){n?r.log.runLog("warn",t,"abort multipart upload failed, bucket:"+e.bucket+", key:"+e.key+", uploadId:"+e.uploadId+", err:"+n):o.CommonMsg.Status>=300?r.log.runLog("warn",t,"abort multipart upload failed, bucket:"+e.bucket+", key:"+e.key+", uploadId:"+e.uploadId+", status:"+o.CommonMsg.Status+", code:"+o.CommonMsg.Code+", message:"+o.CommonMsg.Message):(delete e.uploadId,r.log.runLog("warn",t,"abort multipart upload succeed, bucket:"+e.bucket+", key:"+e.key+", uploadId:"+e.uploadId))})},s=function(e){if(!(e.finishedCount<e.uploadCheckpoint.partCount)){if(e.isAbort)return i(e.uploadCheckpoint,e.funcName,e.that),e.callback("uploadFile failed the upload task is aborted");if(e.isSuspend)return e.callback("the process of uploadFile is suspened, you can retry with the uploadCheckpoint");if(e.hasError)return e.callback("uploadFile finished with error, you can retry with the uploadCheckpoint");for(var t=[],r=0;r<e.uploadCheckpoint.partCount;r++){var n=e.uploadCheckpoint.parts[r];t.push({PartNumber:n.partNumber,ETag:n.etag})}e.that.completeMultipartUpload({Bucket:e.uploadCheckpoint.bucket,Key:e.uploadCheckpoint.key,RequestDate:e.uploadCheckpoint.requestDate,UploadId:e.uploadCheckpoint.uploadId,Parts:t},function(t,r){var n={bucket:e.uploadCheckpoint.bucket,key:e.uploadCheckpoint.key,uploadId:e.uploadCheckpoint.uploadId};return t?(e.eventCallback("completeMultipartUploadFailed",n,t),e.callback(t)):r.CommonMsg.Status>=500?(e.eventCallback("completeMultipartUploadFailed",n,r),e.callback(null,r)):r.CommonMsg.Status>=300&&r.CommonMsg.Status<500?(e.eventCallback("completeMultipartUploadAborted",n,r),i(e.uploadCheckpoint,e.funcName,e.that),e.callback(null,r)):(e.eventCallback("completeMultipartUploadSucceed",n,r),void e.callback(null,r))})}},c=function(r){r.resumeCallback({cancel:function(){r.isSuspend=!0;for(var e=0;e<r.uploadPartParams.length;e++){var t=r.uploadPartParams[e].cancelHook;n(t)&&t()}}},r.uploadCheckpoint);var o=[],i=function(){for(;r.runningTask<r.taskNum&&o.length>0;)o.shift()();0===o.length&&s(r)},c=function(e){return function(t,n,o){r.progressCallback(e,t)}},l=function(e,t,r,n){return n=n||e.type,e.mozSlice?e.mozSlice(t,r,n):e.webkitSlice?e.webkitSlice(t,r,n):e.slice(t,r,n)},u=window.btoa?window.btoa:t.encode,p=function(e){e=new Uint8Array(e);for(var t=0,r=void 0;t<e.length;){var n=t+16384;n=n<=e.length?n:e.length,r?r+=String.fromCharCode.apply(null,e.slice(t,n)):r=String.fromCharCode.apply(null,e.slice(t,n)),t=n}return e=null,r};if(!r.isSuspend){for(var d=0;d<r.uploadCheckpoint.partCount;d++){var h=r.uploadCheckpoint.parts[d];h.isCompleted?(r.finishedCount++,r.finishedBytes+=h.partSize):o.push(function(t){return function(){if(r.runningTask++,r.isSuspend||r.isAbort)return r.runningTask--,r.finishedCount++,r.finishedCount+=o.length,o=[],i();var n=0,s=function(e){if(!n){n=1;var o={Bucket:r.uploadCheckpoint.bucket,Key:r.uploadCheckpoint.key,RequestDate:r.uploadCheckpoint.requestDate,PartNumber:t.partNumber,UploadId:r.uploadCheckpoint.uploadId,SourceFile:r.uploadCheckpoint.sourceFile,Offset:t.offset,PartSize:t.partSize,SseC:r.uploadCheckpoint.sseC,SseCKey:r.uploadCheckpoint.sseCKey,ProgressCallback:c(t.partNumber),ContentMD5:e};r.uploadPartParams.push(o),r.that.uploadPart(o,function(e,n){if(r.runningTask--,r.finishedCount++,r.isSuspend)return i();var o={partNumber:t.partNumber,bucket:r.uploadCheckpoint.bucket,key:r.uploadCheckpoint.key,uploadId:r.uploadCheckpoint.uploadId};e?(r.eventCallback("uploadPartFailed",o,e),r.hasError=!0):n.CommonMsg.Status>=500||400===n.CommonMsg.Status&&"BadDigest"===n.CommonMsg.Code?(r.eventCallback("uploadPartFailed",o,n),r.hasError=!0):n.CommonMsg.Status>=300&&n.CommonMsg.Status<500?(r.isAbort=!0,r.hasError=!0,r.eventCallback("uploadPartAborted",o,n)):(t.etag=n.InterfaceResult.ETag,t.isCompleted=!0,o.etag=t.etag,r.uploadCheckpoint.md5=a(r.uploadCheckpoint),r.eventCallback("uploadPartSucceed",o,n),r.that.log.runLog("debug",r.funcName,"Part "+String(t.partNumber)+" is finished, uploadId "+r.uploadCheckpoint.uploadId)),i()})}};if(r.verifyMd5&&window.FileReader&&(r.uploadCheckpoint.sourceFile instanceof window.File||r.uploadCheckpoint.sourceFile instanceof window.Blob)){var d=l(r.uploadCheckpoint.sourceFile,t.offset,t.offset+t.partSize),h=new window.FileReader;return h.onload=function(t){var r=p(t.target.result),n=u(e.RawMD5(r,!1,!0));r=null,s(n)},h.onerror=function(e){r.that.log.runLog("error",r.funcName,"Caculate md5 for part "+String(t.partNumber)+" failed"),s()},void h.readAsArrayBuffer(d)}s()}}(h))}return 0===o.length?s(r):i()}r.callback("the process of uploadFile is suspened, you can retry with the uploadCheckpoint")},l={};return l.extend=function(e){e.prototype.uploadFile=function(e,t){var n=this;e=e||{};var s="uploadFile",l=r(t,n.log,s),u=o(e.EventCallback),p=e.TaskNum||1,d=e.ProgressCallback||function(){},h=e.ResumeCallback||function(){},f=e.VerifyMd5||!1;n.log.runLog("info",s,"enter uploadFile...");var m=null;if(e.UploadCheckpoint&&e.UploadCheckpoint.sourceFile&&e.UploadCheckpoint.fileStat&&e.UploadCheckpoint.uploadId&&e.UploadCheckpoint.md5===a(e.UploadCheckpoint)?m=e.UploadCheckpoint:i(e.UploadCheckpoint,s,n),m){var y=m.sourceFile;if(!(y instanceof window.File||y instanceof window.Blob))return l("source file is not valid, must be an instanceof [File | Blob]");if(!y.mozSlice&&!y.webkitSlice&&!y.slice)return l("your browser cannot support the slice method for [File | Blob]")}else{var g=e.SourceFile;if(!(g instanceof window.File||g instanceof window.Blob))return l("source file is not valid, must be an instanceof [File | Blob]");if(!g.mozSlice&&!g.webkitSlice&&!g.slice)return l("your browser cannot support the slice method for [File | Blob]");n.log.runLog("debug",s,"Begin to uploadFile to OBS from file:"+g.name);var A=g.size,x=parseInt(e.PartSize),b=0,w=[];if(0===A)x=0,b=1,w.push({partNumber:1,offset:0,partSize:0,isCompleted:!1});else{if(x=isNaN(x)?9437184:x<9437184?9437184:x>5368709120?5368709120:x,(b=Math.floor(A/x))>=1e4&&(x=Math.floor(A/1e4),A%1e4!=0&&(x+=1),b=Math.floor(A/x)),x>5368709120)return l("The source file "+g.name+" is too large");var v=A%x;0!==v&&b++;for(var C=0;C<b;C++)w.push({partNumber:C+1,offset:C*x,partSize:x,isCompleted:!1});0!==v&&(w[b-1].partSize=v)}n.log.runLog("debug",s,"Total parts count "+b),(m={bucket:e.Bucket,key:e.Key,sourceFile:g,partSize:x,partCount:b,parts:w}).fileStat={fileSize:A,lastModified:g.lastModified},e.SseC&&e.SseCKey&&(m.sseC=e.SseC,m.sseCKey=e.SseCKey),m.md5=a(m)}m.requestDate=e.RequestDate;var P={start:(new Date).getTime(),uploadCheckpoint:m,funcName:s,taskNum:p,callback:l,that:n,runningTask:0,finishedCount:0,hasError:!1,finishedBytes:0,isAbort:!1,resumeCallback:h,isSuspend:!1,partsLoaded:{},requestDate:e.RequestDate,uploadPartParams:[],verifyMd5:f};if(P.eventCallback=function(e,t,r){P.isSuspend||u(e,t,r)},P.progressCallback=function(e,t){P.isSuspend||(P.finishedBytes+=t,P.partsLoaded[e]&&(P.finishedBytes-=P.partsLoaded[e]),P.partsLoaded[e]=t,d(P.finishedBytes,P.uploadCheckpoint.fileStat.fileSize,((new Date).getTime()-P.start)/1e3))},!m.uploadId){var S=e.ContentType;return!S&&m.key&&(S=n.util.mimeTypes[m.key.substring(m.key.lastIndexOf(".")+1)]),!S&&m.sourceFile.name&&(S=n.util.mimeTypes[m.sourceFile.name.substring(m.sourceFile.name.lastIndexOf(".")+1)]),void n.initiateMultipartUpload({Bucket:e.Bucket,Key:e.Key,RequestDate:e.RequestDate,ACL:e.ACL,Metadata:e.Metadata,WebsiteRedirectLocation:e.WebsiteRedirectLocation,StorageClass:e.StorageClass,ContentType:S,Expires:e.Expires,SseKms:e.SseKms,SseKmsKey:e.SseKmsKey,SseC:e.SseC,SseCKey:e.SseCKey},function(t,r){var o={bucket:e.Bucket,key:e.Key};if(t)return P.eventCallback("initiateMultipartUploadFailed",o,t),l(t);if(r.CommonMsg.Status>=300)return P.eventCallback("initiateMultipartUploadFailed",o,r),l(null,r);var i=r.InterfaceResult.UploadId;m.uploadId=i,m.md5=a(m),P.uploadCheckpoint=m,o.uploadId=i,n.log.runLog("info",s,"Claim a new upload id "+i),P.eventCallback("initiateMultipartUploadSucceed",o,r),c(P)})}c(P)}},l}),function(e,t){"function"==typeof define&&define.amd?define("ObsClient",["utils","log","enums","posix","resumable"],t):(e.obs=t(e.utils,e.log,e.enums,e.posix,e.resumable),e.ObsClient=e.obs)}(window,function(e,t,r,n,o){function a(e){this.factory(e)}function i(e){return e.slice(0,1).toUpperCase()+e.slice(1)}function s(e){return"[object Function]"===Object.prototype.toString.call(e)}for(var c=["createBucket","listBuckets","getBucketMetadata","headBucket","deleteBucket","setBucketQuota","getBucketQuota","getBucketStorageInfo","setBucketPolicy","getBucketPolicy","deleteBucketPolicy","setBucketVersioningConfiguration","getBucketVersioningConfiguration","getBucketLocation","listVersions","listObjects","setBucketLifecycleConfiguration","getBucketLifecycleConfiguration","deleteBucketLifecycleConfiguration","setBucketAcl","getBucketAcl","setBucketLoggingConfiguration","getBucketLoggingConfiguration","setBucketWebsiteConfiguration","getBucketWebsiteConfiguration","deleteBucketWebsiteConfiguration","setBucketNotification","getBucketNotification","setBucketTagging","getBucketTagging","deleteBucketTagging","getBucketCors","deleteBucketCors","setBucketStoragePolicy","getBucketStoragePolicy","getObject","getObjectMetadata","setObjectMetadata","setObjectAcl","getObjectAcl","deleteObject","deleteObjects","listMultipartUploads","listParts","abortMultipartUpload","completeMultipartUpload","getBucketInventory","setBucketInventory","deleteBucketInventory","getBucketEncryption","setBucketEncryption","deleteBucketEncryption"],l=0;l<c.length;l++){var u=c[l];a.prototype[u]=function(e){return function(t,r){this.exec(i(e),t,r)}}(u)}if(a.prototype.getBucketDisPolicy=function(e,t){e.ApiPath="v1/dis_policies",e.OEFMarker="yes",this.exec("GetBucketDisPolicy",e,t)},a.prototype.setBucketDisPolicy=function(e,t){e.ApiPath="v1/dis_policies",e.OEFMarker="yes",this.exec("SetBucketDisPolicy",e,t)},a.prototype.deleteBucketDisPolicy=function(e,t){e.ApiPath="v1/dis_policies",e.OEFMarker="yes",this.exec("DeleteBucketDisPolicy",e,t)},a.prototype.putObject=function(e,t){if("Body"in e&&"SourceFile"in e){var r="the input body and sourcefile exist at same time,please specify one of eigther a string or file to be send!";return this.log.runLog("error","PutObject",r),t(new Error(r),null)}if(!("ContentType"in e)&&("Key"in e&&(e.ContentType=this.util.mimeTypes[e.Key.substring(e.Key.lastIndexOf(".")+1)]),!e.ContentType&&"SourceFile"in e)){var n=e.SourceFile.name;e.ContentType=this.util.mimeTypes[n.substring(n.lastIndexOf(".")+1)]}this.exec("PutObject",e,t)},a.prototype.appendObject=function(e,t){if("Body"in e&&"SourceFile"in e){var r="the input body and sourcefile exist at same time,please specify one of eigther a string or file to be send!";return this.log.isLevelEnabled("error")&&this.log.runLog("error","PutObject",r),t(new Error(r),null)}"ContentType"in e||("Key"in e&&(e.ContentType=this.util.mimeTypes[e.Key.substring(e.Key.lastIndexOf(".")+1)]),!e.ContentType&&"SourceFile"in e&&(e.ContentType=this.util.mimeTypes[e.SourceFile.substring(e.SourceFile.lastIndexOf(".")+1)])),this.exec("AppendObject",e,t)},a.prototype.copyObject=function(e,t){var r="CopySource";if(r in e){var n=e[r],o=n.lastIndexOf("?versionId=");e[r]=o>0?this.util.encodeURIWithSafe(n.slice(0,o))+n.slice(o):this.util.encodeURIWithSafe(n)}this.exec("CopyObject",e,t)},a.prototype.copyPart=function(e,t){var r="CopySource";if(r in e){var n=e[r],o=n.lastIndexOf("?versionId=");e[r]=o>0?this.util.encodeURIWithSafe(n.slice(0,o))+n.slice(o):this.util.encodeURIWithSafe(n)}this.exec("CopyPart",e,t)},a.prototype.restoreObject=function(e,t){this.exec("RestoreObject",e,function(e,r){!e&&r.InterfaceResult&&r.CommonMsg.Status<300&&(r.InterfaceResult.RestoreStatus=200===r.CommonMsg.Status?"AVALIABLE":"INPROGRESS"),t(e,r)})},a.prototype.initiateMultipartUpload=function(e,t){"ContentType"in e||"Key"in e&&(e.ContentType=this.util.mimeTypes[e.Key.substring(e.Key.lastIndexOf(".")+1)]),this.exec("InitiateMultipartUpload",e,t)},a.prototype.uploadPart=function(e,t){if("Body"in e&&"SourceFile"in e){var r="the input body and sourcefile exist at same time, please specify one of eigther a string or file to be send!";return this.log.runLog("error","UploadPart",r),t(new Error(r),null)}this.exec("UploadPart",e,t)},n.extend(a),o.extend(a),s(Promise))for(var p in a.prototype){var d=a.prototype[p];a.prototype[p]=function(e){return function(t,r){if(s(t))e.call(this,null,t);else{if(!s(r)){var n=this;return new Promise(function(r,o){e.call(n,t,function(e,t){if(e)return o(e);r(t)})})}e.call(this,t,r)}}}(d)}a.prototype.exec=function(e,t,r){var n=this.log;n.runLog("info",e,"enter "+e+"...");var o=(new Date).getTime();t=t||{},r=r||function(){};this.util.exec(e,t,function t(a,i){t.$called||(t.$called=!0,!a||a instanceof Error||(a=new Error(a)),n.runLog("debug",e,"ObsClient cost "+((new Date).getTime()-o)+" ms"),r(a,i))})},a.prototype.initLog=function(e){e=e||{},this.log.setLevel(e.level);var t=["[OBS SDK Version="+this.util.obsSdkVersion];if(this.util.server){var r=this.util.port?":"+this.util.port:"";t.push("Endpoint="+(this.util.is_secure?"https":"http")+"://"+this.util.server+r)}t.push("Access Mode="+(this.util.path_style?"Path":"Virtual Hosting")+"]"),this.log.runLog("warn","init",t.join("];["))},a.prototype.factory=function(r){this.log=new t,this.util=new e(this.log),r=r||{},this.util.initFactory(r.access_key_id,r.secret_access_key,r.is_secure,r.server,r.path_style,r.signature,r.region,r.port,r.timeout,r.security_token,r.is_signature_negotiation,r.is_cname,r.url_prefix,r.region_domains,r.setRequestHeaderHook,r.useRawXhr)},a.prototype.refresh=function(e,t,r){this.util.refresh(e,t,r)},a.prototype.createSignedUrlSync=function(e){return this.util.createSignedUrlSync(e)},a.prototype.createV2SignedUrlSync=function(e){return this.util.createV2SignedUrlSync(e)},a.prototype.createV4SignedUrlSync=function(e){return this.util.createV4SignedUrlSync(e)},a.prototype.createPostSignatureSync=function(e){return this.util.createPostSignatureSync(e)},a.prototype.createV4PostSignatureSync=function(e){return this.util.createV4PostSignatureSync(e)},a.prototype.enums=r;for(var h in a.prototype)a.prototype[i(h)]=a.prototype[h];for(var f in a.prototype){var m=f.indexOf("Configuration");m>0&&m+"Configuration".length===f.length&&(a.prototype[f.slice(0,m)]=a.prototype[f])}return a});