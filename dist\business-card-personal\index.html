<!doctype html>
<html lang="ch">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>资海云-云名片-个人版</title>
    <meta name="description" content="资海云-云名片-个人版">
    <meta name="keywords" content="资海云-云名片-个人版">
    <link rel="icon" href="https://www.china9.cn/static/logo.png" type="image/x-icon">
    <link rel="stylesheet" href="./css/animate.min.css" type="text/css">
    <link rel="stylesheet" href="./css/animate.min.css">
    <link rel="stylesheet" href="./css/index.css" type="text/css">
    <script src="./js/jquery.min.js"></script>
    <script src="./js/jweixin-1.2.0.js"></script>
    <script src="./js/weixinapi.min.js"></script>
    <script>
        var environment = "pro";
    </script>
    <style>
        #zhyHomeHeader .el-menu--horizontal > .el-menu-item{
            height: 70px;
            line-height: 70px;
            display: flex!important;
            align-items: center;
        }
        #zhyFooter .container{
            width: 1320px;
            min-width: 1320px;
            margin: auto;
        }
    </style>
    <script type="text/javascript"
            src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/js-cookie.js?v=1227"></script>
    <script src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/homeHeader.js?v=03"></script>
    <script src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/footer.js?v=1228"></script>
</head>
<body>
<div id="zhyHomeHeader"></div>
<div class="banner business-card-animate">
    <div class="container">
        <!-- 版本 -->
        <div class="version-selector default-button ani" data-animation="fadeInUp">云名片-个人版</div>
        <h1 class="ani animate__delay-1s pc" data-animation="fadeInUp">云端存储不掉队，人脉裂变嗨翻天</h1>
        <h1 class="ani animate__delay-1s phone" data-animation="fadeInUp">云端存储不掉队<br>人脉裂变嗨翻天</h1>
        <h2 class="describe ani animate__delay-2s pc" data-animation="fadeInUp">云名片 如此简单</h2>
        <div class="button primary-button ani animate__delay-3s" data-animation="fadeInUp">立即咨询</div>
    </div>
</div>
<div class="what-we-can-do radio-left-top business-card-animate">
    <div class="container clear-float">
        <div class="left">
            <p class="title title-blue ani" data-animation="fadeInUp">我们能做什么</p>
            <p class="title-en title-blue ani" data-animation="fadeInUp">What can we do</p>
            <p class="advance ani animate__delay-1s" data-animation="fadeInUp">依托微信小程序，名片信息即时更新</p>
            <p class="advance ani animate__delay-2s" data-animation="fadeInUp">扫码轻松交换名片</p>
            <div class="circle ani card-phone" data-animation="headShake">
                <p>通过微信社交，实现</p>
                <p>人脉裂变</p>
            </div>
            <p class="text intro-text ani animate__delay-3s" data-animation="fadeInUp">
                资海云云名片是一款存储于云端的电子名片，依托微信小程序，名片信息即时更新，用户端始终留存最新状态，让人脉关系不丢失，联系信息一键保存至通讯录；扫码轻松交换名片，通过微信社交实现人脉裂变，将公域流量和社交流量转化为精准客户，实现手机与电脑同步共享人脉信息；支持企业品牌展示及企业认证，更易获得客户信赖，促进项目签约合作更快达成。
            </p>
            <div>
                <div class="button default-button ani animate__delay-4s" data-animation="fadeInUp">立即咨询</div>
            </div>
        </div>
        <div class="right">
            <div class="circle ani" data-animation="headShake">
                <p>通过微信社交，实现</p>
                <p>人脉裂变</p>
            </div>
        </div>
    </div>
</div>
<div class="save-to-address-book radio-right-top business-card-animate">
    <div class="container clear-float">
        <div class="left">
            <p class="title title-blue ani save-to-address-book-pc" data-animation="flipInX">
                联系信息一键保存至通讯录，扫码轻松交换名片 <br>
                人脉关系不丢失
            </p>
            <div class="title title-blue ani save-to-address-book-phone" data-animation="flipInX">
                <p class="title-blue">
                    联系信息一键保存至通讯录 <br>
                    扫码轻松交换名片
                </p>
                <p class="title-blue">人脉关系不丢失</p>
            </div>
            <p class="title-en title-blue ani animate__delay-1s" data-animation="flipInX">No loss of contacts</p>
            <div class="circle ani animate__delay-2s" data-animation="rotateInUpLeft"></div>
        </div>
        <img src="./img/img1.png" alt="" class="right ani animate__delay-3s" data-animation="bounceIn">
    </div>
</div>
<div class="core-functions radio-left-top business-card-animate">
    <div class="container">
        <div class="title title-white ani" data-animation="flipInX">我们的核心功能</div>
        <div class="title-en title-white ani animate__delay-1s" data-animation="flipInX">Core functions</div>
        <div class="tab-box ani animate__delay-2s" data-animation="bounceIn">
            <div class="tab-wrap">
                <ul class="tab ani animate__delay-3s" data-animation="shakeXBig">
                    <li class="active">
                        <img src="./img/img3.png" alt="">
                        <p>名片同步</p>
                    </li>
                    <li>
                        <img src="./img/img4.png" alt="">
                        <p>切换名片</p>
                    </li>
                    <li>
                        <img src="./img/img5.png" alt="">
                        <p>名片分享及回赠</p>
                    </li>
                    <li>
                        <img src="./img/img6.png" alt="">
                        <p>名片节日祝福</p>
                    </li>
                    <li>
                        <img src="./img/img7.png" alt="">
                        <p>名片夹</p>
                    </li>
                    <li>
                        <img src="./img/img8.png" alt="">
                        <p>发票及银行账户管理</p>
                    </li>
                    <li>
                        <img src="./img/img9.png" alt="">
                        <p>名片日签</p>
                    </li>
                </ul>
            </div>
            <ul class="tab-panel">
                <li class="active ani-click" data-animation="pulse">
                    <img src="./img/img2.png" alt="">
                    <img src="./img/img10.png" alt="">
                </li>
                <li class="ani-click" data-animation="pulse">
                    <img src="./img/img16.png" alt="">
                    <img src="./img/img10.png" alt="">
                </li>
                <li class="ani-click" data-animation="pulse">
                    <img src="./img/img17.png" alt="">
                    <img src="./img/img10.png" alt="">
                </li>
                <li class="ani-click" data-animation="pulse">
                    <img src="./img/img18.png" alt="">
                    <img src="./img/img10.png" alt="">
                </li>
                <li class="ani-click" data-animation="pulse">
                    <img src="./img/img19.png" alt="">
                    <img src="./img/img10.png" alt="">
                </li>
                <li class="ani-click" data-animation="pulse">
                    <img src="./img/img20.png" alt="">
                    <img src="./img/img10.png" alt="">
                </li>
                <li class="ani-click" data-animation="pulse">
                    <img src="./img/img21.png" alt="">
                    <img src="./img/img10.png" alt="">
                </li>
            </ul>
        </div>
    </div>
</div>
<div class="advantage radio-right-top business-card-animate">
    <div class="container">
        <div class="title title-blue ani" data-animation="slideInLeft">我们的优势</div>
        <div class="title-en title-blue ani animate__delay-1s" data-animation="slideInLeft">advantage</div>
        <ul class="clear-float clear-float">
            <li class="ani animate__delay-1s" data-animation="headShake">
                <img src="./img/img11.png" alt="">
                <p class="title-advantage">信息云端管理</p>
                <div class="line"></div>
                <p class="intro-text">
                    名片信息云端存储，云名片始终为最新状态，人脉关系永不丢失；
                </p>
                <a href="javascript:;">more</a>
            </li>
            <li class="ani animate__delay-2s" data-animation="headShake">
                <img src="./img/img12.png" alt="">
                <p class="title-advantage">名片真实可靠</p>
                <div class="line"></div>
                <p class="intro-text">
                    企业认证标识，权威象征，保证名片信息的真实性，更易获得信赖；
                </p>
                <a href="javascript:;">more</a>
            </li>
            <li class="ani animate__delay-3s" data-animation="headShake">
                <img src="./img/img13.png" alt="">
                <p class="title-advantage">公司品牌营销</p>
                <div class="line"></div>
                <p class="intro-text">
                    企业介绍、实力展示、营销图片视频实时更新，统一输出，更具品牌效应；
                </p>
                <a href="javascript:;">more</a>
            </li>
            <li class="ani animate__delay-4s" data-animation="headShake">
                <img src="./img/img14.png" alt="">
                <p class="title-advantage">商务人脉拓展</p>
                <div class="line"></div>
                <p class="intro-text">
                    通过微信社交实现人脉裂变，轻松收到名片回赠，获客更简单；
                </p>
                <a href="javascript:;">more</a>
            </li>
            <li class="ani animate__delay-5s" data-animation="headShake">
                <img src="./img/img15.png" alt="">
                <p class="title-advantage">通讯录一键保存</p>
                <div class="line"></div>
                <p class="intro-text">
                    联系信息一键保存至通讯录，操作简便，无需担心录入错误；
                </p>
                <a href="javascript:;">more</a>
            </li>
        </ul>
    </div>
</div>
<div class="concat-us business-card-animate">
    <p class="form-title ani" data-animation="fadeInUp">将公域流量和社交流量转化为精准客户</p>
    <p class="form-en-title ani" data-animation="fadeInUp">Transform public domain traffic and social traffic into precise customers</p>
    <div class="form ani animate__delay-1s" data-animation="fadeInUp">
        <div class="input-wrap">
            <input type="text" id="company" placeholder="公司名称">
        </div>
        <div class="input-wrap">
            <input type="text" id="name" placeholder="联系人（必填）">
            <div class="el-form-item__error" style="display: none"></div>
        </div>
        <div class="input-wrap">
            <input type="text" id="telephone" placeholder="电话（必填）">
            <div class="el-form-item__error" style="display: none"></div>
        </div>
    </div>
    <div class="button" onclick="submitForm()">立即咨询</div>
</div>
<!--<img class="goBack" onclick="goBack()" src="./img/goBack.png" alt="" style="display: none">-->
<!--提示弹窗-->
<div class="message-box" style="z-index: 2019; display: none" id="message">
    <span id="message-text"></span>
</div>
<!-- 底部 -->
<footer id="zhyFooter"></footer>
<script>
    // 商桥
    var _hmt = _hmt || [];
    (function() {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?0de415586bc3f5d5aed84bdc980770fc";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
    })();
</script>
<script>
    // 表单验证
    $(".form input").unbind().on("blur", function (){
        const that = this;
        checkVal(that)
    })
    function checkVal(ele){
        if (!$(ele).val()){
            $(ele).siblings(".el-form-item__error").show().text("请输入" + $(ele).attr("placeholder").replace("（必填）", "")).parent().addClass("input-error");
        }else{
            if ($(ele).attr("name") === "telephone"){
                if(!/^1[3456789]\d{9}$/.test($(ele).val())){
                    $(ele).siblings(".el-form-item__error").show().text("请输入正确的手机号码").parent().addClass("input-error");
                }else{
                    $(ele).siblings(".el-form-item__error").hide().text("").parent().removeClass("input-error");
                }
            }else {
                $(ele).siblings(".el-form-item__error").hide().text("").parent().removeClass("input-error");
            }
        }
    }
    // 提交表单
    function submitForm() {
        $(".form input").trigger("blur");
        if ($(".el-form-item__error[style='display:block;']").length || $(".el-form-item__error[style='']").length){
            return
        }
        let company = $('#company').val(),
            name = $('#name').val(),
            telephone = $('#telephone').val();
        var url = 'https://hr.china9.cn/human/user_action/leave_message1';
        $.ajax({
            url: url, //请求地址
            data: {
                company:company,
                name:name,
                telephone:telephone,
                remarks: '云名片-个人版'
            }, //发送的数据
            dataType: 'json', //请求的数据类型
            type: 'post', //发送的请求类型
            success: function(request) {
                if (request.code == 200) {
                    $("#message").fadeIn().find("#message-text").html(request.message);
                    $(".form input").val("")
                    setTimeout(function (){
                        $("#message").fadeOut();
                    }, 3000)
                } else {
                    alert(request.message)
                }
            }
        });
    }
//     核心功能切换
    $(".core-functions .tab li").on("mouseover", function (){
        $(this).addClass("active").siblings().removeClass("active");
        let animateClassName = $(".tab-panel li").data("animation");
        $(".tab-panel li").eq($(this).index()).show().addClass("animate__animated animate__" + animateClassName).siblings().hide().removeClass("animate__animated animate__" + animateClassName);
    })

//     添加动画
    function addAnimate() {
        /*if ($(window).scrollTop() > 300){
            $(".goBack").show()
        }else{
            $(".goBack").hide()
        }*/
        $(".business-card-animate").each(function (i, v) {
            // 滚动条的垂直位置。
            let windowScrollTop = $(window).scrollTop();
            // 元素据文档顶端的距离
            let offsetTop = $(v).offset().top;
            // 窗口大小高度
            let documentHeight = $(window).height();
            // 自身高度
            let selfHeight = $(v).height();
            if (offsetTop - windowScrollTop < documentHeight + 100) {
                $(v).find(".ani").each(function (i1, v1){
                    $(v1).addClass("animate__animated animate__" + $(v1).data("animation"));
                })
                if (selfHeight + offsetTop < windowScrollTop) {
                    $(v).find(".ani").each(function (i1, v1){
                        $(v1).removeClass("animate__animated animate__" + $(v1).data("animation"));
                    })
                }
            } else {
                $(v).find(".ani").each(function (i1, v1){
                    $(v1).removeClass("animate__animated animate__" + $(v1).data("animation"));
                })
            }
        })
    }
    addAnimate()
    $(window).on("scroll", addAnimate)

    //     跳咨询弹窗
    $(".advantage a").on("click", function (){
        window.open("https://p.qiao.baidu.com/cps3/chatIndex?siteToken=89150175fdf6db91904fe12993fb26a5&speedLogId=1598081865315470f_1598081865315_66770&eid=27441212&reqParam={\"from\"%3A0%2C\"sid\"%3A\"-100\"%2C\"tid\"%3A\"-1\"%2C\"ttype\"%3A1%2C\"siteId\"%3A\"15629535\"%2C\"userId\"%3A\"27441212\"%2C\"pageId\"%3A0}")
    })
//     资讯按钮
    $(".button").on("click", function (){
        $('#company').focus();
    })

    //     微信分享
    function shareInit() {
        var imgUrl = 'https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/logo.f983bd4.png';
        // var lineLink = window.location.href;
        var lineLink = window.location.href;
        var shareTitle = '云名片-个人版';
        // var descContent = gameInfo.share_desc || '我用了' + that.formatTime(that.time, 'ms') + '，你也快来试试吧';
        var descContent = '助力企业低成本获客';
        var linkMy = encodeURIComponent(location.href.split('#')[0])
        // alert(lineLink, 'lineLink')
        // alert(linkMy, 'linkMy')
        lineLink = linkMy
        $.post('http://api.china9.cn/api/wechat_Share', {
            url: lineLink
        }).then(function (response) {
            try {
                response = JSON.parse(response)
            } catch (error) {

            }
            var appId = response.appId;
            var timestamp = response.timestamp;
            var nonceStr = response.nonceStr;
            var signature = response.signature;
            var lineLink = response.url;
            //  var signature = 'aaaaa';
            wx.config({
                debug: false,
                appId: appId,
                timestamp: timestamp,
                nonceStr: nonceStr,
                signature: signature,
                jsApiList: ['checkJsApi', 'onMenuShareTimeline', 'onMenuShareAppMessage']
            });
            wx.ready(function () {
                console.log(imgUrl)
                wx.onMenuShareTimeline({
                    title: shareTitle, // 分享标题
                    link: lineLink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                    imgUrl: imgUrl//, // 分享图标
                    // success: function() {
                    // 	// 用户确认分享后执行的回调函数
                    // },
                    // cancel: function() {
                    // 	// 用户取消分享后执行的回调函数
                    // }
                }),
                    wx.onMenuShareAppMessage({
                        title: shareTitle, // 分享标题
                        desc: descContent, // 分享描述
                        link: lineLink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                        imgUrl: imgUrl, // 分享图标
                        type: '', // 分享类型,music、video或link，不填默认为link
                        dataUrl: ''//, // 如果type是music或video，则要提供数据链接，默认为空
                        , success: function () {
                            // 用户确认分享后执行的回调函数
                            // alert('分享成功')
                        },
                        cancel: function () {
                            // 用户取消分享后执行的回调函数
                            // alert('分享取消')
                        }
                    })
            });
        })
    }
    shareInit()
</script>
</body>
</html>
