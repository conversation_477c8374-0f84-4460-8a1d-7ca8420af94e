import{k as E,f as k,aN as L,w as t,aO as G,o as i,aA as D,_ as z,A as v,l as q,V as Z,aP as ee,b as e,a as l,d as te,e as Q,t as x,E as oe,j as V,h as T,Q as N,B as J,c as b,q as R,J as O,H as K,p as w,aE as se,X as Y,F,m as W,a5 as ne,s as le,P as ae,N as re,a7 as ie,K as de,ak as ce}from"./index-BBeD0eDz.js";/* empty css                *//* empty css              *//* empty css               */import{P as pe}from"./ProductDrawer-1eJ7VyQy.js";/* empty css                *//* empty css                  */import{s as ue,c as me,g as _e}from"./index-B2D9HeJa.js";import"./request-Ciyrqj7N.js";const ve=E({__name:"Tips",props:{title:{},type:{},showIcon:{type:Boolean}},setup(B){return(n,f)=>{const p=G;return i(),k(p,{title:n.title,type:n.type,"show-icon":n.showIcon},L({_:2},[n.$slots.title?{name:"title",fn:t(()=>[D(n.$slots,"title",{},void 0,!0)]),key:"0"}:void 0,n.$slots.content?{name:"content",fn:t(()=>[D(n.$slots,"content",{},void 0,!0)]),key:"1"}:void 0,n.$slots.icon?{name:"icon",fn:t(()=>[D(n.$slots,"icon",{},void 0,!0)]),key:"2"}:void 0]),1032,["title","type","show-icon"])}}}),fe=z(ve,[["__scopeId","data-v-b36a05d9"]]),ge={class:"drawer-content"},xe={class:"text-right text-gray-400 text-xs mt-1"},be={class:"text-right text-gray-400 text-xs mt-1"},ye={class:"drawer-footer py-[20px] px-[39px]"},Ce=E({__name:"BrandInfoDrawer",props:{modelValue:{type:Boolean},info:{}},emits:["update:modelValue","save"],setup(B,{emit:n}){const f=B,p=n,s=v(),y=v({brand_name:[{required:!0,message:"请输入企业/品牌名称",trigger:"blur"},{min:2,message:"名称不能少于2个字符",trigger:"blur"}],brand_con:[{required:!0,message:"请输入企业/品牌介绍",trigger:"blur"},{min:10,message:"介绍内容不能少于10个字符",trigger:"blur"}]}),g=q({get:()=>f.modelValue,set:_=>p("update:modelValue",_)}),r=v({brand_name:"",brand_con:""});Z(()=>f.info,_=>{_&&(r.value={..._})},{immediate:!0,deep:!0});const C=()=>{g.value=!1},u=_=>_?_.length:0,m=()=>{g.value=!1},h=()=>{s.value&&s.value.validate(_=>{_?ue(r.value).then(()=>{N.success("保存成功"),g.value=!1,p("save",{...r.value})}):N.error("请填写必填项")})};return(_,d)=>{const I=Q,a=te,o=oe,P=T,U=ee;return i(),k(U,{modelValue:g.value,"onUpdate:modelValue":d[2]||(d[2]=A=>g.value=A),size:"50%",direction:"rtl","before-close":C,class:"brand-drawer"},{header:t(()=>d[3]||(d[3]=[e("div",{class:"flex items-center"},[e("div",{class:"font-bold text-[#111111] text-[18px] mr-[12px]"},"企业/品牌介绍"),e("div",{class:"tips text-[#999999] text-[14px]"},"请务必确保信息真实准确，以便于AI进行智能分析")],-1)])),default:t(()=>[e("div",ge,[l(o,{model:r.value,rules:y.value,ref_key:"formRef",ref:s,"label-position":"top",class:"px-4 brand-form","status-icon":""},{default:t(()=>[l(a,{label:"企业/品牌名称：",prop:"brand_name"},{default:t(()=>[l(I,{modelValue:r.value.brand_name,"onUpdate:modelValue":d[0]||(d[0]=A=>r.value.brand_name=A),placeholder:"请输入企业/品牌名称",maxlength:50},null,8,["modelValue"]),e("div",xe,x(u(r.value.brand_name))+"/50",1)]),_:1}),l(a,{label:"企业/品牌介绍：",prop:"brand_con"},{default:t(()=>[l(I,{modelValue:r.value.brand_con,"onUpdate:modelValue":d[1]||(d[1]=A=>r.value.brand_con=A),type:"textarea",rows:6,maxlength:1e3,resize:"none",placeholder:"请详细描述您企业的核心业务（例：主营IT软件与服务，从事计算机网络技术开发，互联网信息服务等，为企业提供互联网全网站建设、营销、管理平台，一站式解决办公系统...）"},null,8,["modelValue"]),e("div",be,x(u(r.value.brand_con))+"/1000",1)]),_:1})]),_:1},8,["model","rules"])]),e("div",ye,[l(P,{onClick:m,size:"large",class:"w-[100px]"},{default:t(()=>d[4]||(d[4]=[V("取消")])),_:1}),l(P,{type:"primary",onClick:h,size:"large",class:"w-[100px]"},{default:t(()=>d[5]||(d[5]=[V("保存")])),_:1})])]),_:1},8,["modelValue"])}}}),we=z(Ce,[["__scopeId","data-v-e33f2065"]]),he=""+new URL("img1-DoeQEVN1.png",import.meta.url).href,Ae="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABcAAAAXCAYAAADgKtSgAAAAAXNSR0IArs4c6QAABLNJREFUSEuV1V2IFWUYB/Dned935nzth+tpXW13+9BWlwyCkkBQCKToJrrpIrosQiiii64CSVfFLoyQlKW0ECIKgq27sC6ylKWU1ewiqdbPDN3POWfOnDNz5v14npiztnVEOTlzMRfzzu/9z/N+IdzFxcxy4gyMaNYbU1KPdEtx7PlNeOpOBHayj1/mfByb0USLR7XDDamBVFu3wL4XeWTveXmz995d4T9Nc09SgIfDgB5vOnt/3FQ148Q8CTCAIJkBBYDz0ZRe2uy/3xH/epp7Sh5silN4op7CinpkKwYwcCgdMEiB0PaXTOCQ09L2rfnO+LFfzd4bVRGklkPpAViUIkso7xBrCU9K27cWO+Nf/mL2ztTVRUngCW9JNNQuIy2lFwLYAjjUSfHVJ4sHO5blq3Nuz42GuJThoICcc7FxS5+RBWkcSWOc8nN+UUnhPAWGdZJ7fdv/wCfOmT03InXZk6BWl2zvlhF8DRhEhjMzMhM6x/bQD+6ocL7NlUScFwm/+VTXgY7JJ86a3XMNdUVIUP1F27VlHb8CjK26a0eKHHupcbj/mPtGCT/1815YKjRq+57reacj/vmU2V3V6ooAUNYCW6cjQon1BuXna1Ser9qhMG4OeTlZVoi6qHKVcre+cODF3h0d8c+mzFio1dUM/6cxEWC1YYozFbuqsqjXBto9qC33AQMXPFUzgtJvJ+d22C9Gj9+ug+W5++mUGYsSdTUrC1gg5cXKYV6EUVKaD2hgPkweCmowElm/XxB6rFDXyZOnzqdl5fFkAe1b0UerTv63k2X8k9PpWCP1W/i6Hj2wbaM3xsA33/PS1HTAz747GyaULwqW1AQfTv3ezIOvAH0JgsQJ4aI3zJFV57L2y/jRH9NdTev/meFFn+TmB+zTyKCahrxabLqrdSrP1Vzf0ZM2x+CXGEA0Icen/2hK8CWg8ltRfGGv60Pdg234x5PJLk35Fp69sBY0IWAQUtdskNw7U7EbgliMIptBBi4RKxGzB1PTKWY4KB9AIHhsFsx4d/8teLrTobzGgC2cUDA5EmEIpZkgWTMX0shCA0adc2ssQAkZZMMpOHPRQDuuF8x4Tzt+ZDLdyTfxbOWs77drCZ1MYs4vhqY8U3XDc1UzfPpabgUDdgEDNpzCMxf1v8kRwYPbJP/wZLoTpX+NgdS6Mq3etl6N3W56PbM/CFJHJWBPRhl+Qbdq3ipLC7cLZryrPfkHJ8zbQqm/GEApdnxfHw84JFFruMJiTZfnqsnQfM0O/zab7wWnik5KrDY5//MlUwTfB1ASEBWoVvJb8PHj6Q6V869nuHMAEggABUYx5BfreuVsxQ6GsRjS3Cgb6+dY5HA+MuWz55M1kPMBPAnZcEnQgTvcU24N6OET0QuGVDeBGC74/pUMX16hQGBS8CoN6goapi+su5XOQVZvpYWAapMHri9Ar1AIJJAR0bCA2mOD6YSwsoLj36f7IC+nPSsFyfbTJttx0YFMU/CiJhViQwVnySeG1obGEri1bWZ7vBSkUNiiEs1ckZpBRD148LtkHwmVIBFmZwMDsCYAiYItEWQ3OBDagDBEklmgI8LsB4kpWzitDrInAjGysKiES1KLfwOpcKSyjRnOKQAAAABJRU5ErkJggg==",ke={class:"intro"},Ve={class:"flex items-center justify-start"},Ie=["src"],Be={class:"text-[#111111] text-[18px] font-bold"},Ue={key:1,class:"pb-[35px] intro-wrap box-border"},Ee={class:"box-border"},ze={class:"intro-item"},Pe={class:"intro-content"},De=E({__name:"Intro",emits:["infoChanged"],setup(B,{emit:n}){const f=n,p=v(!1),s=v({brand_name:"",brand_con:""}),y=()=>{p.value=!0},g=u=>{s.value=u,f("infoChanged",u)},r=v(!1),C=async()=>{r.value=!0;try{const u=await me();console.log(u,"品牌信息"),s.value=u,f("infoChanged",u)}catch(u){console.error("获取品牌信息失败",u)}finally{r.value=!1}};return J(()=>{C()}),(u,m)=>{const h=T,_=Y,d=K,I=O;return i(),b("div",ke,[R((i(),k(d,null,L({default:t(()=>[s.value.brand_name?(i(),b("div",Ue,[e("div",Ee,[e("div",ze,[m[4]||(m[4]=e("div",{class:"intro-title"},"企业/品牌简介",-1)),e("div",Pe,x(s.value.brand_con),1)])])])):(i(),k(_,{key:0,image:w(he),"image-size":71},{description:t(()=>m[2]||(m[2]=[e("div",{class:"empty-title text-[--el-color-primary] text-[18px] font-bold"},"请完善企业/品牌介绍",-1),e("div",{class:"empty-desc text-[#999999] text-[14px] mt-[14px]"},"请务必确保信息真实准确，AI系统将基于您的描述进行智能分析，确保内容创作与您的品牌形象高度一致 ",-1)])),default:t(()=>[l(h,{type:"primary",class:"w-[120px] mt-[2px] refine",onClick:y},{default:t(()=>m[3]||(m[3]=[V("立即完善")])),_:1})]),_:1},8,["image"]))]),_:2},[s.value.brand_name?{name:"title-left",fn:t(()=>[e("div",Ve,[e("img",{src:w(Ae),alt:"",class:"mr-[11px]"},null,8,Ie),e("div",Be,x(s.value.brand_name),1)])]),key:"0"}:void 0,s.value.brand_name?{name:"title-right",fn:t(()=>[l(h,{type:"primary",class:"w-[120px] refine",icon:w(se),onClick:y},{default:t(()=>m[1]||(m[1]=[V("修改信息")])),_:1},8,["icon"])]),key:"1"}:void 0]),1024)),[[I,r.value]]),l(we,{modelValue:p.value,"onUpdate:modelValue":m[0]||(m[0]=a=>p.value=a),info:s.value,onSave:g},null,8,["modelValue","info"])])}}}),Se=z(De,[["__scopeId","data-v-c63745e8"]]),S=""+new URL("img3-BxcvHwfG.png",import.meta.url).href,Fe={class:"video-list overflow-hidden"},We={key:0,class:"flex items-center"},Te={class:"h-full flex flex-col"},Me={class:"flex items-stretch justify-start list flex-wrap"},Ne=["onClick"],Le={class:"list-item h-full"},Qe={class:"item-intro border-b border-[#E8EDF1]"},Je={class:"item-intro-title flex items-center justify-start"},Re=["src"],Oe={class:"item-title text-[18px] text-[#111] font-bold"},Ke={class:"item-intro-content text-[14px] text-[#3C3C3C] mt-[18px] line-clamp-4 leading-[22px] overflow-hidden mb-[10px]"},Ye={class:"item-sell-point mt-[18px] border-b border-[#E8EDF1]"},$e={class:"item-sell-point-content text-[14px] text-[#3C3C3C] mt-[11px] line-clamp-6 leading-[22px] mb-[10px]"},je={class:"item-customer-portrait mt-[18px]"},He={class:"item-customer-portrait-content text-[14px] text-[#3C3C3C] mt-[11px] line-clamp-6 leading-[22px]"},Xe={key:1,class:"h-full bg-secondary list-item flex justify-center items-center cursor-pointer"},Ge={key:1,class:"h-full bg-secondary list-item flex justify-center items-center cursor-pointer"},qe=E({__name:"VideoList",setup(B){const n=v(!1),f=v(!1),p=v(!1),s=v({type:1,page:1,perPage:10,name:""}),y=v(0),g=v(null),r=v([]),C=async()=>{f.value=!0;try{const a=await _e(s.value);console.log(a,"产品列表"),r.value=a.data,y.value=a.total}catch(a){console.error("获取产品列表失败",a)}finally{f.value=!1}},u=a=>{s.value.perPage=a,C()},m=a=>{s.value.page=a,C()},h=()=>{var a;(a=g.value)==null||a.setFormData({name:"",content:"",selling_point:"",region:[],age:0,gender:[],keywords:[]}),n.value=!0},_=a=>{var o;(o=g.value)==null||o.setFormData(a),n.value=!0},d=a=>{C()},I=()=>{p.value=!0,s.value.page=1,C()};return J(()=>{C()}),(a,o)=>{const P=Q,U=T,A=ne,M=Y,$=ae,j=K,H=O;return i(),b("div",Fe,[R((i(),k(j,{title:"产品库",class:"h-full"},{"title-right":t(()=>[r.value.length>0||p.value?(i(),b("div",We,[l(P,{modelValue:s.value.name,"onUpdate:modelValue":o[0]||(o[0]=c=>s.value.name=c),placeholder:"请输入产品名称",clearable:"",class:"w-[300px]"},null,8,["modelValue"]),l(U,{type:"primary",class:"w-[120px] ml-[10px]",icon:w(re),onClick:I},{default:t(()=>o[4]||(o[4]=[V("搜索")])),_:1},8,["icon"]),l(U,{type:"primary",class:"w-[120px] ml-[10px]",icon:w(ie),onClick:h},{default:t(()=>o[5]||(o[5]=[V("添加")])),_:1},8,["icon"])])):W("",!0)]),default:t(()=>[e("div",Te,[r.value.length>0||p.value?(i(),b(F,{key:0},[r.value.length>0?(i(),k(A,{key:0,class:"flex-1"},{default:t(()=>[e("div",Me,[(i(!0),b(F,null,le(r.value,(c,X)=>(i(),b("div",{class:"list-item-wrap cursor-pointer",key:X,onClick:ot=>_(c)},[e("div",Le,[e("div",Qe,[e("div",Je,[e("img",{src:w(S),alt:"",class:"w-[23px] mr-[11px]"},null,8,Re),e("div",Oe,x(c.name),1)]),e("div",Ke,x(c.content),1)]),e("div",Ye,[o[6]||(o[6]=e("div",{class:"item-sell-point-title text-[14px] text-[#999]"},"产品卖点",-1)),e("div",$e,x(c.selling_point),1)]),e("div",je,[o[7]||(o[7]=e("div",{class:"item-customer-portrait-title text-[14px] text-[#999]"},"客户画像",-1)),e("div",He,[e("p",null,"性别："+x(c.gender_txt),1),e("p",null,"年龄："+x(c.age_txt),1),e("p",null,"地域："+x(c.region_txt),1),e("p",null,"行业/职业："+x(c.keywords_txt),1)])])])],8,Ne))),128))])]),_:1})):(i(),b("div",Xe,[l(M,{image:w(S),"image-size":79,class:"w-[100%] h-[100%]"},{description:t(()=>o[8]||(o[8]=[e("div",{class:"empty-title text-[--el-color-primary] text-[18px] font-bold"},"暂无搜索结果",-1),e("div",{class:"empty-desc text-[#999999] text-[14px] mt-[14px]"},"请换个关键词搜索",-1)])),_:1},8,["image"])])),y.value>0?(i(),k($,{key:2,"current-page":s.value.page,"onUpdate:currentPage":o[1]||(o[1]=c=>s.value.page=c),"page-size":s.value.perPage,"onUpdate:pageSize":o[2]||(o[2]=c=>s.value.perPage=c),total:y.value,onSizeChange:u,onCurrentChange:m},null,8,["current-page","page-size","total"])):W("",!0)],64)):(i(),b("div",Ge,[l(M,{image:w(S),"image-size":79,class:"w-[100%] h-[100%]"},{description:t(()=>o[9]||(o[9]=[e("div",{class:"empty-title text-[--el-color-primary] text-[18px] font-bold"},"添加产品",-1),e("div",{class:"empty-desc text-[#999999] text-[14px] mt-[14px]"},"请介绍您企业的主营产品",-1)])),default:t(()=>[l(U,{type:"primary",class:"w-[120px] mt-[2px] refine",onClick:h},{default:t(()=>o[10]||(o[10]=[V("立即添加")])),_:1})]),_:1},8,["image"])]))])]),_:1})),[[H,f.value]]),l(pe,{ref_key:"productDrawerRef",ref:g,modelValue:n.value,"onUpdate:modelValue":o[3]||(o[3]=c=>n.value=c),onSave:d},null,8,["modelValue"])])}}}),Ze=z(qe,[["__scopeId","data-v-cdbad927"]]),et={class:"knowledge-view"},tt=E({__name:"Index",setup(B){const n=v(!1),f=p=>{p.brand_name?n.value=!0:n.value=!1};return(p,s)=>{const y=de;return i(),b("div",et,[n.value?W("",!0):(i(),b(F,{key:0},[l(fe,{title:"在生成爆款内容之前，请完善您的行业知识库，便于AI抓取行业爆款内容，并使AI生成的文案和剪辑内容更加贴合行业调性以及您的业务",type:"info","show-icon":""},{icon:t(()=>[l(y,null,{default:t(()=>[l(w(ce))]),_:1})]),_:1}),s[0]||(s[0]=e("div",{class:"h-[20px]"},null,-1))],64)),l(Se,{onInfoChanged:f,class:"mb-[20px]"}),l(Ze)])}}}),ut=z(tt,[["__scopeId","data-v-782a81ca"]]);export{ut as default};
