/* eslint-disable */

//token
var access_token = Cookies.get("access_token") || Cookies.get("token");

var version = '202407251658';

// 跳转链接域名
var zhyFooterLinkDomain = "https://dev.china9.cn/#/";
var zhyFooterApiDomain = "https://apidev.china9.cn/";
var zhyImageDomain = "https://zcloud.obs.cn-north-4.myhuaweicloud.com/";
if ("undefined" != typeof environment) {
  if (environment === "pro") {
    zhyFooterLinkDomain = "https://www.china9.cn/#/";
    zhyFooterApiDomain = "https://www.china9.cn/";
  } else if (environment === 'local') {
    zhyFooterLinkDomain = "http://" + window.location.host + "/#/";
    zhyFooterApiDomain = "https://apidev.china9.cn/";
  } else {
    zhyFooterLinkDomain = "https://dev.china9.cn/#/";
    zhyFooterApiDomain = "https://apidev.china9.cn/";
  }
} else {
  zhyFooterLinkDomain = "https://dev.china9.cn/#/";
  zhyFooterApiDomain = "https://apidev.china9.cn/";
}

function getCssPromise(href, idName) {
  return new Promise(resolve => {
    $.get(href, function (res) {
      var style = document.createElement('style');
      style.id = idName;
      style.innerHTML = res;
      if (idName === 'elementCss') {
        // 获取head中的第一个元素
        var firstElement = document.head.firstChild;
        // 将新的style元素插入到第一个元素之前
        document.head.insertBefore(style, firstElement);
      } else {
        document.head.appendChild(style);
      }
      resolve({code: 200, id: idName, content: style})
    })
  })
}

//  先加载样式，防止先出现没有样式的内容
if (!document.getElementById("ziHaiFooterCloud")) {
  var commonCssUrl = 'https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/ziHaiFooterCloud.css?v=' + version;
  if (environment && environment === "local") {
    commonCssUrl = '/static/js/common/ziHaiFooterCloud.css';
  }
  var cssList = [
    getCssPromise(commonCssUrl, 'ziHaiFooterCloud'),
  ];
  Promise.all(cssList).then((res) => {
    var success = res.filter(item => item.code === 200);
    if (success.length === 1) {
      checkHasFooterEle();
    }
  });
}

let footerLoadNum = 0;

function checkHasFooterEle() {
  setTimeout(function () {
    if (footerLoadNum < 2) {
      if (document.getElementById("zhyFooter")) {
        $.get(`${zhyFooterApiDomain}api/bottomCommon`, {enterpriseSide: 'pc'}).then(res => {
          if (res.code === 200 && res.data) {
            addFooter(res.data);
          }
        })
      } else {
        footerLoadNum++;
        checkHasFooterEle();
      }
    }
  }, 500)
}


// footer
function addFooter(data) {
  let str = '<div class="content">' +
      '            <div class="container">';
  data && data.length && data.forEach(value => {
    str += `<div class="product">
        <p>${value.title}</p>`;
    if (value.children && value.children.length) {
      str += `<div>`
      value.children.forEach(val => {
        str += `<div class="product-item">`
        if (val.children && val.children.length) {
          val.children.forEach(v => {
            str += `<div>
                <a href="${v.url}">${v.title}</a>
              </div>`
          })
        }
        str += `</div>`;
      })
      str += `</div>`;
    }
    if (value.title === '云产品') {
    } else {
      if (value.children && value.children.length) {
        str += `<div>`
        value.children.forEach(v => {
          str += `<div class="product-item">
                <a href="${v.url}">${v.title}</a>
              </div>`;
        })
        str += `</div>`;
      }
    }

    str += `</div>`;
  })

  str += '                <div class="address zhyHomeHeaderPc"><p>400-650-5024</p>' +
      '                    <table style="color: rgba(255, 255, 255, 0.6); border-collapse: separate; border-spacing: 0px 5px; font-size: 12px;">' +
      '                        <tr>' +
      '                            <td style="vertical-align: top;">黑龙江资海</td>' +
      '                            <td>黑龙江省哈尔滨市道外区南平街72号2层</td>' +
      '                        </tr>' +
      '                        <tr>' +
      '                            <td style="vertical-align: top;">北京资海</td>' +
      '                            <td>北京市朝阳区高碑店南岸一号（惠河南街）义安门B座3层</td>' +
      '                        </tr>' +
      '                        <tr>' +
      '                            <td style="vertical-align: top;">山西资海</td>' +
      '                            <td>山西省太原市小店区南中环清控创新基地B座3层</td>' +
      '                        </tr>' +
      '                        <tr>' +
      '                            <td style="vertical-align: top;">大连资海</td>' +
      '                            <td>大连市高新区银海万向大厦9楼</td>' +
      '                        </tr>' +
      '                    </table>' +
      '                </div>' +
      '                <div class="qr zhyHomeHeaderPc">' +
      '                    <div class="el-image">' +
      '                        <img src="' + zhyImageDomain + 'static/qrcode_for_gh_95704386c2cb_258.jpg" class="el-image__inner">' +
      '                    </div>' +
      '                    <div class="qr_title">' +
      '                        <p>资海微信公众平台</p>' +
      '                        <p>扫码关注</p>' +
      '                    </div>' +
      '                </div>' +
      '            </div>' +
      '            <div class="version zhyHomeHeaderPc">' +
      '                <span style="margin-right: 10px;">© 2020-2024</span>' +
      '                <a href="https://china9.cn/" style="color: inherit; margin-right: 10px;">china9.cn</a> <a href="http://beian.miit.gov.cn/" style="color: inherit; margin-right: 10px;">黑ICP备09002383号-39</a>' +
      '                <span style="margin-right: 10px;">版权所有：资海科技集团 增值电信业务许可证：黑B2-20121494</span>' +
      '                <span style="margin-left: 30px;">' +
      '                    <a target="_blank" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=23010402000123" style="display: inline-block; text-decoration: none; height: 20px; line-height: 20px;">' +
      '                        <div class="el-image" style="float: left; width: 20px; height: 20px;">' +
      '                            <img src="' + zhyImageDomain + 'static/guohui.png" class="el-image__inner">' +
      '                        </div>' +
      '                        <p style="float: left; height: 20px; line-height: 20px; margin: 0px 0px 0px 5px; color: rgb(147, 147, 147);">黑公网安备 23010402000123号</p>' +
      '                    </a>' +
      '                </span>' +
      '            </div>' +
      '            <div class="version zhyHomeHeaderPhone">' +
      '                <span>© 2023 版权所有：资海科技集团</span>' +
      '                <a href="http://beian.miit.gov.cn/" style="color: inherit; margin-right: 10px;">黑ICP备09002383号-39</a>' +
      '                <span>' +
      '                    <a target="_blank" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=23010402000123" style="display: inline-block; text-decoration: none; height: 20px; line-height: 20px;">' +
      '                        <div class="el-image" style="float: left; width: 20px; height: 20px;">' +
      '                            <img src="' + zhyImageDomain + 'static/guohui.png" class="el-image__inner">' +
      '                        </div>' +
      '                        <p style="float: left; height: 20px; line-height: 20px; margin: 0px 0px 0px 5px; color: rgb(147, 147, 147);">黑公网安备 23010402000123号</p>' +
      '                    </a>' +
      '                </span>' +
      '            </div>' +
      '        </div>' +
      '<img class="goBack" onclick="goBack()" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAGEklEQVRogd2aW2wTVxrHf3NmfMOOY7C3ghL20AtUZQVUXUDqY4oFT/ilWiXgQoQEbXlpn+hDV6qEtLsPi5BaCWvb8taql91VX5Y3tOoKXiLRssqC2l2nKvikoTjBVixnUidjZmYfGIdALsT2OMnuX7JknXO+8/3/c2bO5Tuf5roufkAptQXoBfYAzwFbgRQQBTTABEpAAcgD3wD/kFL+6Id/rR0hSqlfAgNAv67rO0KhEKFQiEAggGEY6LqOpmkAuK6Lbdvcu3ePer3OzMwMMzMz2Lb9HfAF8LGUUq2oEKXUbuC3QohXotGoiEajhEKhlghYlsXU1BSmaTqO43wJ/F5K+a9m+2lKiFLqCeCPuq4fi8fjWldX1+wTbxeu6zI5OUm1WnVt2/4YeFtKOb5c+2ULUUr9BvgoHo8nEomEbwIeheu6VCoVqtVqBXhNSvnX5dg9VohSKgi8FwgETqVSKYLBoA90Hw/LsiiVStTr9Q+At6SU1lLtlxSilIoBX0YikQOpVAohhM90l4bjOJRKJWq12iXgFSmluVjbRYUopdYBf4/FYi8lk8kOUV0eyuUypmkOAmkp5c8LtVnwEXuv08W1IAIgmUwSi8VeAv7mcZuHxd6V9yKRyMtrQUQDyWSSSCSyH3h/ofp5QpRSfYZhnEqlUh0n1yxSqRSGYbyhlOp/tO6hb0Qp9STw7aZNmxIrNTs1C8uyuHPnTgX4lZTyp0b5oyNyNh6Pr1kRAMFgkHg8ngDOzi2fFaKUelHX9cPd3d0rTq5ZJBIJdF0/rJR6sVE2d0Tejcfj2kqvFa1A0zS6u7s14N1GmQBQSkkhxKGuri7fnebzeXp7e9m/fz9Ktby5nYdYLIYQ4pBS6il4MCKvRqNR4ff+KZ/Pk81mGRkZoVAo0NfXRz6f96VvTdOIRqMCOA4PhGRjsZgvDhq4fv062WyWiYmJ2bK7d++SzWZ9E+Nx7gMQSqktuq4/7+dMNTQ0xNGjRx8S0cDExARHjhxhaGiobT/BYBBd17crpZ4WwMvhcLjtThu4evUqAwMDmKaJEIL+/gdrV39/P0IIKpUKAwMDvojxuPcKYI9fozE4OMjx48cxTRPDMDh//jyZTGa2PpPJcO7cOQzDwDRNjh07xuDgYFs+Pe4vCGB7IBBoqzOAYrHIiRMnmJ6exjAMcrkcBw8enNcuk8mQy+UwDIOpqSlOnjxJqVRq2a/H/TkBPOOHELg/k0QiES5cuEA6nV60XTqdJpfLEQ6H2z5petyfNoBf+LEIbty4kcuXLwP3d6qPQzqd5sqVKwghWL9+fct+Pe4JA4j7tZo3u+3345jgce9a+/uRx6CxexdA1XGc1WXTBjwhpgDu/i8L8bhPCOCHer2+ynRah8f9pgCGOylk3bp1s/87sbv2uOcN4BvLWjL21RZ27tzJmTNnEEKwY8cO3/v3uA9phUJhi67rIz09Pb47WQmMjo5i2/YzQkr5o23b/+7kqHQKlmVh2/b3Usqbhlf2qWmav9uwYUPLnQ4PD3Pjxo2WbHft2sW2bduatjNNE+DP4IWDlFJbhRA/9PT0tHxK3L17d6PjppFIJLh27VpTNq7rMjo66jiO86yU8pYAkFIWHMe5ODk52RIRgL1797Zsu2/fvqZtTNPEcZyLUspbMCdAp5T6ta7rX2/evFnr1N2HX3Bdl9u3b7u2be+RUv4T5oSDpJTXbNv+vFKprB7DZaJSqWDb9ucNETA/0ni6Wq1W1vIMZllW4zbr9Nzyh4R4sdRTpVKJtbj/alz8AKfmxn2B++/bo79CofCnsbExd61hbGzMLRQKH7gLcF7sPPJWrVb7qlwud/ohLxvlcplarfYV8OZC9f/fV28AnsEB0zQvjY+Pr8o34zgO4+PjmKZ5CTiwmAhYQgiAd4t6qFarfVgsFlnJ2cyyLIrFIrVa7UPg0FI3urD2EwZel1L+ZTl2raRwnNV1/WiHUzg+AU53JIVjLpRSLwDvdCip5g9SyqaDwn6lOR3Wdf35cDhMMBhcVpqTZVlMT09j2/Z/gM9YjTSnJUT1AnuB7TxIPGtcvMxNPBsGvuZ+4tmIH/7/CwD1bJs1dpBtAAAAAElFTkSuQmCC" alt="" style="display: none">'
  $("#zhyFooter").html(str);
}

// 监听hash路由，解决路由变化后返回加不上头部的问题
window.addEventListener("hashchange", function () {
  addFooter();
})

//     回到顶部
function goBack() {
  window.scrollTo({
    top: 0,
    behavior: "smooth"
  });
}

window.addEventListener("scroll", toggleGoBackShow)

function toggleGoBackShow() {
  if ($(window).scrollTop() > 300) {
    $(".goBack").show()
  } else {
    $(".goBack").hide()
  }
}

toggleGoBackShow()
