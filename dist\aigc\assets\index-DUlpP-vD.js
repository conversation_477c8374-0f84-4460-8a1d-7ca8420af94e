import{k as u,A as l,c,a as o,w as s,H as f,o as i,b as t,f as v,r as h,h as x,j as k,S as w,_ as g}from"./index-BBeD0eDz.js";/* empty css               */import{_ as y}from"./no-data-CNqxJ4EX.js";import{W as C}from"./Writing-CreIJVvw.js";import{A as B}from"./AIDocumentForm-5TS4hQsu.js";/* empty css                 */import"./common-DBXWCL9C.js";import"./aiDocument-BqsEIq7y.js";import"./request-Ciyrqj7N.js";import"./aiVideo-CrvXrva8.js";import"./aiVideo-B86MWzMI.js";/* empty css                *//* empty css                     *//* empty css                  */import"./ProductSelector-BTeBhjvS.js";import"./ProductDrawer-1eJ7VyQy.js";/* empty css                */import"./index-B2D9HeJa.js";const b={class:"copywriting-container h-full"},A={class:"content-wrapper h-full"},G={class:"left-section h-full flex flex-col justify-between"},I={class:"right-section"},N={key:0,class:"preview-area"},V=u({__name:"index",setup(W){w("isMultiple",!0);const n=l(!1),a=l([]),p=r=>{n.value=!0,console.log(r,"文案生成结果"),a.value=r};return(r,e)=>{const m=x,_=h("router-link"),d=f;return i(),c("div",b,[o(d,{class:"h-full",title:"文案AI生成"},{"title-right":s(()=>[o(_,{to:"/document/template/history"},{default:s(()=>[o(m,{type:"primary",icon:"Clock"},{default:s(()=>e[0]||(e[0]=[k("历史文案")])),_:1})]),_:1})]),default:s(()=>[t("div",A,[t("div",G,[o(B,{onGenerate:p})]),t("div",I,[n.value?(i(),v(C,{key:1,data:a.value,maxSelectCount:5},null,8,["data"])):(i(),c("div",N,e[1]||(e[1]=[t("div",{class:"preview-icon"},[t("img",{src:y,alt:"文档图标"})],-1),t("div",{class:"preview-text"},"爆款文案即将生成",-1)])))])])]),_:1})])}}}),X=g(V,[["__scopeId","data-v-c29d7bc7"]]);export{X as default};
