<!doctype html>
<html lang="ch">
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
	      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>资海云-云名片-商城版</title>
	<meta name="description" content="资海云-云名片-商城版">
	<meta name="keywords" content="资海云-云名片-商城版">
	<link rel="icon" href="https://www.china9.cn/static/logo.png" type="image/x-icon">
	<link rel="stylesheet" href="./css/animate.min.css" type="text/css">
	<link rel="stylesheet" href="./css/index.css" type="text/css">
	<script src="./js/jquery.min.js"></script>
	<script src="./js/jweixin-1.2.0.js"></script>
	<script src="./js/weixinapi.min.js"></script>
	<script>
      var environment = "pro";
	</script>
	<style>
        #zhyHomeHeader .el-menu--horizontal > .el-menu-item {
            height: 70px;
            line-height: 70px;
            display: flex !important;
            align-items: center;
        }
        #zhyFooter .container {
            width: 1320px;
            min-width: 1320px;
            margin: auto;
        }
	</style>
	<script type="text/javascript"
	        src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/js-cookie.js?v=1227"></script>
	<script src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/homeHeader.js?v=03"></script>
	<script src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/footer.js?v=1228"></script>
</head>
<body>
<div id="zhyHomeHeader"></div>
<div class="banner business-card-animate">
	<div class="container clear-float">
		<div class="left">
			<!-- 版本 -->
			<div class="version-selector default-button ani" data-animation="fadeInUp">云名片-商城版</div>
			<!-- <h1 class="ani animate__delay-1s pc" data-animation="fadeInUp">云端存储不掉队，人脉裂变嗨翻天</h1> -->
			<h1 class="ani animate__delay-1s" data-animation="fadeInUp">名片商城朋友价<br>私域流量运营新方式</h1>
			<div class="button primary-button ani animate__delay-2s" data-animation="fadeInUp">立即咨询</div>
		</div>
		<img src="./img/banner-img.png" alt="" class="right ani animate__delay-3s pc" data-animation="fadeInRight">
		<img src="./img/phone-banner.png" alt="" class="right ani animate__delay-3s phone" data-animation="fadeInRight">
	</div>
</div>
<div class="what-we-can-do radio-left-top business-card-animate">
	<div class="container clear-float">
		<div class="left">
			<p class="title title-blue ani" data-animation="fadeInUp">我们能做什么</p>
			<p class="title-en title-blue ani" data-animation="fadeInUp">What can we do</p>
			<p class="advance ani animate__delay-1s" data-animation="fadeInUp">购物商务两不误，玩转云端新潮流</p>
			<p class="advance ani animate__delay-2s" data-animation="fadeInUp">让你的生活更有趣</p>
			<div class="phone">
				<div class="circle-container ani" data-animation="rotateInUpLeft">
					<p>“朋友价”购买商品</p>
					<p class="large">成交新型模式，提高转化率</p>
					<div class="circle-view circle-one"></div>
					<div class="circle-view circle-main"></div>
					<div class="circle-view circle-two"></div>
				</div>
			</div>
			<p class="text intro-text ani animate__delay-3s" data-animation="fadeInUp">
				云名片商城版，不仅拥有企业数字化名片，达到交换名片，轻松获客的能力，更是升级了商城功能。平台将为企业客户开通商城后台，商品一键添加，名片同步展示，好友随时下单，更可以“朋友价”购买商品！随时成交的新型模式，有效提高销售转化率。
			</p>
			<div>
				<div class="button primary-button ani animate__delay-4s" data-animation="fadeInUp">立即咨询</div>
			</div>
		</div>
		<div class="right pc">
			<div class="circle-container ani" data-animation="rotateInUpLeft">
				<p>“朋友价”购买商品</p>
				<p class="large">成交新型模式，提高转化率</p>
				<div class="circle-view circle-one"></div>
				<div class="circle-view circle-main"></div>
				<div class="circle-view circle-two"></div>
			</div>
		</div>
	</div>
</div>
<div class="save-to-address-book radio-right-top business-card-animate">
	<div class="container">
		<img src="./img/img1.png" alt="" class="img-box ani" data-animation="bounceIn">
		<div class="price-view">
			<img class="ani t-icon" data-animation="fadeInUp" src="./img/img24.png" alt="">
			<p class="title title-blue ani animate__delay-1s" data-animation="fadeInUp">专属朋友价</p>
			<!-- 无序列表 -->
			<ul class="tips ani animate__delay-2s" data-animation="fadeInUp">
				<li>商品一键添加</li>
				<li>私域流量尽在掌握</li>
				<li>有效提高销售转化率</li>
			</ul>
			<div class="button default-button ani animate__delay-3s" style="border: none;width: max-content;"
			     data-animation="fadeInUp">立即咨询 &gt;
			</div>
		</div>
	</div>
</div>
<div class="core-functions radio-left-top business-card-animate">
	<div class="container">
		<div class="title title-white ani" data-animation="flipInX">我们的核心功能</div>
		<div class="title-en title-white ani animate__delay-1s" data-animation="flipInX">Core functions</div>
		<div class="tab-box ani animate__delay-2s" data-animation="bounceIn">
			<div class="tab-wrap">
				<ul class="tab ani animate__delay-3s" data-animation="shakeXBig">
					<li class="active">
						<img src="./img/img3.png" alt="">
						<p>名片同步</p>
					</li>
					<li>
						<img src="./img/img4.png" alt="">
						<p>切换名片</p>
					</li>
					<li>
						<img src="./img/img5.png" alt="">
						<p>名片分享及回赠</p>
					</li>
					<li>
						<img src="./img/img6.png" alt="">
						<p>名片节日祝福</p>
					</li>
					<li>
						<img src="./img/img7.png" alt="">
						<p>名片夹</p>
					</li>
					<li>
						<img src="./img/img8.png" alt="">
						<p>发票及银行账户管理</p>
					</li>
					<li>
						<img src="./img/img22.png" alt="">
						<p>朋友价</p>
					</li>
					<li>
						<img src="./img/img9.png" alt="">
						<p>名片日签</p>
					</li>
				</ul>
			</div>
			<ul class="tab-panel">
				<li class="active ani-click" data-animation="pulse">
					<img src="./img/img2.png" alt="">
					<img src="./img/img10.png" alt="">
				</li>
				<li class="ani-click" data-animation="pulse">
					<img src="./img/img16.png" alt="">
					<img src="./img/img10.png" alt="">
				</li>
				<li class="ani-click" data-animation="pulse">
					<img src="./img/img17.png" alt="">
					<img src="./img/img10.png" alt="">
				</li>
				<li class="ani-click" data-animation="pulse">
					<img src="./img/img18.png" alt="">
					<img src="./img/img10.png" alt="">
				</li>
				<li class="ani-click" data-animation="pulse">
					<img src="./img/img19.png" alt="">
					<img src="./img/img10.png" alt="">
				</li>
				<li class="ani-click" data-animation="pulse">
					<img src="./img/img20.png" alt="">
					<img src="./img/img10.png" alt="">
				</li>
				<li class="ani-click" data-animation="pulse">
					<img src="./img/img23.png" alt="">
					<img src="./img/img10.png" alt="">
				</li>
				<li class="ani-click" data-animation="pulse">
					<img src="./img/img21.png" alt="">
					<img src="./img/img10.png" alt="">
				</li>
			</ul>
		</div>
	</div>
</div>
<div class="advantage radio-right-top business-card-animate">
	<div class="container">
		<div class="title title-blue ani" data-animation="slideInLeft">我们的优势</div>
		<div class="title-en title-blue ani animate__delay-1s" data-animation="slideInLeft">advantage</div>
		<ul class="clear-float clear-float">
			<li class="ani animate__delay-1s" data-animation="headShake">
				<img src="./img/img11.png" alt="">
				<p class="title-advantage">信息云端管理</p>
				<div class="line"></div>
				<p class="intro-text">
					名片信息云端存储，云名片始终为最新状态，人脉关系永不丢失；
				</p>
				<a href="javascript:;">more</a>
			</li>
			<li class="ani animate__delay-2s" data-animation="headShake">
				<img src="./img/img12.png" alt="">
				<p class="title-advantage">名片真实可靠</p>
				<div class="line"></div>
				<p class="intro-text">
					企业认证标识，权威象征，保证名片信息的真实性，更易获得信赖；
				</p>
				<a href="javascript:;">more</a>
			</li>
			<li class="ani animate__delay-3s" data-animation="headShake">
				<img src="./img/img13.png" alt="">
				<p class="title-advantage">企业全员营销</p>
				<div class="line"></div>
				<p class="intro-text">
					打造企业销售智能云名片，公司人人推广产品，人人变销售，线上线下流量裂变获取；
				</p>
				<a href="javascript:;">more</a>
			</li>
			<li class="ani animate__delay-4s" data-animation="headShake">
				<img src="./img/img14.png" alt="">
				<p class="title-advantage">商务人脉拓展</p>
				<div class="line"></div>
				<p class="intro-text">
					通过微信社交实现人脉裂变，轻松收到名片回赠，获客更简单；
				</p>
				<a href="javascript:;">more</a>
			</li>
			<li class="ani animate__delay-5s" data-animation="headShake">
				<img src="./img/img15.png" alt="">
				<p class="title-advantage">通讯录一键保存</p>
				<div class="line"></div>
				<p class="intro-text">
					联系信息一键保存至通讯录，操作简便，无需担心录入错误；
				</p>
				<a href="javascript:;">more</a>
			</li>
		</ul>
	</div>
</div>
<div class="concat-us business-card-animate">
	<p class="form-title ani" data-animation="fadeInUp">将公域流量和社交流量转化为精准客户</p>
	<p class="form-en-title ani" data-animation="fadeInUp">Transform public domain traffic and social traffic into
		precise customers</p>
	<div class="form ani animate__delay-1s" data-animation="fadeInUp">
		<div class="input-wrap">
			<input type="text" id="company" placeholder="公司名称">
		</div>
		<div class="input-wrap">
			<input data-required="true" type="text" id="name" placeholder="联系人（必填）">
			<!-- <div class="el-form-item__error" style="display: none"></div> -->
		</div>
		<div class="input-wrap">
			<input data-required="true" type="text" id="telephone" placeholder="电话（必填）">
			<!-- <div class="el-form-item__error" style="display: none"></div> -->
		</div>
	</div>
	<div class="button concat-us-btn">立即咨询</div>
</div>
<!--<img class="goBack" onclick="goBack()" src="./img/goBack.png" alt="" style="display: none">-->
<!--提示弹窗-->
<div class="message-box" style="z-index: 2019; display: none" id="message">
	<span id="message-text"></span>
</div>
<!-- 底部 -->
<footer id="zhyFooter"></footer>

<script src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/consult.js"></script>
<script>
  // 商桥
  var _hmt = _hmt || [];
  (function () {
    var hm = document.createElement("script");
    hm.src = "https://hm.baidu.com/hm.js?0de415586bc3f5d5aed84bdc980770fc";
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(hm, s);
  })();
</script>
<script>
  /*     // 表单验证
	  $(".form input").unbind().on("blur", function (){
		  const that = this;
		  checkVal(that)
	  })
	  function checkVal(ele){
		  if (!$(ele).val()){
			  $(ele).siblings(".el-form-item__error").show().text("请输入" + $(ele).attr("placeholder").replace("（必填）", "")).parent().addClass("input-error");
		  }else{
			  if ($(ele).attr("name") === "telephone"){
				  if(!/^1[3456789]\d{9}$/.test($(ele).val())){
					  $(ele).siblings(".el-form-item__error").show().text("请输入正确的手机号码").parent().addClass("input-error");
				  }else{
					  $(ele).siblings(".el-form-item__error").hide().text("").parent().removeClass("input-error");
				  }
			  }else {
				  $(ele).siblings(".el-form-item__error").hide().text("").parent().removeClass("input-error");
			  }
		  }
	  }
	  // 提交表单
	  function submitForm() {
		  $(".form input").trigger("blur");
		  if ($(".el-form-item__error[style='display:block;']").length || $(".el-form-item__error[style='']").length){
			  return
		  }
		  let company = $('#company').val(),
			  name = $('#name').val(),
			  telephone = $('#telephone').val();
		  var url = 'https://hr.china9.cn/human/user_action/leave_message1';
		  $.ajax({
			  url: url, //请求地址
			  data: {
				  company:company,
				  name:name,
				  telephone:telephone,
				  remarks: '云名片-商城版'
			  }, //发送的数据
			  dataType: 'json', //请求的数据类型
			  type: 'post', //发送的请求类型
			  success: function(request) {
				  if (request.code == 200) {
					  $("#message").fadeIn().find("#message-text").html(request.message);
					  $(".form input").val("")
					  setTimeout(function (){
						  $("#message").fadeOut();
					  }, 3000)
				  } else {
					  alert(request.message)
				  }
			  }
		  });
	  } */

  new ConsultForm({
    formEle: $(".form"),
    submitBtn: $('.concat-us-btn'),
    formData: {
      company: $('#company'),
      name: $('#name'),
      telephone: $('#telephone'),
      remarks: "云名片-商城版",
      content: $('#content')
    },
  })

  //     核心功能切换
  $(".core-functions .tab li").on("mouseover", function () {
    $(this).addClass("active").siblings().removeClass("active");
    let animateClassName = $(".tab-panel li").data("animation");
    $(".tab-panel li").eq($(this).index()).show().addClass("animate__animated animate__" + animateClassName).siblings().hide().removeClass("animate__animated animate__" + animateClassName);
  })

  //     添加动画
  function addAnimate() {
    /*if ($(window).scrollTop() > 300){
		$(".goBack").show()
	}else{
		$(".goBack").hide()
	}*/
    $(".business-card-animate").each(function (i, v) {
      // 滚动条的垂直位置。
      let windowScrollTop = $(window).scrollTop();
      // 元素据文档顶端的距离
      let offsetTop = $(v).offset().top;
      // 窗口大小高度
      let documentHeight = $(window).height();
      // 自身高度
      let selfHeight = $(v).height();
      if (offsetTop - windowScrollTop < documentHeight + 100) {
        $(v).find(".ani").each(function (i1, v1) {
          $(v1).addClass("animate__animated animate__" + $(v1).data("animation"));
        })
        if (selfHeight + offsetTop < windowScrollTop) {
          $(v).find(".ani").each(function (i1, v1) {
            $(v1).removeClass("animate__animated animate__" + $(v1).data("animation"));
          })
        }
      } else {
        $(v).find(".ani").each(function (i1, v1) {
          $(v1).removeClass("animate__animated animate__" + $(v1).data("animation"));
        })
      }
    })
  }

  addAnimate()
  $(window).on("scroll", addAnimate)

  //     跳咨询弹窗
  $(".advantage a").on("click", function () {
    window.open("https://p.qiao.baidu.com/cps3/chatIndex?siteToken=89150175fdf6db91904fe12993fb26a5&speedLogId=1598081865315470f_1598081865315_66770&eid=27441212&reqParam={\"from\"%3A0%2C\"sid\"%3A\"-100\"%2C\"tid\"%3A\"-1\"%2C\"ttype\"%3A1%2C\"siteId\"%3A\"15629535\"%2C\"userId\"%3A\"27441212\"%2C\"pageId\"%3A0}")
  })
  //     资讯按钮
  $(".button:not(.concat-us-btn)").on("click", function () {
    $('#company').focus();
  })

  //     微信分享
  function shareInit() {
    var imgUrl = 'https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/logo.f983bd4.png';
    // var lineLink = window.location.href;
    var lineLink = window.location.href;
    var shareTitle = '云名片-商城版';
    // var descContent = gameInfo.share_desc || '我用了' + that.formatTime(that.time, 'ms') + '，你也快来试试吧';
    var descContent = '助力企业低成本获客';
    var linkMy = encodeURIComponent(location.href.split('#')[0])
    // alert(lineLink, 'lineLink')
    // alert(linkMy, 'linkMy')
    lineLink = linkMy
    $.post('http://api.dev.china9.cn/api/wechat_Share', {
      url: lineLink
    }).then(function (response) {
      try {
        response = JSON.parse(response)
      } catch (error) {

      }
      var appId = response.appId;
      var timestamp = response.timestamp;
      var nonceStr = response.nonceStr;
      var signature = response.signature;
      var lineLink = response.url;
      //  var signature = 'aaaaa';
      wx.config({
        debug: false,
        appId: appId,
        timestamp: timestamp,
        nonceStr: nonceStr,
        signature: signature,
        jsApiList: ['checkJsApi', 'onMenuShareTimeline', 'onMenuShareAppMessage']
      });
      wx.ready(function () {
        console.log(imgUrl)
        wx.onMenuShareTimeline({
          title: shareTitle, // 分享标题
          link: lineLink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          imgUrl: imgUrl//, // 分享图标
          // success: function() {
          // 	// 用户确认分享后执行的回调函数
          // },
          // cancel: function() {
          // 	// 用户取消分享后执行的回调函数
          // }
        }),
          wx.onMenuShareAppMessage({
            title: shareTitle, // 分享标题
            desc: descContent, // 分享描述
            link: lineLink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
            imgUrl: imgUrl, // 分享图标
            type: '', // 分享类型,music、video或link，不填默认为link
            dataUrl: ''//, // 如果type是music或video，则要提供数据链接，默认为空
            , success: function () {
              // 用户确认分享后执行的回调函数
              // alert('分享成功')
            },
            cancel: function () {
              // 用户取消分享后执行的回调函数
              // alert('分享取消')
            }
          })
      });
    })
  }

  shareInit()
</script>
</body>
</html>
