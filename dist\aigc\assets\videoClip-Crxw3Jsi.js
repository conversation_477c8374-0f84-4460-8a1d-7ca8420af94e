import{h as s}from"./request-Ciyrqj7N.js";const r=async e=>s.post("/precisevideo/nameCreate",e),i=async e=>s.post("/precisevideo/update",e),o=async e=>s.post("/precisevideo/create",e),p=async e=>s.post("/precisevideo/copywriting",e),n=async e=>s.post("/precisevideo/structure",e),c=async e=>s.post("/precisevideo/narratorurl",e),a=async e=>s.post("/precisevideo/videoCreate",e),u=async e=>s.post("/precisevideo/submit",e),d=e=>s.post("/precisevideo/list",e),y=e=>s.post("/precisevideo/show",e,{returnFullResponse:!0}),v=e=>s.post("/precisevideo/delete",e,{returnFullResponse:!0}),A=e=>s.post("/precisevideo/copy",e);export{c as a,n as b,A as c,v as d,u as e,o as f,d as g,r as h,y as i,a as j,i as s,p as u};
