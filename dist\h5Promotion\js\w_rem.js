// rem 原理    1rem  =    html 根标签字体的大小
// 设备的可视宽度   750  移动端页面原图宽度
var designWidth = 750
function change() {
	var kk = document.documentElement.clientWidth || document.body.clientWidth
	if(kk > 750){
		document.documentElement.style.fontSize = '16px';
	}else document.documentElement.style.fontSize = kk * 100 / designWidth + 'px'
	console.log('1rem=' + kk * 100 / designWidth + 'px')
}
change()
// 窗口事件
window.onresize = function () {
	change()
}
// 横竖屏事件
window.onorientationchange = function () {
	change()
}