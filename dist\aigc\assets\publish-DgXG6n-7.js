import{k as Q,A as S,c as $,o as V,b as n,a as t,F as N,s as Y,n as be,m as K,p as E,i as fe,t as P,K as ye,w as c,a1 as Be,ax as Je,_ as ee,f as A,I as xe,V as O,d as re,W as ce,Y as pe,q,a7 as qe,r as Qe,J as We,l as de,E as Ve,e as ke,ar as we,v as G,aF as Ge,al as Ce,am as De,j as M,aL as $e,T as Se,h as Le,Q as F,y as Ie,u as Ue,a2 as Te,aq as Ke,B as Ee,aQ as Xe,aR as Ze,ag as et,a3 as tt}from"./index-BBeD0eDz.js";/* empty css                  *//* empty css               *//* empty css                    *//* empty css                    */import{_ as lt}from"./AddMaterial-C0wyZ-zd.js";/* empty css                     */import"./input-delete-BxT9zbwn.js";import{E as ot,_ as at,V as st}from"./video-cover-DBcJ77EJ.js";import{I as it}from"./ImportFromMaterial-lo8a_mzn.js";import{f as nt,b as oe}from"./common-DBXWCL9C.js";/* empty css                */import{u as ae}from"./shortVideo-BIt_F0ml.js";/* empty css               *//* empty css                */import{A as dt}from"./AddAccount-C5BKmRJu.js";import{Q as ut,T as rt}from"./TabsWrapper-Kt3D1kC9.js";import{u as je,a as Fe,b as Ae,c as ct,d as pt}from"./List-CZfQXqH2.js";import{a as Ne}from"./request-Ciyrqj7N.js";import{T as mt}from"./TitleTags-CufboRUd.js";/* empty css                 *//* empty css                */const vt={class:"video-list w-full"},ft={class:"video-grid flex flex-wrap"},_t={class:"video-item"},gt=["onClick"],ht={class:"video-thumbnail"},bt=["src"],yt=["onClick"],xt={class:"video-bottom absolute left-0 bottom-0 flex items-center justify-between"},Vt={class:"video-duration"},kt={key:0,class:"select-icon"},wt=Q({__name:"VideoList",props:{videoList:{},selectedVideoId:{}},emits:["update:videoList","update:selectedVideoId","delete"],setup(d,{emit:w}){const C=d,f=w,h=e=>{console.log("导入视频",e),e.length&&f("update:selectedVideoId",e[0].id),e=e.filter(L=>!C.videoList.find(m=>m.id===L.id)),f("update:videoList",[...C.videoList,...e])},b=S(!1),s=S({id:"",url:"",thumbnail:"",title:""}),k=e=>{s.value={id:e.id,url:e.url_wz||e.url||"",thumbnail:e.cover_wz||e.cover||"",title:e.title||`视频 ${e.id}`},b.value=!0},u=()=>{console.log("视频播放结束")},_=e=>{f("update:selectedVideoId",e)},r=e=>{var m;console.log("删除视频",e);let L=C.videoList.filter(a=>a.id!==e);f("update:videoList",L),C.selectedVideoId===e&&f("update:selectedVideoId",((m=L[0])==null?void 0:m.id)||0)};return(e,L)=>{const m=ye;return V(),$("div",vt,[n("div",ft,[n("div",_t,[t(it,{theme:"article",label:"选择视频上传",btn2Text:"本地上传","active-index":"video",imgWidth:"100%",imgHeight:"100%",type:"video",size:"small",onSubmit:h})]),(V(!0),$(N,null,Y(e.videoList,(a,l)=>(V(),$("div",{key:l,class:be(["video-item",{"video-select-mask":e.selectedVideoId==a.id}]),onClick:D=>_(a.id)},[n("div",ht,[n("img",{src:a.cover_wz||a.cover||E(ot),alt:"视频缩略图"},null,8,bt),n("div",{class:"video-play-icon",onClick:fe(D=>k(a),["stop"])},L[1]||(L[1]=[n("img",{src:at,alt:""},null,-1)]),8,yt),n("div",xt,[n("div",Vt,P(E(nt)(a.times)),1),t(m,{class:"delete-icon cursor-pointer",size:16,color:"#fff",onClick:fe(D=>r(a.id),["stop"])},{default:c(()=>[t(E(Be))]),_:2},1032,["onClick"])]),e.selectedVideoId==a.id?(V(),$("div",kt,[t(m,{class:"check-icon"},{default:c(()=>[t(E(Je))]),_:1})])):K("",!0)])],10,gt))),128))]),t(st,{visible:b.value,"onUpdate:visible":L[0]||(L[0]=a=>b.value=a),"video-url":s.value.url,poster:s.value.thumbnail,title:s.value.title||"视频播放",onEnded:u},null,8,["visible","video-url","poster","title"])])}}}),Ct=ee(wt,[["__scopeId","data-v-2c3d066d"]]),Dt={class:"dialog-content h-[40vh] mt-[20px]"},$t=Q({__name:"AddAccountDialog",props:{mediaType:{type:[Number,String],default:1}},emits:["success"],setup(d,{expose:w,emit:C}){const f=C,h=S(!1),b=()=>{h.value=!1,f("success")};return w({openDialog:()=>{h.value=!0}}),(k,u)=>{const _=xe;return V(),A(_,{title:"选择平台账号进行授权",modelValue:h.value,"onUpdate:modelValue":u[0]||(u[0]=r=>h.value=r),"align-center":"",width:"80%"},{default:c(()=>[n("div",Dt,[t(dt,{onSuccess:b,mediaType:d.mediaType},null,8,["mediaType"])])]),_:1},8,["modelValue"])}}}),St={class:"account-list","element-loading-background":"rgba(255, 255, 255, 0.7)"},Lt={class:"account-grid"},It={class:"add-account-section"},Ut=["onClick"],Tt={key:0,class:"select-icon small"},Et={class:"platform-icon"},jt=["src"],Ft={key:1,class:"platform-placeholder"},At={class:"account-info"},Nt={class:"account-avatar"},zt=["src","alt"],Mt={class:"account-name"},Ot=Q({__name:"AccountList",props:{mediaType:{type:[String,Number],default:""},modelValue:{type:Array,default:()=>[]},multiple:{type:Boolean,default:!0},showSelect:{type:Boolean,default:!0}},emits:["update:modelValue","change"],setup(d,{emit:w}){const C=S(),{platformList:f,fetchPlatformList:h,getAccountList:b,accountList:s,accountListLoading:k}=ae(),u=d,_=w,r=S([]),e=()=>new Promise(async l=>{let D={type:u.mediaType};r.value.length&&(D.media_id=r.value.join(",")),console.log("获取账号列表参数：",D),await b(D),l(!0)}),L=l=>{console.log("选择的平台：",l),e()};O(()=>u.mediaType,l=>{let D={};l&&(D.type=l),h(D)},{immediate:!0}),O(()=>u.modelValue,async l=>{l&&(console.log("选中的账号ID:",l),console.log("accountList.value",s.value),s.value.length||await e(),s.value.forEach(D=>{console.log(l.includes(D.id)),D.selected=l.includes(D.id)}))},{immediate:!0}),O(()=>u.showSelect,l=>{},{immediate:!0});const m=l=>{u.multiple?l.selected=!l.selected:s.value.forEach(v=>{v.selected=v.id===l.id?!v.selected:!1});const D=s.value.filter(v=>v.selected).map(v=>v.id);_("update:modelValue",D),_("change",D)},a=()=>{console.log("添加账号:"),u.showSelect&&C.value.openDialog()};return(l,D)=>{const v=pe,H=ce,U=re,B=ye,R=Qe("Check"),te=We;return V(),$("div",null,[t(U,{label:"选择账号：",prop:"platform","label-position":"left"},{default:c(()=>[d.showSelect?(V(),A(H,{key:0,modelValue:r.value,"onUpdate:modelValue":D[0]||(D[0]=x=>r.value=x),multiple:"",placeholder:"选择平台",onChange:L},{default:c(()=>[(V(!0),$(N,null,Y(E(f),x=>(V(),A(v,{key:x.id,label:x.title,value:x.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])):K("",!0)]),_:1}),t(U,{label:"",prop:"accounts","label-position":"left"},{default:c(()=>[q((V(),$("div",St,[n("div",Lt,[n("div",It,[n("div",{class:"add-account-card",onClick:a},[t(B,{class:"add-icon"},{default:c(()=>[t(E(qe))]),_:1}),D[1]||(D[1]=n("span",{class:"add-text"},"添加授权账号",-1))])]),(V(!0),$(N,null,Y(E(s),x=>(V(),$("div",{key:x.id,class:be(["account-card",{selected:x.selected}]),onClick:o=>m(x)},[x.selected?(V(),$("div",Tt,[t(B,{class:"check-icon"},{default:c(()=>[t(R)]),_:1})])):K("",!0),n("div",Et,[x.media&&x.media.icon?(V(),$("img",{key:0,src:x.media.icon,alt:"平台图标"},null,8,jt)):(V(),$("div",Ft))]),n("div",At,[n("div",Nt,[n("img",{src:x.avatar,alt:x.title},null,8,zt)]),n("div",Mt,P(x.title),1)])],10,Ut))),128))])])),[[te,E(k)]])]),_:1}),t($t,{ref_key:"addAccountDialogRef",ref:C,"media-type":d.mediaType,onSuccess:e},null,8,["media-type"])])}}}),ze=ee(Ot,[["__scopeId","data-v-bbedab92"]]),Pt={class:"flex h-full"},Yt={class:"bg-secondary h-full p-[20px] w-1/2 mr-[20px] flex flex-col"},Ht={class:"h-full"},Rt={class:"bg-secondary h-full p-[20px] flex-1 flex flex-col"},Bt={class:"flex-1 overflow-y-auto"},Jt={class:"w-full"},qt={class:"flex items-center"},Qt={class:"flex flex-wrap push-settings bg-[#FFFFFF] border p-[20px]"},Wt={class:"flex items-center"},Gt={class:"btn-wrapper flex items-center mt-[16px]"},Kt=Q({__name:"PublishVideo",props:{type:{type:Number,default:1},detailId:{type:[Number,String],default:""}},setup(d,{expose:w}){const{publishTimes:C,publishIntervals:f,accountSettings:h,addPublishTask:b}=ae(),{getMaterialListById:s}=je(),k=Ie(),u=Ue(),_=d,r=de(()=>_.type==3?"标题":"帐号与标题/简介"),e=de(()=>_.type==1),L=g=>g.getTime()<Date.now()-864e5,m=S([]),a=S(0),l=S({title:`自动发布${oe()}`,type:1,sync:1,sendlog:[],is_caogao:1}),D={1:{send_set:1,send_time:"",jg_time:"",jg_time_set:1,jg_set:1},3:{send_num:1}};O(()=>_.type,g=>{g!=2&&(l.value.type=g,g==3?l.value.title=`扫码发布${oe()}`:l.value.title=`自动发布${oe()}`,l.value={...l.value,...JSON.parse(JSON.stringify(D[g]))},console.log("formData",l.value))},{immediate:!0}),S([]);const v=S([]),H=S({title:"",intro:"",media_auth_id:[]}),U=S({title:"",intro:"",media_auth_id:[]}),B=g=>{R()},R=()=>{console.log("accountSettingChange",U.value),v.value.length&&(l.value.sync==1?v.value.forEach(g=>{g.title=U.value.title,g.intro=U.value.intro,g.media_auth_id=U.value.media_auth_id}):v.value.forEach(g=>{g.video_id==a.value&&(g.title=U.value.title,g.intro=U.value.intro,g.media_auth_id=U.value.media_auth_id)}))};O(()=>a.value,g=>{if(g){let i=v.value.find(j=>j.video_id===g);if(i)U.value=i;else{let j=JSON.parse(JSON.stringify(H.value));l.value.sync==1&&(j=JSON.parse(JSON.stringify(U.value))),v.value.push({video_id:g,...j})}}}),O(()=>m.value,g=>{g.length&&g.forEach(i=>{if(!v.value.filter(T=>T.video_id===i.id).length){let T=JSON.parse(JSON.stringify(H.value));l.value.sync==1&&(T=JSON.parse(JSON.stringify(U.value))),v.value.push({video_id:i.id,...T})}})});const te=()=>{R()},x=g=>{console.log("选中的账号ID:",g),R()},o=()=>{l.value.is_caogao=2,J()},I=()=>{l.value.is_caogao=1,J()},z=()=>{console.log("生成发布二维码"),l.value.is_caogao=1,J()},J=()=>{if(console.log("发布任务：",l.value),console.log("logList",v.value),!v.value.length){F.error("请选择视频");return}if(_.type==3){if(v.value.some(i=>!i.title)){F.error("请输入标题");return}}else if(v.value.some(i=>!i.media_auth_id.length||!i.title||!i.intro)){F.error("请选择账号、输入标题/简介");return}b({...l.value,sendlog:v.value}).then(g=>{console.log("发布任务：",g);let i={message:"",tab:""};if(l.value.is_caogao==1&&_.type==1?(i.message="发布任务已提交，您可以在发布任务列表中查看发布进度",i.tab="published"):(i.message="保存成功",i.tab="draft"),_.type==3&&l.value.is_caogao==1){W(g);return}F({message:i.message,type:"success",duration:1500,onClose:()=>{k.push({path:`/short-video?tab=${i.tab}`})}})})},X=S(!1),le=S(""),W=g=>{le.value=g,X.value=!0},Z=()=>{k.push({path:"/short-video"})},se=g=>{console.log(g,"initData",_.type),l.value=g.json_res,l.value.id=g.id,v.value=g.json_res.sendlog,m.value=g.video_list,console.log("logList",v.value),console.log("videoList",m.value),console.log("formData",l.value),a.value=m.value.length?m.value[0].id:0};return O(()=>u.query,async(g,i)=>{const{videos:j,type:T}=g;if(j&&Number(T)==_.type){const p=await s({id:j});m.value=p}},{immediate:!0}),w({initData:se}),(g,i)=>{const j=ke,T=re,p=we,Oe=Ge,me=De,ve=Ce,Pe=$e,Ye=pe,He=ce,ie=Le,Re=Ve;return V(),A(Re,{class:"h-full",modelValue:l.value,"onUpdate:modelValue":i[14]||(i[14]=y=>l.value=y),"label-position":"top"},{default:c(()=>[n("div",Pt,[n("div",Yt,[t(T,{label:"发布任务名称",prop:"title"},{default:c(()=>[t(j,{modelValue:l.value.title,"onUpdate:modelValue":i[0]||(i[0]=y=>l.value.title=y),placeholder:"请输入任务名称",maxlength:50,"show-word-limit":""},null,8,["modelValue"])]),_:1}),t(T,{label:"上传视频：",prop:"videoList",class:"video-wrapper flex-1 overflow-hidden !mb-0"},{default:c(()=>[n("div",Ht,[t(Ct,{class:"h-full overflow-y-auto","video-list":m.value,"onUpdate:videoList":i[1]||(i[1]=y=>m.value=y),selectedVideoId:a.value,"onUpdate:selectedVideoId":i[2]||(i[2]=y=>a.value=y)},null,8,["video-list","selectedVideoId"])])]),_:1})]),n("div",Rt,[n("div",Bt,[t(T,{label:`将${r.value}设置同步至所有视频：`,prop:"isSync","label-position":"left"},{default:c(()=>[t(p,{modelValue:l.value.sync,"onUpdate:modelValue":i[3]||(i[3]=y=>l.value.sync=y),"active-value":1,"inactive-value":0,"active-text":"开","inactive-text":"关","inline-prompt":"",onChange:te},null,8,["modelValue"])]),_:1},8,["label"]),q(t(ze,{modelValue:U.value.media_auth_id,"onUpdate:modelValue":i[4]||(i[4]=y=>U.value.media_auth_id=y),multiple:!0,mediaType:1,onChange:x},null,8,["modelValue"]),[[G,d.type==1]]),t(T,{label:`标题${e.value?"/简介":""}：`,prop:"title",class:"!mb-[6px]"},{default:c(()=>[n("div",Jt,[t(j,{modelValue:U.value.title,"onUpdate:modelValue":i[5]||(i[5]=y=>U.value.title=y),placeholder:"请输入视频标题，根据各平台规则，超出将自动截取。建议在30字以内",maxlength:30,"show-word-limit":"",onInput:B},null,8,["modelValue"]),e.value?(V(),A(j,{key:0,class:"mt-[10px]",type:"textarea",modelValue:U.value.intro,"onUpdate:modelValue":i[6]||(i[6]=y=>U.value.intro=y),onInput:B,placeholder:"请输入视频简介，根据各平台规则，超出将自动截取。建议在300字以内",rows:4,maxlength:300,"show-word-limit":""},null,8,["modelValue"])):K("",!0)])]),_:1},8,["label"]),d.type==3?(V(),$(N,{key:0},[t(T,{label:"全网抖音账号扫码发布：",class:"!mt-[20px]"},{default:c(()=>i[15]||(i[15]=[n("div",{class:"tips-wrapper"},[n("p",{class:"tips"},"任何抖音账号都可通过扫描生成的二维码进行视频发布（二维码仅对本次发布任务生效）"),n("p",{class:"tips"},"说明：扫码领取发布任务后需及时发布，若长时间未发布，2小时后将会允许他人再次发布")],-1)])),_:1}),t(T,{label:"单账号可发数量：",prop:"send_set","label-position":"left",class:"!mb-[6px]"},{default:c(()=>[n("div",qt,[t(Oe,{modelValue:l.value.send_num,"onUpdate:modelValue":i[7]||(i[7]=y=>l.value.send_num=y),precision:0,min:1,max:100,class:"w-[100px]"},null,8,["modelValue"]),i[16]||(i[16]=n("span",{class:"text-[15px] text-[#555555] ml-[12px]"},"条",-1)),i[17]||(i[17]=n("span",{class:"text-[14px] text-[#999999] ml-[12px]"},"(支持1-100条)",-1))])]),_:1})],64)):(V(),$(N,{key:1},[t(T,{label:"发布设置：",prop:"send_set","label-position":"left",class:"!mb-[6px]"},{default:c(()=>[t(ve,{modelValue:l.value.send_set,"onUpdate:modelValue":i[8]||(i[8]=y=>l.value.send_set=y)},{default:c(()=>[(V(!0),$(N,null,Y(E(C),y=>(V(),A(me,{key:y.value,value:y.value},{default:c(()=>[M(P(y.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(Se,{name:"el-fade-in-linear"},{default:c(()=>[q(n("div",Qt,[t(T,{class:"mr-[30px]",label:"发布时间：",prop:"send_time","label-position":"left"},{default:c(()=>[t(Pe,{modelValue:l.value.send_time,"onUpdate:modelValue":i[9]||(i[9]=y=>l.value.send_time=y),type:"datetime","disabled-date":L,format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"选择发布时间"},null,8,["modelValue"])]),_:1}),t(T,{class:"",label:"每条视频间隔时长：",prop:"publish_interval","label-position":"left"},{default:c(()=>[n("div",Wt,[t(j,{type:"number",modelValue:l.value.jg_time,"onUpdate:modelValue":i[10]||(i[10]=y=>l.value.jg_time=y),placeholder:"发布间隔"},null,8,["modelValue"]),t(He,{class:"ml-[10px] !w-[120px]",modelValue:l.value.jg_time_set,"onUpdate:modelValue":i[11]||(i[11]=y=>l.value.jg_time_set=y)},{default:c(()=>[(V(!0),$(N,null,Y(E(f),y=>(V(),A(Ye,{key:y.value,value:y.value,label:y.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])])]),_:1}),t(T,{class:"w-full !mb-0"},{default:c(()=>[t(ve,{modelValue:l.value.jg_set,"onUpdate:modelValue":i[12]||(i[12]=y=>l.value.jg_set=y)},{default:c(()=>[(V(!0),$(N,null,Y(E(h),y=>(V(),A(me,{key:y.value,value:y.value},{default:c(()=>[M(P(y.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})],512),[[G,l.value.send_set===2]])]),_:1}),i[18]||(i[18]=n("div",{class:"tips-wrapper mt-[16px]"},[n("p",{class:"tips"},"抖音平台定时发布规则：支持2小时后及14天内的定时发布。"),n("p",{class:"tips"},"创建定时发送任务后，若需要修改定时时间、取消定时发布计划，需前往抖音创作者中心进行操作！")],-1))],64))]),n("div",Gt,[t(ie,{class:"w-[140px] !h-[38px] !text-[var(--el-color-primary)] !border-[var(--el-color-primary)] !text-[16px]",onClick:o},{default:c(()=>i[19]||(i[19]=[M("保存草稿")])),_:1}),d.type==3?(V(),$(N,{key:0},[t(ie,{type:"primary",class:"w-[140px] !h-[38px] !text-[16px]",onClick:z},{default:c(()=>i[20]||(i[20]=[M("生成发布二维码")])),_:1}),i[21]||(i[21]=n("span",{class:"text-[14px] text-[#999] ml-[10px]"},"*扫码发布视频仅支持抖音平台",-1))],64)):(V(),A(ie,{key:1,type:"primary",class:"w-[140px] !h-[38px] !text-[16px]",onClick:I},{default:c(()=>i[22]||(i[22]=[M("提交发布任务")])),_:1}))])])]),t(ut,{modelValue:X.value,"onUpdate:modelValue":i[13]||(i[13]=y=>X.value=y),"qr-code-data":le.value,"cancel-text":"返回发布列表",onCancel:Z,"object-title":l.value.title},null,8,["modelValue","qr-code-data","object-title"])]),_:1},8,["modelValue"])}}}),Xt=ee(Kt,[["__scopeId","data-v-e44c33cd"]]),Me=d=>d+"_"+(Date.now()+Math.floor(Math.random()*1e6)),ne=()=>{const d=typeof window<"u"?window:global;return d&&"tinymce"in d?d.tinymce:null};function _e(d){return d?d.getContent():""}function ge(d,w){w&&w.setContent(d)}function Zt(d,w){if(w){if(w.resetContent)return w.resetContent(d);w.setContent(d),w.setDirty(!1),w.undoManager.clear()}}function he(d,w=!0){d&&d.mode.set(w?"readonly":"design")}async function el(d,w,C,f){let{custom_images_upload_callback:h}=d||{},b=new FormData;console.log(w,"blobInfo");let s=w.file||w.blob(),k=s?s.name:w.filename();b.append("file",s),b.append("token",Ne.get("token")),console.log(b instanceof File);const u=Te.service({lock:!0,text:"0",background:"rgba(255, 255, 255, 0.7)"});try{Fe(b,{headers:{"content-type":"application/x-www-form-urlencoded"},onUploadProgress:_=>{let r=_.loaded/_.total*100|0;""+r?u.setText("已上传"+r+"%"):u.setText("上传中...")}}).then(_=>{if(_){F.success(k+"上传成功");let r="https://images.china9.cn/"+_,e=typeof h=="function"?h(r):r;Ae({title:k,category_id:0,flod_id:0,type:1,bigtype:1,filetype:2,url:_,size:s.size}),C(e)}else C(""),F.error("上传失败")}).catch(_=>{F.error("上传失败！"),console.log(_)}).finally(()=>{u.close()})}catch{u.close()}}function tl(d,w,C,f){let{upload_callback:h,custom_video_upload_callback:b}=d||{};if(d.file_picker_types==="media"){let s=document.createElement("input");s.setAttribute("type","file"),s.onchange=function(){let k=this.files[0];if(!k.type.includes("video")&&!k.type.includes("audio"))return F.error("请选择视频或视频文件");let u=new FormData,_=k.name;u.append("file",k),u.append("token",Ne.get("token"));const r=Te.service({lock:!0,text:"0",background:"rgba(255, 255, 255, 0.7)"});try{const e=a=>{if(a.type.includes("video"))return"video";if(a.type.includes("audio"))return"audio";if(a.type.includes("image"))return"image"},L=a=>{if(a.type.includes("video"))return 1;if(a.type.includes("audio"))return 3;if(a.type.includes("image"))return 2};(e(k)==="video"?ct:Fe)(u,{headers:{"content-type":"application/x-www-form-urlencoded"},onUploadProgress:a=>{let l=a.loaded/a.total*100|0;""+l?r.setText("已上传"+l+"%"):r.setText("上传中...")}}).then(a=>{if(a){F.success(a.msg||"上传成功");let l="https://images.china9.cn/"+a,D=typeof b=="function"?e(k)==="video"?b(a.url_wz):b(l):e(k)==="video"?a.url_wz:l;const v={title:_,category_id:0,flod_id:0,type:1,bigtype:1,filetype:L(k)};e(k)==="video"?(v.url=a.url,v.size=k.size,v.times=a.times,v.cover=a.cover,v.fbl=a.fbl,v.fps=a.fps,v.zhb=a.zhb):(v.url=a,v.size=k.size,v.times="",v.cover=a.url,v.fbl="",v.fps="",v.zhb=""),Ae(v).then(H=>{e(k)==="video"&&pt({id:H})}),w(D)}else F.error("上传失败")}).catch(a=>{F.error("上传失败！"),console.log(a)}).finally(()=>{r.close()})}catch{r.close()}},s.click()}}const ll=()=>{let d={status:{},loadedCallbacks:{}};const w=(h,b)=>{const s=document.createElement("script");s.id=Me("tiny-script"),s.type="application/javascript",s.src=h,s.referrerPolicy="origin";const k=()=>{s.removeEventListener("load",k),b()};s.addEventListener("load",k),(document.head||document.body).appendChild(s)},C=h=>{d.loadedCallbacks[h]&&(d.loadedCallbacks[h].forEach(b=>{typeof b.handler=="function"&&b.handler.call(b.scope)}),d.loadedCallbacks[h]=void 0)};return{load:(h,b,s)=>{if(b&&(d.loadedCallbacks[h]||(d.loadedCallbacks[h]=[]),d.loadedCallbacks[h].push({handler:b,scope:s||void 0})),d.status[h]==="LOADED"){C(h);return}d.status[h]!=="LOADING"&&(d.status[h]="LOADING",w(h,()=>{d.status[h]="LOADED",C(h)}))}}},ol=ll(),al=["id"],sl={key:0},il={name:"Vue3Tinymce"},ue=Object.assign(il,{props:{modelValue:String,setting:{type:Object,default:()=>({})},setup:Function,disabled:Boolean,scriptSrc:String,debug:Boolean},emits:["update:modelValue","init","change"],setup(d,{expose:w,emit:C}){const f=d,h=C;let b=!0;const s=Ke({editor:null,id:Me("tinymce"),err:""}),k=()=>String(f.modelValue??""),u=m=>h("update:modelValue",m),_=(m,a,l)=>{f.debug&&console.log(`来自：${m.type} | 
 ${a} 
 ${l||"--"}`)},r=(m,a)=>{a||(a=s.editor);const l=_e(a);_(m,l),u(l),h("change",l)},e=m=>{s.editor=m,ge(k(),m),f.disabled&&m.mode.get()!=="readonly"&&he(m),m.on("change input undo redo",a=>{r(a,m)}),h("init",m)},L=()=>{if(ne()===null){s.err="tinymce is null";return}f.debug&&console.warn("vue3-tinymce 进入debug模式");let m={...f.setting,selector:"#"+s.id,setup:a=>{f.setup&&f.setup(a),a.on("init",()=>e(a))}};f.setting.custom_images_upload&&(m.images_upload_handler=(...a)=>{el.apply(null,[f.setting||{},...a])}),f.setting.custom_images_upload&&(m.file_picker_callback=(...a)=>{tl.apply(null,[f.setting||{},...a])}),ne().init(m),b=!1};return O(()=>f.modelValue,(m,a)=>{if(!(!s.editor||!s.editor.initialized)&&!(a===m||m===_e(s.editor))){if(_({type:"propsChanged to setContent"},m,a),m===null)return Zt("",s.editor);ge(k(),s.editor)}}),O(()=>f.disabled,m=>{!s.editor||!s.editor.initialized||he(s.editor,m)}),w({id:s.id,editor:s.editor}),Ee(()=>{if(ne()!==null){L();return}const m=f.scriptSrc??"./tinymce/tinymce.min.js";ol.load(m,L)}),Xe(()=>{b||L()}),Ze(()=>{s.editor&&s.editor.remove()}),et(()=>{s.editor&&s.editor.remove()}),(m,a)=>(V(),$(N,null,[n("div",{id:s.id,class:"tiny-textarea"},null,8,al),s.err?(V(),$("p",sl,P(s.err),1)):K("",!0)],64))}});ue.install=function(d){d.component("Vue3Tinymce",ue)};const nl=Q({__name:"Index",props:{height:{default:"500px"},modelValue:{default:""}},emits:["update:modelValue"],setup(d,{emit:w}){const C=d,f=w,h=S({setting:{height:C.height,language:"zh_CN",menubar:"file edit insert view format tools help",menu:{},toolbar:"undo redo | fullscreen | formatselect alignleft aligncenter alignright alignjustify | link unlink | numlist bullist | image axupimgs media table | fontsizeselect forecolor backcolor | bold italic underline strikethrough | indent indent2em outdent | superscript subscript | removeformat | tpLayout",toolbar_drawer:"sliding",quickbars_selection_toolbar:"removeformat | bold italic underline strikethrough | fontsizeselect forecolor backcolor | code",plugins:"link image media table lists fullscreen quickbars code axupimgs indent2em tpLayout",fontsize_formats:"12px 14px 16px 18px 20px 22px 24px 26px 28px 30px 32px 34px 36px 38px 40px 42px 44px 46px 48px 50px",lineheight_formats:"1 1.5 2 2.5 3 3.5 4 4.5 5",default_link_target:"_blank",link_title:!1,nonbreaking_force_tab:!0,language_url:"./tinymce/langs/zh-Hans.js",custom_images_upload:!0,images_upload_url:"",custom_images_upload_callback:s=>s,custom_video_upload:!0,file_picker_types:"media",custom_video_upload_callback:s=>s}}),b=de({get:()=>C.modelValue,set:s=>{f("update:modelValue",s)}});return(s,k)=>(V(),A(E(ue),{modelValue:b.value,"onUpdate:modelValue":k[0]||(k[0]=u=>b.value=u),setting:h.value.setting},null,8,["modelValue","setting"]))}}),dl={class:"flex h-full"},ul={class:"bg-secondary h-full p-[20px] w-1/2 mr-[20px] flex flex-col"},rl={class:"flex-1 content-wrapper flex flex-col overflow-hidden"},cl={class:"editor-wrapper flex-1 overflow-hidden"},pl={class:"preview-btn flex items-center justify-center mt-[20px]"},ml={class:"bg-secondary h-full p-[20px] flex-1 flex flex-col"},vl={class:"flex-1 overflow-y-auto"},fl={class:"flex items-center w-[320px] h-[200px] bg-[#FFFFFF]"},_l={class:"w-full"},gl={class:"flex items-center"},hl={class:"flex flex-wrap push-settings bg-[#FFFFFF] border p-[20px]"},bl={class:"flex items-center"},yl={class:"btn-wrapper flex items-center mt-[16px]"},xl={class:"preview-content"},Vl={class:"preview-title text-center text-[24px] font-bold mb-4"},kl={class:"preview-author text-center text-[14px] text-[#666] mb-6"},wl={class:"ml-4"},Cl=["innerHTML"],Dl=Q({__name:"PublishArticle",props:{type:{type:Number,default:2},detailId:{type:[Number,String],default:""}},setup(d,{expose:w}){const C=Ie(),{publishTimes:f,publishIntervals:h,accountSettings:b,addPublishTask:s}=ae(),k=d,u=S({title:`自动发布文章${oe()}`,type:2,send_set:1,sync:1,sendlog:[],send_time:"",jg_time:"",jg_time_set:1,jg_set:1,is_caogao:1}),_={content:"",media_auth_id:[],title:"",cover:"",author:"",intro:"",original:1},r=S(0),e=S([JSON.parse(JSON.stringify(_))]),L=S(""),m=()=>{tt(()=>{var o;const x=(o=document.querySelector(".editor-wrapper"))==null?void 0:o.clientHeight;console.log("editorWrapperHeight",x),L.value=(x||527)+"px"})},a=x=>{console.log("选择的账号ID：",x)},l=x=>{if(x==1){let o=e.value[r.value];e.value.forEach(I=>{I.cover=o.cover,I.video_id=o.video_id,I.title=o.title,I.intro=o.intro,I.media_auth_id=o.media_auth_id,I.author=o.author,I.original=o.original})}},D=x=>{console.log("封面处理：",x),e.value[r.value].video_id=x.id},v=S(!1),H=()=>{if(!e.value[r.value].content){F.warning("文章内容不能为空");return}v.value=!0},U=()=>{u.value.is_caogao=2,R()},B=()=>{u.value.is_caogao=1,R()},R=()=>{if(console.log("发布任务：",u.value),console.log("logList",e.value),!e.value.length){F.error("请添加文章");return}if(e.value.some(o=>!o.media_auth_id.length||!o.video_id||!o.title||!o.intro||!o.content||!o.author||!o.original)){F.error("请选择账号、封面、原创性，填写标题、简介、内容、作者");return}s({...u.value,sendlog:e.value}).then(o=>{console.log("发布任务：",o);let I={message:"",tab:""};u.value.is_caogao==1?(I.message="发布任务已提交，您可以在发布任务列表中查看发布进度",I.tab="published"):(I.message="保存成功",I.tab="draft"),F({message:I.message,type:"success",duration:3e3,onClose:()=>{C.push({path:`/short-video?tab=${I.tab}`})}})})};return O(()=>k.type,(x,o)=>{console.log("newVal-type-article",x),x==2&&m()}),w({initData:x=>{console.log(x,"initData"),u.value=x.json_res,u.value.id=x.id,e.value=x.json_res.sendlog||[],r.value=0,u.value.type==2&&m()}}),Ee(()=>{}),(x,o)=>{const I=ke,z=re,J=Le,X=we,le=lt,W=De,Z=Ce,se=$e,g=pe,i=ce,j=Ve,T=xe;return V(),$("div",null,[t(j,{class:"h-full",modelValue:u.value,"onUpdate:modelValue":o[16]||(o[16]=p=>u.value=p),"label-position":"top"},{default:c(()=>[n("div",dl,[n("div",ul,[t(z,{label:"发布任务名称",prop:"title"},{default:c(()=>[t(I,{modelValue:u.value.title,"onUpdate:modelValue":o[0]||(o[0]=p=>u.value.title=p),placeholder:"请输入任务名称",maxlength:50,"show-word-limit":""},null,8,["modelValue"])]),_:1}),n("div",rl,[t(mt,{ref:"titleTagsRef",modelValue:r.value,"onUpdate:modelValue":o[1]||(o[1]=p=>r.value=p),tags:e.value,"onUpdate:tags":o[2]||(o[2]=p=>e.value=p),"max-visible-tags":4,"default-data":_,"tag-text":"文章",editable:!0},null,8,["modelValue","tags"]),n("div",cl,[L.value?(V(),A(nl,{key:0,modelValue:e.value[r.value].content,"onUpdate:modelValue":o[3]||(o[3]=p=>e.value[r.value].content=p),height:L.value},null,8,["modelValue","height"])):K("",!0)]),n("div",pl,[t(J,{class:"",type:"primary",onClick:H,disabled:!e.value[r.value].content,icon:"View"},{default:c(()=>o[18]||(o[18]=[M("预览")])),_:1},8,["disabled"])])])]),n("div",ml,[n("div",vl,[t(z,{label:"将账号与文章设置同步至所有文章：",prop:"isSync","label-position":"left"},{default:c(()=>[t(X,{modelValue:u.value.sync,"onUpdate:modelValue":o[4]||(o[4]=p=>u.value.sync=p),"active-value":1,"inactive-value":0,"active-text":"开","inactive-text":"关","inline-prompt":"",onChange:l},null,8,["modelValue"])]),_:1}),t(ze,{modelValue:e.value[r.value].media_auth_id,"onUpdate:modelValue":o[5]||(o[5]=p=>e.value[r.value].media_auth_id=p),multiple:!0,mediaType:2,onChange:a},null,8,["modelValue"]),t(z,{label:"封面设置：",prop:"cover",class:"!mb-[6px]"},{default:c(()=>[n("div",fl,[t(le,{image:e.value[r.value].cover,"onUpdate:image":o[6]||(o[6]=p=>e.value[r.value].cover=p),onChange:D},null,8,["image"])])]),_:1}),t(z,{label:"标题/简介：",prop:"title",class:"!mb-[6px]"},{default:c(()=>[n("div",_l,[t(I,{modelValue:e.value[r.value].title,"onUpdate:modelValue":o[7]||(o[7]=p=>e.value[r.value].title=p),placeholder:"请输入文章标题",maxlength:50,"show-word-limit":""},null,8,["modelValue"]),t(I,{class:"mt-[10px]",type:"textarea",modelValue:e.value[r.value].intro,"onUpdate:modelValue":o[8]||(o[8]=p=>e.value[r.value].intro=p),placeholder:"请输入文章简介，根据各平台规则，超出将自动截取。建议在300字以内",rows:4,maxlength:300,"show-word-limit":""},null,8,["modelValue"])])]),_:1}),n("div",gl,[t(z,{label:"作者：",prop:"author","label-position":"left"},{default:c(()=>[t(I,{modelValue:e.value[r.value].author,"onUpdate:modelValue":o[9]||(o[9]=p=>e.value[r.value].author=p),placeholder:"请输入作者名称",maxlength:30,"show-word-limit":""},null,8,["modelValue"])]),_:1}),t(z,{class:"ml-[20px]",label:"来源：",prop:"original","label-position":"left"},{default:c(()=>[t(Z,{modelValue:e.value[r.value].original,"onUpdate:modelValue":o[10]||(o[10]=p=>e.value[r.value].original=p)},{default:c(()=>[t(W,{label:"原创",value:1},{default:c(()=>o[19]||(o[19]=[M("原创")])),_:1}),t(W,{label:"非原创",value:2},{default:c(()=>o[20]||(o[20]=[M("非原创")])),_:1})]),_:1},8,["modelValue"])]),_:1})]),t(z,{label:"发布设置：",prop:"settings","label-position":"left",class:"!mb-[6px]"},{default:c(()=>[t(Z,{modelValue:u.value.send_set,"onUpdate:modelValue":o[11]||(o[11]=p=>u.value.send_set=p)},{default:c(()=>[(V(!0),$(N,null,Y(E(f),p=>(V(),A(W,{key:p.value,value:p.value},{default:c(()=>[M(P(p.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(Se,{name:"el-fade-in-linear"},{default:c(()=>[q(n("div",hl,[t(z,{label:"发布时间：",prop:"publishTime","label-position":"left"},{default:c(()=>[t(se,{modelValue:u.value.send_time,"onUpdate:modelValue":o[12]||(o[12]=p=>u.value.send_time=p),type:"datetime",placeholder:"选择发布时间",format:"YYYY-MM-DD hh:mm:ss","value-format":"YYYY-MM-DD hh:mm:ss"},null,8,["modelValue"])]),_:1}),t(z,{class:"ml-[30px]",label:"每条文章间隔时长：",prop:"publish_interval","label-position":"left"},{default:c(()=>[n("div",bl,[t(I,{type:"number",modelValue:u.value.jg_time,"onUpdate:modelValue":o[13]||(o[13]=p=>u.value.jg_time=p),placeholder:"发布间隔"},null,8,["modelValue"]),t(i,{class:"ml-[10px]",modelValue:u.value.jg_time_set,"onUpdate:modelValue":o[14]||(o[14]=p=>u.value.jg_time_set=p)},{default:c(()=>[(V(!0),$(N,null,Y(E(h),p=>(V(),A(g,{key:p.value,value:p.value,label:p.label},null,8,["value","label"]))),128))]),_:1},8,["modelValue"])])]),_:1}),t(z,{class:"w-full !mb-0"},{default:c(()=>[t(Z,{modelValue:u.value.jg_set,"onUpdate:modelValue":o[15]||(o[15]=p=>u.value.jg_set=p)},{default:c(()=>[(V(!0),$(N,null,Y(E(b),p=>(V(),A(W,{key:p.value,value:p.value},{default:c(()=>[M(P(p.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})],512),[[G,u.value.send_set===2]])]),_:1})]),n("div",yl,[t(J,{class:"w-[140px] !h-[38px] !text-[var(--el-color-primary)] !border-[var(--el-color-primary)] !text-[16px]",onClick:U},{default:c(()=>o[21]||(o[21]=[M("保存草稿")])),_:1}),t(J,{type:"primary",class:"w-[140px] !h-[38px] !text-[16px]",onClick:B},{default:c(()=>o[22]||(o[22]=[M("提交发布任务")])),_:1})])])])]),_:1},8,["modelValue"]),t(T,{modelValue:v.value,"onUpdate:modelValue":o[17]||(o[17]=p=>v.value=p),title:"文章预览",width:"800px","destroy-on-close":"",class:"preview-dialog"},{default:c(()=>[n("div",xl,[n("h1",Vl,P(e.value[r.value].title||"未设置标题"),1),n("div",kl,[n("span",null,"作者："+P(e.value[r.value].author||"未设置作者"),1),n("span",wl,"发布时间："+P(new Date().toLocaleDateString()),1)]),n("div",{class:"preview-body rich-text-content",innerHTML:e.value[r.value].content},null,8,Cl)])]),_:1},8,["modelValue"])])}}}),$l=ee(Dl,[["__scopeId","data-v-d9592e8b"]]),Sl={class:"publish-wrap h-full flex flex-col"},Ll={class:"select-view-content-item flex items-center justify-center mb-[17px]"},Il=Q({__name:"publish",setup(d){const{types:w,getTaskDetail:C}=ae();je();const f=S(1),h=Ue(),b=S(),s=S(),k=S();O(()=>h.query,async(_,r)=>{if(console.log("newVal",_),_.id&&_.type){console.log("newVal",_),f.value=Number(_.type),b.value=Number(_.id);const e=await C(b.value);console.log("result",e),f.value==2?k.value&&k.value.initData(e):s.value&&s.value.initData(e)}},{immediate:!0});const u=_=>{console.log(_)};return(_,r)=>(V(),$("div",Sl,[n("div",Ll,[q(t(rt,{modelValue:f.value,"onUpdate:modelValue":r[0]||(r[0]=e=>f.value=e),items:E(w),onChange:u},null,8,["modelValue","items"]),[[G,!b.value]])]),q(t(Xt,{class:"flex-1 overflow-hidden",ref_key:"publishVideoRef",ref:s,type:f.value,detailId:b.value},null,8,["type","detailId"]),[[G,f.value===1||f.value===3]]),q(t($l,{class:"flex-1 overflow-hidden",ref_key:"publishArticleRef",ref:k,detailId:b.value,type:f.value},null,8,["detailId","type"]),[[G,f.value===2]])]))}}),Zl=ee(Il,[["__scopeId","data-v-4039f41c"]]);export{Zl as default};
