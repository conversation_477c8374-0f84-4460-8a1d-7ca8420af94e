<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0;" />
  <title>资海云-营销平台1.0</title>
  <meta name="description" content="资海云营销平台1.0，基于大模型技术，实现快速建站、智能问答、内容生成、SEO优化等一站式服务。">
  <meta name="keywords" content="营销平台1.0,建站通,建站,智能问答,内容生成,SEO优化,资海云">
  <link rel=icon href="img/logo.png">
  <link rel="stylesheet" type="text/css" href="css/animate.min.css" />
  <link rel="stylesheet" type="text/css" href="css/common.css" />
  <script src="js/jquery.min.js" type="text/javascript" charset="utf-8"></script>
  <script src="./js/jweixin-1.2.0.js"></script>
  <script src="./js/weixinapi.min.js"></script>
  <!-- 资海云头部 -->
  <script>
    var environment = "pro";
  </script>
  <script type="text/javascript"
    src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/js-cookie.js?v=1734"></script>
  <script src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/homeHeader.js?v=1734"></script>
  <script src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/static/footer.js?v=1734"></script>
</head>

<body>
  <!-- 头部 -->
  <div style="background-color: #ffffff;">
    <div id="zhyHomeHeader"></div>
  </div>
  <!-- banner -->
  <div class="banner-box img-box">
    <img class="banner-bg banner ani" animate-effect="fadeIn" animate-duration="0.8s" animate-delay="0s" src="img/banner.png" alt="">
    <img class="banner-bg banner-s ani" animate-effect="fadeIn" animate-duration="0.8s" animate-delay="0s" src="img/banner-s.png" alt="">
    <div class="container banner-content">
      <img class="c-img ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.8s" src="img/b1.png" alt="">
      <h1 class="ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="1.2s">一站式AI智能营销<br />轻松赋能企业增长</h1>
    </div>
  </div>
  <!-- 企业营销面临的问题 -->
  <section class="section01">
    <div class="container">
      <div class="box-title ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0s">
        <div class="justify-center align-center">
          <img src="img/u01.png" alt="">
          <p>企业营销<span>面临的问题</span></p>
          <img src="img/u01.png" alt="">
        </div>
        <div class="s-title">企业没有推广效果令人头痛？</div>
      </div>
      <div class="s1-content flex-row">
        <div class="flex-row img-left">
          <div class="img1 ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.4s">
            <img src="img/u1-1.png" alt="">
          </div>
          <div class="img2 ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.8s">
            <img src="img/u1-2.png" alt="">
          </div>
        </div>
        <div class="s1-list-box">
          <div class="item flex-row ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="1.2s">
            <span class="i-num">01</span>
            <p class="i-text">缺乏高效的营销工具，导致内容创作、流量分析、 客户管理等工作效率低下，难以快速响应市场变化？</p>
          </div>
          <div class="item flex-row ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="1.6s">
            <span class="i-num">02</span>
            <p class="i-text">企业运营数据分散在多个平台或系统中，难以统一管 理和分析，导致决策困难？</p>
          </div>
          <div class="item flex-row ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="2s">
            <span class="i-num">03</span>
            <p class="i-text">想要营销却没有好的创意、好的点子，不知道如何在 互联网自媒体时代抢占营销先机？</p>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- 全方位解决企业营销难题 -->
  <section class="section06">
    <div class="container">
      <div class="box-title ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0s">
        <div class="justify-center align-center">
          <img src="img/u01.png" alt="">
          <p>全方位解决<span>企业营销难题</span></p>
          <img src="img/u01.png" alt="">
        </div>
        <div class="s-title">提升效果，高效获客，让营销更简单</div>
      </div>
      <!-- 内容 -->
      <div class="s6-list-box">
        <div class="item flex-row align-center ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s">
          <div class="item-img">
            <img src="img/u6-01.png" alt="">
          </div>
          <div class="item-text">
            <h3 class="item-title">3端8站12个平台+全域媒体投放</h3>
            <p class="item-desc">无需东奔西走，全网营销媒体平台投放一站式投放，营销内容高效分发，降本增效</p>
          </div>
        </div>
        <div class="item flex-row align-center ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
          <div class="item-img">
            <img src="img/u6-02.png" alt="">
          </div>
          <div class="item-text">
            <h3 class="item-title">全网营销数据分析报告</h3>
            <p class="item-desc">媒体投放数据、媒体账号与作品数据分析报告，集中赋能营销决策</p>
          </div>
        </div>
        <div class="item flex-row align-center ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s">
          <div class="item-img">
            <img src="img/u6-03.png" alt="">
          </div>
          <div class="item-text">
            <h3 class="item-title">AI宣传计划+营销方案策划</h3>
            <p class="item-desc">营销日历精准标注营销宣传热点事件，一眼掌握市场动态；一键使用AI生成对应的策划方案，助您抢占先机</p>
          </div>
        </div>
        <div class="item flex-row align-center ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
          <div class="item-img">
            <img src="img/u6-04.png" alt="">
          </div>
          <div class="item-text">
            <h3 class="item-title">各类工具赋能企业营销</h3>
            <p class="item-desc">灵感创意中心提供优质营销资源；小游戏营销与H5邀请函有效掌握私域流量市场；智能编辑器一键排版文章...更多营销技巧与工具等您来</p>
          </div>
        </div>
      </div>
      <a class="item-btn content-btn" href="#consult">立即咨询</a>
    </div>
  </section>
  <!-- 我们能为您做什么？ -->
  <section class="section02" id="section02">
    <div class="container">
      <div class="box-title ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0s">
        <div class="justify-center align-center">
          <img src="img/u01.png" alt="">
          <p>我们<span>能为您做什么？</span></p>
          <img src="img/u01.png" alt="">
        </div>
        <div class="s-title">7大行业优势 为您保驾护航</div>
      </div>
      <div class="section02-list flex-row flex-wrap">
        <div class="item ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.4s">
          <div class="item01 align-center">
            <span class="item-num">01</span>
            <span class="item-txt">高效分发</span>
          </div>
          <h3 class="item-title">解决内容：</h3>
          <p class="item-desc">支持多数主流媒体平台一键式发布，优化工作流程，削减不必要机械操作步骤，满足全场最需求，降低时间成本。</p>
          <div class="item-btn">多数主流媒体平台一键式发布</div>
          <div class="icon-box">
            <img src="img/u2-01-.png" />
          </div>
          <div class="num-box">
            <img src="img/u2-01.png" />
          </div>
        </div>
        <div class="item ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.8s">
          <div class="item01 align-center">
            <span class="item-num">02</span>
            <span class="item-txt">提升质量</span>
          </div>
          <h3 class="item-title">解决内容：</h3>
          <p class="item-desc">有效提升内容、落地页编辑与创作质量，并在流量投放过程助力企业提高关键词质量度得分，AI辅助文案/图片创作，有效提升全网曝光、咨询量。</p>
          <div class="item-btn">提高关键词质量得分</div>
          <div class="icon-box">
            <img src="img/u2-02-.png" />
          </div>
          <div class="num-box">
            <img src="img/u2-02.png" />
          </div>
        </div>
        <div class="item ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.4s">
          <div class="item01 align-center">
            <span class="item-num">03</span>
            <span class="item-txt">账号运营</span>
          </div>
          <h3 class="item-title">解决内容：</h3>
          <p class="item-desc">一次绑定无需多次登录，解决以往账号管理混乱、内容管理繁琐、推广效果差、账号数据获取统计难等问题。</p>
          <div class="item-btn">一次绑定无需多次登录</div>
          <div class="icon-box">
            <img src="img/u2-03-.png" />
          </div>
          <div class="num-box">
            <img src="img/u2-03.png" />
          </div>
        </div>
        <div class="item ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.8s">
          <div class="item01 align-center">
            <span class="item-num">04</span>
            <span class="item-txt">营销工具</span>
          </div>
          <h3 class="item-title">解决内容：</h3>
          <p class="item-desc">移动端H5在线实时生成、营销小游戏内置推广、AI营销创作助手、灵感创意中心、营销日历等多款营销工具赋能企业业务推广。</p>
          <div class="item-btn">多款营销工具赋能企业业务推广</div>
          <div class="icon-box">
            <img src="img/u2-04-.png" />
          </div>
          <div class="num-box">
            <img src="img/u2-04.png" />
          </div>
        </div>
        <div class="item ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.4s">
          <div class="item01 align-center">
            <span class="item-num">05</span>
            <span class="item-txt">节约成本</span>
          </div>
          <h3 class="item-title">解决内容：</h3>
          <p class="item-desc">利用工具推送广告效果监控体系计算的数据，合理推算浪费的广告投入并可及时提醒。</p>
          <div class="item-btn">推算浪费的广告投入</div>
          <div class="icon-box">
            <img src="img/u2-05-.png" />
          </div>
          <div class="num-box">
            <img src="img/u2-05.png" />
          </div>
        </div>
        <div class="item ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.8s">
          <div class="item01 align-center">
            <span class="item-num">06</span>
            <span class="item-txt">精准获客</span>
          </div>
          <h3 class="item-title">解决内容：</h3>
          <p class="item-desc">融合全网千万级企业数据库线索，数千个维度筛选精准客户，实时更新全网企业信息，为企业拓客降本增效。</p>
          <div class="item-btn">融合全网千万级企业数据库线索</div>
          <div class="icon-box">
            <img src="img/u2-06-.png" />
          </div>
          <div class="num-box">
            <img src="img/u2-06.png" />
          </div>
        </div>
        <div class="item ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.4s">
          <div class="item01 align-center">
            <span class="item-num">07</span>
            <span class="item-txt">数据统计</span>
          </div>
          <h3 class="item-title">解决内容：</h3>
          <p class="item-desc">灵活匹配公司业务，通过部门回款、销售排名、业绩统计等报表，提升销售管理时效。</p>
          <div class="item-btn">灵活匹配公司业务</div>
          <div class="icon-box">
            <img src="img/u2-07-.png" />
          </div>
          <div class="num-box">
            <img src="img/u2-07.png" />
          </div>
        </div>
        <div class="contact-box ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.8s">
          <div class="c-title">即刻获取AI营销网站</div>
          <div class="input-box flex-col form-data-1">
            <div class="input-wrap">
              <input class="input-item" type="text" id="name-1" value="" placeholder="联系人姓名（必填）" />
              <div class="el-form-item__error" style="display: none"></div>
            </div>
            <div class="input-wrap">
              <input class="input-item" type="text" id="telephone-1" value="" placeholder="联系人电话（必填）" />
              <div class="el-form-item__error" style="display: none"></div>
            </div>
            <div onclick="submitForm(1)" class="input-btn">立即咨询</div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- 了解建站通营销 -->
  <section class="section03">
    <div class="container">
      <div class="box-title ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0s">
        <div class="justify-center align-center">
          <img src="img/u01.png" alt="">
          <p>了解<span>营销平台1.0</span></p>
          <img src="img/u01.png" alt="">
        </div>
        <div class="s-title">为企业提升营销效果全面赋能</div>
      </div>
      <div class="content-box flex-row justify-between flex-wrap align-center professional-pc">
        <div class="section03-list ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s">
          <div class="item flex-row align-center ani" animate-effect="fadeInLeft" animate-duration="0.8s"
            animate-delay="0.4s">
            <img src="img/u3-01.png" />
            <p>营销平台1.0各类营销/策划工具，一站式解决企业营销中不会做、没人做、做不好的难题，为企业提升营销效果全面赋能。</p>
          </div>
          <div style="height: 152px;"></div>
          <div class="item flex-row align-center ani" animate-effect="fadeInLeft" animate-duration="0.8s"
            animate-delay="0.8s">
            <img src="img/u3-01.png">
            <p>有着支持企业线索、客户、订单、回款等内容 管理的专业CRM系统，全网线索精准获客，全 面提升商务销售运营效率。</p>
          </div>
        </div>
        <div class="img03-box ani" animate-effect="fadeIn" animate-duration="0.8s" animate-delay="0.8s">
          <img src="img/u3-02.png" />
        </div>
        <div class="section03-list ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
          <div class="item flex-row align-center ani" animate-effect="fadeInRight" animate-duration="0.8s"
            animate-delay="0.4s">
            <img src="img/u3-01.png" />
            <p>及时分析流量数据，提供各类新型营销手段， 帮助企业快速提升各项营销指标，企业运营数 据一目了然。</p>
          </div>
        </div>
      </div>
      <div class="content-box flex-row justify-between flex-wrap align-center professional-phone">
        <div class="img03-box ani" animate-effect="fadeIn" animate-duration="0.8s" animate-delay="0.8s">
          <img src="img/u3-02.png" />
        </div>
        <div class="section03-list ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s">
          <div class="item flex-row align-center ani" animate-effect="fadeInLeft" animate-duration="0.8s"
            animate-delay="0.4s">
            <img src="img/u3-01.png" />
            <p>营销平台1.0各类营销/策划工具，一站式解决企业营销中不会做、没人做、做不好的难题，为企业提升营销效果全面赋能。</p>
          </div>
          <div class="item flex-row align-center ani" animate-effect="fadeInLeft" animate-duration="0.8s"
            animate-delay="0.6s">
            <img src="img/u3-01.png" />
            <p>有着支持企业线索、客户、订单、回款等内容 管理的专业CRM系统，全网线索精准获客，全 面提升商务销售运营效率。</p>
          </div>
          <div class="item flex-row align-center ani" animate-effect="fadeInLeft" animate-duration="0.8s"
            animate-delay="0.8s">
            <img src="img/u3-01.png">
            <p>及时分析流量数据，提供各类新型营销手段， 帮助企业快速提升各项营销指标，企业运营数 据一目了然。</p>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- 12大核心功能 -->
  <section class="section04">
    <div class="container">
      <div class="box-title ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0s">
        <div class="justify-center align-center">
          <img src="img/u01.png" alt="">
          <p>12大核心功能</p>
          <img src="img/u01.png" alt="">
        </div>
        <div class="s-title">优秀功能升级 助力企业推广</div>
      </div>
      <div class="s4-content">
        <!-- 切换按钮 -->
        <div class="s4-tab flex-row justify-between">
          <div class="tab-item active">全域网站建设</div>
          <div class="tab-item">媒体同步</div>
          <div class="tab-item">运营报告</div>
          <div class="tab-item">AI问答</div>
          <div class="tab-item">广告平台</div>
          <div class="tab-item">小游戏营销</div>
          <div class="tab-item">宣传计划</div>
          <div class="tab-item">灵感创意</div>
          <div class="tab-item">营销日历</div>
          <div class="tab-item">H5邀请函</div>
          <div class="tab-item">CRM客户管理</div>
          <div class="tab-item">客户端分析服务</div>
        </div>
        <!-- 切换内容 -->
        <div class="s4-content-box">
          <div class="s4-content-item active flex-row align-center">
            <img class="c-img ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s" src="img/u4-i1.png" alt="">
            <div class="item ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
              <div class="top-box">
                <div class="img-text-box flex-row align-center">
                  <div class="img-box flex-row align-center justify-center">
                    <img src="img/u4-01.png" alt="">
                  </div>
                  <h3 class="s-title">全域网站建设</h3>
                </div>
              </div>
              <div class="bot-box">
                <p class="item-desc">高效适配多端网站搭建与定制，微信、百度、抖音等多平台小程序同步网站内容，对企业品牌实力、产品信息等内容全面展示</p>
                <a class="item-btn content-btn" href="#consult">立即咨询</a>
              </div>
            </div>
          </div>
          <div class="s4-content-item flex-row align-center">
            <img class="c-img ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s" src="img/u4-i2.png" alt="">
            <div class="item ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
              <div class="top-box">
                <div class="img-text-box flex-row align-center">
                  <div class="img-box flex-row align-center justify-center">
                    <img src="img/u4-02.png" alt="">
                  </div>
                  <h3 class="s-title">媒体同步</h3>
                </div>
              </div>
              <div class="bot-box">
                <p class="item-desc">抖音、快手、公众号等数十家媒体平台一键发布图文视频，多账号管理及素材高效编辑赋能创作，不同媒体账号、不同作品数据一站查询。</p>
                <a class="item-btn content-btn" href="#consult">立即咨询</a>
              </div>
            </div>
          </div>
          <div class="s4-content-item flex-row align-center">
            <img class="c-img ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s" src="img/u4-i3.png" alt="">
            <div class="item ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
              <div class="top-box">
                <div class="img-text-box flex-row align-center">
                  <div class="img-box flex-row align-center justify-center">
                    <img src="img/u4-03.png" alt="">
                  </div>
                  <h3 class="s-title">运营报告</h3>
                </div>
              </div>
              <div class="bot-box">
                <p class="item-desc">构建系统化投放数据监控体系，实时监控媒体平台、搜索推广效果，网站、媒体、推广数据一目了然，全方面多维度生成可视化数据报告。</p>
                <a class="item-btn content-btn" href="#consult">立即咨询</a>
              </div>
            </div>
          </div>
          <div class="s4-content-item flex-row align-center">
            <img class="c-img ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s" src="img/u4-i4.png" alt="">
            <div class="item ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
              <div class="top-box">
                <div class="img-text-box flex-row align-center">
                  <div class="img-box flex-row align-center justify-center">
                    <img src="img/u4-04.png" alt="">
                  </div>
                  <h3 class="s-title">AI问答营销策划</h3>
                </div>
              </div>
              <div class="bot-box">
                <p class="item-desc">在营销或创作时如遇到问题与瓶颈，可随时向AI机器人进行提问，有问必答，提供个性化且专业的内容策划方案。也可以选择您想提问的问题模板，向其咨询企业管理等其他问题。<br/>
                AI助力营销内容更优质、更简单、更灵活，赋能企业推广效率与创作思路。</p>
                <a class="item-btn content-btn" href="#consult">立即咨询</a>
              </div>
            </div>
          </div>
          <div class="s4-content-item flex-row align-center">
            <img class="c-img ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s" src="img/u4-i5.png" alt="">
            <div class="item ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
              <div class="top-box">
                <div class="img-text-box flex-row align-center">
                  <div class="img-box flex-row align-center justify-center">
                    <img src="img/u4-05.png" alt="">
                  </div>
                  <h3 class="s-title">广告平台</h3>
                </div>
              </div>
              <div class="bot-box">
                <p class="item-desc">一站式管理多广告平台：存储企业推广所用的各类广告平台账号，避免管理太多平台时的混乱，并可一键跳转对应的广告平台，简单快捷方便。</p>
                <a class="item-btn content-btn" href="#consult">立即咨询</a>
              </div>
            </div>
          </div>
          <div class="s4-content-item flex-row align-center">
            <img class="c-img ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s" src="img/u4-i6.png" alt="">
            <div class="item ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
              <div class="top-box">
                <div class="img-text-box flex-row align-center">
                  <div class="img-box flex-row align-center justify-center">
                    <img src="img/u4-06.png" alt="">
                  </div>
                  <h3 class="s-title">小游戏营销</h3>
                </div>
              </div>
              <div class="bot-box">
                <p class="item-desc">精选热门小游戏：数字瞬时记忆、拼手速、像素小鸟、斗地主等多个小游戏。游戏智能绑定企业宣传信息，一键转发朋友圈、微信、微博等媒体平台，用户可在游戏时加深企业印象，有效利用移动互联网碎片化时间赋能营销推广。</p>
                <a class="item-btn content-btn" href="#consult">立即咨询</a>
              </div>
            </div>
          </div>
          <div class="s4-content-item flex-row align-center">
            <img class="c-img ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s" src="img/u4-i7.png" alt="">
            <div class="item ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
              <div class="top-box">
                <div class="img-text-box flex-row align-center">
                  <div class="img-box flex-row align-center justify-center">
                    <img src="img/u4-07.png" alt="">
                  </div>
                  <h3 class="s-title">宣传计划</h3>
                </div>
              </div>
              <div class="bot-box">
                <p class="item-desc">想要制定精准高效的宣传计划，却苦于没有专业团队？别担心，我们的AI助手来帮您！只需简单提问，AI便能根据您的需求，迅速生成个性化宣传方案。无论是产品定位、目标受众，还是宣传渠道、预算分配，AI都能为您一一规划。高效便捷，省时省力，让您的宣传活动更加精准、有效。</p>
                <a class="item-btn content-btn" href="#consult">立即咨询</a>
              </div>
            </div>
          </div>
          <div class="s4-content-item flex-row align-center">
            <img class="c-img ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s" src="img/u4-i8.png" alt="">
            <div class="item ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
              <div class="top-box">
                <div class="img-text-box flex-row align-center">
                  <div class="img-box flex-row align-center justify-center">
                    <img src="img/u4-08.png" alt="">
                  </div>
                  <h3 class="s-title">灵感创意</h3>
                </div>
              </div>
              <div class="bot-box">
                <p class="item-desc">我们为您精心挑选了一系列提供营销创意的网站，只需轻轻一点，即可快捷跳转，随时探索新奇的创意点子。<br />无论是想要寻找创意灵感，还是收藏优质资源，这里都能满足您的需求。让您的营销活动更有趣、更具吸引力！赶快加入我们，探索更多创意，让您的品牌在市场上脱颖而出！</p>
                <a class="item-btn content-btn" href="#consult">立即咨询</a>
              </div>
            </div>
          </div>
          <div class="s4-content-item flex-row align-center">
            <img class="c-img ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s" src="img/u4-i9.png" alt="">
            <div class="item ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
              <div class="top-box">
                <div class="img-text-box flex-row align-center">
                  <div class="img-box flex-row align-center justify-center">
                    <img src="img/u4-09.png" alt="">
                  </div>
                  <h3 class="s-title">营销日历</h3>
                </div>
              </div>
              <div class="bot-box">
                <p class="item-desc">想抓住每日营销热点，不错过任何商机？营销日历精准标注营销宣传热点事件，让您一眼掌握市场动态。并且为企业筛选出近期的热门营销事件，让您在海量信息中迅速找到焦点，轻松制定营销策略。无论是节日促销、新品发布还是行业盛会，都能轻松捕捉，助您抢占先机。别再让商机从指尖溜走！</p>
                <a class="item-btn content-btn" href="#consult">立即咨询</a>
              </div>
            </div>
          </div>
          <div class="s4-content-item flex-row align-center">
            <img class="c-img ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s" src="img/u4-i10.png" alt="">
            <div class="item ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
              <div class="top-box">
                <div class="img-text-box flex-row align-center">
                  <div class="img-box flex-row align-center justify-center">
                    <img src="img/u4-10.png" alt="">
                  </div>
                  <h3 class="s-title">H5邀请函</h3>
                </div>
              </div>
              <div class="bot-box">
                <p class="item-desc">三步轻松发布移动端H5页面，复制模板、拖拽修改、一键发布。提供海量精美邀请函模板，内容包含了邀请函素材和创意内容，可供在线编辑制作，选择喜爱的邀请函模板，修改文字替换图片即可快速完成邀请函制作，一键免费发布。简单高效，赋能营销。</p>
                <a class="item-btn content-btn" href="#consult">立即咨询</a>
              </div>
            </div>
          </div>
          <div class="s4-content-item flex-row align-center">
            <img class="c-img ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s" src="img/u4-i11.png" alt="">
            <div class="item ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
              <div class="top-box">
                <div class="img-text-box flex-row align-center">
                  <div class="img-box flex-row align-center justify-center">
                    <img src="img/u4-11.png" alt="">
                  </div>
                  <h3 class="s-title">CRM客户管理</h3>
                </div>
              </div>
              <div class="bot-box">
                <p class="item-desc">数字化连接销售业务上游下游，实现线索、客户到订单回款的完整业绩闭环，在线全流程管理获取线索、方案报备、跟进预警等，并支持移动端随时查看跟进，让企业把握每一次成交机会。</p>
                <a class="item-btn content-btn" href="#consult">立即咨询</a>
              </div>
            </div>
          </div>
          <div class="s4-content-item flex-row align-center">
            <img class="c-img ani" animate-effect="fadeInLeft" animate-duration="0.8s" animate-delay="0.4s" src="img/u4-i12.png" alt="">
            <div class="item ani" animate-effect="fadeInRight" animate-duration="0.8s" animate-delay="0.4s">
              <div class="top-box">
                <div class="img-text-box flex-row align-center">
                  <div class="img-box flex-row align-center justify-center">
                    <img src="img/u4-12.png" alt="">
                  </div>
                  <h3 class="s-title">客服端分析服务</h3>
                </div>
              </div>
              <div class="bot-box">
                <p class="item-desc">营销平台1.0独有的客服端，开通客服管理后，广告以及自媒体平台代管理以及运营，客服可及时向企业进行线上报告推送，客户随时掌握企业营销信息。</p>
                <a class="item-btn content-btn" href="#consult">立即咨询</a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="width: 70%; margin: auto;" class="contact-box ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.8s">
        <div class="c-title">即刻获取AI营销网站</div>
        <div class="input-box flex-col form-data-2">
          <div class="input-wrap">
            <input class="input-item" type="text" id="name-2" value="" placeholder="联系人姓名（必填）" />
            <div class="el-form-item__error" style="display: none"></div>
          </div>
          <div class="input-wrap">
            <input class="input-item" type="text" id="telephone-2" value="" placeholder="联系人电话（必填）" />
            <div class="el-form-item__error" style="display: none"></div>
          </div>
          <div onclick="submitForm(2)" class="input-btn">立即咨询</div>
        </div>
        <div class="txt-box">提交表单立即咨询营销平台1.0</div>
        <div class="c-img-box">
          <img src="img/renwu.png" />
        </div>
      </div>
    </div>
  </section>
  <!-- 营销平台1.0 一站式整合营销 -->
  <section class="section05" id="consult">
    <div class="container flex-col align-center">
      <div class="c-title ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0s">营销平台1.0 一站式整合营销
      </div>
      <div class="c-title-s ani" animate-effect="fadeInUp" animate-duration="0.8s" animate-delay="0.4s">Jianzhantong AI
        version one-stop integrated marketing
      </div>
      <div class="input-box flex-row ani form-data-3" animate-effect="fadeInUp" animate-duration="0.8s"
        animate-delay="0.8s">
        <div class="input-wrap">
          <input class="input-item" type="text" id="company-3" value="" placeholder="公司名称" />
        </div>
        <div class="input-wrap">
          <input data-required="true" class="input-item" type="text" id="name-3" value="" placeholder="联系人（必填）" />
          <!--<div class="el-form-item__error" style="display: none"></div>-->
        </div>
        <div class="input-wrap">
          <input data-required="true" class="input-item" type="text" id="telephone-3" value="" placeholder="联系电话（必填）" />
          <!--<div class="el-form-item__error" style="display: none"></div>-->
        </div>
        <div class="input-wrap">
          <input class="input-item" type="text" id="content-3" value="" placeholder="内容" />
        </div>
      </div>
      <div class="input-btn ani" animate-effect="fadeInUp" animate-duration="0.8s"
        animate-delay="1.2s">立即获取体验账号
      </div>
    </div>
  </section>
  <!-- 底部 -->
  <footer id="zhyFooter"></footer>
  <!--<img class="goBack" onclick="goBack()" src="./img/goBack.png" alt="" style="display: none">-->

  <!--提示弹窗-->
  <div class="message-box" style="z-index: 2019; display: none" id="message">
    <span id="message-text"></span>
  </div>
</body>

</html>
<script src="https://zcloud.obs.cn-north-4.myhuaweicloud.com/consult.js"></script>
<script>
  // 商桥
  var _hmt = _hmt || [];
  (function () {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?0de415586bc3f5d5aed84bdc980770fc";
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(hm, s);
  })();
</script>
<script type="text/javascript">
  // 创建一个观察者实例
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      const target = entry.target; // 获取进入视口的元素
      const effect = target.getAttribute('animate-effect');
      const duration = target.getAttribute('animate-duration');
      const delay = target.getAttribute('animate-delay');
      if (entry.isIntersecting) {
        // 当元素进入视口时...
        // console.log(entry, 'Element entered the viewport');
        // 在此处添加你想要执行的动画
        // 判断是否有animated类
        if (!target.classList.contains('animated')) {
          // 添加动画类
          target.classList.add('animated');
          // 获取target attr
          target.classList.add(effect);
          // 获取target attr
          target.style.animationDuration = duration;
          // 获取target attr
          target.style.animationDelay = delay;
          setTimeout(() => {
            // 移除animated类
            target.classList.remove('animated')
            // 移除动画类
            target.classList.remove(effect);
            // 移除动画类
            target.style.animationDuration = '';
            // 移除动画类
            target.style.animationDelay = '';
          }, delay.replace('s', '') * 1000 + duration.replace('s', '') * 1000);
        }
        // 停止观察，以避免重复触发动画
        // observer.unobserve(target);
      } else {
        // 当元素离开视口时...
        // 在此处添加你想要执行的动画
        // target.classList.remove('animated');
        // target.classList.remove(effect);
        // target.style.animationDuration = '';
        // target.style.animationDelay = '';
        // 开始观察，以再次触发动画
        // observer.observe(target);
      }
    });
  });

  // 为需要动画的元素设置观察
  document.querySelectorAll('.ani').forEach((element) => {
    observer.observe(element);
  });

  // 12大核心功能 切换效果
  $('.s4-content .s4-tab .tab-item').click(function () {
    $(this).addClass('active').siblings().removeClass('active');
    $('.s4-content .s4-content-box .s4-content-item').eq($(this).index()).addClass('active').siblings().removeClass('active');
    // 判断当前选中的为中间 tab滚动条向左滚动
    if ($(this).index() > 1) {
      // 当前滚动条左移50px
      // 获取$('.s4-content .s4-tab')滚动条的位置
      $('.s4-content .s4-tab').animate({
        scrollLeft: $(this).scrollLeft() + 120 * $(this).index()
      }, 500);
    } else {
      $('.s4-content .s4-tab').animate({
        scrollLeft: '0'
      }, 500);
    }
  });


  /* //提交咨询信息 20211018
  function submitForm(id) {
    $(`.form-data-${id} input`).trigger("blur");
    if ($(".el-form-item__error[style='display:block;']").length || $(".el-form-item__error[style='']").length) {
      return
    }

    let company = $('#company-' + id).val(),
      name = $('#name-' + id).val(),
      telephone = $('#telephone-' + id).val(),
      content = $('#content-' + id).val();

    var url = 'https://hr.china9.cn/human/user_action/leave_message1';
    $.ajax({
      url: url, //请求地址
      data: {
        company,
        name,
        telephone,
        content,
        remarks: '营销平台1.0咨询'
      }, //发送的数据
      dataType: 'json', //请求的数据类型
      type: 'post', //发送的请求类型
      success: function (request) {
        if (request.code == 200) {
          // layer.msg(request.message, {
          // 	icon: 1
          // });
          $("#message").fadeIn().find("#message-text").html(request.message);
          setTimeout(function () {
            $("#message").fadeOut();
          }, 3000)
          $(`.form-data-${id} input`).val('')
        } else {
          // layer.msg(request.message, {
          // 	icon: 2
          // });
          $("#message").fadeIn().find("#message-text").html(request.message);
          setTimeout(function () {
            $("#message").fadeOut();
          }, 3000)
        }
      }
    });
  }

  // 表单验证
  $("input").unbind().on("blur", function () {
    const that = this;
    checkVal(that)
  })

  function checkVal(ele) {
    if (!$(ele).val()) {
      $(ele).siblings(".el-form-item__error").show().text("请输入" + $(ele).attr("placeholder").replace("（必填）", "")).parent().addClass("input-error");
    } else {
      if ($(ele).attr("name") === "telephone") {
        if (!/^1[3456789]\d{9}$/.test($(ele).val())) {
          $(ele).siblings(".el-form-item__error").show().text("请输入正确的手机号码").parent().addClass("input-error");
        } else {
          $(ele).siblings(".el-form-item__error").hide().text("").parent().removeClass("input-error");
        }
      } else {
        $(ele).siblings(".el-form-item__error").hide().text("").parent().removeClass("input-error");
      }
    }
  } */
  // 提交表单
  new ConsultForm({
    formEle: $(".input-box"),
    submitBtn: $('.input-btn'),
    formData: {
      company: $('#company-3'),
      name: $('#name-3'),
      telephone: $('#telephone-3'),
      remarks: "营销平台1.0咨询",
      content: $('#content-3')
    },
  })

  // 微信分享
  function shareInit() {
    var imgUrl = 'https://images.china9.cn/images/l99Y8qoDu7ch9Wb2UL7m51EiagxfjRK3HMolGLfp.png';
    var lineLink = window.location.href;
    var shareTitle = '营销平台1.0';
    var descContent = '全域网站建设、营销、推广管理平台';
    var linkMy = encodeURIComponent(location.href.split('#')[0])
    $.post('https://api.china9.cn/api/wechat_Share', {
      url: lineLink
    }, function (response) {
      const { data } = response
      var appId = data.appId;
      var timestamp = data.timestamp;
      var nonceStr = data.nonceStr;
      var signature = data.signature;
      var lineLink = data.url;
      wx.config({
        debug: false,
        appId: appId,
        timestamp: timestamp,
        nonceStr: nonceStr,
        signature: signature,
        jsApiList: ['checkJsApi', 'onMenuShareTimeline', 'onMenuShareAppMessage']
      });
      wx.ready(function () {
        console.log(imgUrl)
        wx.onMenuShareTimeline({
          title: shareTitle, // 分享标题
          link: lineLink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          imgUrl: imgUrl//, // 分享图标
        }),
          wx.onMenuShareAppMessage({
            title: shareTitle, // 分享标题
            desc: descContent, // 分享描述
            link: lineLink, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
            imgUrl: imgUrl, // 分享图标
            type: '', // 分享类型,music、video或link，不填默认为link
            dataUrl: ''//, // 如果type是music或video，则要提供数据链接，默认为空
            , success: function () {
              // 用户确认分享后执行的回调函数
              // alert('分享成功')
            },
            cancel: function () {
              // 用户取消分享后执行的回调函数
              // alert('分享取消')
            }
          })
      });
    }, "json")
  }
  shareInit()
</script>
