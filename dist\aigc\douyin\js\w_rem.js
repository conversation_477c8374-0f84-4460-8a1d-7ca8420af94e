
// 基于750px设计图的rem适配
function setRootFontSize() {
  const designWidth = 750; // 设计图宽度
  const rootElement = document.documentElement;
  const clientWidth = rootElement.clientWidth || window.innerWidth;

  // 计算font-size，最小12px，最大100px
  const fontSize = Math.max(12, Math.min(100, (clientWidth / designWidth) * 100));
  rootElement.style.fontSize = fontSize + 'px';

  console.log(`屏幕宽度: ${clientWidth}px, 计算font-size: ${fontSize}px`);
}

// 页面加载时设置
setRootFontSize();

// 监听窗口大小变化
window.addEventListener('resize', setRootFontSize);

// 监听屏幕方向变化
window.addEventListener('orientationchange', function () {
  setTimeout(setRootFontSize, 100);
});