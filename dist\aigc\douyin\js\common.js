let loadingOverlay = null; // 加载弹窗元素


// 创建加载弹窗的样式
function createLoadingStyles() {
  const style = document.createElement('style');
  style.textContent = `
    .js-loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .js-loading-overlay.show {
      opacity: 1;
      visibility: visible;
    }

    .js-loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .js-loading-spinner {
      width: 1.2rem;
      height: 1.2rem;
      border: 0.08rem solid rgba(255, 255, 255, 0.3);
      border-top: 0.08rem solid rgb(24, 109, 245);
      border-radius: 50%;
      animation: js-spin 1s linear infinite;
      margin-bottom: 0.4rem;
    }

    .js-loading-text {
      color: white;
      font-size: 0.32rem;
      text-align: center;
      margin-top: 0.2rem;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    }

    .js-loading-dots::after {
      content: '';
      animation: js-loadingDots 1.5s infinite;
    }

    @keyframes js-spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes js-loadingDots {
      0%, 20% { content: ''; }
      40% { content: '.'; }
      60% { content: '..'; }
      80%, 100% { content: '...'; }
    }
  `;
  document.head.appendChild(style);
}

// 创建加载弹窗元素
function createLoadingOverlay() {
  if (loadingOverlay) return loadingOverlay;

  // 创建主容器
  loadingOverlay = document.createElement('div');
  loadingOverlay.className = 'js-loading-overlay';

  // 创建内容容器
  const content = document.createElement('div');
  content.className = 'js-loading-content';

  // 创建加载动画
  const spinner = document.createElement('div');
  spinner.className = 'js-loading-spinner';

  // 创建文字容器
  const text = document.createElement('div');
  text.className = 'js-loading-text';
  text.innerHTML = '正在加载<span class="js-loading-dots"></span>';

  // 组装元素
  content.appendChild(spinner);
  content.appendChild(text);
  loadingOverlay.appendChild(content);

  // 添加到页面
  document.body.appendChild(loadingOverlay);

  // 防止滚动穿透
  loadingOverlay.addEventListener('touchmove', function(e) {
    e.preventDefault();
  }, { passive: false });

  return loadingOverlay;
}

// 显示加载弹窗
function showLoading(text = '正在加载') {
  if (!loadingOverlay) {
    createLoadingStyles();
    createLoadingOverlay();
  }

  const textElement = loadingOverlay.querySelector('.js-loading-text');
  textElement.innerHTML = text + '<span class="js-loading-dots"></span>';

  loadingOverlay.classList.add('show');

  // 防止页面滚动
  document.body.style.overflow = 'hidden';
}

// 隐藏加载弹窗
function hideLoading() {
  if (loadingOverlay) {
    loadingOverlay.classList.remove('show');
    // 恢复页面滚动
    document.body.style.overflow = '';
  }
}

// 销毁加载弹窗
function destroyLoading() {
  if (loadingOverlay) {
    loadingOverlay.remove();
    loadingOverlay = null;
    document.body.style.overflow = '';
  }
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', function() {
  destroyLoading();
  destroyErrorToast();
});

// ==================== 错误提示弹窗 ====================
let errorToast = null; // 错误提示弹窗元素
let errorToastTimer = null; // 自动隐藏定时器

// 创建错误提示弹窗的样式
function createErrorToastStyles() {
  const style = document.createElement('style');
  style.textContent = `
    .js-error-toast {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      border-radius: 0.16rem;
      padding: 0.4rem 0.6rem;
      max-width: 6rem;
      width: max-content;
      min-width: 3rem;
      z-index: 10000;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
      backdrop-filter: blur(0.1rem);
      box-shadow: 0 0.08rem 0.24rem rgba(0, 0, 0, 0.3);
    }

    .js-error-toast.show {
      opacity: 1;
      visibility: visible;
      transform: translate(-50%, -50%) scale(1);
    }

    .js-error-toast.hide {
      opacity: 0;
      visibility: hidden;
      transform: translate(-50%, -50%) scale(0.9);
    }

    .js-error-toast-content {
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      position: relative;
    }

    .js-error-toast-icon {
      width: 0.4rem;
      height: 0.4rem;
      margin-right: 0.2rem;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .js-error-toast-icon svg {
      width: 100%;
      height: 100%;
      fill: #ff4757;
    }

    .js-error-toast-text {
      font-size: 0.28rem;
      line-height: 1.4;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      word-break: break-word;
      flex: 1;
    }

    .js-error-toast-close {
      position: absolute;
      top: -0.3rem;
      right: -0.5rem;
      width: 0.4rem;
      height: 0.4rem;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      border: none;
      padding: 0;
    }

    .js-error-toast-close:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: scale(1.1);
    }

    .js-error-toast-close svg {
      width: 0.24rem;
      height: 0.24rem;
      fill: white;
    }

    /* 不同类型的错误提示样式 */
    .js-error-toast.error .js-error-toast-icon svg {
      fill: rgba(255, 71, 87, 1)
    }

    .js-error-toast.warning .js-error-toast-icon svg {
      fill: rgba(255, 193, 7, 1)
    }

    .js-error-toast.info .js-error-toast-icon svg {
      fill: rgba(23, 162, 184, 1)
    }

    .js-error-toast.success .js-error-toast-icon svg {
      fill: rgba(40, 167, 69, 1)
    }

    /* 动画效果 */
    @keyframes js-shake {
      0%, 100% { transform: translate(-50%, -50%) translateX(0); }
      25% { transform: translate(-50%, -50%) translateX(-0.1rem); }
      75% { transform: translate(-50%, -50%) translateX(0.1rem); }
    }

    .js-error-toast.shake {
      animation: js-shake 0.5s ease-in-out;
    }
  `;
  document.head.appendChild(style);
}

// 创建错误提示弹窗元素
function createErrorToast(message, type = 'error', duration = 3000) {
  // 如果已存在，先销毁
  if (errorToast) {
    destroyErrorToast();
  }

  // 创建主容器
  errorToast = document.createElement('div');
  errorToast.className = `js-error-toast ${type}`;

  // 创建内容容器
  const content = document.createElement('div');
  content.className = 'js-error-toast-content';

  // 创建图标
  const icon = document.createElement('div');
  icon.className = 'js-error-toast-icon';

  // 根据类型设置不同的图标
  let iconSvg = '';
  switch (type) {
    case 'error':
      iconSvg = `
        <svg viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
        </svg>
      `;
      break;
    case 'warning':
      iconSvg = `
        <svg viewBox="0 0 24 24">
          <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
        </svg>
      `;
      break;
    case 'info':
      iconSvg = `
        <svg viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
        </svg>
      `;
      break;
    case 'success':
      iconSvg = `
        <svg viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      `;
      break;
  }
  icon.innerHTML = iconSvg;

  // 创建文字容器
  const text = document.createElement('div');
  text.className = 'js-error-toast-text';
  text.textContent = message;

  // 创建关闭按钮
  const closeBtn = document.createElement('button');
  closeBtn.className = 'js-error-toast-close';
  closeBtn.innerHTML = `
    <svg viewBox="0 0 24 24">
      <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
    </svg>
  `;

  // 组装元素
  content.appendChild(icon);
  content.appendChild(text);
  content.appendChild(closeBtn);
  errorToast.appendChild(content);

  // 添加到页面
  document.body.appendChild(errorToast);

  // 绑定关闭事件
  closeBtn.addEventListener('click', function() {
    hideErrorToast();
  });

  // 点击弹窗外部关闭（可选）
  errorToast.addEventListener('click', function(e) {
    if (e.target === errorToast) {
      hideErrorToast();
    }
  });

  // 防止滚动穿透
  errorToast.addEventListener('touchmove', function(e) {
    e.preventDefault();
  }, { passive: false });

  // 设置自动隐藏
  if (duration > 0) {
    errorToastTimer = setTimeout(() => {
      hideErrorToast();
    }, duration);
  }

  return errorToast;
}

// 显示错误提示弹窗
function showErrorToast(message, type = 'error', duration = 3000) {
  if (!errorToast) {
    createErrorToastStyles();
    createErrorToast(message, type, duration);
  } else {
    // 更新现有弹窗内容
    const textElement = errorToast.querySelector('.js-error-toast-text');
    textElement.textContent = message;

    // 更新类型
    errorToast.className = `js-error-toast ${type}`;
    // 重置定时器
    if (errorToastTimer) {
      clearTimeout(errorToastTimer);
    }
    if (duration > 0) {
      errorToastTimer = setTimeout(() => {
        hideErrorToast();
      }, duration);
    }
  }

  // 显示弹窗
  setTimeout(() => {
    errorToast.classList.add('show');
  }, 10);
}

// 隐藏错误提示弹窗
function hideErrorToast() {
  if (errorToast) {
    errorToast.classList.remove('show');
    errorToast.classList.add('hide');

    // 清除定时器
    if (errorToastTimer) {
      clearTimeout(errorToastTimer);
      errorToastTimer = null;
    }

    // 动画结束后销毁
    setTimeout(() => {
      destroyErrorToast();
    }, 300);
  }
}

// 销毁错误提示弹窗
function destroyErrorToast() {
  if (errorToast) {
    errorToast.remove();
    errorToast = null;
  }
  if (errorToastTimer) {
    clearTimeout(errorToastTimer);
    errorToastTimer = null;
  }
}

// 显示震动效果的错误提示
function showErrorToastWithShake(message, type = 'error', duration = 3000) {
  showErrorToast(message, type, duration);
  if (errorToast && type != 'success') {
    errorToast.classList.add('shake');
    setTimeout(() => {
      if (errorToast) {
        errorToast.classList.remove('shake');
      }
    }, 500);
  }
}

/**
 * 获取地址栏参数 兼容汉字
 * @param {string} name 参数名
 * @returns {string|null} 参数值
 * @example getUrlParam('name') // '张三'
 */
function getUrlParam(name) {
  const url = decodeURIComponent(window.location.href); // 解码URL
  const regex = new RegExp(`[?&]${name}=([^&#]*)`, 'i'); // 忽略大小写
  const results = regex.exec(url);
  return results === null ? null : decodeURIComponent(results[1]); // 解码参数值
}