webpackJsonp([6],{FJsP:function(e,t){},bBv7:function(e,t){},r4TU:function(e,t){},sUZ3:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=a("woOf"),o=a.n(i),l=a("mvHQ"),n=a.n(l),r=a("STSY"),s=a("cMGX"),c=a("Dd8w"),d=a.n(c),u={name:"ShowSomebody",props:{data:{type:Object,default:function(){}}},data:function(){return{showSomebody:!1,somebodyList:[],page:{page:1,limit:10},total:0,loading:!1}},methods:{handelShowSomebody:function(){this.showSomebody=!0,this.getUserList()},getUserList:function(){var e=this;this.loading=!0,Object(r.h)(d()({id:this.data.id},this.page)).then(function(t){var a=t.data;e.somebodyList=a.data,e.total=a.total}).catch(function(e){console.log(e)}).finally(function(){e.loading=!1})},handleDel:function(e){var t=this;console.log(e),this.$confirm("确定要删除当前人员吗？","提示",{distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消"}).then(function(){Object(r.c)({user_id:e.uid,role_id:t.data.id}).then(function(e){200===e.code?(t.$message.success("删除成功"),t.getUserList()):t.$message.error(e.msg||e.message||"删除失败")})}).catch(function(e){console.log("取消删除")})}}},h={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-button",{attrs:{type:"text",icon:"el-icon-view",size:"small"},on:{click:e.handelShowSomebody}},[e._v("查看人员")]),e._v(" "),a("el-drawer",{staticClass:"role-drawer",attrs:{title:"人员列表",visible:e.showSomebody,size:"65%","show-close":"","custom-class":"user-list-drawer"},on:{"update:visible":function(t){e.showSomebody=t}}},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[e.somebodyList&&e.somebodyList.length?[a("el-table",{staticStyle:{"min-width":"600px"},attrs:{data:e.somebodyList}},[a("el-table-column",{attrs:{prop:"name",label:"姓名",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"companyName",label:"公司","min-width":"300"}}),e._v(" "),a("el-table-column",{attrs:{prop:"department",label:"部门",width:"220"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleDel(t.row)}}},[e._v("删除")])]}}],null,!1,2364879090)})],1),e._v(" "),a("div",{staticClass:"page-wrap"},[a("el-pagination",{attrs:{background:"",layout:"prev, pager, next","current-page":e.page.page,"page-size":e.page.limit,total:e.total},on:{"update:currentPage":function(t){return e.$set(e.page,"page",t)},"update:current-page":function(t){return e.$set(e.page,"page",t)},"current-change":e.getUserList}})],1)]:a("el-empty")],2)])],1)},staticRenderFns:[]};var m=a("VU/8")(u,h,!1,function(e){a("FJsP")},"data-v-3c3f1710",null).exports,g=a("lbHh"),p=a.n(g),b={name:"ManageBranchRole.vue",data:function(){return{branchRoleForm:{id:void 0,role_true:1,role_power:[]},roleList:[],loading:!1,isIndeterminate:!1,checkAll:!1}},watch:{"branchRoleForm.role_power":{handler:function(e){var t=e.length;this.isIndeterminate=t>0&&t<this.roleList.length,this.checkAll=t===this.roleList.length}}},methods:{getList:function(){var e=this;this.loading=!0,Object(r.d)().then(function(t){200===t.code&&t.data&&(t.data.role_power=t.data.role_power.map(function(e){return+e}),e.branchRoleForm=t.data,e.roleList=t.data.role_list),e.loading=!1})},roleCheckChange:function(e){this.branchRoleForm.role_power=e||[]},handleCheckAllChange:function(e){this.isIndeterminate=!1,this.branchRoleForm.role_power=e?this.roleList.map(function(e){return e.id}):[]},saveRoleSettings:function(){var e=this,t={token:p.a.get("token")||p.a.get("access_token"),id:this.branchRoleForm.id,role_true:this.branchRoleForm.role_true,role_power:this.branchRoleForm.role_power.length?this.branchRoleForm.role_power.join(","):""};Object(r.l)(t).then(function(t){200===t.code&&(e.$message.success("保存成功"),e.$emit("update:visible",!1))})}},mounted:function(){this.getList()}},f={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"branchRoleFormRef",attrs:{model:e.branchRoleForm,"label-width":"130px"}},[a("el-form-item",{attrs:{prop:"role_true",label:"是否启用集团控制"}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.branchRoleForm.role_true,callback:function(t){e.$set(e.branchRoleForm,"role_true",t)},expression:"branchRoleForm.role_true"}})],1),e._v(" "),a("el-form-item",{attrs:{prop:"role_power",label:"角色列表"}},[a("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选")]),e._v(" "),a("el-checkbox-group",{on:{change:e.roleCheckChange},model:{value:e.branchRoleForm.role_power,callback:function(t){e.$set(e.branchRoleForm,"role_power",t)},expression:"branchRoleForm.role_power"}},e._l(e.roleList,function(t){return a("el-checkbox",{key:t.id,attrs:{label:t.id}},[e._v(e._s(t.name))])}),1)],1),e._v(" "),a("el-form-item",{attrs:{"label-width":"0"}},[a("div",{staticStyle:{"text-align":"center","margin-top":"20px"}},[a("el-button",{attrs:{type:"primary"},on:{click:e.saveRoleSettings}},[e._v("保存")]),e._v(" "),a("el-button",{on:{click:function(t){return e.$emit("update:visible",!1)}}},[e._v("取消")])],1)])],1)},staticRenderFns:[]};var v={name:"role",components:{ManageBranchRole:a("VU/8")(b,f,!1,function(e){a("r4TU")},"data-v-0f383777",null).exports,ShowSomebody:m,Pagination:s.a},data:function(){return{query:{total:0,page:1,limit:10},tableData:[],tableLoading:!1,dialogForm:{name:"",status:1,description:"",productData:[],is_owner:!1},productData:[],dialogButtonLoading:!1,dialogFormTitle:"",dialogFormLabelWidth:"120px",dialogFormVisible:!1,drawer:{},drawerVisible:!1,isGroup:0,manageBranchShow:!1,hasRolePermission:!1}},computed:{hasPermission:function(){return 1===this.isGroup||2===this.isGroup||this.hasRolePermission},isDetail:function(){return"角色详情"===this.dialogFormTitle}},created:function(){this.getProducts(),this.getList(),this.getIsGroup()},watch:{dialogFormVisible:{handler:function(e){e||(this.dialogForm={})}}},methods:{getList:function(){var e=this;this.tableLoading=!0,Object(r.j)(this.query).then(function(t){200===t.code&&(e.tableData=t.data.data,e.query.limit=parseInt(t.data.per_page),e.query.page=t.data.current_page,e.query.total=t.data.total),e.tableLoading=!1})},getProducts:function(){var e=this;Object(r.k)().then(function(t){200===t.code&&(e.productData=t.data,e.productData.forEach(function(t){e.handelPermissionTree(t)}))})},handelPermissionTree:function(e){e.dialogLoading=!0,e.dialogStatus=0,Object(r.f)({id:e.id}).then(function(t){e.dialogLoading=!1,e.dialogMessage=t.message,t&&200===t.code?(e.dialogStatus=1,e.dialogPermissionNumber=t.data.count,e.dialogPermissions=t.data.items):e.dialogStatus=0}).catch(function(t){e.dialogLoading=!1,e.dialogStatus=0,e.dialogMessage=t.message,e.dialogPermissions=[]})},handelCreate:function(){this.dialogFormTitle="添加角色",this.dialogForm.name="",this.dialogForm.status=1,this.dialogForm.description="",delete this.dialogForm.id,this.dialogForm.productData=this.productData,this.dialogForm.productData.forEach(function(e){e.dialogCheckPermissions=[]}),this.dialogFormVisible=!0},handelUpdate:function(e,t){var a=this;this.dialogFormTitle=t,this.dialogForm=JSON.parse(n()(e)),console.log(this.dialogForm,e),this.productData.length>0?(this.productData.forEach(function(t){var i=a.dialogForm.productData.findIndex(function(e){return parseInt(e.id)===parseInt(t.id)});-1!==i?e.productData[i]&&(console.log(t.name,e.productData[i].dialogCheckPermissions),a.dialogForm.productData[i]=o()(t,{dialogCheckPermissions:e.productData[i].dialogCheckPermissions}),console.log(a.dialogForm.productData[i],"this.dialogForm.productData[index]")):(t.dialogCheckPermissions=[],a.dialogForm.productData.push(t))}),this.dialogForm.productData.forEach(function(e,t){-1===a.productData.findIndex(function(t){return parseInt(t.id)===parseInt(e.id)})&&delete a.dialogForm.productData[t]})):this.dialogForm.productData=[],this.dialogFormVisible=!0},handelDelete:function(e){var t=this;this.$confirm("此操作将删除该角色, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){Object(r.b)(e).then(function(e){200===e.code&&(t.$message.success("删除成功"),t.getList())})})},handelSubmit:function(){var e=this;this.dialogButtonLoading=!0;var t={name:this.dialogForm.name,description:this.dialogForm.description,status:this.dialogForm.status,productData:[],is_owner:this.dialogForm.is_owner};this.dialogForm.productData.forEach(function(e){t.productData.push({id:e.id,dialogCheckPermissions:e.dialogCheckPermissions})}),this.dialogForm.id&&(t.id=this.dialogForm.id),console.log(t),t.id?Object(r.m)(t).then(function(t){e.dialogButtonLoading=!1,200===t.code&&(e.dialogFormVisible=!1,e.$message.success("编辑成功"),e.getList())}):Object(r.a)(t).then(function(t){e.dialogButtonLoading=!1,200===t.code&&(e.dialogFormVisible=!1,e.$message.success("添加成功"),e.getList())}).catch(function(t){e.dialogButtonLoading=!1})},handelClickDrawer:function(e){this.drawer=e,this.drawerVisible=!0;var t=this;t.$nextTick(function(){t.drawer.dialogCheckPermissions.forEach(function(e){var a=t.$refs.permissionTree.getNode(e);a&&a.isLeaf&&t.$refs.permissionTree.setChecked(a,!0)})})},handelDrawerCheckedKeys:function(){this.drawer.dialogCheckPermissions=this.$refs.permissionTree.getCheckedKeys().concat(this.$refs.permissionTree.getHalfCheckedKeys()),this.drawerVisible=!1},handelDrawerCheckAll:function(){this.$refs.permissionTree.setCheckedNodes(this.drawer.dialogPermissions)},handelDrawerResetChecked:function(){this.$refs.permissionTree.setCheckedKeys([])},getIsGroup:function(){var e=this;Object(r.g)({token:p.a.get("token")||p.a.get("access_token")}).then(function(t){200===t.code&&(e.isGroup=t.data.returnRole)})},handleManageBranchRole:function(){this.manageBranchShow=!0}}},_={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[e._v("角色列表")]),e._v(" "),a("el-button",{staticStyle:{float:"right",padding:"0"},attrs:{type:"text",icon:"el-icon-refresh",size:"small"},on:{click:e.getList}},[e._v("\n        刷新\n      ")]),e._v(" "),e.hasPermission?a("el-button",{staticStyle:{float:"right",padding:"0","margin-right":"15px"},attrs:{type:"text",icon:"el-icon-plus",size:"small"},on:{click:e.handelCreate}},[e._v("添加角色\n      ")]):e._e(),e._v(" "),1===e.isGroup?a("el-button",{staticStyle:{float:"right",padding:"0","margin-right":"15px"},attrs:{type:"text",icon:"el-icon-setting",size:"small"},on:{click:e.handleManageBranchRole}},[e._v("\n        分公司角色管理\n      ")]):e._e()],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%","min-height":"400px"},attrs:{data:e.tableData,stripe:""}},[a("el-table-column",{attrs:{label:"序号",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.$index+1))]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"角色名称",width:"180"}}),e._v(" "),a("el-table-column",{attrs:{prop:"description",label:"描述"}}),e._v(" "),a("el-table-column",{attrs:{label:"创建时间",prop:"created_at",width:"180"}}),e._v(" "),a("el-table-column",{attrs:{label:"更新时间",prop:"updated_at",width:"180"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"240"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.hasPermission?[a("el-button",{attrs:{type:"text",icon:"el-icon-edit",size:"small"},on:{click:function(a){return e.handelUpdate(t.row,"编辑角色")}}},[e._v("编辑")]),e._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-delete",size:"small"},on:{click:function(a){return e.handelDelete(t.row)}}},[e._v("删除")]),e._v(" "),t.row.userList?a("show-somebody",{staticStyle:{display:"inline-block","margin-left":"10px"},attrs:{data:t.row}}):e._e()]:[a("el-button",{attrs:{type:"text",icon:"el-icon-view",size:"small"},on:{click:function(a){return e.handelUpdate(t.row,"角色详情")}}},[e._v("详情")])]]}}])})],1),e._v(" "),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.query.total>0,expression:"query.total>0"}],staticStyle:{"text-align":"left"},attrs:{total:e.query.total,page:e.query.page,limit:e.query.limit},on:{"update:page":function(t){return e.$set(e.query,"page",t)},"update:limit":function(t){return e.$set(e.query,"limit",t)},pagination:e.getList}})],1),e._v(" "),a("el-dialog",{staticClass:"role-dialog",attrs:{title:e.dialogFormTitle,visible:e.dialogFormVisible,width:"50%"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[e.dialogFormVisible?a("el-form",{ref:"form",attrs:{model:e.dialogForm}},[a("el-form-item",{attrs:{label:"名称","label-width":e.dialogFormLabelWidth}},[e.isDetail?a("span",[e._v(e._s(e.dialogForm.name))]):a("el-input",{staticStyle:{"max-width":"300px"},attrs:{placeholder:"请输入角色名称"},model:{value:e.dialogForm.name,callback:function(t){e.$set(e.dialogForm,"name",t)},expression:"dialogForm.name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态","label-width":e.dialogFormLabelWidth}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-color":"#13ce66","inactive-color":"#ff4949",disabled:e.isDetail},model:{value:e.dialogForm.status,callback:function(t){e.$set(e.dialogForm,"status",t)},expression:"dialogForm.status"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"超级管理员","label-width":e.dialogFormLabelWidth}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-color":"#13ce66","inactive-color":"#ff4949",disabled:e.isDetail},model:{value:e.dialogForm.is_owner,callback:function(t){e.$set(e.dialogForm,"is_owner",t)},expression:"dialogForm.is_owner"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"描述","label-width":e.dialogFormLabelWidth}},[e.isDetail?a("span",[e._v(e._s(e.dialogForm.description))]):a("el-input",{staticStyle:{"max-width":"500px"},attrs:{type:"textarea",placeholder:"请输入角色描述",rows:5},model:{value:e.dialogForm.description,callback:function(t){e.$set(e.dialogForm,"description",t)},expression:"dialogForm.description"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"已购产品权限","label-width":e.dialogFormLabelWidth}},[a("el-table",{attrs:{data:e.dialogForm.productData}},[a("el-table-column",{attrs:{prop:"name",label:"产品名称",width:"180"}}),e._v(" "),a("el-table-column",{attrs:{label:"已选/总计"},scopedSlots:e._u([{key:"default",fn:function(t){return[!0===t.row.dialogLoading?a("div",[e._v("\n                获取中 "),a("i",{staticClass:"el-icon-loading"})]):0===t.row.dialogStatus?a("div",[e._v("\n                "+e._s(t.row.dialogMessage)+"\n              ")]):a("div",[e._v("\n                "+e._s(t.row.dialogCheckPermissions.length)+" / "+e._s(t.row.dialogPermissionNumber)+"\n                "),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"如果出现已选大于总计的情况，请重新选择",placement:"right"}},[a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-warning"}})],1)],1)]}}],null,!1,627277576)}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.dialogStatus?a("div",[a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",size:"small"},on:{click:function(a){return e.handelClickDrawer(t.row)}}},[e._v("\n                  "+e._s(e.isDetail?"详情":"选择")+"\n                ")])],1):0===t.row.dialogStatus&&!1===t.row.dialogLoading?a("div",[a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-refresh-right",size:"small"},on:{click:function(a){return e.handelPermissionTree(t.row)}}},[e._v("重试\n                ")])],1):e._e()]}}],null,!1,2984543965)})],1)],1)],1):e._e(),e._v(" "),e.isDetail?e._e():a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small"},on:{click:function(t){e.dialogFormVisible=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary",size:"small",loading:e.dialogButtonLoading},on:{click:e.handelSubmit}},[e._v("确 定")])],1)],1),e._v(" "),a("el-drawer",{staticClass:"role-drawer",attrs:{title:e.drawer.name,visible:e.drawerVisible,size:"20%","show-close":""},on:{"update:visible":function(t){e.drawerVisible=t}}},[e.drawerVisible?a("div",{staticClass:"drawer"},[a("el-tree",{ref:"permissionTree",staticStyle:{height:"500px","overflow-y":"auto"},attrs:{data:e.drawer.dialogPermissions,"show-checkbox":"","default-expand-all":"","node-key":"id","highlight-current":"",props:{children:"children",label:"label"}}}),e._v(" "),a("hr"),e._v(" "),e.isDetail?e._e():a("div",{staticClass:"buttons"},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.handelDrawerCheckedKeys}},[e._v("确定")]),e._v(" "),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.handelDrawerCheckAll}},[e._v("全选")]),e._v(" "),a("el-button",{attrs:{size:"small"},on:{click:e.handelDrawerResetChecked}},[e._v("清空")])],1),e._v(" "),e.isDetail?a("div",{staticClass:"mask"}):e._e()],1):e._e()]),e._v(" "),a("el-dialog",{attrs:{title:"分公司角色管理",visible:e.manageBranchShow},on:{"update:visible":function(t){e.manageBranchShow=t}}},[e.manageBranchShow?a("manage-branch-role",{attrs:{visible:e.manageBranchShow},on:{"update:visible":function(t){e.manageBranchShow=t}}}):e._e()],1)],1)},staticRenderFns:[]};var w=a("VU/8")(v,_,!1,function(e){a("bBv7")},"data-v-035256c2",null);t.default=w.exports}});