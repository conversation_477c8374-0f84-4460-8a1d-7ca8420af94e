import{k as g,c as t,o as a,b as h,m as i,a6 as s,n,a as o,K as b,w as x,p as S,a7 as p,aA as c,j as d,t as r,_ as k}from"./index-BBeD0eDz.js";const w={key:0,class:"label-text"},C={key:0,class:"label-text mt-[12px]"},z=g({__name:"AddBtn",props:{width:{type:String,default:"100%"},height:{type:String,default:"38px"},radius:{type:String,default:"4px"},label:{type:String,default:"添加"},iconSize:{type:String,default:"16px"},align:{type:String,default:"row"},labelPosition:{type:String,default:"inside"}},emits:["click"],setup(e,{emit:u}){const f=u,m=()=>{f("click")};return(l,B)=>{const y=b;return a(),t("div",{class:n(["add-btn-wrap","w-full","flex",{"flex-col":e.align==="col"},"justify-center","items-center"])},[h("div",{class:n(["add-material","flex","justify-center","items-center","cursor-pointer",{"flex-col":e.align==="col"}]),style:s({width:e.width,height:e.height,borderRadius:e.radius}),onClick:m},[o(y,{style:s({fontSize:e.iconSize})},{default:x(()=>[o(S(p))]),_:1},8,["style"]),e.labelPosition==="inside"?(a(),t("span",w,[c(l.$slots,"default",{},()=>[d(r(e.label),1)],!0)])):i("",!0)],6),e.labelPosition==="outside"?(a(),t("span",C,[c(l.$slots,"default",{},()=>[d(r(e.label),1)],!0)])):i("",!0)],2)}}}),N=k(z,[["__scopeId","data-v-ebdbcc10"]]);export{N as A};
