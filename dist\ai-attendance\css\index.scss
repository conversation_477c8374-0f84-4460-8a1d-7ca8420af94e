* {
  margin: 0;
  padding: 0;
}

$primary-color: #306ae4;

li {
  list-style: none;
}

.container {
  width: 69%;
  margin: 0 auto;
}

.flex {
  display: flex;
}

.flex-align-center {
  display: flex;
  align-items: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-align-stretch {
  display: flex;
  align-items: stretch;
}

.flex-align-start {
  display: flex;
  align-items: flex-start;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-col {
  flex-direction: column;
}

.flex-space-between {
  display: flex;
  justify-content: space-between;
}

.title {
  h2 {
    font-weight: 800;
    font-size: 2.29vw;
    color: #222222;
  }

  p {
    font-size: 0.94vw;
    color: #222222;
    margin-top: 0.89vw;
    text-transform: uppercase;
  }
}

.btn {
  width: 10.42vw;
  height: 3.13vw;
  background: #2d68ff;
  font-size: 1.25vw;
  color: #ffffff;
  cursor: pointer;
  @extend .flex-center;
}

#zhyHomeHeader {
  height: 70px;
}

.pc {
  display: block;
}

.phone {
  display: none;
}

.section-1 {
  background: url("../img/bg.png") no-repeat #fff;
  background-size: 100% auto;

  .banner {
    padding-top: 11.77vw;

    .banner-title {
      font-weight: 800;
      font-size: 3.13vw;
      color: #0b1c72;
      line-height: 4.69vw;
    }

    .btn {
      margin-top: 3.28rem;
    }
  }

  .pain-point {
    margin-top: 9.43vw;
    padding-bottom: 4.48vw;

    .pain-point-list {
      gap: 1.56vw;
      margin-top: 3.07vw;

      li {
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        padding: 2.55vw 2.4vw;
        @for $num from 1 through 3 {
          &:nth-child(#{$num}) {
            background-image: url("../img/pain-point-bg#{$num}.png");
          }
        }

        img {
          width: 13%;
          height: auto;
          margin-top: 12.86vw;
        }

        .pain-title {
          margin-top: 1.82vw;
          font-size: 1.56vw;
          color: #ffffff;
          line-height: 2.19vw;
        }

        .pain-desc {
          margin-top: 1.61vw;
          font-size: 0.73vw;
          color: #b2c7ff;
          line-height: 1.15vw;
        }
      }
    }
  }
}

.section-2 {
  background: #f1f6ff;

  .container {
    padding: 3.65vw 0 2.81vw;

    .idx1_con {
      background: #fff;
      margin-top: 1.77vw;
      padding-bottom: 1.04vw;

      & > ul {
        display: flex;
        align-items: center;

        & > li {
          cursor: pointer;
          padding: 2.667vw 0 1.333vw 0;
          text-align: center;
          flex: 1 0 80px;

          .img {
            overflow: hidden;
            height: 2.6vw;
            width: 100%;

            img {
              transform: translateY(-20vw);
              filter: drop-shadow(0 20vw #707070);
            }
          }

          p {
            font-size: 0.83vw;
            color: #707070;
          }

          &:hover,
          &.active {
            .img {
              img {
                filter: drop-shadow(0 20vw $primary-color);
              }
            }

            p {
              color: $primary-color;
            }
          }
        }
      }

      .con {
        margin-top: 4.167vw;
        padding: 0 2vw;
        display: none;

        &.active {
          display: flex;
        }

        .img {
          margin-right: 2.083vw;

          img {
            width: 28.438vw;
          }
        }

        .con_r {
          $section2-color: #565656;
          .title {
            font-size: 1.667vw;
            color: $section2-color;

            & + span {
              display: block;
              width: 6.5625vw;
              height: 1px;
              background: $primary-color;
              margin: 1.302vw 0;
              position: relative;

              &::before {
                content: "";
                position: absolute;
                width: 1.6vw;
                height: 3px;
                background: $primary-color;
                top: 50%;
                left: 0;
                transform: translateY(-50%);
              }
            }
          }

          .til {
            font-size: 1.042vw;
            font-weight: bold;
            white-space: nowrap;
            color: $section2-color;

            & + div {
              margin-top: 2.083vw;

              p {
                line-height: 2;
                font-size: 0.729vw;
                color: $section2-color;
                margin-bottom: 1.82vw;
                @extend .flex-align-center;
                position: relative;

                img {
                  width: 1.46vw;
                  height: 1.46vw;
                  object-fit: contain;
                  display: block;
                  margin-right: 0.5rem;
                }
              }
            }
          }
        }
      }
    }

    .btn {
      margin-top: 1.67vw;
    }
  }
}

.section-3 {
  background-color: #fff;
  padding: 3.854vw 0 6.25vw;

  .intro-wrap {
    margin-top: 2.92vw;
    background: #2d68ff;
    @extend .flex-align-stretch;

    img {
      display: block;
      object-fit: cover;
      width: 30.26vw;
    }

    .intro {
      padding: 4.58vw 3.33vw;

      p {
        font-size: 16px;
        color: #d1deff;
        line-height: 34px;

        & + p {
          margin-top: 2.08vw;
        }
      }
    }
  }
}

.section-4 {
  background-color: #d7e3f6;
  padding: 3.49vw 0;

  .advantage-wrap {
    margin-top: 3.07vw;

    .advantage-item {
      width: calc(100% / 3 - 2.24vw + 2.24vw / 3);
      background: linear-gradient(266deg, #dfe9f8, #eef2fc);
      box-shadow:
        0vw 0.26vw 0vw 0vw rgba(255, 255, 255, 0.83),
        0vw 0.052vw 0.104vw 0vw #ffffff;
      padding: 3.96vw 2.24vw;

      & + .advantage-item {
        margin-left: 2.24vw;
      }

      img {
        width: 2.97vw;
        height: 2.97vw;
      }

      h3.title {
        font-weight: bold;
        font-size: 1.458vw;
        color: #2a2f4d;
        margin-top: 2.14vw;
      }

      .content {
        margin-top: 3.23vw;
        font-size: 0.938vw;
        color: #2a2f4d;
        line-height: 1.875vw;
      }
    }
  }

  .btn-wrap {
    margin-top: 3.65vw;
  }
}

.concat-us {
  background: url("../img/bg2.png") no-repeat;
  background-size: cover;
  padding: 4.01vw 0 2.66vw;
  text-align: center;

  .form-title {
    font-size: 1.15vw;
    font-weight: 500;
    color: #ffffff;
  }

  .form-en-title {
    font-size: 0.63vw;
    font-weight: 500;
    color: #ffffff;
    margin-top: 0.63vw;
  }

  .form {
    width: 42.7vw;
    margin: 2.08vw auto 0;
    @extend .flex-space-between;

    .input-wrap {
      width: 29.3%;
      position: relative;

      input {
        width: 100%;
        height: 2.08vw;
        background: transparent;
        outline: transparent;
        border: 1px solid rgba(255, 255, 255, 0.5);
        border-radius: 2.08vw;
        padding: 0 1vw;
        box-sizing: border-box;
        color: #fff;
        font-size: 0.73vw;
      }
    }
  }

  .btn-wrap {
    margin-top: 1.77vw;

    .consulting-button {
      @extend .btn;
      width: 15.625vw;
      height: 2.708vw;
      border-radius: 1.354vw;
      font-size: 1.042vw;
    }
  }
}

@media screen and (max-width: 1024px) {
  .container {
    width: 91.33%;
  }

  .title {
    h2 {
      font-size: 5.867vw;
    }

    p {
      font-size: 2.4vw;
      margin-top: 1vw;
    }
  }

  .btn {
    width: 26.667vw;
    height: 9.333vw;
    font-size: 3.733vw;
    letter-spacing: 1px;
  }

  .pc {
    display: none;
  }

  .phone {
    display: block;
  }

  .section-1 {
    background: url("../img/phone/bg.png") no-repeat #fff;
    background-size: 100% auto;

    .banner {
      padding-top: 21.6vw;

      h2.banner-title {
        font-size: 4vw;
        line-height: 1;
      }

      h1.banner-title {
        font-size: 7.8vw;
        line-height: 11.467vw;
        margin-top: 4.8vw;
      }

      .btn {
        margin-top: 4.933vw;
      }
    }

    .pain-point {
      margin-top: 14.467vw;
      padding-bottom: 10.667vw;

      .pain-point-list {
        display: block;

        &::after {
          content: "";
          display: block;
          clear: both;
          overflow: hidden;
        }

        li {
          width: 78.267vw;
          float: left;
          padding: 5.733vw 8.133vw;
          box-sizing: border-box;

          & + li {
            margin-top: 6.533vw;
          }

          &:nth-child(even) {
            float: right;
          }

          @for $num from 1 through 3 {
            &:nth-child(#{$num}) {
              background-image: url("../img/phone/pain-point-bg#{$num}.png");
            }
          }

          img {
            margin-top: 36.8vw;
            width: 9.333vw;
          }

          .pain-title {
            margin-top: 5.6vw;
            font-size: 5.333vw;
            line-height: 6.4vw;
          }

          .pain-desc {
            margin-top: 4.533vw;
            font-size: 3.467vw;
            line-height: 4.533vw;
          }
        }
      }
    }
  }

  .section-2 {
    padding-top: 11.733vw;
    padding-bottom: 11.733vw;

    .container {
      .idx_type {
        overflow: hidden;

        .swiper-slide {
          overflow: hidden;
        }

        p.title {
          font-size: 4.8vw;
          text-align: center;
          margin: 7.2vw 0;
        }

        .img {
          width: 87.467vw;
          margin: auto;

          img {
            width: 100%;
            height: auto;
            max-width: 100%;
            display: block;
          }
        }

        div.con {
          width: 95%;
          margin-top: 3.2vw;

          .til {
            font-size: 2.933vw;
            font-weight: bold;
            text-align: center;

            & + div {
              margin-top: 4.8vw;

              p {
                line-height: 2;
                margin-top: 0.3rem;
                font-size: 2.933vw;
                display: flex;
                align-items: center;
                padding-left: 2.667vw;

                &::before {
                  content: "";
                  width: 0.8vw;
                  height: 0.8vw;
                  background: $primary-color;
                  border-radius: 50%;
                  margin-right: 0.5rem;
                }
              }
            }
          }
        }

        .swiper-pagination {
          display: flex;
          overflow: auto;
          position: static;
          justify-content: center;
          margin-top: 6.667vw;

          .swiper-pagination-bullet {
            display: inline-block;
            width: 2.667vw;
            height: 1.6vw;
            background: $primary-color;
            border-radius: 0.8vw;

            &.swiper-pagination-bullet-active {
              width: 4vw;
            }
          }
        }
      }

      .btn {
        margin-top: 10.667vw;
      }
    }
  }

  .section-3 {
    padding-top: 14.667vw;
    padding-bottom: 10.667vw;

    .intro-wrap {
      display: block;
      margin-top: 9.733vw;

      img {
        width: 100%;
      }

      .intro {
        padding: 12vw 6.4vw;

        p {
          font-size: 3.733vw;
          line-height: 5.6vw;

          & + p {
            margin-top: 5.6vw;
          }
        }
      }
    }
  }

  .section-4 {
    padding-top: 11.733vw;
    padding-bottom: 11.733vw;

    .advantage-wrap {
      margin-top: 12vw;
      display: block;

      .advantage-item {
        width: 100%;
        box-sizing: border-box;
        padding: 10.267vw 5.733vw;

        & + .advantage-item {
          margin-left: 0;
          margin-top: 4.667vw;
        }

        img {
          width: 10.667vw;
          height: 10.667vw;
        }

        h3.title {
          font-size: 5.333vw;
          margin-top: 7.067vw;
        }

        .content {
          margin-top: 4.4vw;
          font-size: 3.467vw;
          line-height: 4.8vw;
        }
      }
    }

    .btn-wrap {
      margin-top: 10.667vw;
    }
  }

  .concat-us {
    background: url("../img/phone/footer-bg.png") no-repeat;
    background-size: 100% 100%;
    padding: 12.533vw 2.4vw 13.333vw;

    .form-title {
      font-weight: bold;
      font-size: 5.067vw;
    }

    .form-en-title{
      font-size: 2.133vw;
      margin-top: 2.533vw;
    }

    .form{
      display: flex;
      flex-direction: column;
      width: 92vw;
      margin-top: 7.733vw;

      .input-wrap {
        width: 100%;
        
        input {
          height: 10.667vw;
          display: block;
          border-radius: 10.667vw;
          padding: 0 6vw;
          font-size: 3.733vw;
        }

        & + .input-wrap {
          margin-top: 6.667vw;
        }
      }
    }
    
    .btn-wrap {
      margin-top: 9.867vw;

      .btn {
        border-radius: 10.667vw;
        width: 92vw;
        height: 10.667vw;
      }
    }
  }
}

@media screen and (max-width: 700px) {
  #zhyHomeHeader {
    height: 50px;
  }
}
