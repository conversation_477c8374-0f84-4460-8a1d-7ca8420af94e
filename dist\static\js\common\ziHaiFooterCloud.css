#zhyFooter, #zhy<PERSON>ooter * {
  margin: 0;
  padding: 0;
}

#z<PERSON><PERSON><PERSON><PERSON>,
#z<PERSON><PERSON>ooter div,
#z<PERSON><PERSON>ooter dl,
#zhy<PERSON>ooter dt,
#zhy<PERSON>ooter dd,
#zhyFooter ol,
#zhy<PERSON>ooter ul,
#zhy<PERSON>ooter li,
#zhy<PERSON>ooter h1,
#zhy<PERSON>ooter h2,
#z<PERSON><PERSON>ooter h3,
#zhy<PERSON>ooter h4,
#zhy<PERSON>ooter h5,
#zhy<PERSON>ooter h6,
#zhy<PERSON>ooter pre,
#zhyFooter form,
#zhyFooter fieldset,
#zhyFooter input,
#zhyFooter textarea,
#zhyFooter p,
#zhyFooter blockquote,
#zhyFooter th,
#zhyFooter td,
#zhyFooter p {
  margin: 0;
  padding: 0;
  font-size: 14px;
}

#zhyFooter li {
  list-style: none;
}

#zhyFooter {
  width: 100%;
  background-color: #202020;
}

#zhyFooter .content {
  position: relative;
  margin: 0 auto;
  padding: 60px 10% 30px;
  width: 80%;
  box-sizing: content-box;
}

#zhyFooter .content .container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  margin: auto;
}

#zhyFooter .content .container p {
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 25px;
}

#zhyFooter .content .container li {
  margin-bottom: 15px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
}

#zhyFooter .content .container li a {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  text-decoration: none;
}

#zhyFooter td {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  padding-right: 20px;
  padding-bottom: 15px;
}

#zhyFooter .content .container li:hover {
  color: white;
}

#zhyFooter .content .container .product .product-item > div a {
  font-size: 12px;
  line-height: 1.5;
}

#zhyFooter .content .container .product:first-of-type .product-item + .product-item {
  margin-left: 30px;
}

#zhyFooter .content .container .product {
  margin-right: 30px;
}

#zhyFooter .content .container .product:first-of-type > div {
  display: flex;
}

#zhyFooter .content .container .product:first-of-type .product-item > div{
  margin-bottom: 15px;
}

#zhyFooter .content .container .product:not(:first-of-type) .product-item {
  margin-bottom: 15px;
}

#zhyFooter .content .container .solve {
  width: 240px;
}

#zhyFooter .content .container .serve {
  width: 145px;
}

#zhyFooter .footer-info-wrap.zhyHomeHeaderPhone {
  display: none;
}

#zhyFooter a {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
}

#zhyFooter a:hover {
  color: rgba(255, 255, 255, 1);
  text-decoration: none;
}

#zhyFooter .content .container .address p {
  margin-top: -15px;
  margin-bottom: 14px;
  font-size: 30px;
  color: rgba(255, 255, 255, 0.6);
  font-family: bs;
}

#zhyFooter .content .container .qr {
  width: 120px;
  float: right;
  text-align: center;
  box-sizing: content-box;
}

#zhyFooter .content .qr.zhyHomeHeaderPhone {
  display: none;
}

#zhyFooter .content .container .qr .el-image {
  width: 120px;
  height: 120px;
  margin-bottom: 30px;
}

#zhyFooter .content .container .qr p:nth-child(1) {
  font-size: 14px;
  margin-bottom: 9px;
}

#zhyFooter .content .version {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 26px;
}

#zhyFooter .content .version span, #zhyFooter .content .version a, #zhyFooter .content .version span a {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
}

#zhyFooter a {
  text-decoration: none;
}

#zhyFooter .version.zhyHomeHeaderPhone{
  display: none;
}

.goBack{
  position: fixed;
  right: 20px;
  bottom: 30px;
  z-index: 99;
  cursor: pointer;
}

@media (max-width: 1680px) {
  #zhyFooter .content .version {
    width: 100%;
    position: static;
    margin-top: 30px;
  }

  #zhyFooter .content {
    padding-bottom: 20px;
  }
}

@media (max-width: 1600px) {
  #zhyFooter .content {
    padding-left: 4%;
    padding-right: 4%;
    width: 92%;
  }
}

@media (max-width: 1470px) {
  #zhyFooter .content .container .product:first-of-type {
    padding: 0;
  }

  #zhyFooter .content .container .product ul {
    margin-right: 10px;
  }

  #zhyFooter .content .container .qr {
    width: auto;
  }
}

@media (max-width: 1380px) {
  #zhyFooter .content .container {
    flex-wrap: wrap;
  }

  #zhyFooter .content .container .address {
    padding-top: 30px;
    width: 50%;
  }

  #zhyFooter .content .container .address p {
    margin-top: 0;
  }

  #zhyFooter .content {
    padding-left: 7%;
    padding-right: 7%;
    width: 86%;
  }

  #zhyFooter .content .container .qr {
    flex-grow: 0;
    width: 50%;
    box-sizing: border-box;
  }
}

@media (max-width: 1100px) {
  #zhyFooter .footer-info-wrap.zhyHomeHeaderPhone {
    display: flex;
    justify-content: flex-start;
  }

  #zhyFooter .content .qr.zhyHomeHeaderPhone {
    display: flex;
    justify-content: center;
    align-items: center;
    float: none;
  }

  #zhyFooter .content .qr.zhyHomeHeaderPhone .el-image {
    width: 120px;
    margin-right: 20px;
  }

  #zhyFooter .content .version {
    margin-top: 30px;
    position: static;
  }

  #zhyFooter .content .container .address p {
    margin-top: 0;
  }
}

@media (max-width: 1000px) {
  #zhyFooter .content .container .address.zhyHomeHeaderPhone {
    display: block;
    flex-grow: 1;
    padding-right: 20px;
    width: calc(50% - 10px);
  }

  #zhyFooter .content .qr.zhyHomeHeaderPhone {
    width: calc(50% - 10px);
  }

  #zhyFooter .content .container {
    min-width: auto;
  }

  #zhyFooter .content .version:not(.zhyHomeHeaderPhone) {
    display: block;
    text-align: center;
  }

  #zhyFooter .content .version span:last-child {
    display: block;
    margin-top: 10px;
  }
}

@media (max-width: 900px) {
  #zhyFooter .content .qr.zhyHomeHeaderPhone {
    margin-top: 30px;
  }
}

@media (max-width: 700px) {
  #zhyFooter .content .version:not(.zhyHomeHeaderPhone){
    display: none;
  }
  #zhyFooter .content .version.zhyHomeHeaderPhone{
    display: block;
  }
  #zhyFooter .content .version.zhyHomeHeaderPhone span,
  #zhyFooter .content .version.zhyHomeHeaderPhone a{
    text-align: center;
  }

  #zhyFooter .content .container {
    display: none;
  }

  #zhyFooter .version.zhyHomeHeaderPc{
    display: none;
  }

  #zhyFooter .content .version {
    margin-top: 0;
  }

  #zhyFooter .content .version span, #zhyFooter .content .version a, #zhyFooter .content .version span a {
    font-size: 14px;
  }

  #zhyFooter .content {
    padding: 30px 0;
  }

  #zhyFooter .content .version.zhyHomeHeaderPhone > a,
  #zhyFooter .content .version.zhyHomeHeaderPhone > span{
    display: block;
    line-height: 1;
  }
  #zhyFooter .content .version a{
    margin-top: 10px;
  }
}

@media (max-width: 600px) {
  .goBack{
    width: 50px;
    height: 50px;
    bottom: 22%;
    right: 10px;
  }
}
