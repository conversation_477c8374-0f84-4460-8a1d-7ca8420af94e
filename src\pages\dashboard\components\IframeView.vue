<template>
  <div class="iframe-container">
    <!-- 加载中 -->
    <div v-if="loading" class="loading">
      <img class="loading-icon" src="@/assets/image/dashboard/layout/loading.gif" alt="">
       <span class="text">加载中...</span>
    </div>
    <iframe ref="iframeRef" class="iframe-wrap" :src="src" @load="handleIframeLoad"></iframe>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'IframeView',
  data() {
    return {
      src: '',
      loading: true
    };
  },
  computed: {
    ...mapState(['user', 'zhUserRole', 'dashboardMenus']),
    userInfo() {
      console.log('userInfo', this.user)
      return this.user && this.user.memberInfo
    },
    companyInfo() {
      return this.user && this.user.companyInfo
    },
    currentMenu() {
      return this.dashboardMenus.find(item => item.path == this.$route.path)
    }
  },
  created() {
    this.setSrc();
  },
  watch: {
    $route() {
      this.setSrc();
    }, 
  },
  methods: {
    setSrc(url) {
      let that = this, route = that.$route,
        meta = route.meta;
      that.src = ''; // 重置src
      let path = this.currentMenu.component
      // 判断有没有?
      if (path.includes('?')) {
        path += `&v=${new Date().getTime()}`;
      } else {
        path += `?v=${new Date().getTime()}`;
      }
      if (meta.params && meta.params.length) {
        meta.params.forEach((item) => {
          path += `&${item.key}=${that.handleParams(item.value)}`;
        })
      }

      that.loading = true; // 重置加载状态
      that.src = path;
    },
    // 处理参数
    handleParams(value) {
      switch (value) {
        case 'user_uid':
          return this.userInfo.uid
        default:
          break;
      }
    },
    handleIframeLoad() {
      const that = this, iframe = that.$refs.iframeRef;
      console.log('iframe加载完成');
      that.loading = false; // 加载完成
      // 访问iframe内容（注意跨域限制）
      try {
        const iframeDoc = iframe.contentDocument;
        console.log('iframe内容:', iframeDoc);
      } catch (e) {
        console.warn('跨域访问被阻止', e);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.iframe-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  .loading {
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(255, 255, 255, 1);
    padding: 10px 20px;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .loading-icon {
      width: 100px;
    }
   .text {
      color: #666666;
      font-size: 16px 
   }
  }
}
.iframe-wrap {
  width: 100%;
  height: 100%;
  border: none;
}
</style>