webpackJsonp([107],{HKW6:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-list"},[e.educationList.length?i("el-timeline",{attrs:{reverse:!0}},e._l(e.educationList,function(t){return i("el-timeline-item",{key:t.key,attrs:{timestamp:t.begin_time,placement:"top"}},[i("p",[e._v("就读时间："+e._s(t.begin_time)+" -- "+e._s(t.end_time))]),e._v(" "),i("p",[e._v("院校及系："+e._s(t.school))]),e._v(" "),i("p",[e._v("所学专业："+e._s(t.subjects))]),e._v(" "),i("p",[e._v("毕（结、肄）业："+e._s(t.graduate))]),e._v(" "),i("p",[e._v("证明人："+e._s(t.certifier))]),e._v(" "),i("p",[e._v("学历："+e._s(t.educationback))])])}),1):i("el-empty")],1)},staticRenderFns:[]};var s=i("VU/8")({name:"ArchivesEducation",props:["data"],computed:{educationList:function(){return this.data.educationlist||[]}}},n,!1,function(e){i("YhoA")},"data-v-68ab020c",null);t.default=s.exports},YhoA:function(e,t){}});