webpackJsonp([8],{"/73A":function(e,t){},"8s6G":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("Dd8w"),i=a.n(n),r=a("Xxa5"),s=a.n(r),l=a("exGp"),o=a.n(l),c=a("vLgD");function d(e){return Object(c.a)({url:"/api/meeting/fieldIndex",method:"post",data:e})}function u(e){return Object(c.a)({url:"/api/meeting/fieldDestory",method:"post",data:e})}function f(e){return Object(c.a)({url:"/api/meeting/fieldDestoryList",method:"post",data:e})}var p=a("woOf"),g=a.n(p),m=a("mvHQ"),h=a.n(m),b=a("//Fk"),v=a.n(b),_={name:"Add",props:{dialogVisible:{type:Boolean,default:!1},data:{type:Object,default:function(){return{}}}},data:function(){return{form:{id:void 0,title:""},defaultForm:{id:void 0,title:""},rules:{title:[{required:!0,message:"请输入词汇名称",trigger:"blur"}]},loading:!1}},computed:{title:function(){return this.data.id?"编辑词汇":"添加词汇"},visible:{get:function(){return this.dialogVisible},set:function(e){this.$emit("update:dialogVisible",e)}}},methods:{submit:function(){var e=this;return o()(s.a.mark(function t(){var a,n,i;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,e.loading=!0,t.next=4,r=e.form,Object(c.a)({url:"/api/meeting/fieldStore",method:"post",data:r});case 4:if(a=t.sent,n=a.code,i=a.message,200===n){t.next=9;break}return e.$message({message:i||"提交失败",type:"error"}),t.abrupt("return",v.a.reject(new Error(i||"Error")));case 9:return e.$message({message:"提交成功",type:"success"}),t.abrupt("return",v.a.resolve(!0));case 13:return t.prev=13,t.t0=t.catch(0),console.log(t.t0),t.abrupt("return",v.a.reject(t.t0));case 17:return t.prev=17,e.loading=!1,t.finish(17);case 20:case"end":return t.stop()}var r},t,e,[[0,13,17,20]])}))()},handleSubmit:function(){var e,t=this;this.$refs.form.validate((e=o()(s.a.mark(function e(a){return s.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!a){e.next=8;break}return console.log("提交表单",t.form),e.next=4,t.submit();case 4:e.sent&&(t.$emit("submit",JSON.parse(h()(t.form))),t.handleCancel()),e.next=10;break;case 8:return console.log("error submit!!"),e.abrupt("return",!1);case 10:case"end":return e.stop()}},e,t)})),function(t){return e.apply(this,arguments)}))},handleCancel:function(){this.visible=!1,this.$refs.form.clearValidate(),this.form=this.defaultForm,this.$emit("cancel")}},watch:{dialogVisible:function(e){e&&(this.form=g()({},this.data))}}},x={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{visible:e.visible,width:"30%"},on:{"update:visible":function(t){e.visible=t}}},[a("div",{staticClass:"header_title",attrs:{slot:"title"},slot:"title"},[a("h1",[e._v(e._s(e.title))])]),e._v(" "),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"词汇名称",prop:"title"}},[a("el-input",{attrs:{placeholder:"请输入词汇名称",clearable:""},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",loading:e.loading},on:{click:e.handleSubmit}},[e._v("提交")]),e._v(" "),a("el-button",{attrs:{loading:e.loading},on:{click:e.handleCancel}},[e._v("取消")])],1)],1)},staticRenderFns:[]};var w={name:"CompanyMeeting",components:{Add:a("VU/8")(_,x,!1,function(e){a("/73A")},"data-v-e0f888fe",null).exports},data:function(){return{filterForm:{title:""},loading:!1,tableData:[],page:{page:1,limit:20},total:1e3,delBatchLoading:!1,delBatchIds:[],updateVisible:!1,row:{id:"",name:"",created_at:""}}},methods:{addWord:function(){console.log("添加词汇"),this.row={id:"",title:"",created_at:""},this.updateVisible=!0},edit:function(e){console.log(e),this.updateVisible=!0,this.row=e},delApi:function(e){var t=this;return o()(s.a.mark(function a(){var n,i,r,l,o;return s.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(n=t.tableData.find(function(t){return t.id===e[0]}),i=e.length>1?f:u,a.prev=2,0!==e.length){a.next=6;break}return t.$message({message:"请选择要删除的词汇",type:"warning"}),a.abrupt("return");case 6:return t.delBatchIds.length>0?t.delBatchLoading=!0:t.$set(t.tableData.find(function(t){return t.id===e[0]}),"loading",!0),a.next=9,i({id:e.join(",")});case 9:if(r=a.sent,l=r.code,o=r.message,200===l){a.next=14;break}return t.$message({message:o||"删除失败",type:"error"}),a.abrupt("return");case 14:t.$message({message:"删除成功",type:"success"}),t.getList(),a.next=22;break;case 18:a.prev=18,a.t0=a.catch(2),console.log(a.t0),t.$message({message:a.t0.message||"删除失败",type:"error"});case 22:return a.prev=22,t.delBatchIds=[],t.delBatchLoading=!1,1===e.length&&n&&t.$set(n,"loading",!1),a.finish(22);case 27:case"end":return a.stop()}},a,t,[[2,18,22,27]])}))()},del:function(e){var t=this;return o()(s.a.mark(function a(){return s.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(console.log(e),e.id){a.next=4;break}return t.$message({message:"请选择要删除的词汇",type:"warning"}),a.abrupt("return");case 4:return t.$set(e,"loading",!0),a.next=7,t.delApi([e.id]);case 7:case"end":return a.stop()}},a,t)}))()},handleSelectedChange:function(e){console.log("🚀 ~ handleSelectedChange ~ val:",e),this.delBatchIds=e.map(function(e){return e.id})},delBatch:function(){var e=this;0!==this.delBatchIds.length?this.$confirm("确定删除所选词汇吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.delApi(e.delBatchIds),console.log("确定")}).catch(function(){console.log("取消")}):this.$message({message:"请选择要删除的词汇",type:"warning"})},handleCurrentChange:function(e){this.page.page=e,this.getList()},handleSizeChange:function(e){this.page.limit=e,this.getList()},getList:function(){var e=this;return o()(s.a.mark(function t(){var a,n,r,l;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,e.loading=!0,t.next=4,d(i()({},e.filterForm,e.page));case 4:if(a=t.sent,n=a.code,r=a.data,l=a.message,200!==n&&e.$message({message:l||"获取列表失败",type:"error"}),r){t.next=11;break}return e.tableData=[],e.total=0,t.abrupt("return");case 11:e.total=r.total,e.tableData=r.data||[],t.next=18;break;case 15:t.prev=15,t.t0=t.catch(0),console.log(t.t0);case 18:return t.prev=18,e.loading=!1,t.finish(18);case 21:case"end":return t.stop()}},t,e,[[0,15,18,21]])}))()},submitSuccess:function(e){this.row=e,console.log("提交成功"),this.tableData=this.tableData.map(function(t,a){return t.id===e.id?e:t}),this.getList()}},created:function(){this.getList()},mounted:function(){}},y={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"header_title"},[a("h1",[e._v("会议纪要词汇库")]),e._v(" "),a("span",{staticClass:"header_title_desc"},[e._v("请在此页添加您企业的特有词汇（包括行业术语、企业产品等），用于AI生成会议纪要时进行精准识别与转义")])]),e._v(" "),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"meeting_content"},[a("div",{staticClass:"filter_container"},[a("div",{staticClass:"btns"},[a("el-button",{staticClass:"add_btn",attrs:{type:"primary",size:"small"},on:{click:e.addWord}},[e._v("添加特有词汇")]),e._v(" "),a("el-button",{staticClass:"del_btn",attrs:{type:"danger",size:"small",loading:e.delBatchLoading},on:{click:e.delBatch}},[e._v("批量删除")])],1),e._v(" "),a("el-form",{ref:"filterForm",attrs:{model:e.filterForm,inline:""}},[a("el-form-item",[a("el-input",{attrs:{placeholder:"请输入词汇名称",size:"small",clearable:""},model:{value:e.filterForm.title,callback:function(t){e.$set(e.filterForm,"title",t)},expression:"filterForm.title"}})],1),e._v(" "),a("el-form-item",[a("el-button",{staticClass:"filter_btn",attrs:{type:"primary",size:"small"},on:{click:e.getList}},[e._v("搜索")])],1)],1)],1),e._v(" "),a("div",{staticClass:"meeting_list"},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,height:"calc(100% - 50px)",size:"small","header-cell-class-name":"header-cell-class"},on:{"selection-change":e.handleSelectedChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),e._v(" "),a("el-table-column",{attrs:{prop:"title",label:"词汇名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"created_at",label:"添加时间"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-button",{staticClass:"edit_btn",attrs:{type:"text",size:"small",loading:n.loading},on:{click:function(t){return e.edit(n)}}},[e._v("编辑")]),e._v(" "),a("el-popconfirm",{attrs:{title:"确定删除该词汇吗？"},on:{confirm:function(t){return e.del(n)}}},[a("template",{slot:"reference"},[a("el-button",{staticClass:"del_btn",staticStyle:{"margin-left":"10px"},attrs:{type:"text",size:"small",loading:n.loading}},[e._v("删除")])],1)],2)]}}])})],1),e._v(" "),a("el-pagination",{attrs:{background:"",layout:"total, sizes, prev, pager, next, jumper",total:e.total,"page-size":e.page.limit,"current-page":e.page.page},on:{"current-change":e.handleCurrentChange,"size-change":e.handleSizeChange}})],1)])]),e._v(" "),a("add",{attrs:{"dialog-visible":e.updateVisible,data:e.row},on:{"update:dialogVisible":function(t){e.updateVisible=t},"update:dialog-visible":function(t){e.updateVisible=t},submit:e.submitSuccess}})],1)},staticRenderFns:[]};var C=a("VU/8")(w,y,!1,function(e){a("gtdQ")},"data-v-41598130",null);t.default=C.exports},gtdQ:function(e,t){}});