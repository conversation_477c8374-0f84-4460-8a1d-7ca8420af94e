@mixin flex {
  display: flex;
}
@mixin flexWrap {
  flex-wrap: wrap;
}
@mixin contentCenter {
  justify-content: center;
}
@mixin contentBetween {
  justify-content: space-between;
}
@mixin flexRowCenter {
  //使用@mixin命令定义可重复使用的代码块
  display: flex;
  align-items: center;
}
@mixin flexRowEnd {
  //使用@mixin命令定义可重复使用的代码块
  display: flex;
  align-items: flex-end;
}
@mixin flexRowBetween {
  //使用@mixin命令定义可重复使用的代码块
  display: flex;
  align-content: space-between;
}
@mixin flexColumn {
  //使用@mixin命令定义可重复使用的代码块
  display: flex;
  flex-direction: column;
}
@mixin flexColumnCenter {
  //使用@mixin命令定义可重复使用的代码块
  display: flex;
  flex-direction: column;
  align-items: center;
}
@mixin flexColumnEnd {
  //使用@mixin命令定义可重复使用的代码块
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
@mixin imgStyle {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
@for $i from 1 through 32 {
	.item#{$i} {
		width: calc(100% / $i - (0.13rem * ($i - 1) / $i) - 0.06rem);
    margin-right: .13rem;
    cursor: pointer;
    &:nth-child(#{$i}n) {
      margin-right: 0;
    }
	}
}
* { margin: 0; padding: 0; }
a { text-decoration: none; }
[v-cloak] { display: none; }
body { width: 100vw; height: 100vh; font-size: 0.16rem; background-color: #f5f5f5; }
img { display: inline-block; }

.container {
  @include flexColumn;
  height: 100%;
  .banner {
    width: 100%;
    img {
      @include imgStyle;
    }
  }
  .chat-container {
    flex-grow: 1;
    padding: 0.3rem;
    overflow: hidden auto;
    .chat-item {
      @include flex;
      margin-top: .2rem;
      margin-bottom: .4rem;
      .chat-img {
        width: 0.8rem;
        height: 0.8rem;
        background: #69C997;
        border: 1px solid #E0E0E0;
        border-radius: 0.1rem;
        flex-shrink: 0;
        margin-right: .21rem;
        overflow: hidden;
        img {
          @include imgStyle;
        }
      }
      .chat-content {
        background: #FFFFFF;
        border-radius: 0.1rem;
        font-size: 0.28rem;
        color: #333333;
        line-height: 0.4rem;
        padding: .25rem 0.33rem;
        span {
          color: rgba(54, 105, 255, 1);
          font-weight: bold;
        }
        .tab-box {
          @include flexRowCenter;
          @include flexWrap;
          .tab-item {
            @include flexRowCenter;
            @include contentCenter;
            height: 0.7rem;
            background: #FFFFFF;
            border: 1px solid #3669FF;
            border-radius: 0.04rem;
            font-size: 0.28rem;
            color: #3669FF;
            margin-top: .14rem;
            transition: all ease-in-out .3s;
            &.active {
              background-color: #3669FF;
              color: #FFFFFF;
            }
          }
        }
      }
      &.user {
        flex-direction: row-reverse;
        .chat-img {
          margin-left: .21rem;
          margin-right: 0;
        }
        .chat-content {
          background: #5183F7;
          color: #FFFFFF;
        }
      }
    }
  }
  // 表单
  .form-container {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 1;
    margin: auto;
    width: 100%;
    height: 100%;
    z-index: 100;
    .form-shake {
      background-color: rgba($color: #000000, $alpha: .5);
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }
    .form-main {
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 2;
      width: 100%;
      padding: .25rem;
      box-sizing: border-box;
      background: #FFFFFF;
      border-radius: 0.2rem 0.2rem 0rem 0rem;
      .f-title {
        font-size: 0.28rem;
        text-align: center;
        font-weight: bold;
        color: #222222;
        line-height: 0.95rem;
        margin-bottom: .25rem;
      }
      .form-list {
        .form-item {
          height: 0.9rem;
          background: #F5F5F5;
          border-radius: 0.45rem;
          margin-bottom: .2rem;
          padding: .2rem .4rem;
          box-sizing: border-box;
          input {
            width: 100%;
            height: 100%;
            border: none;
            background: none;
            font-size: .28rem;
            font-family: "Microsoft YaHei";
            &:focus {
              outline: none;
              border-color: #00bfff00;
              box-shadow: 0 0 5px #00bfff00;
            }
          }
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
      .read {
        height: 1rem;
        @include flexRowCenter;
        img {
          width: 0.36rem;
          height: 0.36rem;
          margin-left: .3rem;
          cursor: pointer;
        }
        .text {
          font-size: 0.28rem;
          color: #999999;
          margin-left: .18rem;
          a {
            color: rgba(54, 105, 255, 1);
          }
        }
      }
      .form-btn {
        cursor: pointer;
        @include flexRowCenter;
        @include contentCenter;
        height: 0.9rem;
        background: #3669FF;
        border-radius: 0.45rem;
        font-size: 0.28rem;
        color: #FFFFFF;
        margin-bottom: .24rem;
      }
    }
  }
}
@media screen and (min-width: 768px) {
  $scale: 1.2;
  
  @for $i from 1 through 32 {
    .item#{$i} {
      width: calc(100% / $i - (6px * $scale * ($i - 1) / $i) - 3px * $scale);
      margin-right: 6px * $scale;
    }
  }
  .container {
    width: 375px * $scale;
    margin: auto;
    .chat-container {
      padding: 15px * $scale;
      .chat-item {
        margin-top: 10px * $scale;
        margin-bottom: 20px * $scale;
        .chat-img {
          width: 40px * $scale;
          height: 40px * $scale;
          border-radius: 5px * $scale;
          margin-right: 10px * $scale;
        }
        .chat-content {
          border-radius: 5px * $scale;
          font-size: 14px * $scale;
          line-height: 20px * $scale;
          padding: 12px * $scale 16px * $scale;
          .tab-box {
            .tab-item {
              height: 35px * $scale;
              border-radius: 2px * $scale;
              font-size: 14px * $scale;
              margin-top: 7px * $scale;
            }
          }
        }
        &.user {
          .chat-img {
            margin-left: 10px * $scale;
          }
        }
      }
    }
    // 表单
    .form-container {
      width: 375px * $scale;
      .form-main {
        padding: 12px * $scale;
        border-radius: 10px * $scale 10px * $scale 0rem 0rem;
        .f-title {
          font-size: 14px * $scale;
          line-height: 42px * $scale;
          margin-bottom: 12px * $scale;
        }
        .form-list {
          .form-item {
            height: 45px * $scale;
            border-radius: 24px * $scale;
            margin-bottom: 10px * $scale;
            padding: 10px * $scale 20px * $scale;
            input {
              font-size: 14px * $scale;
            }
          }
        }
        .read {
          height: 50px * $scale;
          img {
            width: 18px * $scale;
            height: 18px * $scale;
            margin-left: 15px * $scale;
          }
          .text {
            font-size: 14px * $scale;
            margin-left: 9px * $scale;
          }
        }
        .form-btn {
          height: 45px * $scale;
          border-radius: 24px * $scale;
          font-size: 14px * $scale;
          margin-bottom: 12px * $scale;
        }
      }
    }
  }
}