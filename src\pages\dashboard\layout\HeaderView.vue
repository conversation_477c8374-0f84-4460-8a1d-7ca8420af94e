<template>
  <div class="header-wrapper flex flex-align-center">
    <div class="header-left flex flex-align-center">
      <router-link class="logo" to="/home">
        <img src="@/assets/image/dashboard/layout/logo.png" alt="logo" />
      </router-link>
      <!-- 竖线 -->
      <div class="line"></div>
      <div v-if="userInfo" class="company-info flex flex-align-center">
        <span class="company-title">{{ userInfo.companyName }}</span>
        <img v-if="userInfo.is_certified == 1" class="icon" src="@/assets/image/dashboard/layout/qy-icon.png" alt="">
        <img v-else class="icon" src="@/assets/image/dashboard/layout/qyn-icon.png" alt="">
        <i class="change-wrap flex flex-align-center"><img src="@/assets/image/dashboard/layout/change.png" alt=""></i>
      </div>
    </div>
    <div class="header-right flex flex-align-center">
      <div class="nav-wrapper flex flex-align-center">
        <template v-for="(item, index) in headerNav">
          <div v-if="filterNav(item)" class="nav-item flex flex-align-center">
            <div class="nav-title" @click="navClick(item)">{{ item.name }}</div>
            <div v-if="item.children && item.children.length" class="second-nav-wrap">
              <div class="second-nav">
                <template v-for="(child, indexC) in item.children">
                  <div class="second-item flex flex-align-center">
                    <div class="second-title" @click="navClick(child)">{{ child.name }}</div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </template>
      </div>
      <div class="line"></div>
      <router-link class="icon-box" to="/message">
        <el-tooltip effect="dark" content="消息" placement="bottom">
          <img src="@/assets/image/dashboard/layout/h-1.png" alt="">
        </el-tooltip>
      </router-link>
      <div class="line"></div>
      <a class="icon-box" href="/#/home/<USER>" target="_blank">
        <el-tooltip effect="dark" content="帮助文档" placement="bottom">
          <img src="@/assets/image/dashboard/layout/h-2.png" alt="">
        </el-tooltip>
      </a>
      <div class="line"></div>
      <div v-if="userInfo" class="user-info flex flex-align-center">
        <div v-if="userInfo.avatar" class="user-avatar">
          <img :src="userInfo.avatar" alt="">
        </div>
        <div v-if="userInfo.phone" class="user-name">
          {{ userInfo.phone | formatPhone }}
        </div>
        <!-- 红点 -->
        <div v-if="weakPass" class="red-dot"></div>
        <div class="second-nav-wrap">
          <div class="second-nav">
            <router-link class="second-item flex flex-align-center" to="/personal">
              <div class="second-title">个人信息</div>
            </router-link>
            <div class="second-item flex flex-align-center" @click="userLogout">
              <div class="second-title">退出登录</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

let navList = [
  {
    name: '费用', path: '/console/order', children: [
      { name: '订单', path: '/console/order' },
      { name: '续费', path: '/console/orderRenew' },
      { name: '充值', path: '/console/wallet' },
      { name: '发票', path: '/console/invoice' },
    ]
  },
  {
    name: '工单', path: '/workorder', children: [
      { name: '提交工单', path: '/workorder' },
      { name: '我的工单', path: '/workorder/list' },
    ]
  },
  // {
  //   name: '企业', path: '/company/detail', children: [
      // { name: '企业信息', path: '/company/detail' },
      // { name: '企业认证', path: '/company' },
      // { name: '企业消息', path: '/information/list' },
      // { name: '企业官媒', path: '/company/thirdSave' },
      // { name: '开发者认证', path: '/company/developerCertification' },
      // { name: '企业云盘', path: '/company/yunpan/list' },
      // { name: '操作日志', path: '/company/log/list' }
  //   ]
  // },
  { name: '首页', path: '/home', target: '_blank' }
]

export default {
  name: 'HeaderView',
  computed: {
    ...mapState(['user', 'zhUserRole', 'userInfo', 'weakPass', 'companyNav']),
    isOwner() {
      return this.userInfo && this.userInfo.company && this.userInfo.company.is_owner == 1
    },
    // 是否员工
    isEmployee() {
      // 判断token最后一位是u
      return this.token && this.token.slice(-1) == 'U'
    },
    token() {
      return (
        this.$cookies.get('token') ||
        this.$cookies.get('access_token') ||
        this.$store.getters.token
      )
    },
    headerNav() {
      let list = JSON.parse(JSON.stringify(navList))
      list.forEach(item => {
        if (item.name == '企业') {
          item.children = this.companyNav.map(item => {
            return { name: item.label, path: item.url }
          })
        }
      })
      return list
    }
  },
  filters: {
    formatPhone(phone) {
      if (!phone) return ''
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    }
  },
  data() {
    return {
      
    }
  },
  methods: {
    ...mapActions(['getWeakPss', 'userLogout', 'getUserInfo', 'getCompanyNav']),
    navClick(item) {
      if (item.target && item.target == '_blank') {
        window.open(`/#${item.path}`, '_blank')
        return
      }
      this.$router.push(item.path)
    },
    // 处理菜单显示问题
    filterNav(item) {
      if (item.children && item.children.length == 0) {
        return false
      }
      switch (item.name) {
        case '费用':
          return this.isOwner;
        case '工单':
          return !this.isEmployee;
        case '企业':
          item.path = this.isEmployee ? '/company/information/list' : '/company/detail'
          return true
        default:
          return true
      }
    }
  },
  created() {
    this.getCompanyNav()
    this.getWeakPss()
    this.getUserInfo()
  }
}
</script>

<style scoped lang="scss">
.header-wrapper {
  justify-content: space-between;
  height: 52px;
  padding: 0 20px;

  .line {
    width: 1px;
    height: 16px;
    background: #D5D9E5;
    margin: 0 14px;
  }

  .header-left {
    .logo {
      height: 26px;
    }

    .company-info {
      .company-title {
        font-weight: 500;
        font-size: 14px;
        color: #111111;
      }

      .icon {
        width: 14px;
        margin: 0 7px;
      }

      .change-wrap {
        justify-content: center;
        width: 30px;
        height: 20px;
        background: rgba(255, 255, 255, 0.65);
        border-radius: 10px;
        cursor: pointer;
        margin-left: 3px;

        img {
          width: 12px;
        }
      }
    }
  }

  .header-right {
    .nav-wrapper {
      .nav-item {
        padding: 0 13px;
        height: 32px;
        border-radius: 6px;
        background-color: transparent;
        cursor: pointer;
        position: relative;
        // transition: all .3s;
        margin: 0 10px;

        .nav-title {
          font-weight: 500;
          font-size: 14px;
          color: #40434A;
        }



        &:hover {
          background: #CBDFF1;
          border-radius: 4px;

          .second-nav-wrap {
            // height: 160px;
            z-index: 99;
          }
        }
      }
    }

    .second-nav-wrap {
      position: absolute;
      top: 100%;
      left: -44px;
      width: 150px;
      z-index: -1;
      // height: 0px;
      padding-top: 8px;
      transition: all .3s;

      .second-nav {
        width: 110px;
        background: #FFFFFF;
        box-shadow: 0px 3px 20px 0px rgba(107, 117, 141, 0.1);
        border-radius: 6px;
        padding: 10px 0 5px 0;
        margin: auto;
      }

      .second-item {
        padding: 0 10px;
        height: 32px;
        cursor: pointer;
        justify-content: center;
        margin: 0 10px 5px 10px;

        .second-title {
          font-weight: 500;
          font-size: 14px;
          color: #6D717A;
        }

        &:hover {
          background: #F1F5FF;
          border-radius: 4px;
        }
      }
    }

    .icon-box {
      width: 20px;
      height: 20px;
      cursor: pointer;
      margin: 0 10px;

      img {
        width: 100%;
        display: block
      }
    }

    .user-info {
      cursor: pointer;
      position: relative;

      .user-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin-right: 8px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          display: block;
          object-fit: cover;
        }
      }

      .user-name {
        font-weight: 500;
        font-size: 14px;
        color: #40434A;
      }
      .red-dot {
        position: absolute;
        top: 0;
        right: -5px;
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background: #FF0000;
      }
      .second-nav-wrap {
        left: -10px;
        padding-top: 10px;
      }

      &:hover {
        .second-nav-wrap {
          z-index: 99999;
        }
      }
    }
  }
}
</style>