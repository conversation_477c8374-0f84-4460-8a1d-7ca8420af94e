<template>
  <div v-if="secondNav && secondNav.length" class="second-wrapper">
    <template v-for="(item, index) in secondNav">
      <div class="second-item is-top" :class="{'active': item.path == $route.path}" @click="toPath(item)">
        <span>{{ item.statistic }}</span>
      </div>
      <div class="second-child" v-if="item.children && item.children.length">
        <template v-for="(child, indexC) in item.children">
          <div class="second-item" :class="{'active': child.path == $route.path}" @click="toPath(child)">
            <span>{{ child.statistic }}</span>
          </div>
        </template>
      </div>
    </template>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { findChildren } from '@/utils/index'

export default {
  name: 'SecondMenu',
  computed: {
    ...mapState(['dashboardMenus']),
    secondNav() {
      const route = this.$route;
      const path = route.path;
      // 当前path和哪个相同或子集中相同
      let nav = [], redirect = ''
      this.dashboardMenus.forEach(item => {
        if (item.path === path) {
          nav = item.children, redirect = item.redirect
        } else {
          const childrenPath = findChildren(item)
          // console.log('childrenPath', childrenPath)
          if (childrenPath.includes(path)) {
            nav = item.children, redirect = item.redirect
          }
        }
      })
      return redirect ? nav : []
    }
  },
  created() {
  },
  methods: {
    toPath(item) {
      if (item.path === this.$route.path) return;
      if (item.redirect) {
        this.$router.push(item.redirect)
        return;
      }
      this.$router.push(item.path)
    }
  }
}
</script>

<style lang="scss" scoped>
.second-wrapper {
  background: #fff;
  width: 130px;
  flex-shrink: 0;
  transition: all .3s ease-in-out;
  padding: 18px 7px 20px 7px;
  .second-item {
    padding: 0 11px;
    height: 34px;
    line-height: 34px;
    font-size: 14px;
    color: #6d717a;
    cursor: pointer;
    &.is-top {
      height: 36px;
      line-height: 36px;
      font-weight: bold;
      font-size: 15px;
      color: #111;
    }
    &.active {
      background: #f5f9ff;
      color: #2859ff;
    }
  }
}
</style>