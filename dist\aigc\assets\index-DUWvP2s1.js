import{k as W,A as u,f as R,w as r,I as Y,o as a,q as H,b as e,J as O,c,F as S,s as V,y as M,t as h,_ as K,B as j,a as l,m as N,H as F,r as D,n as G,j as J,K as P,p as f,L as z,M as T,h as b,e as X,N as Z,O as q}from"./index-BBeD0eDz.js";/* empty css               */import{_ as $}from"./index-BUwi-MV-.js";/* empty css                *//* empty css               *//* empty css                */import{g as ee,a as te,b as se,c as oe}from"./index-sm6CVty6.js";import{c as ae,d as ne}from"./aiVideo-B86MWzMI.js";/* empty css              */import"./empty-CSpEo1eL.js";import"./request-Ciyrqj7N.js";const ie="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAl1JREFUOE99VE1oE1EQ/r63sTFNWykVT4Xqoaci4kFbCh79a2y7W/HSi1g86MVjkehasqF4FkE9iWAjKLRJRaSI6K0aQS+KelCr2KtWSLMmpPtGNjVtdtP03d58M9/MfDPvEQ1HaE6XT0DrcQoGQXQDoADLBBZFqUzuSnRh3bR5WH8Zdsp9EXp3ARxuTLBpESAvhjExn4x+rFk3iMz06jFCzQJo245kAxOsampr3m577tuqRMNOoc9gJE9IvJ5EBG9JLoigRMohAKcAqLqGCtpQA35lBIRW2s0D9B3rEsr1nB1PJi7/6Wzp2KWySf6yUu4ZKDwKVixvsnbrAMdSpYQo/SQACpZXdH5fp9E/AuChX4UIz+auxWbMtJtnSEPRMkQz7WYIjId0mcvarafNdHGW4JiPCXkvdzV2znLcORBWSIIZWs7fJVD2hoi+ApiE4IY/fgHKIjza8n7n4tqB0hdIyF/wzdfHCwrYMLMStB6JfIi/WNvv3gF5foupVvzWKgQizUZO4NKc3XqziQS1sAotx10CEW5tg7eyVupRRrTLIN813S+/NdNxM2SD2JtroNSQeHJQUaabEQlwn2OpYkIUg+MPRvwQIE5gd/ON1yfXF9Jx82BwIatBgu8rOtbbIe4eI8Ll2ksIEb7O2rHB6hMx08WLBG9tkfG39rwj5I4eKv10C7wAeP1Zu/1TlchyihdA3m5eeiMiQEFrb/TxVPtLH60SjaZcSylM/nfvAtC7LanglYoYE7PJ6OeaX+A/Wjf6j7h8XCDjhAwK0O07ieAnIYta5MH8VPxZ+GP7ByII59yuogziAAAAAElFTkSuQmCC",le="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAYCAYAAADkgu3FAAAAAXNSR0IArs4c6QAAAkxJREFUSEvtlj9MU1EYxc+5UCi1RvlTXlGJMTqYYKJxYJIYFuNoGEkwRB0c2sqgiW4wSeKg0sbFAUM0Lg6OhsV/TC6Y+GdUMSJ9LaLEtiJt7zGvhaYgIIlr7/Ry7/ed37v3fee7j6ga4WjuCoARQI0CkpBeWlN/Lz3mf1Idt/45FMucNhaDAE+Q6ICwLGjETQRHV2O5FpRNA2j7S1R6tkwzsBAPfKlea4nm9jUAE4B61+dIWnQTwd0bgpxo9juBymJ1soRZwPS4iaaP3rwT+XGA9D0H0LnxbrmUjAeaKiAn9vuIlN+TigcntwJ5CRJeu8uB7hKoIfeKxLHNj7QMao9mTpH6SieanTXkxNxY4Nq/QJ5o0ZqTQAF1xni72WKUQR2x3HVrNcBwNCuSo9sFQbb8PWiebhck6WoNhNrReVVXK4aybWo+qhVDpYH+TzFIuu0mgkPhSPYtiK4tu/K2QXifjO/ocmK5W5Qulbo3gHfJZOBoW/vvg4aF8wYMCdpP0rvYWtaANwctCJwmNCMyVSwWxudDOz+Ev/16A+gww5HsZxCdAh/Yor2cvhNMVoSHZcJupkf1dUOEzmzkI4GPjeXNuZB/CsO0q7mtF3N7fT6NAegDME8nlr1A4e5KgJWQApGC4AL4RGIasI+seJzkOGX7S76FeShokIuZKTXv6jPW9oI8BKhVQDMBB4BZcfmN0l+QE8n0k+YcpGaQPwGlIRZKglQTAb+V7ueNeeGT8Xnzedp8o9At6GzpwhWWABbLLyG/J0uiQdKk684M/wHcJ/dQ8C17rgAAAABJRU5ErkJggg==",re="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAYCAYAAAD+vg1LAAAAAXNSR0IArs4c6QAAAkVJREFUSEutlE1IVFEUx3/nDlr6HKJw5g1hEEVBm6CdkIs+CFq5CKOgpbTpA6T0RbRpUYtmJiyMiJCWIUhI2xZRUKs2WVBR0KKgZlSo1Pd8anNPjYWZzsybV97lPf//j3POPfcINU7gue0WTomyB2QjEIrhhVWGHau3JF/0q9mlUkAPkwg2Z64jnAAqaoAPlOhyrhaeVWJUNPmeOwjSXauackxVJ1VMezL7+fVy7Qpw6KUPlDAPoqCLcdWnTq7YEQn2Pfc+SGfdYKBBS7sac+PPl3pWZOz3ZcYRWuOAgZNOtnCzNtjLhMCaOGARzjVfKWRrggPPfavItjhgtXqsJV+8GwXuV6QnBnjOztCWHCiMR4A3tFkaXgmSrAeuqv0tueKZyKkoC3zP7RRkWKGxFvznj3zUFBYOygCzdYHLosmzqY5EInEb2LECrjovhhtNQfF8JWhZX+27LrD0ImY2SO8vIXsXdoVqCPIS+T7iZCc+1aymWnCq100bNZskYRsq7pOSmbdiPybzxbHIXTF1OpMya7UH4SjIlnoeD/S9VYYI5drSyVhsReClDylmEFhfH/BvlcIXY213c35sZLHHgZfqUswQSOJfoH88WhLVI825sXsy3ZfKIOaNIOv+D/rLreg3tWwX33MvgVxYDeiSzC+L3+eOIrJzlcGjEniZUGNus8gklFnxvYxGCuMLtNzjJyC743urO1R5LNrrOjOG46qyD8NWLK0iOMDC6CnaCGKWTa0VZO733XdFp1EmgHdW5OHUV3PnBxLU1LG0OGUCAAAAAElFTkSuQmCC",ce="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAXCAYAAAD+4+QTAAAAAXNSR0IArs4c6QAAAUlJREFUSEvtlj1Lw1AUht+ToLbxAz/AxIKjq3+hZBCEQhHEoZsguMU/oyg6ZXMQUQrFQURHR3eLiEJJFLFCUqsmORJBxYrNvYGAQ+96z3keznvgckm3/AMCFpBwGGhEoVq838zVk2o778mwfJZouuHgzXS3Rq8keiAridnXzLwtIiEoESl8kkYiwv+uYTxmLwFSxSU3yf+VMO5AmJQZR2onzKiqr1ol7G/tEqEsKpKR1Bw0FrE+8wLrcsBAYR9ASUQkJGHGsetpZdjU/oIuc04falWJMJckSpQw6NR1HkrYm37+BVu6zevGRI3AZjdRV0n8XhGwCo78GNL2goumPdYcWXka1/LK7AeYlEEGdggo/CVKnORHI0emszF8NrXmzTPTUVJMn/c9SXZxcRRVKAzOofYVoZCdzU5EqR11covvSUR/KymTAgOH7xBcrth9ip+lAAAAAElFTkSuQmCC",Ae="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAbCAYAAAB836/YAAAAAXNSR0IArs4c6QAAAzdJREFUSEvFVEFoFGcU/r5/k9TsBCVoMkMPoUqx7aFRi+2hUERFqBYRtQdBWlpKi4UetDqTCqbdRsFkV0sOQkW09SB4UYgebA9txEMp1UIUwRRBbC9hZxtpAjuTZOP+r+wfNk5mZ9fgxTnssO9973vv+7//DZHw/PkZml9dZr+vFHeLxluAvDgH4xgVbmotl/+a9C+tP4PZeDnjgcB1tgE4BWJlUrP5mOAhgC+sXP5aFLeAMPCcDARfg6hplEguEBB9VjafqebnCwPXPgLyaMOp6iVFeq2cf8wcSuVnyl2xQTM1DFAl1YigQGIaQFcyp2gl5U2tufEbFIChZ98B+Hr9AcrrOCs+WprG6iuQu+msv4ZFt+NdMvXTE6Bo0fgAKXRReNzIeKy6WmVsPGx2QvNfMAyle7WoHwmsrtaKlLcy8OyzAD+JHGrRL+Q7XjqPmcBzRgm8AsE1zEneZQi1fjt9ovB74Dk/APg4Msw5Bp5zD8BrUSkCGUwJhzTlCsBlNTKJPtH6N0JdBfFCJD/KwHWmY8FnMtoUCWYqhDp+7whMlYETCngDwHsLOszJvwXABZCO5aRyhhO1suSslfU/rYCDHmcrBYNmAGK/NZA3BhZd+wLJvQvlyGRlwpsg3owlHpTKM5vbT078Y4gyaDFmZFCqvMOe9i5Iy68CvrygjviDoesMCOElHFwIrfvT44UczxuHIR9hSbii04VSX9XINddJ+lk81NlNpW5XtyZOLJDrbVl/k5Hp2cMENya6JhAt0m1WL3DtqyC3J9srt62sv87gPHsE4No6uCEr6+80hBMH7FXNTRgBubQWvBhCmSyVuLZ9MP935GvTsQ1QQyCbYwY9bcKSSHlHW+7fn41x0eKi17mFUBcBLI/E/0uXldnXIKXvE2iP5B6llOxZ0u//Uo3VfrE953sA+6KNBDI5171mDU9b2fznUeyiCJNNMNHnQBj2dH4pok42mGo+JdQH2wYK3zWUXFmzqcD+Vsg19S67WWuRO62W/011HeuaUiGcDu13yqJi1yc2M/WslfZvMIPHjU1x7aMgjyxKssjhtpzf35Cw2ON8SJFzAJueQloisDedzV+K4v4HO7FJ0zHdvREAAAAASUVORK5CYII=",ue="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAYCAYAAADkgu3FAAAAAXNSR0IArs4c6QAAAvFJREFUSEu1lUtIVGEUx///746Kc9XEzDtaWhg9iFoUIW2EVkXaQ9IBIXpAYZaOtehlEploqyDqWosWbVoEGUTvkOgBUZuk2rSKICqZq2H5uKOFc0/MaDHqvNS6y/udc37f//uf833Ef/qy9vbnpLtTjhGyF8Bn/mvOvIM9GUplHgKdIwSyQ/UF/PDvQD5JM2S4lpCTIPImCuCN2YPWP3UZq0p2ETgNoCj6CcmJWYCEnoZhLwQtgCyLZ4GjtA0zAnnq7DIotAJYnYzHthPMnRZonm+4VIO0AVIaDSCCr6BkEsyKWP/kN/VFSYFy6+w1rjEFm2IrkPcQfgSxOTJGBLesdn17XNDc+oHlLmgtBKpAxIwV4B3gXCXUhckbEeCUZeqtUZPzGwJFjuM0g2onIa74PsibUWGNBnlMcs6UWAfl/kv6gwkgo1bykGI3EdwPIC2h0YLXoFMmUHcIrIsW7yjx9FzIsMKg7D3fs9N011EqrQGQjISA0LQLXgXd7o1aINBIojFqcwDdlqnPD60xr96uVUQbgJxkAGMxfD5qp2/R0u0SKHYSUDFAdy1T3xrOMHxD/ZPaMS5PRJ6gf2grs3W3QL0lUBAzgTjjv6g3h0Ee3+AKobaPIpWxr5CxUgJ0Wv6+CnQsGPHUB+6BKIu3KyEqrIv67TAoMtDjC5Qg1MqCSlCKJxW570d3JcwlPw1f4DAh5xMd9S+wsM90f5kCikwMDalGVJHYIYIuy3JXo4O/xof3ZRJd2eM3deNPzcQ3g1c09IJ4xtHQW6MpdxfIpYnUAHjkN/W/N0liUETFgpqB3GCadoiC3SAK4/oDnLVMvSl5RRHVjAMjxdBGt1HjCyfIVAVUAOIFsXAyNBh0vL2XM2/ODFQ3WE2lro934IAjqO5t1x/m19trBeIVspLAYoH8CPx0lgxeyfo2a1BoaOXHQLl1zWNHqjFqB1cGXarvW7veHfl/Wh4ZfxVFh8TzbFqg8Muq8bikppdZ5zhBSaIu/A1T4AYY0+szyQAAAABJRU5ErkJggg==",de={class:"guide-steps bg-[#F9FAFF] p-[30px] !pb-[10px]"},pe={class:"flex items-center"},me={class:"step-icon flex items-center justify-center mr-[14px]"},ge=["src"],ve={class:"step-title text-[16px] text-[#111111] font-medium mb-1"},fe=["innerHTML"],he={class:"step-status"},Ce={key:0,class:"step-action success flex items-center justify-center"},Ie=["onClick"],ye=W({__name:"guide",setup(L,{expose:x}){const B=M(),d=u(!1),C=u([{icon:le,type:1,title:"第一步：构建知识库",desc:'补充企业<span class="text-[--el-color-primary]">知识库</span>完善企业信息及主营产品信',status:"done",action:"完善",route:"/document/knowledge"},{icon:ce,type:2,title:"第二步：上传素材",desc:'进入素材广场，点击<span class="text-[--el-color-primary]">上传素材</span>添加剪辑所需的视频、图片、音频素材',status:"todo",action:"上传",route:"/material-market"},{icon:Ae,type:3,title:"第三步：智能创作",desc:'进入智能成片，根据场景点击<span class="text-[--el-color-primary]">AI一键成片、AI精准成片、数字人口播</span>成功生成第一个视频',status:"todo",action:"创作",route:"/smart-material/ai-image"},{icon:re,type:4,title:"第四步：账号授权",desc:'进入矩阵分发，点击<span class="text-[--el-color-primary]">账号授权</span>添加首个账号',status:"done",action:"授权",route:"/short-video/auth"},{icon:ue,type:5,title:"第五步：矩阵分发",desc:'最后点击<span class="text-[--el-color-primary]">发布视频</span>将制作好的内容发布至授权平台账号中',status:"todo",action:"发布",route:"/short-video/publish"}]),E=A=>{B.push(A.route),d.value=!1},g=()=>{d.value=!0,k()},I=()=>{d.value=!1},y=u(!1),k=async()=>{y.value=!0;try{const A=await ee({});console.log(A,"引导状态");const{list:i}=A;i.length>0&&i.forEach(p=>{const m=C.value.find(n=>n.type===p.type);m&&(p.status==1?m.status="done":m.status="todo")})}catch(A){console.error("获取引导状态失败",A)}finally{y.value=!1}};return x({openDialog:g,closeDialog:I}),(A,i)=>{const p=Y,m=O;return a(),R(p,{modelValue:d.value,"onUpdate:modelValue":i[0]||(i[0]=n=>d.value=n),title:"新手引导",width:"900px","align-center":"","show-close":!0,"close-on-click-modal":!1,class:"guide-dialog"},{default:r(()=>[H((a(),c("div",de,[(a(!0),c(S,null,V(C.value,(n,s)=>(a(),c("div",{class:"step-item flex items-center justify-between mb-4",key:s},[e("div",pe,[e("div",me,[e("img",{src:n.icon,alt:"step-icon"},null,8,ge)]),e("div",null,[e("div",ve,h(n.title),1),e("div",{class:"step-desc text-[14px] text-[#999999]",innerHTML:n.desc},null,8,fe)])]),e("div",he,[n.status==="done"?(a(),c("div",Ce,"已完成")):(a(),c("div",{key:1,class:"step-action flex items-center justify-center",onClick:t=>E(n)},"去"+h(n.action),9,Ie))])]))),128))])),[[m,y.value]]),i[1]||(i[1]=e("div",{class:"h-[10px]"},null,-1))]),_:1},8,["modelValue"])}}}),xe=K(ye,[["__scopeId","data-v-b8fd19c4"]]),Ee=""+new URL("data-icon-CQfW2I9e.png",import.meta.url).href,_e=""+new URL("doc-icon-K3p153_y.png",import.meta.url).href,Be=""+new URL("ai-icon-BFuHa2V0.png",import.meta.url).href,ke=""+new URL("distribute-icon-DKkPNGuF.png",import.meta.url).href,Ue=""+new URL("pricing-jc-CW-wH5U6.png",import.meta.url).href,we=""+new URL("pricing-zy-CKIoCJro.png",import.meta.url).href,Fe=""+new URL("pricing-qy-nm5CKGhD.png",import.meta.url).href,Re=""+new URL("pricing-qj-Blu0Y_cy.png",import.meta.url).href,Se={class:"home-container"},Ve={class:"features-section"},Qe={class:"features-left"},Ne={class:"shortcut-cards"},Je=["onClick"],We={class:"card-content"},Me={class:"card-text"},Ke={class:"card-icon"},Le=["src","alt"],Ye={class:"data-cards"},He={class:"data-title"},Oe={class:"data-value-wrap"},je={class:"data-value"},De={class:"data-trend"},Ge={key:0,class:"search-box flex items-center"},Pe=W({__name:"index",setup(L){const x=M(),B=[{id:1,title:"数字人口播",icon:Ee,route:"/smart-material/user-portrait"},{id:2,title:"文案生成",icon:_e,route:"/document/template"},{id:3,title:"AI一键成片",icon:Be,route:"/smart-material/ai-image"},{id:4,title:"矩阵分发",icon:ke,route:"/short-video"}],d=s=>{x.push(s)},C=u([{title:"账号总播放量",key:"media_auth_play",value:0,comparedToYesterday:0,trend:"up"},{title:"剪辑成片数",key:"smart_material",value:0,comparedToYesterday:0,trend:"up"},{title:"绑定账号数",key:"media_auth",value:0,comparedToYesterday:0,trend:"up"},{title:"账号粉丝数",key:"media_auth_fans",value:0,comparedToYesterday:0,trend:"up"}]),E=u([{name:"基础版",color:"#727AA2",headerImage:Ue,crownColor:"#627797",num:"--",user_num:"--",is_completefilm:0,is_txt:0,seat:"--",matrix:"--",space:"--",content:"--",price:"--"},{name:"专业版",color:"#FFFFFF",headerImage:we,crownColor:"#0C83AF",num:"--",user_num:"--",is_completefilm:0,is_txt:0,seat:"--",matrix:"--",space:"--",content:"--",price:"--"},{name:"企业版",color:"#F27601",headerImage:Fe,crownColor:"#F37B02",num:"--",user_num:"--",is_completefilm:0,is_txt:0,seat:"--",matrix:"--",space:"--",content:"--",price:"--"},{name:"旗舰版",color:"#E7C1A8",headerImage:Re,crownColor:"#FF7338",num:"--",user_num:"--",is_completefilm:0,is_txt:0,seat:"--",matrix:"--",space:"--",content:"--",price:"--"}]),g=u(""),I=u([]),y=u([]),k=()=>{te({}).then(s=>{console.log(s,"首页数据");const{news:t,prolist:U}=s;y.value=t,E.value=E.value.map(v=>{let _=U.filter(w=>w.name===v.name)[0];return _?{...v,..._}:v})})},A=()=>{se({}).then(s=>{console.log(s,"数据概览数据"),C.value.forEach(t=>{s[t.key]&&(t.value=s[t.key].num,t.comparedToYesterday=s[t.key].ratio,t.trend=s[t.key].type==1?"up":"down")})})},i=()=>{oe({name:g.value}).then(s=>{console.log(s,"我的剪辑项目数据"),I.value=s})},p=u(),m=()=>{p.value.openDialog()},n=s=>{x.push(`/smart-material/ai-preview/${s.id}`)};return j(()=>{k(),A(),i()}),(s,t)=>{const U=b,v=P,_=X,w=D("router-link");return a(),c("div",Se,[e("div",{class:"welcome-section"},[t[2]||(t[2]=e("div",{class:"welcome-text flex items-center"},[e("span",null,"欢迎，AI引擎已就位，开始生成您的爆款内容")],-1)),e("div",{class:"flex items-center"},[e("div",{class:"flex items-center ml-[24px] cursor-pointer",onClick:m},t[1]||(t[1]=[e("img",{class:"w-[18px] mr-[4px]",src:ie,alt:""},null,-1),e("span",{class:"text-[#5183F6] text-[16px]"},"新手引导",-1)]))])]),e("div",Ve,[e("div",Qe,[l(F,{title:"推荐快捷方式"},{default:r(()=>[e("div",Ne,[(a(),c(S,null,V(B,o=>e("div",{key:o.id,class:"shortcut-card",onClick:Q=>d(o.route)},[e("div",We,[e("div",Me,h(o.title),1),e("div",Ke,[e("img",{src:o.icon,alt:o.title},null,8,Le)])])],8,Je)),64))])]),_:1})])]),l(F,{title:"数据概览"},{"title-right":r(()=>[l(U,{type:"primary",plain:"",size:"small",class:"today-btn"},{default:r(()=>t[3]||(t[3]=[J("当日")])),_:1})]),default:r(()=>[e("div",Ye,[(a(!0),c(S,null,V(C.value,(o,Q)=>(a(),c("div",{key:Q,class:"data-card"},[e("div",He,h(o.title),1),e("div",Oe,[e("div",je,h(o.value),1),e("div",De,[t[4]||(t[4]=e("span",null,"较昨日",-1)),e("span",{class:G(o.trend==="up"?"trend-up":"trend-down")},[l(v,null,{default:r(()=>[o.trend==="up"?(a(),R(f(z),{key:0})):(a(),R(f(T),{key:1}))]),_:2},1024),J(" "+h(o.comparedToYesterday.toFixed(2))+"% ",1)],2)])])]))),128))])]),_:1}),N("",!0),l(F,{title:"我的项目",class:"project-list-section"},{"title-right":r(()=>[I.value.length>0||g.value?(a(),c("div",Ge,[l(_,{modelValue:g.value,"onUpdate:modelValue":t[0]||(t[0]=o=>g.value=o),onChange:i,placeholder:"请输入项目名称","prefix-icon":f(Z),clearable:"",size:"default"},null,8,["modelValue","prefix-icon"]),l(w,{class:"more-btn ml-[20px] flex items-center flex-shrink-0 text-[#999]",to:"/smart-material/ai-history"},{default:r(()=>[t[6]||(t[6]=e("span",null,"更多",-1)),l(v,null,{default:r(()=>[l(f(q))]),_:1})]),_:1})])):N("",!0)]),default:r(()=>[l($,{projectList:I.value,timeText:"生成时间",onRefresh:i,"show-copy":!1,field:{title:"title",thumbnail:"imgurl",lastEditTime:"updated_at"},onClick:n,"del-api":f(ne),"copy-api":f(ae)},null,8,["projectList","del-api","copy-api"])]),_:1}),l(xe,{ref_key:"guideDialogRef",ref:p},null,512)])}}}),at=K(Pe,[["__scopeId","data-v-a2a66a10"]]);export{at as default};
