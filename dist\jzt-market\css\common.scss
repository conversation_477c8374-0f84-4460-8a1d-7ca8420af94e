body * {
  box-sizing: border-box;
  flex-shrink: 0;
}
body {
  font-family: PingFangSC-Regular, Roboto, Helvetica Neue, Helvetica, Tahoma,
    Arial, PingFang SC-Light, Microsoft YaHei;
  background-color: #ffffff;
  overflow-x: hidden;
  /*min-width: 1100px;*/
}
body,
html,
p,
h3 {
  margin: 0;
  padding: 0;
}

.goBack {
  position: fixed;
  right: 20px;
  bottom: 30px;
  z-index: 99;
}

a {
  text-decoration: none;
}

a,
span {
  display: inline-block;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-center {
  display: flex;
  justify-content: center;
}
.flex-wrap {
  flex-wrap: wrap;
}
.justify-end {
  display: flex;
  justify-content: flex-end;
}
.justify-evenly {
  display: flex;
  justify-content: space-evenly;
}
.justify-around {
  display: flex;
  justify-content: space-around;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.align-start {
  display: flex;
  align-items: flex-start;
}
.align-center {
  display: flex;
  align-items: center;
}
.align-end {
  display: flex;
  align-items: flex-end;
}
.container {
  width: 1320px;
  margin: auto;
}
.img-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.mg-b24 {
  margin-bottom: 24px;
}
.mg-b16 {
  margin-bottom: 16px;
}
.mg-b20 {
  margin-bottom: 20px;
}
.mg-b30 {
  margin-bottom: 30px;
}
.img-box img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-box {
  position: relative;
  height: 571px;
  .banner-s {
    display: none;
  }
  .banner-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  .banner-content {
    position: relative;
    z-index: 10;
    padding-top: 116px;
    .c-img {
      width: 361px;
    }
    h1 {
      font-weight: 800;
      font-size: 66px;
      color: #2c2e4c;
      line-height: 86px;
    }
  }
}
.box-title {
  p {
    font-size: 40px;
    font-weight: bold;
    color: #333333;
    margin-left: 20px;
    margin-right: 20px;
    span {
      color: #4e6fdc;
    }
  }
  .s-title {
    font-size: 20px;
    color: #4e6fdc;
    padding: 0 26px;
    height: 42px;
    line-height: 42px;
    background: rgba(78, 111, 220, 0.1);
    border-radius: 21px;
    width: -webkit-max-content;
    width: max-content;
    margin: 18px auto 0;
  }
}
// 企业营销面临的问题
.section01 {
  padding: 90px 0;
  .s1-content {
    margin-top: 104px;
    @extend .justify-center;
    .img-left {
      margin-top: 54px;
    }
    .s1-list-box {
      flex: 1;
      overflow: hidden;
      .item {
        width: 100%;
        background: #4e6fdc;
        border-radius: 20px;
        padding: 24px 36px;
        box-sizing: border-box;
        color: #ffffff;
        margin-bottom: 70px;
        @extend .align-center;
        .i-num {
          font-weight: 800;
          font-size: 34px;
        }
        .i-text {
          font-size: 20px;
          line-height: 30px;
          flex: 1;
          margin-left: 24px;
        }
      }
    }
  }
}
// 全方位解决企业营销难题
.section06 {
  padding: 90px 0px;
  background: #f5f8ff;
  .s6-list-box {
    padding-top: 80px;
    .item {
      margin-bottom: 60px;
      padding: 0 50px;
      .item-img {
        margin-right: 34px;
      }
      .item-text {
        flex: 1;
        overflow: hidden;
        .item-title {
          font-weight: 800;
          font-size: 28px;
          color: #3C3C3C;
          margin-bottom: 50px;
        }
        .item-desc {
          font-size: 16px;
          color: #8A8A8A;
          line-height: 24px;
        }
      }
      // 偶数
      &:nth-child(2n) {
        flex-direction: row-reverse;
        .item-img {
          margin-left: 34px;
          margin-right: 0;
        }
      }
    }
  }
}

// 我们能做什么
.section02 {
  background: url(../img/u3-1.png) no-repeat left bottom;
  padding-bottom: 60px;
  padding-top: 83px;
}
.section02-list {
  margin-top: 20px;
  .item {
    width: calc(100% / 2 - 20px);
    height: 370px;
    background: linear-gradient(23deg, #f6f8ff, #ffffff);
    border: 1px solid #ececec;
    box-shadow: 0px 1px 50px 0px rgba(213, 213, 213, 0.3);
    opacity: 0.97;
    border-radius: 6px;
    padding: 41px 33px;
    margin-top: 40px;
    margin-right: 40px;
    box-sizing: border-box;
    position: relative;
    &:nth-child(2n) {
      margin-right: 0;
    }
    .item01 {
      font-size: 24px;
      color: #fefeff;
      position: absolute;
      top: 41px;
      left: -10px;
      .item-num {
        width: 44px;
        height: 44px;
        line-height: 44px;
        text-align: center;
        background: #ffdc1b;
        font-weight: bold;
      }
      .item-txt {
        width: 140px;
        height: 44px;
        line-height: 44px;
        background: #4e6fdc;
        box-shadow: 0px 1px 10px 0px rgba(128, 136, 159, 0.2);
        border-radius: 0px 22px 22px 0px;
        text-align: center;
      }
    }
    .item-title {
      font-size: 20px;
      font-weight: bold;
      color: #4e6fdc;
      margin-top: 80px;
    }
    .item-desc {
      font-size: 16px;
      color: #222222;
      line-height: 24px;
      margin-top: 19px;
      height: 48px;
      position: relative;
      z-index: 2;
    }
    .item-btn {
      height: 41px;
      line-height: 41px;
      border: 1px solid #4e6fdc;
      border-radius: 21px;
      padding: 0 17px;
      width: -webkit-max-content;
      width: max-content;
      font-size: 16px;
      color: #4e6fdc;
      margin-top: 58px;
    }
    .num-box {
      position: absolute;
      top: 38px;
      right: 42px;
      z-index: 1;
      img {
        display: block;
      }
    }
    .icon-box {
      position: absolute;
      bottom: 0px;
      right: 0px;
      img {
        display: block;
      }
    }
  }
  .contact-box {
    width: calc(100% / 2 - 20px);
    height: 370px;
    margin-top: 40px;
    border-radius: 6px;
    .input-box {
      margin-left: 46px;
      margin-right: 46px;
      .input-btn {
        width: 100%;
      }
    }
  }
}

.section03 {
  background: #f5f8ff;
  // background: url(../img/u3.png) no-repeat top center;
  // background-size: cover;
  padding-top: 60px;
  padding-bottom: 120px;
  border-bottom: #f8faff solid 20px;
  .content-box {
    margin-top: 74px;
    &.professional-phone {
      display: none;
    }
  }
  .section03-list {
    width: 445px;
    .item {
      padding: 0 40px;
      height: 100px;
      background: #ffffff;
      margin-bottom: 0px;
      border-radius: 10px;
      img {
        width: 22px;
        height: 22px;
        margin-right: 2.13vw;
      }
      p {
        width: calc(100% - 75px);
        color: #222222;
        line-height: 24px;
      }
    }
  }
}

.section04 {
  background-color: #ffffff;
  padding-top: 120px;
  padding-bottom: 70px;
  position: relative;
  overflow: hidden;
  // &::before {
  //   content: "";
  //   width: 600px;
  //   height: 600px;
  //   background-color: transparent;
  //   border: 100px solid rgba(78, 111, 220, 0.1);
  //   border-radius: 50%;
  //   position: absolute;
  //   right: -400px;
  //   top: 314px;
  //   z-index: 1;
  // }
  // &::after {
  //   content: "";
  //   width: 400px;
  //   height: 400px;
  //   background-color: transparent;
  //   border: 100px solid rgba(78, 111, 220, 0.1);
  //   border-radius: 50%;
  //   position: absolute;
  //   left: -300px;
  //   top: 1069px;
  //   z-index: 1;
  // }
  .box-title p {
    margin-right: 20px;
  }
  .s4-content {
    margin-top: 110px;
    position: relative;
    z-index: 10;
    .s4-tab {
      overflow-x: auto;
      overflow-y: hidden;
      width: 100%;
      margin-bottom: 84px;
      .tab-item {
        @extend .flex-row;
        @extend .align-center;
        @extend .justify-center;
        min-width: 120px;
        height: 40px;
        background: #F5F5F5;
        border-radius: 25px;
        padding: 0 18px;
        box-sizing: border-box;
        font-size: 15px;
        color: #111111;
        margin-right: 38px;
        cursor: pointer;
        transition: all 0.3s ease-in-out;
        &.active {
          background: #4E6FDC;
          color: #ffffff;
        }
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .s4-content-box {
      margin-bottom: 80px;
      .s4-content-item {
        display: none;
        &.active {
          @extend .flex-row;
        }
        .c-img {
          flex: 1;
          margin-right: 30px;
        }
        .item {
          width: 420px;
          background: #ffffff;
          box-shadow: 0px 1px 50px 0px rgba(213, 213, 213, 0.4);
          cursor: pointer;
          .top-box {
            height: 140px;
            background: #4e6fdc;
            .img-text-box {
              height: calc(100% - 15px);
              .img-box {
                width: 158px;
                height: 100%;
                background: url(../img/u4-01-.png) no-repeat center bottom;
                background-size: 100%;
                img {
                  width: 40px;
                  height: 40px;
                  object-fit: scale-down;
                }
              }
              h3 {
                font-size: 28px;
                font-weight: 800;
                color: #ffffff;
              }
            }
          }
          .bot-box {
            padding: 45px 33px 50px;
            .item-title {
              font-size: 20px;
              font-weight: bold;
              color: #4e6fdc;
              margin-bottom: 26px;
            }
            .item-desc {
              font-size: 16px;
              color: #222222;
              line-height: 24px;
            }
          }
        }
      }
    }
  }
}

// 按钮
.content-btn {
  display: block;
  width: 268px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  background: #4e6fdc;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 800;
  color: #ffffff;
  margin: 50px auto 0;
}

/* 咨询弹框 */
.contact-box {
  flex: 1;
  height: 560px;
  background: #feeebd;
  box-shadow: 0px 1px 50px 0px rgba(213, 213, 213, 0.4);
  position: relative;
  margin-top: 20px;
  padding-top: 1px;
  .c-title {
    width: 387px;
    height: 73px;
    line-height: 55px;
    text-align: center;
    background: url(../img/u02.png) no-repeat;
    color: #9c5400;
    font-size: 24px;
    margin: -10px auto 0;
  }
  .input-box {
    margin: 20px 85px 0;
    .input-item {
      height: 60px;
      border: 1px solid #fea427;
      background: linear-gradient(0deg, #fef8e6 0%, #fefaed 99%);
      border-radius: 30px;
      padding: 0 49px;
      font-size: 18px;
      color: #a49568;
      width: 100%;
      margin-bottom: 30px;
      &:focus {
        outline: 1px solid #fea427;
      }
    }
    .input-btn {
      width: 550px;
      height: 60px;
      line-height: 60px;
      background: linear-gradient(0deg, #ff8533 0%, #ff633b 100%);
      box-shadow: 1px 10px 21px 0px rgba(255, 101, 59, 0.23);
      border-radius: 30px;
      text-align: center;
      font-size: 24px;
      font-weight: 800;
      color: #ffffff;
      margin: 9px auto 0;
      cursor: pointer;
    }
  }
  .c-img-box {
    position: absolute;
    bottom: 0;
    right: 7px;
    width: 282px;
    height: 270px;
    img {
      width: 100%;
    }
  }
  .txt-box {
    height: 100px;
    line-height: 100px;
    background: linear-gradient(90deg, #ff7936 0%, transparent 100%);
    box-shadow: 0px 1px 50px 0px rgba(213, 213, 213, 0.4);
    padding: 0 54px;
    font-size: 26px;
    font-weight: 800;
    color: #ffffff;
    margin-top: 60px;
  }
}

.section05 {
  background: #4e6fdc;
  // background: url(../img/u5.png) no-repeat center;
  // background-size: cover;
  padding-top: 60px;
  padding-bottom: 60px;
  .c-title {
    font-size: 26px;
    color: #ffffff;
  }
  .c-title-s {
    font-size: 14px;
    margin-top: 11px;
    color: rgba(255, 255, 255, 0.5);
    margin-bottom: 42px;
  }
  .input-box {
    width: 100%;
    justify-content: space-between;
    .input-item {
      width: 300px;
      height: 40px;
      line-height: 40px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      margin-right: 20px;
      border: none;
      box-shadow: none;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      padding: 0 15px;
      box-sizing: border-box;
      //margin-bottom: 30px;
      &::-webkit-input-placeholder {
        color: rgba(255, 255, 255, 0.8);
      }
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .input-btn {
    width: 300px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border: 1px solid #ffffff;
    border-radius: 20px;
    font-size: 14px;
    color: #ffffff;
    margin-top: 38px;
    cursor: pointer;
  }
}

.input-wrap {
  position: relative;
}
.input-error {
  border-color: #f56c6c;
}

.el-form-item__error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 9px;
  position: absolute;
  bottom: 9px;
  left: 49px;
}

.section05 .el-form-item__error {
  left: 15px;
  color: #fff;
}

/*滚动条*/
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-thumb {
  border: 3px solid transparent;
  background-clip: padding-box;
  border-radius: 7px;
  min-height: 84px;
  background-color: rgba(187, 187, 187, 0.88);
}
::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 适配 */
@media (max-width: 1400px) {
  .container {
    width: 94%;
  }
}
@media (max-width: 1300px) {
  .section03 {
    .section03-list {
      width: 33.33%;
    }
    .img03-box {
      width: 33.33%;
      text-align: center;
    }
  }

  .section05 {
    .input-box {
      flex-direction: column;
      width: 50%;
      .input-wrap{
        margin-bottom: 30px;
        .input-item {
          margin-right: 0;
          width: 100%;
          height: 46px;
          border-radius: 30px;
          line-height: 46px;
          padding: 0 30px;
          font-size: 16px;
          margin-bottom: 0;
        }
      }
    }
    .input-btn {
      height: 60px;
      border-radius: 30px;
      line-height: 60px;
      font-size: 16px;
    }
  }
  .section04 {
    .s4-content {
      .s4-content-box {
        .s4-content-item {
          flex-direction: column !important;
          .item {
            width: 70%;
          }
        }
      }
    }
  }
}
@media (max-width: 1100px) {
  .banner-box {
    margin-top: 70px;
  }
  .section06 {
    .s6-list-box {
      .item {
        flex-direction: column;
        padding: 0;
        .item-img {
          margin-right: 0;
          width: 100%;
          img {
            width: 100%;
          }
        }
        .item-text {
          width: 100%;
          padding: 0 30px;
          text-align: center;
          .item-title {
            margin-bottom: 34px;
          }
        }
        &:nth-child(2n) {
          flex-direction: column;
          .item-img {
            margin-left: 0;
          }
        }
      }
    }
  }
  .section03 {
    .section03-list {
      .item {
        height: auto;
        padding: 14px 0 14px 14px;
      }
    }
  }
  .section04 {
    .s4-content {
      .s4-content-box {
        .s4-content-item {
          flex-direction: column !important;
          .item {
            width: 80%;
          }
        }
      }
    }
  }
}
@media (max-width: 1024px) {
  body {
    min-width: auto;
    width: 100%;
    overflow-x: hidden;
  }
  .container {
    width: calc(100% - 6.4vw);
    margin-left: 3.2vw;
    margin-right: 3.2vw;
    box-sizing: border-box;
  }
  .banner {
    display: none;
  }
  .banner-box {
    height: 65.6vw;
    .banner-s {
      display: block;
    }
    .banner-content {
      padding-top: 12.8vw;
      .c-img {
        width: 48.13vw;
      }
      h1 {
        font-size: 8.8vw;
        color: #2C2E4C;
        line-height: 11.47vw;
      }
    }
  }
  .box-title p {
    font-size: 4.8vw;
    margin-left: 2.67vw;
    margin-right: 2.67vw;
  }
  .box-title img {
    width: 3.33vw;
  }
  .box-title .s-title {
    height: 5.6vw;
    line-height: 5.6vw;
    font-size: 2.93vw;
    margin-top: 2.4vw;
    border-radius: 2.8vw;
    padding: 0 3.33vw;
  }
  .section01 {
    padding: 12vw 0;
    .s1-content {
      flex-direction: column;
      align-items: center;
      margin-top: 13.6vw;
      .img-left {
        margin-top: 0;
        margin-bottom: 10.93vw;
        .img1 {
          img {
            width: 39.6vw;
          }
        }
        .img2 {
          display: none;
        }
      }
      .s1-list-box {
        width: 80%;
        overflow: visible;
        .item {
          margin-bottom: 4.13vw;
          .i-num {
            font-size: 5.33vw;
          }
          .i-text {
            font-size: 3.47vw;
            line-height: 4.8vw;
          }
        }
      }
    }
  }
  .section06 {
    padding-top: 7.47vw;
    padding-bottom: 12.8vw;
    .s6-list-box {
      padding-top: 3.33vw;
      .item {
        .item-text {
          margin-top: 3.74vw;
          .item-title {
            font-size: 4.27vw;
          }
          .item-desc {
            font-size: 3.2vw;
            line-height: 4.8vw;
          }
        }
      }
    }
  }
  .content-btn {
    width: 35.73vw;
    height: 9.33vw;
    line-height: 9.33vw;
    font-size: 4vw;
    border-radius: 100px;
    margin: 16vw auto 0;
  }
  .section02 {
    padding-top: 8.93vw;
    padding-bottom: 8.93vw;
    background-size: contain;
  }
  .section02-list {
    margin-top: 10px;
    position: relative;
    padding-bottom: 53.33vw;
  }
  .section02-list .item {
    width: 100%;
    height: auto;
    box-sizing: border-box;
    margin-right: 0;
    margin-top: 5.33vw;
    padding: 6.4vw;
  }
  .section02-list .item .item01 {
    font-size: 4vw;
    top: 6vw;
  }
  .section02-list .item .item01 .item-num {
    width: 6.93vw;
    height: 6.93vw;
    line-height: 6.93vw;
  }
  .section02-list .item .item01 .item-txt {
    width: 22.13vw;
    height: 6.93vw;
    line-height: 6.93vw;
    border-radius: 0px 3.47vw 3.47vw 0px;
  }
  .section02-list .item .item-title {
    margin-top: calc(5.33vw + 6.93vw);
    font-size: 3.73vw;
  }
  .section02-list .item .item-desc {
    font-size: 3.47vw;
    margin-top: 2.27vw;
    line-height: 1.54;
    height: auto;
  }
  .section02-list .item .item-btn {
    font-size: 3.47vw;
    height: 6.67vw;
    line-height: 6.67vw;
    border-radius: 3.33vw;
    margin-top: 6.13vw;
    position: relative;
    z-index: 2;
    padding: 0 3.2vw;
  }
  .section02-list .item .num-box {
    width: 17.33vw;
    height: auto;
    right: 4.93vw;
    top: 4.4vw;
  }
  .section02-list .item .num-box img,
  .section02-list .item .icon-box img {
    width: 100%;
  }
  .section02-list .item:first-of-type .icon-box {
    width: 32.13vw;
  }
  .section02-list .item:nth-of-type(2) .icon-box {
    width: 29.07vw;
  }
  .section02-list .item:nth-of-type(3) .icon-box {
    width: 36.8vw;
  }
  .section02-list .item:nth-of-type(4) .icon-box {
    width: 35.2vw;
  }
  .section02-list .item:nth-of-type(5) .icon-box {
    width: 45.87vw;
  }
  .section02-list .item:nth-of-type(6) .icon-box {
    width: 28.27vw;
  }
  .section02-list .item:nth-of-type(7) .icon-box {
    width: 35.87vw;
  }
  .section02-list .contact-box {
    width: 100%;
    height: auto;
    padding-bottom: 6.67vw;
    margin-top: 5.33vw;
    position: absolute;
    bottom: -16vw;
  }
  .contact-box .c-title {
    width: 51.6vw;
    height: 9.73vw;
    line-height: 7vw;
    font-size: 4vw;
    background-size: cover;
  }
  .section02-list .contact-box .input-box {
    margin: 2.33vw 3.47vw 0;
  }
  .contact-box .input-box .input-item,
  .contact-box .input-box .input-btn {
    height: 12vw;
    line-height: 12vw;
    border-radius: 6vw;
  }
  .contact-box .input-box .input-wrap {
    margin-bottom: 4vw;
  }
  .contact-box .input-box .input-item {
    font-size: 3.73vw;
    padding: 0 7.73vw;
  }
  .contact-box .input-box .input-btn {
    font-size: 4.93vw;
    margin-top: 1.33vw;
  }

  .section03 {
    padding-top: 20.8vw;
    padding-bottom: 12vw;
  }
  .section03 .content-box {
    justify-content: center;
    margin-top: 4vw;
  }
  .section03 .section03-list {
    width: 100%;
  }
  .section03 .section03-list .item {
    height: auto;
    min-height: 13.33vw;
    border-radius: 1.07vw;
    padding: 2.67vw 3.47vw;
    margin-bottom: 4vw;
  }
  .section03 .section03-list .item img {
    width: 3.47vw;
    height: 3.47vw;
  }
  .section03 .section03-list .item p {
    font-size: 3.47vw;
    line-height: 4.53vw;
  }
  .section03 .content-box.professional-phone {
    display: block;
  }
  .section03 .content-box.professional-pc {
    display: none;
  }
  .section03 .img03-box {
    width: auto;
    height: 37.07vw;
    margin: 5.07vw 0 4vw;
    padding: 0;
  }
  .section03 .img03-box img {
    width: auto;
    height: 100%;
  }
  .section04 {
    padding-top: 11.6vw;
    padding-bottom: 6.67vw;
    .s4-content {
      margin-top: 6.8vw;
      .s4-tab {
        margin-bottom: 6vw;
        .tab-item {
          min-width: 28vw;
          height: 9.33vw;
          border-radius: 4.67vw;
          font-size: 3.47vw;
          margin-right: 2vw;
        }
      }
      .s4-content-box {
        margin-bottom: 9vw;
        .s4-content-item {
          padding: 0 4vw;
          .c-img {
            margin-right: 0;
            width: 100%;
          }
          .item {
            width: 100%;
            .top-box {
              height: 21.33vw;
              .img-text-box {
                height: 100%;
                align-items: flex-end;
                .img-box {
                  width: 36.13vw;
                  height: 16.8vw;
                  margin-left: -3.2vw;
                  img {
                    width: 8vw;
                    height: auto;
                    object-fit: contain;
                    margin-bottom: 10vw;
                  }
                }
                .s-title {
                  line-height: 21.33vw;
                  font-size: 4.27vw;
                }
              }
            }
            .bot-box {
              .item-desc {
                font-size: 3.47vw;
                line-height: 4.53vw;
              }
              .item-btn {
                margin-top: 10vw;
              }
            }
          }
        }
      }
    }
    .contact-box {
      width: calc(100% - 8vw) !important;
    }
  }

  .contact-box {
    height: auto;
    padding-bottom: 6.67vw;
  }
  .contact-box .input-box {
    margin: 2.33vw 3.47vw 0;
  }
  .contact-box .input-box .input-btn {
    width: 100%;
  }
  .contact-box .txt-box,
  .contact-box .c-img-box {
    display: none;
  }

  .section05 {
    padding-top: 9.87vw;
  }
  .section05 .c-title {
    font-size: 4.53vw;
  }
  .section05 .c-title-s {
    font-size: 2.4vw;
    margin-top: 1.47vw;
    margin-bottom: 7.73vw;
  }
  .section05 .input-box {
    flex-direction: column;
    width: 100%;
  }
  .section05 .input-btn {
    width: 100%;
    height: 12vw;
    line-height: 12vw;
    border-radius: 6vw;
    font-size: 4.53vw;
    margin-top: 2.13vw;
  }
  .section05 .input-box .input-wrap{
    margin-bottom: 6vw;
  }

  .section05 .input-box .input-item {
    width: 100%;
    height: 12vw;
    line-height: 12vw;
    border-radius: 6vw;
    font-size: 3.73vw;
    padding: 0 7.6vw;
  }
  .el-form-item__error {
    bottom: 6px;
  }
  ::-webkit-scrollbar {
    width: 0px;
    height: 0px;
  }
  ::-webkit-scrollbar-thumb {
    border: 0px solid transparent;
    background-clip: padding-box;
    border-radius: 0px;
    min-height: 0px;
    background-color: rgba(187, 187, 187, 0.88);
  }
  ::-webkit-scrollbar-track {
    background-color: transparent;
  }
}
@media (max-width: 700px) {
  .banner-box {
    margin-top: 50px;
  }
}

@media (max-width: 600px) {
  .goBack {
    width: 60px;
    height: 60px;
  }
}
.message-box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgb(0, 0, 0);
  color: #fff;
  padding: 20px 30px;
  border-radius: 4px;
}
#message-text {
  color: #fff;
}
