import{U as P,A as a}from"./index-BBeD0eDz.js";import{g as S,a as H,b as L}from"./aiDocument-BqsEIq7y.js";const b=P("aiDocument",()=>{const l=a([]),n=a(!1),t=a({product:"",type:"",date:"",perPage:10,page:1}),r=a(0),i=a(1),s=a(null),o=async()=>{n.value=!0;try{const e=await S(t.value);t.value.page===1?l.value=e.data:l.value=[...l.value,...e.data],r.value=e.total,i.value=e.last_page,(!s.value||t.value.page===1)&&(s.value=l.value[0]),l.value.length===0&&(s.value=null)}catch(e){console.error("获取历史记录失败",e)}finally{n.value=!1}},h=e=>{t.value.perPage=e,o()},m=e=>{t.value.page=e,o()},y=()=>{t.value.page=1,o()},d=e=>{const c=e;if(!c)return;const{scrollTop:p,clientHeight:u,scrollHeight:D}=c;if(p+u>=D-10){if(t.value.page*t.value.perPage>=r.value||n.value)return;t.value.page++,o()}},A=()=>{t.value={product:"",type:"",date:"",perPage:10,page:1},l.value=[],r.value=0,i.value=1,n.value=!1,o()},v=a([]),f=a([]),g=a(!1);return{initHistoryList:A,pageLoading:n,total:r,listQ:t,historyList:l,fetchHistoryList:o,handleSizeChange:h,handleCurrentChange:m,refreshData:y,handleScroll:d,lastPage:i,publishScene:v,allForm:f,typeLoading:g,getAiDocumentType:async()=>{if(!(v.value.length>0)){g.value=!0;try{const e=await H({});console.log(e,"文案类型"),v.value=e.type,f.value=e.form}catch(e){console.error("获取文案类型失败",e)}finally{g.value=!1}}},getAiDocumentInfo:async e=>new Promise((c,p)=>{L({id:e}).then(u=>{c(u)}).catch(u=>{p(u)})}),defaultSelectItem:s}});export{b as u};
