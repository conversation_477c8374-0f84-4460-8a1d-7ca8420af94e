webpackJsonp([43],{"/6zR":function(t,e){},Qd7b:function(t,e){},mGY0:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("Xxa5"),n=a.n(i),s=a("exGp"),o=a.n(s),c=a("Dd8w"),r=a.n(c),l=a("Aw0F"),d=a("wU6q"),u=a("xNuv"),p={name:"GuideDialog",data:function(){return{dialogVisible:!1,loading:!1,steps:[{icon:a("w4nv"),type:1,title:"第一步：构建知识库",desc:"补充企业<span>知识库</span>完善企业信息及主营产品信",status:"done",action:"完善",route:"/document/knowledge"},{icon:a("kHDt"),type:2,title:"第二步：账号授权",desc:"进入矩阵分发，点击<span>账号授权</span>添加首个账号",status:"done",action:"授权",route:"/short-video/auth"},{icon:a("Pgpn"),type:3,title:"第三步：上传素材",desc:"进入素材广场，点击<span>上传素材</span>添加剪辑所需的视频、图片、音频素材",status:"todo",action:"上传",route:"/material-market"},{icon:a("8o4M"),type:4,title:"第四步：智能创作",desc:"进入智能剪辑，根据场景点击<span>AI一键成片、智能混剪、数字人口播</span>成功生成第一个视频",status:"todo",action:"创作",route:"/smart-material/ai-image"},{icon:a("x7YU"),type:5,title:"第五步：矩阵分发",desc:"最后点击<span>发布视频</span>将制作好的内容发布至授权平台账号中",status:"todo",action:"发布",route:"/short-video/publish"}]}},methods:{handleStepClick:function(t){window.open("https://dev.china9.cn/aigc/#"+t.route,"_blank"),this.dialogVisible=!1},openDialog:function(){this.dialogVisible=!0,this.checkGuideStatus()},closeDialog:function(){this.dialogVisible=!1},checkGuideStatus:function(){var t=this;return o()(n.a.mark(function e(){var a,i;return n.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.loading=!0,e.prev=1,e.next=4,Object(u.d)({});case 4:200===(a=e.sent).code?(i=a.data.list).length>0&&i.forEach(function(e){var a=t.steps.find(function(t){return t.type===e.type});a&&(1==+e.status?a.status="done":a.status="todo")}):console.error("获取引导状态失败",a.message),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1),console.error("获取引导状态失败",e.t0);case 11:return e.prev=11,t.loading=!1,e.finish(11);case 14:case"end":return e.stop()}},e,t,[[1,8,11,14]])}))()}}},m={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-dialog",{staticClass:"guide-dialog",attrs:{visible:t.dialogVisible,width:"900px","show-close":!0,"close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogVisible=e}}},[i("template",{slot:"title"},[i("div",{staticClass:"title"},[i("img",{attrs:{src:a("xf/s"),alt:""}}),t._v(" "),i("span",[t._v("新手引导")])])]),t._v(" "),i("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"guide-steps"},t._l(t.steps,function(e,a){return i("div",{key:a,staticClass:"step-item flex justify-between align-center mb-4"},[i("div",{staticClass:"flex align-center step-content"},[i("div",{staticClass:"step-icon flex align-center justify-center mr-[14px]"},[i("img",{attrs:{src:e.icon,alt:"step-icon"}})]),t._v(" "),i("div",[i("div",{staticClass:"step-title"},[t._v(t._s(e.title))]),t._v(" "),i("div",{staticClass:"step-desc",domProps:{innerHTML:t._s(e.desc)}})])]),t._v(" "),i("div",{staticClass:"step-status"},["done"===e.status?i("div",{staticClass:"step-action success flex justify-center align-center"},[t._v("已完成")]):i("div",{staticClass:"step-action flex align-center justify-center",on:{click:function(a){return t.handleStepClick(e)}}},[t._v("去"+t._s(e.action)+"\n        ")])])])}),0),t._v(" "),i("div",{staticClass:"h-[10px]"})],2)},staticRenderFns:[]};var v=a("VU/8")(p,m,!1,function(t){a("/6zR")},"data-v-d4e5637a",null).exports,h={name:"ContentList",props:{projectList:{type:Array,default:function(){return[]}},toPath:{type:String,default:""},field:{type:Object,default:function(){return{title:"title",thumbnail:"thumbnail",lastEditTime:"lastEditTime"}}},timeText:{type:String,default:"最后编辑时间"},showCopy:{type:Boolean,default:!0},showDelete:{type:Boolean,default:!0},delApi:{type:Function,default:null},copyApi:{type:Function,default:null}},data:function(){return{copyLoading:!1,delLoading:!1,EmptyCover:a("mn+h")}},methods:{handleProjectClick:function(t){this.toPath?window.open("https://dev.china9.cn/aigc/#"+this.toPath+"?id="+t.id,"_blank"):this.$emit("click",t)},handleStarProject:function(t){var e=this;return console.log("复制项目",t),this.copyLoading?this.$message.warning("正在复制中..."):this.copyApi?void this.$confirm("确定要复制该项目吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(o()(n.a.mark(function a(){return n.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return e.copyLoading=!0,a.prev=1,a.next=4,e.copyApi({id:t});case 4:e.$message.success("复制成功"),e.$emit("refresh"),a.next=10;break;case 8:a.prev=8,a.t0=a.catch(1);case 10:return a.prev=10,e.copyLoading=!1,a.finish(10);case 13:case"end":return a.stop()}},a,e,[[1,8,10,13]])}))).catch(function(){}):this.$message.warning("复制接口未实现")},handleDeleteProject:function(t){var e=this;return console.log("删除项目",t),this.delLoading?this.$message.warning("正在删除中..."):this.delApi?void this.$confirm("确定要删除该项目吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(o()(n.a.mark(function a(){return n.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return e.delLoading=!0,a.prev=1,a.next=4,e.delApi({id:t});case 4:e.$message.success("删除成功"),e.$emit("refresh"),a.next=10;break;case 8:a.prev=8,a.t0=a.catch(1);case 10:return a.prev=10,e.delLoading=!1,a.finish(10);case 13:case"end":return a.stop()}},a,e,[[1,8,10,13]])}))).catch(function(){}):this.$message.warning("删除接口未实现")}}},f={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-row",{staticClass:"project-list",attrs:{gutter:20}},t._l(t.projectList,function(e,i){return a("el-col",{key:i,attrs:{xs:24,sm:12,md:8,lg:5,xl:6}},[a("div",{key:i,staticClass:"project-item",on:{click:function(a){return t.handleProjectClick(e)}}},[a("div",{staticClass:"project-thumbnail"},[a("img",{attrs:{src:e[t.field.thumbnail]||t.EmptyCover,alt:"项目缩略图"}})]),t._v(" "),a("div",{staticClass:"project-info"},[a("div",{staticClass:"project-title"},[t._v(t._s(e[t.field.title]))]),t._v(" "),a("div",{staticClass:"project-actions",on:{click:function(t){t.stopPropagation()}}},[t.showCopy?a("i",{staticClass:"el-icon-document-copy",on:{click:function(a){return t.handleStarProject(e.id)}}}):t._e(),t._v(" "),t.showDelete?a("i",{staticClass:"el-icon-delete",on:{click:function(a){return t.handleDeleteProject(e.id)}}}):t._e()])]),t._v(" "),a("div",{staticClass:"project-meta"},[e[t.field.lastEditTime]?a("div",{staticClass:"meta-item"},[a("i",{staticClass:"el-icon-time"}),t._v(" "),a("span",[t._v(t._s(t.timeText)+"："+t._s(e[t.field.lastEditTime]))])]):t._e()])])])}),1)},staticRenderFns:[]};var g=a("VU/8")(h,f,!1,function(t){a("mHG/")},"data-v-eaade4a8",null).exports,_=a("NYxO"),C=a("KHo7"),y=window.location.hostname.includes("dev.china9.cn")?"http://jzt_dev_1.china9.cn/":"https://zhjzt.china9.cn/",k={components:{ContentList:g,GuideDialog:v,CardWrap:d.a,PageWrap:l.a},mixins:[C.a],data:function(){return{domain:y,helpCenterList:[],pricingPlans:[{name:"基础版",color:"#727AA2",headerImage:a("CsDA"),crownColor:"#627797",num:"--",user_num:"--",is_completefilm:0,is_txt:0,seat:"--",matrix:"--",space:"--",content:"--",price:"--"},{name:"专业版",color:"#FFFFFF",headerImage:a("tQEQ"),crownColor:"#0C83AF",num:"--",user_num:"--",is_completefilm:0,is_txt:0,seat:"--",matrix:"--",space:"--",content:"--",price:"--"},{name:"企业版",color:"#F27601",headerImage:a("fRE1"),crownColor:"#F37B02",num:"--",user_num:"--",is_completefilm:0,is_txt:0,seat:"--",matrix:"--",space:"--",content:"--",price:"--"},{name:"旗舰版",color:"#E7C1A8",headerImage:a("/5Z2"),crownColor:"#FF7338",num:"--",user_num:"--",is_completefilm:0,is_txt:0,seat:"--",matrix:"--",space:"--",content:"--",price:"--"}],shortcuts:[{id:1,title:"数字人口播",icon:a("b7Jm"),route:"/smart-material/user-portrait"},{id:2,title:"文案生成",icon:a("BMpG"),route:"/document/template"},{id:3,title:"AI一键成片",icon:a("JebX"),route:"/smart-material/ai-image"},{id:4,title:"矩阵分发",icon:a("gJdE"),route:"/short-video"}],dataCards:[{title:"账号总播放量",key:"media_auth_play",value:0,comparedToYesterday:0,trend:"up"},{title:"剪辑成片数",key:"smart_material",value:0,comparedToYesterday:0,trend:"up"},{title:"绑定账号数",key:"media_auth",value:0,comparedToYesterday:0,trend:"up"},{title:"账号粉丝数",key:"media_auth_fans",value:0,comparedToYesterday:0,trend:"up"}],projectList:[],searchKeyword:""}},computed:r()({},Object(_.e)(["jztSiteInfo"]),{jztSiteId:function(){return this.jztSiteInfo&&this.jztSiteInfo.id},jzt1CookieDomain:function(){return"china9.cn"}}),created:function(){this.$cookies.set("jzt1_nav_id","7",null,null,this.jzt1CookieDomain)},mounted:function(){this.getDataOverview(),this.getHomeData(),this.getMyProjectList()},methods:{copyAiVideoApi:u.b,delAiVideoApi:u.c,toGuide:function(){this.$refs.guideDialogRef.openDialog()},getBg:function(t){return[{backgroundImage:"url("+a("uap/")+")"},{backgroundImage:"url("+a("ybEJ")+")"},{backgroundImage:"url("+a("te1I")+")"},{backgroundImage:"url("+a("V+pS")+")"}][t]},getHomeData:function(){var t=this;return o()(n.a.mark(function e(){var a,i,s,o;return n.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(u.e)();case 3:200===(a=e.sent).code&&a.data?(i=a.data,s=i.news,o=i.prolist,t.helpCenterList=s,t.pricingPlans=t.pricingPlans.map(function(t){var e=o.filter(function(e){return e.name===t.name})[0];return e?r()({},t,e):t})):console.log("🚀 ~ getHomeData ~ res: ",a),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),console.log(e.t0);case 10:case"end":return e.stop()}},e,t,[[0,7]])}))()},toUrl:function(t){window.open("https://dev.china9.cn/aigc/#"+t,"_blank")},handleShortcutClick:function(t){this.toUrl(t)},getMyProjectList:function(){var t=this;return o()(n.a.mark(function e(){var a;return n.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(u.f)();case 3:200===(a=e.sent).code?t.projectList=a.data:console.log("🚀 ~ getMyProjectList ~ res: ",a),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0),console.log(e.t0);case 10:case"end":return e.stop()}},e,t,[[0,7]])}))()},getDataOverview:function(){var t=this;Object(u.g)({}).then(function(e){if(200===e.code){var a=e.data;a&&t.dataCards.forEach(function(t){a[t.key]&&(t.value=a[t.key].num,t.comparedToYesterday=a[t.key].ratio,t.trend=1==a[t.key].type?"up":"down")})}})}}},x={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("page-wrap",[[i("div",{staticClass:"home-container"},[i("div",{staticClass:"welcome-section"},[i("div",{staticClass:"welcome-text flex items-center"},[i("span",[t._v("欢迎，AI引擎已就位，开始生成您的爆款内容")]),t._v(" "),t._e()]),t._v(" "),i("div",{staticClass:"options"},[i("div",{staticClass:"guide",on:{click:t.toGuide}},[i("img",{attrs:{src:a("xf/s"),alt:""}}),t._v(" "),i("span",[t._v("新手引导")])]),t._v(" "),i("el-button",{attrs:{size:"small"},on:{click:function(e){return t.toPage({pathType:"link",path:t.domain+"jzt_media/"})}}},[i("div",{staticClass:"go-back-btn"},[i("img",{attrs:{src:a("sYMi"),alt:""}}),t._v("\n              回到旧版\n            ")])])],1)]),t._v(" "),i("div",{staticClass:"features-section"},[i("div",{staticClass:"features-left"},[i("CardWrap",{attrs:{title:"推荐快捷方式"}},[[i("div",{staticClass:"shortcut-cards"},t._l(t.shortcuts,function(e,a){return i("div",{key:e.id,staticClass:"shortcut-card",style:t.getBg(a),on:{click:function(a){return t.handleShortcutClick(e.route)}}},[i("div",{staticClass:"card-content"},[i("div",{staticClass:"card-text"},[t._v(t._s(e.title))]),t._v(" "),i("div",{staticClass:"card-icon"},[i("img",{attrs:{src:e.icon,alt:e.title}})])])])}),0)]],2)],1)]),t._v(" "),i("CardWrap",{attrs:{title:"数据概览"}},[i("template",{slot:"right"},[i("el-button",{staticClass:"today-btn",attrs:{type:"primary",plain:"",size:"small"}},[t._v("当日")])],1),t._v(" "),i("div",{staticClass:"data-cards"},t._l(t.dataCards,function(e,a){return i("div",{key:a,staticClass:"data-card"},[i("div",{staticClass:"data-title"},[t._v(t._s(e.title))]),t._v(" "),i("div",{staticClass:"data-value-wrap"},[i("div",{staticClass:"data-value"},[t._v(t._s(e.value))]),t._v(" "),i("div",{staticClass:"data-trend"},[i("span",[t._v("较前日")]),t._v(" "),i("span",{class:"up"===e.trend?"trend-up":"trend-down"},[i("el-icon",["up"===e.trend?i("CaretTop"):i("CaretBottom")],1),t._v("\n              "+t._s(e.comparedToYesterday.toFixed(2))+"%\n            ")],1)])])])}),0)],2),t._v(" "),i("CardWrap",{staticClass:"project-list-section",attrs:{title:"我的剪辑项目"}},[i("template",{slot:"right"},[i("div",{staticClass:"search-box"},[i("el-input",{attrs:{placeholder:"请输入项目名称","prefix-icon":"el-icon-search",clearable:"",size:"default"},model:{value:t.searchKeyword,callback:function(e){t.searchKeyword=e},expression:"searchKeyword"}})],1)]),t._v(" "),[t.projectList.length>0?i("ContentList",{attrs:{projectList:t.projectList,field:{title:"title",thumbnail:"imgurl",lastEditTime:"updated_at"},"del-api":t.delAiVideoApi,"copy-api":t.copyAiVideoApi,toPath:"/smart-material/ai-image"}}):i("el-empty",{attrs:{image:a("RoGG"),"image-size":300,description:"暂无项目"}})]],2),t._v(" "),i("GuideDialog",{ref:"guideDialogRef"})],1)]],2)},staticRenderFns:[]};var w=a("VU/8")(k,x,!1,function(t){a("Qd7b")},"data-v-aaf4280a",null);e.default=w.exports},"mHG/":function(t,e){}});