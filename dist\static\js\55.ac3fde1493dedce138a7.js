webpackJsonp([55],{"327n":function(t,e){},"9sPF":function(t,e){},SbGy:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=a("+PoZ"),i=a("irLX"),n={name:"detail",data:function(){return{upload_url:"//api/workorder/upload",loading:!1,recommentLoading:!1,dataDetail:{},fileList:[],formData:{attrs:[]},id:this.$route.query.id}},computed:{uploadParam:function(){return{token:this.$cookies.get("token")||this.$cookies.get("access_token")||this.$store.getters.token}}},created:function(){this.formData.id=this.id,this.workorderDetail()},methods:{handleSuccess:function(t,e,a){200===t.code&&(this.fileList=a)},workorderDetail:function(){var t=this;this.loading=!0,Object(s.f)({id:this.id}).then(function(e){t.loading=!1,0===e.data.status?e.data.statusText="待回复":1===e.data.status?e.data.statusText="正在处理中":2===e.data.status?e.data.statusText="已完结":3===e.data.status&&(e.data.statusText="已关闭"),t.dataDetail=e.data,i.a.$emit("workorderRedRefresh")}).catch(function(){t.loading=!1})},recomment:function(){var t=this;if(void 0===this.formData.question)return this.$message.error("请输入问题描述");this.recommentLoading=!0,this.formData.attrs=[];var e=this;this.fileList.forEach(function(t){e.formData.attrs.push(t.response.data.url)}),Object(s.h)(this.formData).then(function(e){200===e.code?(t.recommentLoading=!1,t.$message.success("回复成功"),t.workorderDetail()):(t.recommentLoading=!1,t.$message.error(e.messages))}).catch(function(){t.recommentLoading=!1})},handleRemove:function(t,e){this.fileList=e},handlePreview:function(t){},handleExceed:function(t,e){this.$message.warning("当前限制选择 3 个文件，本次选择了 "+t.length+" 个文件，共选择了 "+(t.length+e.length)+" 个文件")},beforeRemove:function(t,e){return this.$confirm("确定移除 "+t.name+"？")}}},o={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("el-row",[a("el-col",[a("div",{staticClass:"header_title"},[a("h1",[t._v("工单详情")])]),t._v(" "),a("el-card",[a("el-row",{staticClass:"detail"},[a("el-col",[a("span",[a("span",[t._v("工单编号:")]),t._v(" "),a("span",[t._v(t._s(t.dataDetail.work_num))])]),t._v(" "),a("span",[a("span",[t._v("状态:")]),t._v(" "),a("span",[t._v(t._s(t.dataDetail.statusText))])]),t._v(" "),a("span",[a("span",[t._v("提交时间:")]),t._v(" "),a("span",[t._v(t._s(t.dataDetail.created_at))])])]),t._v(" "),a("el-col",[a("span",[t._v("问题描述:")]),t._v(" "),a("span",[t._v(t._s(t.dataDetail.question))])])],1)],1),t._v(" "),a("div",{staticClass:"list",staticStyle:{"margin-top":"20px"}},[t.dataDetail.comments?a("el-card",[a("el-row",[a("el-col",[a("ul",t._l(t.dataDetail.comments,function(e,s){return a("li",{key:s},[a("div",{staticClass:"content"},[a("p",[t._v(t._s(1===e.type?"我的提问":"工程师回复")+":")]),t._v(" "),a("p",{domProps:{innerHTML:t._s(e.question)}})])])}),0)])],1)],1):t._e()],1)],1)],1)],1),t._v(" "),a("el-card",{staticStyle:{"margin-top":"20px"}},[a("el-row",[a("el-col",[a("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入问题描述"},model:{value:t.formData.question,callback:function(e){t.$set(t.formData,"question",e)},expression:"formData.question"}}),t._v(" "),a("el-upload",{attrs:{data:t.uploadParam,action:t.upload_url,"on-preview":t.handlePreview,"on-remove":t.handleRemove,"before-remove":t.beforeRemove,"on-success":t.handleSuccess,multiple:"",limit:3,"on-exceed":t.handleExceed}},[a("el-button",{staticStyle:{"margin-top":"20px"},attrs:{size:"small",type:"primary"}},[t._v("点击上传附件")]),t._v(" "),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("只能上传jpg/png文件，且不超过2M")])],1),t._v(" "),a("el-button",{staticStyle:{"margin-top":"20px"},attrs:{type:"primary",loading:t.recommentLoading},on:{click:t.recomment}},[t._v("提问")])],1)],1)],1)],1)},staticRenderFns:[]};var r=a("VU/8")(n,o,!1,function(t){a("9sPF"),a("327n")},"data-v-1ec47b20",null);e.default=r.exports}});