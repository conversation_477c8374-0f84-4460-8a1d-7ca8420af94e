# 抖音授权登录页面

这是一个基于抖音开放平台OAuth 2.0的授权登录页面，严格按照设计图实现，支持手机端响应式布局。

## 功能特性

- ✅ 完全按照设计图实现的UI界面
- ✅ 基于750px设计图的rem适配方案
- ✅ JS动态计算html font-size实现完美适配
- ✅ 手机端响应式设计
- ✅ 抖音OAuth 2.0授权登录流程
- ✅ 安全的state参数验证
- ✅ 完整的错误处理机制
- ✅ 加载状态和用户反馈
- ✅ 授权回调处理页面
- ✅ 蓝色渐变装饰图形严格按照设计图实现

## 文件说明

- `login.html` - 主登录页面（基于rem适配，含JavaScript加载弹窗和错误提示）
- `auth-callback.html` - 授权回调处理页面（基于rem适配）
- `test-responsive.html` - 响应式测试页面
- `loading-demo.html` - CSS加载弹窗演示页面
- `js-loading-demo.html` - JavaScript加载弹窗演示页面
- `error-toast-demo.html` - 错误提示弹窗演示页面
- `js/common.js` - 公共JavaScript库（包含加载弹窗和错误提示功能）
- `README.md` - 说明文档

## JavaScript加载弹窗

### 设计特性

- **完全JavaScript实现**: 无需HTML和CSS，纯JS创建元素和样式
- **动态创建**: 按需创建，用完即销毁，不占用DOM空间
- **黑色透明背景**: `rgba(0, 0, 0, 0.7)` 全屏遮罩
- **优雅动画**: 淡入淡出过渡效果和旋转加载动画
- **防滚动穿透**: 自动阻止页面滚动
- **响应式适配**: 基于rem单位，完美适配各种设备

### 核心功能

```javascript
// 显示加载弹窗
showLoading('正在授权登录');

// 隐藏加载弹窗
hideLoading();

// 销毁加载弹窗（释放内存）
destroyLoading();
```

### 实现原理

1. **动态样式注入**: 使用`document.createElement('style')`创建CSS
2. **DOM元素创建**: 使用`document.createElement()`创建弹窗结构
3. **事件监听**: 防止滚动穿透和页面卸载时清理资源
4. **状态管理**: 通过变量控制弹窗的显示/隐藏状态

### 技术亮点

- **内存管理**: 提供销毁方法，避免内存泄漏
- **样式隔离**: 使用特殊类名前缀避免样式冲突
- **性能优化**: 懒加载创建，只在需要时初始化
- **用户体验**: 平滑动画和防误操作设计

## 错误提示弹窗

### 设计特性

- **抖音风格设计**: 参考抖音App的错误提示样式
- **完全JavaScript实现**: 无需HTML和CSS，纯JS创建元素和样式
- **4种提示类型**: 错误(error)、警告(warning)、信息(info)、成功(success)
- **智能定位**: 居中显示，自适应内容大小
- **自动销毁**: 点击关闭或超时后自动销毁DOM元素
- **震动效果**: 支持震动动画增强用户感知
- **响应式适配**: 基于rem单位，完美适配各种设备

### 核心功能

```javascript
// 显示错误提示
showErrorToast('网络连接失败', 'error', 3000);

// 显示带震动效果的错误提示
showErrorToastWithShake('密码错误', 'error', 3000);

// 显示不同类型的提示
showErrorToast('请先完成实名认证', 'warning', 3000);
showErrorToast('正在处理中...', 'info', 2000);
showErrorToast('操作成功！', 'success', 2000);

// 显示持久提示（不自动消失）
showErrorToast('重要提示信息', 'info', 0);

// 手动关闭
hideErrorToast();

// 销毁弹窗（释放内存）
destroyErrorToast();
```

### 参数说明

- **message**: 提示文字内容
- **type**: 提示类型 ('error' | 'warning' | 'info' | 'success')
- **duration**: 显示时长（毫秒），0表示不自动消失

### 样式特点

- **半透明背景**: `rgba(0, 0, 0, 0.8)` 磨砂玻璃效果
- **圆角设计**: 0.16rem圆角，现代化外观
- **图标指示**: 每种类型都有对应的SVG图标
- **颜色区分**: 不同类型使用不同的背景色
- **动画效果**: 淡入淡出和缩放动画
- **关闭按钮**: 右上角圆形关闭按钮

## rem适配方案

### 设计原理

本项目采用基于750px设计图的rem适配方案：

1. **设计图基准**: 750px宽度设计图
2. **基准font-size**: 100px（750px ÷ 7.5 = 100px）
3. **rem换算**: 设计图尺寸 ÷ 100 = rem值
4. **动态计算**: JS实时计算并设置html的font-size

### 换算公式

```javascript
// 计算公式
const fontSize = (屏幕宽度 / 750) * 100;

// 示例：375px宽度的手机
const fontSize = (375 / 750) * 100 = 50px;
// 此时 1rem = 50px，设计图上100px = 1rem
```

### 使用方法

1. **设计图标注**: 假设设计图上某元素宽度为150px
2. **rem换算**: 150px ÷ 100 = 1.5rem
3. **CSS写法**: `width: 1.5rem;`

### 适配范围

- **最小font-size**: 12px（防止文字过小）
- **最大font-size**: 100px（防止在大屏上过大）
- **适配设备**: 手机、平板、桌面端

## 配置步骤

### 1. 注册抖音开放平台应用

1. 访问 [抖音开放平台](https://open.douyin.com/)
2. 注册开发者账号并创建应用
3. 获取 `Client Key` 和 `Client Secret`
4. 配置授权回调地址

### 2. 修改配置

在 `login.html` 文件中找到以下配置并修改：

```javascript
const DOUYIN_CONFIG = {
    client_key: 'YOUR_CLIENT_KEY', // 替换为你的Client Key
    redirect_uri: encodeURIComponent(window.location.origin + '/douyin/auth-callback.html'),
    scope: 'user_info', // 根据需要调整授权范围
    state: generateRandomState()
};
```

### 3. 设置回调地址

在抖音开放平台的应用配置中，将授权回调地址设置为：
```
https://yourdomain.com/douyin/auth-callback.html
```

## 授权流程

1. 用户点击"一键授权登录"按钮
2. 跳转到抖音授权页面
3. 用户确认授权
4. 抖音回调到 `auth-callback.html` 页面
5. 页面处理授权码并完成登录

## 安全考虑

- ✅ 使用state参数防止CSRF攻击
- ✅ 授权码只能使用一次
- ✅ 敏感信息应在服务端处理
- ✅ 完整的错误处理和用户提示

## 后端集成

授权回调页面目前使用模拟数据，实际使用时需要：

1. 创建后端API接口 `/api/auth/douyin/callback`
2. 使用授权码换取access_token
3. 获取用户信息并创建会话
4. 返回登录结果

### 后端API示例

```javascript
// POST /api/auth/douyin/callback
{
    "code": "授权码",
    "state": "状态参数"
}

// 响应
{
    "success": true,
    "access_token": "用户访问令牌",
    "user_info": {
        "nickname": "用户昵称",
        "avatar": "头像URL"
    }
}
```

## 抖音开放平台API

### 获取access_token

```
POST https://open.douyin.com/oauth/access_token/
```

参数：
- `client_key`: 应用的Client Key
- `client_secret`: 应用的Client Secret
- `code`: 授权码
- `grant_type`: 固定值 "authorization_code"

### 获取用户信息

```
GET https://open.douyin.com/oauth/userinfo/
```

参数：
- `access_token`: 用户访问令牌
- `open_id`: 用户的open_id

## 常见问题

### Q: 授权失败怎么办？
A: 检查Client Key配置、回调地址设置、网络连接等。

### Q: 如何获取更多用户权限？
A: 修改scope参数，可选值包括：user_info、video.list、video.data等。

### Q: 如何处理token过期？
A: 使用refresh_token刷新access_token，或重新授权。

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 许可证

MIT License
