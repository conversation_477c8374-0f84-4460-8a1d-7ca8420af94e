import{k as L,l as c,c as h,o as u,b as i,m as _,t as k,_ as G,A as D,V as ne,B as ie,u as re,a as n,w as d,K as de,H as ue,y as ce,p as a,O as pe,f as v,E as me,d as he,W as fe,F as M,s as j,Y as ge,aL as ve,e as _e,q as be,J as ye,ad as ke,h as Ce,j as Q,X as Ve,ac as Ae,P as we,a4 as De,Q as Ee}from"./index-BBeD0eDz.js";/* empty css                *//* empty css                     *//* empty css                 *//* empty css                *//* empty css              *//* empty css               *//* empty css                  *//* empty css                    */import{_ as Te}from"./PublishContent-DQvymxsk.js";import{Q as xe,T as Se}from"./TabsWrapper-Kt3D1kC9.js";import{u as Fe}from"./shortVideo-BFWTeahp.js";import{u as We}from"./shortVideo-BIt_F0ml.js";import{E as Ne}from"./empty-CSpEo1eL.js";import"./video-cover-DBcJ77EJ.js";/* empty css               *//* empty css                */import"./request-Ciyrqj7N.js";const Qe="data:image/png;base64,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",ze={class:"circular-progress"},Me=["width","height"],je=["cx","cy","r","stroke","stroke-width"],Le=["cx","cy","r","stroke-width","stroke-dasharray","stroke-dashoffset"],Ge=["cx","cy","r","stroke","stroke-width","stroke-dasharray","stroke-dashoffset"],Re={class:"progress-text"},Ke={class:"progress-number"},Ye={class:"progress-label"},qe=L({__name:"CircularProgress",props:{total:{default:0},success:{default:0},failed:{default:0},size:{default:60},strokeWidth:{default:4}},setup(z){const r=z,f=c(()=>r.size/2),l=c(()=>(r.size-r.strokeWidth)/2),m=c(()=>2*Math.PI*l.value),p=c(()=>r.total===0?0:r.success/r.total*100),A=c(()=>r.total===0?0:r.failed/r.total*100),E=c(()=>{const o=p.value;return m.value-o/100*m.value}),T=c(()=>{const o=m.value-p.value/100*m.value,C=A.value/100*m.value;return o-C}),w=c(()=>"#E5E7EB"),b=c(()=>r.success===r.total&&r.total>0||r.success>0?"#186DF5":"#E5E7EB"),y=c(()=>r.success),x=c(()=>r.total==r.success+r.failed?"完成":"发布中");return(o,C)=>(u(),h("div",ze,[(u(),h("svg",{width:o.size,height:o.size,class:"progress-ring"},[i("circle",{cx:f.value,cy:f.value,r:l.value,fill:"none",stroke:w.value,"stroke-width":o.strokeWidth},null,8,je),A.value>0?(u(),h("circle",{key:0,cx:f.value,cy:f.value,r:l.value,fill:"none",stroke:"#DE2F46","stroke-width":o.strokeWidth,"stroke-dasharray":m.value,"stroke-dashoffset":T.value,"stroke-linecap":"round",class:"failed-circle"},null,8,Le)):_("",!0),i("circle",{cx:f.value,cy:f.value,r:l.value,fill:"none",stroke:b.value,"stroke-width":o.strokeWidth,"stroke-dasharray":m.value,"stroke-dashoffset":E.value,"stroke-linecap":"round",class:"progress-circle"},null,8,Ge)],8,Me)),i("div",Re,[i("div",Ke,k(y.value)+"/"+k(o.total),1),i("div",Ye,k(x.value),1)])]))}}),Be=G(qe,[["__scopeId","data-v-08a969e4"]]),Ue={class:"short-video-view h-full flex flex-col"},He={class:"top-buttons flex items-center"},Oe={class:"button-card bg-main flex flex-auto justify-center"},Je={class:"h-full flex flex-col"},Pe={class:"content-header flex items-center justify-between"},Ze={class:"record-table flex-1 overflow-hidden"},Xe={class:"content-cell"},Ie={class:"title-cell"},$e={class:"status-cell"},et={class:"paly-cell"},tt={key:0,class:"platform-cell flex items-center"},st=["src"],lt={key:1,class:"platform-cell"},ot={class:"operation-cell flex items-center"},at={class:"empty-content mt-[60px]"},nt=L({__name:"index",setup(z){const{publishRecord:r,recordListLoading:f,recordListQ:l,recordTotal:m,getPublishRecordList:p,deletePublishRecord:A,types:E,handleType:T}=We(),w=Fe(),b=ce(),y=re(),x=D([{label:"发布记录",value:"published"},{label:"草稿箱",value:"draft"}]),o=D("published");l.value.is_caogao=1,c(()=>w.platformList),w.fetchPlatformList();const C=s=>{o.value=s,b.push({path:"/short-video",query:{tab:s}}),l.value.is_caogao=s==="published"?1:2,l.value.page=1,p()},R=s=>{l.value.page=s,p()},K=s=>{l.value.limit=s,p()},Y=s=>s.videos,q=s=>{console.log("详情:",s),o.value=="published"?b.push(`/short-video/detail?type=${s.type}&id=${s.id}`):o.value=="draft"&&b.push(`/short-video/publish?type=${s.type}&id=${s.id}`)},B=async s=>{De.confirm(`确定要删除"${s.title}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await A(s.id),Ee.success("删除成功"),l.value.page=1,p()}catch(t){console.error("删除失败:",t)}finally{console.log("删除操作已完成")}})},U=()=>{b.push("/short-video/publish")},S=D(!1),F=D({id:"",title:"",qrcode:""}),H=s=>{F.value={id:s.id,title:s.title,...s},S.value=!0},O=s=>{console.log("下载二维码:",s)},J=()=>{console.log("取消二维码弹窗")};return ne(()=>y.query,()=>{console.log(y.query)}),ie(()=>{p(),y.query.tab&&C(y.query.tab)}),(s,t)=>{const P=de,Z=ge,X=fe,W=he,I=ve,$=_e,ee=me,g=ke,N=Ce,te=Ve,se=Ae,le=we,oe=ue,ae=ye;return u(),h("div",Ue,[i("div",He,[i("div",Oe,[i("div",{class:"publish-btn flex items-center",onClick:U},[t[7]||(t[7]=i("img",{src:Qe,alt:"立即发布"},null,-1)),t[8]||(t[8]=i("span",null,"立即发布",-1)),n(P,{class:"arrow-icon"},{default:d(()=>[n(a(pe))]),_:1})])])]),n(oe,{class:"record-container flex-1",showHeader:!1},{default:d(()=>[i("div",Je,[i("div",Pe,[n(Se,{modelValue:o.value,"onUpdate:modelValue":t[0]||(t[0]=e=>o.value=e),items:x.value,"style-config":{width:"100px",height:"40px"},onChange:C},null,8,["modelValue","items"]),n(ee,{class:"search-form",inline:"",form:a(l)},{default:d(()=>[n(W,{label:"发布类型",prop:"status"},{default:d(()=>[n(X,{style:{width:"120px"},modelValue:a(l).type,"onUpdate:modelValue":t[1]||(t[1]=e=>a(l).type=e),placeholder:"全部",clearable:"",onChange:a(p)},{default:d(()=>[(u(!0),h(M,null,j(a(E),e=>(u(),v(Z,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),n(W,{label:"发布时间",prop:"publishTime"},{default:d(()=>[n(I,{class:"!w-[350px] !px-[8px]",modelValue:a(l).publishTime,"onUpdate:modelValue":t[2]||(t[2]=e=>a(l).publishTime=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD hh:mm:ss","value-format":"YYYY-MM-DD hh:mm:ss",clearable:"",onChange:a(p)},null,8,["modelValue","onChange"])]),_:1}),n(W,{label:"发布名称",prop:"title"},{default:d(()=>[n($,{modelValue:a(l).title,"onUpdate:modelValue":t[3]||(t[3]=e=>a(l).title=e),placeholder:"请输入发布名称",clearable:"",onChange:a(p)},null,8,["modelValue","onChange"])]),_:1})]),_:1},8,["form"])]),i("div",Ze,[be((u(),v(se,{data:a(r),class:"!h-full"},{empty:d(()=>[i("div",at,[n(te,{class:"flex-1",description:"暂无记录",image:a(Ne),"image-size":293},null,8,["image"])])]),default:d(()=>[n(g,{label:"内容",width:"120"},{default:d(({row:e})=>[i("div",Xe,[n(Te,{"content-list":Y(e),type:e.type==2?2:1},null,8,["content-list","type"])])]),_:1}),n(g,{label:"发布名称",prop:"title","show-overflow-tooltip":""},{default:d(({row:e})=>[i("div",Ie,k(e.title),1)]),_:1}),o.value=="published"?(u(),v(g,{key:0,label:"发布状态",width:"120",align:"center"},{default:d(({row:e})=>[i("div",$e,[n(Be,{total:e.media_send_total,success:e.media_send_success,failed:e.media_send_error,size:60,"stroke-width":4},null,8,["total","success","failed"])])]),_:1})):_("",!0),o.value=="published"?(u(),v(g,{key:1,label:"总播放量",width:"100",prop:"paly","show-overflow-tooltip":""},{default:d(e=>[i("div",et,k(e.row.play_num||0),1)]),_:1})):_("",!0),o.value=="published"?(u(),v(g,{key:2,label:"发布平台",prop:"platform",width:"300"},{default:d(({row:e})=>[e.media&&e.media.length?(u(),h("div",tt,[(u(!0),h(M,null,j(e.media,V=>(u(),h("img",{key:V.id,src:V.icon,class:"w-[40px] mr-[5px]",alt:"平台图标"},null,8,st))),128))])):(u(),h("div",lt,t[9]||(t[9]=[i("span",{class:"empty-text"},"--",-1)])))]),_:1})):_("",!0),o.value=="published"?(u(),v(g,{key:3,label:"发布类型",prop:"type","show-overflow-tooltip":"",width:"150"},{default:d(({row:e})=>[i("span",null,k(a(T)(e.type)),1)]),_:1})):_("",!0),n(g,{label:"创建时间",prop:"created_at",width:"180"}),n(g,{label:"操作",fixed:"right",width:o.value=="published"?220:120},{default:d(({row:e})=>[i("div",ot,[o.value=="published"&&e.type==3&&e.media_send_error+e.media_send_success<e.media_send_total?(u(),v(N,{key:0,type:"primary",link:"",onClick:V=>H(e)},{default:d(()=>t[10]||(t[10]=[Q("发布二维码")])),_:2},1032,["onClick"])):_("",!0),n(N,{type:"primary",link:"",onClick:V=>q(e)},{default:d(()=>t[11]||(t[11]=[Q("详情")])),_:2},1032,["onClick"]),n(N,{type:"primary",link:"",onClick:V=>B(e)},{default:d(()=>t[12]||(t[12]=[Q("删除")])),_:2},1032,["onClick"])])]),_:1},8,["width"])]),_:1},8,["data"])),[[ae,a(f)]])]),a(m)>0?(u(),v(le,{key:0,"current-page":a(l).page,"onUpdate:currentPage":t[4]||(t[4]=e=>a(l).page=e),"page-size":a(l).limit,"onUpdate:pageSize":t[5]||(t[5]=e=>a(l).limit=e),total:a(m),onSizeChange:K,onCurrentChange:R},null,8,["current-page","page-size","total"])):_("",!0)])]),_:1}),n(xe,{modelValue:S.value,"onUpdate:modelValue":t[6]||(t[6]=e=>S.value=e),"object-title":F.value.title,"qr-code-data":F.value.qrcode,onDownload:O,onCancel:J},null,8,["modelValue","object-title","qr-code-data"])])}}}),wt=G(nt,[["__scopeId","data-v-03b855da"]]);export{wt as default};
