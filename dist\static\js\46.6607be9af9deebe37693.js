webpackJsonp([46],{"2iJn":function(t,e){},HuHq:function(t,e){},mm2y:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=a("cMGX"),l=a("wU6q"),n=a("pI5c"),o={name:"detail-buyConfig",components:{},props:{dialogVisible:{required:!1,type:Boolean,default:function(){return!1}},formData:{required:!0,type:Object,default:function(){return{}}}},watch:{formData:function(t,e){this.data={order_type:2,type:"1",app_id:t.app_id,package_id:t.package_id,month:12*this.year,activity_id:"",source:"pc",original_order_no:t.order_no,remark:""}}},data:function(){return{tableLoading:!1,btn_loading:!1,data:{no:"",type:0},year:1,remark:""}},created:function(){},methods:{commit:function(){var t=this;this.tableLoading=!0,this.data.month=12*this.year,this.data.remark=this.remark,Object(n._14)(this.data).then(function(e){t.tableLoading=!1,t.$emit("pay",e.data.order_no)}).catch(function(){t.tableLoading=!1})},getAppAmount:function(){this.data.month=12*this.year,this.amount=this.year*this.item.renew_price},close:function(){this.$emit("closepop")}}},r={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{title:"确认订单",visible:t.dialogVisible,width:"600px","before-close":t.close},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticClass:"demo-form-inline",attrs:{inline:!0,model:t.formData,"label-position":"right","label-width":"90px",size:"small"}},[a("div",{staticClass:"container"},[a("el-steps",{staticStyle:{"margin-bottom":"20px"},attrs:{active:1,"align-center":"","finish-status":"success"}},[a("el-step",{attrs:{title:"确认订单"}}),t._v(" "),a("el-step",{attrs:{title:"选择支付方式"}}),t._v(" "),a("el-step",{attrs:{title:"支付成功"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"产品名称",prop:"name"}},[t._v(t._s(t.formData.name))]),t._v(" "),a("el-form-item",{attrs:{label:"产品配置",prop:"package"}},[t._v(t._s(t.formData.package))]),t._v(" "),a("el-form-item",{attrs:{label:"购买年限"}},[a("el-input-number",{attrs:{"controls-position":"right",step:1,min:1,max:5},model:{value:t.year,callback:function(e){t.year=e},expression:"year"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"续费价格",prop:"renew_price"}},[t._v(t._s(t.formData.renew_price))]),t._v(" "),a("el-form-item",{attrs:{label:"备注",prop:"desc"}},[a("el-input",{staticStyle:{width:"430px"},attrs:{type:"textarea","show-word-limit":!0,rows:6,placeholder:"请填写备注内容"},model:{value:t.remark,callback:function(e){t.remark=e},expression:"remark"}})],1),t._v(" "),a("div",{staticClass:"price-view"},[a("p",[t._v("续费金额：")]),t._v(" "),a("p",[t._v("￥"+t._s(t.formData.renew_price*t.year))]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.commit()}}},[t._v("前往支付")])],1),t._v(" "),a("div",{staticClass:"delegate"},[a("p",[t._v("点击以上按钮，代表您已阅读并同意")]),t._v(" "),a("a",[t._v("《云市场平台服务协议》")])])],1)])],1)},staticRenderFns:[]};var s=a("VU/8")(o,r,!1,function(t){a("tA7u")},"data-v-52509f9a",null).exports,c={name:"detail-pay",components:{payComponent:a("rSnQ").a},props:{dialogVisible:{required:!1,type:Boolean,default:function(){return!1}},no:{required:!0,type:[String,Number],default:function(){return""}}},watch:{no:function(t,e){t&&(this.getOrderDetail(),this.walletMe())}},data:function(){return{tableLoading:!1,btn_loading:!1,formData:{},month:1,pay_type:"",wallet:{},walletPay:0}},created:function(){},methods:{getOrderDetail:function(){var t=this;this.tableLoading=!0,Object(n._15)({no:this.no}).then(function(e){t.tableLoading=!1,t.formData=e.data}).catch(function(){t.tableLoading=!1})},walletMe:function(){var t=this;Object(n._34)({}).then(function(e){t.wallet=e.data.amount})},closeDialog:function(){this.dialogVisible=!1},pay:function(){0===this.pay_type?this.$message.error("请选择支付方式"):this.no&&(this.dialogVisible=!1,this.$refs.payComponent.pay(this.no,this.pay_type))},changePayRadio:function(t){this.walletPay=4===t?parseInt(this.wallet)>parseInt(this.formData.money)?parseInt(this.formData.money):parseInt(this.wallet):0},close:function(){this.$emit("closepop")}}},p={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{title:"确认订单",visible:t.dialogVisible,width:"600px","before-close":t.close},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-form",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticClass:"demo-form-inline",attrs:{inline:!0,model:t.formData,"label-position":"right","label-width":"90px",size:"small"}},[a("div",{staticClass:"container"},[a("el-steps",{staticStyle:{"margin-bottom":"20px"},attrs:{active:2,"align-center":"","finish-status":"success"}},[a("el-step",{attrs:{title:"确认订单"}}),t._v(" "),a("el-step",{attrs:{title:"选择支付方式"}}),t._v(" "),a("el-step",{attrs:{title:"支付成功"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"产品名称",prop:"name"}},[t._v(t._s(t.formData.title))]),t._v(" "),a("el-form-item",{attrs:{label:"产品配置",prop:"package"}},[t._v(t._s(t.formData.desc))]),t._v(" "),a("el-form-item",{attrs:{label:"购买方式",prop:"package"}},[t._v("周期购买")]),t._v(" "),a("el-form-item",{attrs:{label:"购买时长",prop:"month"}},[t._v(t._s(t.formData.month))]),t._v(" "),a("el-form-item",{attrs:{label:"续费价格",prop:"renew_price"}},[a("span",{staticClass:"red"},[t._v("￥"+t._s(t.formData.money))])]),t._v(" "),a("el-form-item",{attrs:{label:"使用余额"}},[a("span",{staticClass:"yellow"},[t._v("当前账户余额 ￥"+t._s(t.wallet))])]),t._v(" "),a("el-form-item",{attrs:{label:"支付方式"}},[a("el-radio-group",{on:{change:t.changePayRadio},model:{value:t.pay_type,callback:function(e){t.pay_type=e},expression:"pay_type"}},[a("el-radio",{attrs:{label:1}},[t._v("支付宝")]),t._v(" "),a("el-radio",{attrs:{label:2}},[t._v("微信")]),t._v(" "),a("el-radio",{staticStyle:{display:"none"},attrs:{label:3}},[t._v("银联")]),t._v(" "),a("el-radio",{attrs:{label:4}},[t._v("余额")])],1)],1)],1),t._v(" "),a("div",{staticClass:"price-view"},[a("div",{staticStyle:{flex:"1"}}),t._v(" "),a("div",[a("div",{staticClass:"flex"},[a("div",{staticStyle:{flex:"1"}}),t._v(" "),a("p",[t._v("实际金额：")]),t._v(" "),a("p",{staticClass:"red"},[t._v("￥"+t._s(t.formData.money))])]),t._v(" "),a("div",{staticClass:"flex"},[a("div",{staticStyle:{flex:"1"}}),t._v(" "),a("p",[t._v("应付金额：￥"+t._s(t.formData.money)+" - 使用余额：")]),t._v(" "),a("p",{staticClass:"yellow"},[t._v("￥"+t._s(t.walletPay))])])]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.pay()}}},[t._v("支付")])],1),t._v(" "),a("payComponent",{ref:"payComponent"})],1)],1)},staticRenderFns:[]};var d=a("VU/8")(c,p,!1,function(t){a("HuHq")},"data-v-3b794957",null).exports,m={name:"order",components:{Pagination:i.a,detailBuyConfig:s,detailPay:d,CardWrap:l.a},data:function(){return{tableLoading:!1,tableData:[],total:0,queryList:{page:1,perPage:10},detailBuyConfigVisible:!1,detailPayVisible:!1,itemData:{},order_no:""}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.tableLoading=!0,Object(n._19)(this.queryList).then(function(e){t.total=e.data.total,t.tableLoading=!1,t.tableData=e.data.data}).catch(function(){t.tableLoading=!1})},clickPay:function(t){this.order_no=t,console.log(this.order_no),this.detailBuyConfigVisible=!1,this.detailPayVisible=!0},renew:function(t){this.$store.state.temp=t,this.$router.push({path:"/console/sureOrderRenew",query:{no:t.order_no}})}}},u={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("card-wrap",{attrs:{title:"续费管理"}},[a("div",{staticClass:"container-wrapper"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.queryList,size:"small"}},[a("el-form-item",{attrs:{label:"产品名称"}},[a("el-input",{attrs:{placeholder:"产品名称",clearable:""},model:{value:t.queryList.name,callback:function(e){t.$set(t.queryList,"name",e)},expression:"queryList.name"}})],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v("搜索")])],1)],1),t._v(" "),a("div",{staticClass:"table"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,height:"100%",stripe:""}},[a("el-table-column",{attrs:{prop:"name",label:"产品名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"package",label:"产品配置"}}),t._v(" "),a("el-table-column",{attrs:{prop:"expiration_at",label:"到期时间"}}),t._v(" "),a("el-table-column",{attrs:{label:"倒计时(天)"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(Number(e.row.days)?e.row.days+"天后到期":e.row.days))]),t._v(" "),e.row.days<10?a("span",[t._v("即将到期，请及时续费")]):t._e()]}}])})],1)],1),t._v(" "),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticStyle:{"text-align":"center"},attrs:{total:t.total,page:t.queryList.page,limit:t.queryList.perPage},on:{"update:page":function(e){return t.$set(t.queryList,"page",e)},"update:limit":function(e){return t.$set(t.queryList,"perPage",e)},pagination:t.getList}})],1)]),t._v(" "),a("detail-buy-config",{attrs:{dialogVisible:t.detailBuyConfigVisible,formData:t.itemData},on:{closepop:function(e){t.detailBuyConfigVisible=!1},pay:t.clickPay}}),t._v(" "),a("detail-pay",{attrs:{dialogVisible:t.detailPayVisible,no:t.order_no},on:{closepop:function(e){t.detailPayVisible=!1}}})],1)},staticRenderFns:[]};var f=a("VU/8")(m,u,!1,function(t){a("2iJn")},"data-v-3a49fc7f",null);e.default=f.exports},tA7u:function(t,e){}});