import{k as Y,A as i,l as G,f as g,w as o,aP as H,o as u,b as n,a as s,d as K,e as X,t as P,av as Z,aw as $,W as ee,c as _,s as x,Y as le,F as y,E as te,m as ae,j as B,h as oe,a4 as se,Q as U,_ as re}from"./index-BBeD0eDz.js";/* empty css                *//* empty css               *//* empty css                  */import{f as ne,d as ue,b as de,e as ie}from"./index-B2D9HeJa.js";const me={class:"drawer-content"},pe={class:"form-section"},ce={class:"text-right text-gray-400 text-xs mt-1"},ge={class:"text-right text-gray-400 text-xs mt-1"},fe={class:"text-right text-gray-400 text-xs mt-1"},ve={class:"form-section mt-4"},_e={class:"drawer-footer py-[20px] px-[39px]"},xe=Y({__name:"ProductDrawer",props:{modelValue:{type:Boolean},info:{}},emits:["update:modelValue","save"],setup(F,{expose:S,emit:I}){const z=F,V=I,b=i(),O=i([]),h=i([]),w=i([{id:0,name:"全国"}]),D=i([]);(async()=>{try{const t=await ne(),{agemap:e,citymap:r,gendermap:d,industrymap:c}=t;h.value=e,w.value=[...w.value,...r],O.value=d,D.value=c}catch(t){console.error("获取客户画像选项失败",t)}})();const T=i({name:[{required:!0,message:"请输入产品名称",trigger:"blur"},{max:50,message:"产品名称不能超过50个字符",trigger:"blur"}],content:[{required:!0,message:"请输入产品介绍",trigger:"blur"},{max:300,message:"产品介绍不能超过300个字符",trigger:"blur"}],selling_point:[{required:!0,message:"请输入产品卖点",trigger:"blur"},{max:300,message:"产品卖点不能超过300个字符",trigger:"blur"}]}),p=G({get:()=>z.modelValue,set:t=>V("update:modelValue",t)}),l=i({name:"",content:"",selling_point:"",region:[],age:[],gender:0,keywords:[]}),q=t=>{if(t){let{gender:e}=t;l.value=t,k(t,"region"),k(t,"keywords"),k(t,"age"),l.value.gender=e?Number(e):0,console.log(l.value,"产品信息表单数据")}},k=(t,e)=>{if(!t)return"";if(t[e]&&Array.isArray(t[e]))return l.value[e]=t[e].map(r=>Number(r))},R=()=>{p.value=!1},j=()=>{p.value=!1},E=i(!1),J=()=>{b.value&&b.value.validate(t=>{if(t){let e=de;l.value.id&&(e=ie),E.value=!0;let r=JSON.parse(JSON.stringify(l.value));r.region=C(r.region),r.age=C(r.age),r.keywords=C(r.keywords),e(r).then(()=>{V("save",l.value),p.value=!1,U.success("保存成功")}).finally(()=>{E.value=!1})}else U.error("请填写必填项")})},C=t=>t?Array.isArray(t)?t.join(","):t:"",A=i(!1),L=()=>{l.value.id&&se.confirm("是否确认删除该产品？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{A.value=!0,ue({id:l.value.id}).then(()=>{V("save",l.value),p.value=!1,U.success("删除成功")}).finally(()=>{A.value=!1})})};return S({setFormData:q}),(t,e)=>{const r=X,d=K,c=le,f=ee,v=$,M=Z,Q=te,N=oe,W=H;return u(),g(W,{modelValue:p.value,"onUpdate:modelValue":e[7]||(e[7]=a=>p.value=a),size:"50%",direction:"rtl","before-close":R,class:"product-drawer"},{header:o(()=>e[8]||(e[8]=[n("div",{class:"flex items-center"},[n("div",{class:"font-bold text-[#111111] text-[18px] mr-[12px]"},"主营产品介绍"),n("div",{class:"tips text-[#999999] text-[14px]"},"请务必确保信息真实准确，以便于AI进行智能分析")],-1)])),default:o(()=>[n("div",me,[s(Q,{model:l.value,rules:T.value,ref_key:"formRef",ref:b,"label-position":"top",class:"product-form","status-icon":""},{default:o(()=>[n("div",pe,[e[9]||(e[9]=n("div",{class:"section-title"},"产品信息",-1)),s(d,{label:"产品名称：",prop:"name"},{default:o(()=>[s(r,{modelValue:l.value.name,"onUpdate:modelValue":e[0]||(e[0]=a=>l.value.name=a),placeholder:"请输入产品名称"},null,8,["modelValue"]),n("div",ce,P(l.value.name.length)+"/50",1)]),_:1}),s(d,{label:"产品介绍：",prop:"content"},{default:o(()=>[s(r,{modelValue:l.value.content,"onUpdate:modelValue":e[1]||(e[1]=a=>l.value.content=a),type:"textarea",rows:4,placeholder:"请简要介绍产品及服务内容"},null,8,["modelValue"]),n("div",ge,P(l.value.content.length)+"/300",1)]),_:1}),s(d,{label:"产品卖点：",prop:"selling_point"},{default:o(()=>[s(r,{modelValue:l.value.selling_point,"onUpdate:modelValue":e[2]||(e[2]=a=>l.value.selling_point=a),type:"textarea",rows:4,placeholder:"请介绍产品卖点，例如：简约设计，滋润质地，口感香甜..."},null,8,["modelValue"]),n("div",fe,P(l.value.selling_point.length)+"/300",1)]),_:1})]),n("div",ve,[e[10]||(e[10]=n("div",{class:"section-title"},"客户画像",-1)),s(M,{gutter:10},{default:o(()=>[s(v,{md:12,sm:24},{default:o(()=>[s(d,{prop:"region"},{default:o(()=>[s(f,{modelValue:l.value.region,"onUpdate:modelValue":e[3]||(e[3]=a=>l.value.region=a),multiple:"",filterable:"",placeholder:"客户地域",class:"w-full"},{default:o(()=>[(u(!0),_(y,null,x(w.value,a=>(u(),g(c,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),s(v,{md:12,sm:24},{default:o(()=>[s(d,{prop:"age"},{default:o(()=>[s(f,{modelValue:l.value.age,"onUpdate:modelValue":e[4]||(e[4]=a=>l.value.age=a),multiple:"",placeholder:"客户年龄",class:"w-full"},{default:o(()=>[(u(!0),_(y,null,x(h.value,(a,m)=>(u(),g(c,{key:m,label:a,value:Number(m)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),s(v,{md:12,sm:24},{default:o(()=>[s(d,{prop:"gender"},{default:o(()=>[s(f,{modelValue:l.value.gender,"onUpdate:modelValue":e[5]||(e[5]=a=>l.value.gender=a),placeholder:"客户性别",class:"w-full"},{default:o(()=>[(u(!0),_(y,null,x(O.value,(a,m)=>(u(),g(c,{key:m,label:a,value:Number(m)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),s(v,{md:12,sm:24},{default:o(()=>[s(d,{prop:"keywords"},{default:o(()=>[s(f,{modelValue:l.value.keywords,"onUpdate:modelValue":e[6]||(e[6]=a=>l.value.keywords=a),multiple:"",placeholder:"客户行业/职业",class:"w-full"},{default:o(()=>[(u(!0),_(y,null,x(D.value,(a,m)=>(u(),g(c,{key:m,label:a,value:Number(m)},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})])]),_:1},8,["model","rules"])]),n("div",_e,[s(N,{onClick:j,size:"large",class:"w-[100px]"},{default:o(()=>e[11]||(e[11]=[B("取消")])),_:1}),s(N,{loading:E.value,type:"primary",onClick:J,size:"large",class:"w-[100px]"},{default:o(()=>e[12]||(e[12]=[B("保存")])),_:1},8,["loading"]),l.value.id?(u(),g(N,{key:0,loading:A.value,type:"danger",onClick:L,size:"large",class:"w-[100px]"},{default:o(()=>e[13]||(e[13]=[B("删除")])),_:1},8,["loading"])):ae("",!0)])]),_:1},8,["modelValue"])}}}),Ce=re(xe,[["__scopeId","data-v-7752a71b"]]);export{Ce as P};
