import{k as d,A as l,B as u,q as p,J as _,c as x,b as s,t as n,u as f,Q as v,y as m,o as g}from"./index-BBeD0eDz.js";/* empty css                */import{e as h}from"./index-sm6CVty6.js";import"./request-Ciyrqj7N.js";const b={class:"h-full flex flex-col"},y={class:"text-[22px] text-[#111111] font-bold mb-[6px] text-center"},k={class:"text-[16px] text-[#555555] mb-[16px] text-center"},B={class:"flex-1 overflow-auto p-6 border-t"},D=["innerHTML"],E=d({__name:"detail",setup(H){const a=f(),r=m(),o=l(!1),t=l({}),i=async()=>{if(!a.params.id){v.error("参数错误"),r.back();return}o.value=!0;try{const e=await h({id:a.params.id});console.log(e,"帮助中心详情"),t.value=e}catch(e){console.log(e)}finally{o.value=!1}};return u(()=>{i()}),(e,M)=>{const c=_;return p((g(),x("div",b,[s("div",y,n(t.value.title),1),s("div",k,n(t.value.publish_at),1),s("div",B,[s("div",{class:"text-[16px] text-[#333333] leading-7",innerHTML:t.value.content},null,8,D)])])),[[c,o.value]])}}});export{E as default};
