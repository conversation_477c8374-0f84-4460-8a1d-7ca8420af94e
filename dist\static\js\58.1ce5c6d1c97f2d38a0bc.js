webpackJsonp([58],{"3pUE":function(e,t){},JaoR:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a("SgJ7"),n={name:"OperationalData",components:{PageWrap:a("Aw0F").a},data:function(){return{pageNav:[{name:"operational",title:"运营数据",path:"/console/display/E_commerce/operational",pathType:"page"}],activeTab:"today",tableData:[]}},created:function(){this.fetchData()},methods:{handleTabClick:function(e){this.activeTab=e.name,this.fetchData()},fetchData:function(){var e=this,t=this.activeTab;Object(r.l)({datetype:t}).then(function(t){200===t.code?e.tableData=t.data:e.$message.error("获取数据失败")}).catch(function(t){console.error("获取数据失败:",t),e.$message.error("获取数据失败")})},enterStore:function(e){this.$message.info("该功能正在开发中，敬请期待！")},getSummaries:function(e){var t=e.columns,a=e.data,r=[];return t.forEach(function(e,n){if(0!==n)if(n!==t.length-1){var l=a.map(function(t){return Number(t[e.property])});if(l.every(function(e){return isNaN(e)}))r[n]="--";else{var o=l.reduce(function(e,t){var a=Number(t);return isNaN(a)?e:e+t},0),c=a.reduce(function(e,t){return e+t.sale},0),i=a.reduce(function(e,t){return e+t.order},0),s=a.reduce(function(e,t){return e+t.visitor},0);"unit_price"==e.property?(r[n]=0==c&&0==i?0:c/i,r[n]=r[n].toFixed(2)):"conversion_rate"==e.property?(r[n]=0==i&&0==s?0:i/s*100,r[n]=r[n].toFixed(2)+"%"):r[n]=o.toFixed(2)}}else r[n]="";else r[n]="总计/平均"}),r}}},l={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("page-wrap",{attrs:{nav:e.pageNav}},[a("div",{staticClass:"operational-data"},[a("h2",{staticClass:"title"},[e._v("我的店铺运营数据")]),e._v(" "),a("el-tabs",{staticClass:"custom-tabs",on:{"tab-click":e.handleTabClick},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"今日",name:"today"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"昨日",name:"yesterday"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"7日",name:"week"}})],1),e._v(" "),a("el-table",{staticStyle:{width:"100%",flex:"1"},attrs:{data:e.tableData,"header-cell-style":{background:"#F5F7FA",color:"#606266"},"show-summary":"","row-key":"id","summary-method":e.getSummaries,border:""}},[a("el-table-column",{attrs:{prop:"storename",label:"数据指标"}}),e._v(" "),a("el-table-column",{attrs:{prop:"sale",label:"销售额(元)",width:"120",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"order",label:"订单数",width:"100",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"unit_price",label:"客单价(元)",width:"120",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"orderUser",label:"下单买家数",width:"120",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"conversion_rate",label:"支付转化率",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(t.row.conversion_rate)+"%\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"store_collection",label:"店铺收藏量",width:"120",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"sale_products",label:"上架商品数",width:"120",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.enterStore(t.row)}}},[e._v("进入店铺")])]}}])})],1)],1)])},staticRenderFns:[]};var o=a("VU/8")(n,l,!1,function(e){a("3pUE")},"data-v-e02d1cd0",null);t.default=o.exports}});