<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <title>登录 - 资海云AIGC</title>
  <!-- <script src="./js/eruda.js"></script>
  <script>eruda.init();</script> -->

  <!-- 引入脚本 -->
  <script src="./js/jquery-3.7.1.min.js"></script>
  <script src="./js/<EMAIL>"></script>
  <script src="./js/w_rem.js"></script>
  <script src="./js/common.js"></script>
  <script src="./js/douyin_open.umd.js"></script>
  <script src="./js/douyin_auth.js?v=1.0.9"></script>
  
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html {
      /* 基础字体大小，将通过JS动态计算 */
      font-size: 50px; /* 750px设计图下的基准值 */
      background-color: #FFFFFF; /* 白色背景 */;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      position: relative;
    }
    /* 背景颜色 */
    body::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 80%;
      background: linear-gradient(180deg, #cfe1ff 0%, #FFFFFF 100%);
      z-index: -1;
    }

    /* 主要内容区域 */
    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 1.2rem 0.64rem 0.8rem;
      position: relative;
    }

    /* 欢迎文字 */
    .welcome-text {
      font-size: 0.36rem;
      font-weight: bold;
      color: #222222;
      margin-bottom: 1.1rem;
      text-align: center;
      line-height: 1.4;
    }

    /* 装饰图形 - 重新添加蓝色渐变形状 */
    .decoration-container {
      position: relative;
      width: 5.46rem;
      margin-bottom: 0.98rem;
    }
    .decoration-container img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    /* 登录按钮 */
    .login-btn {
      width: 100%;
      max-width: 6.4rem;
      height: 0.9rem;
      background: rgb(24, 109, 245);
      border: none;
      border-radius: 0.5rem;
      color: white;
      font-size: 0.3rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: auto;
    }

    .login-btn:hover {
      transform: translateY(-0.04rem);
      box-shadow: 0 0.12rem 0.32rem rgba(24, 109, 245, 0.4);
    }

    .login-btn:active {
      transform: translateY(0);
    }

    /* 版权信息 */
    .copyright {
      text-align: center;
      color: #555555;
      font-size: 0.24rem;
      padding: 0.4rem;
      margin-top: auto;
    }

    /* 加载状态 */
    .loading {
      opacity: 0.7;
      pointer-events: none;
    }

    .loading::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0.4rem;
      height: 0.4rem;
      margin: -0.2rem 0 0 -0.2rem;
      border: 0.04rem solid transparent;
      border-top: 0.04rem solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }
    [v-cloak] {
      display: none;
    }
  </style>
</head>

<body>
  
  <!-- 主要内容 -->
  <main id="app" v-cloak class="main-content">
    <template v-if="isError">
      <!-- 装饰图形 -->
      <div class="decoration-container">
        <img src="./images/video.png" alt="">
      </div>
      <!-- 错误提示 -->
      <div class="welcome-text">
        {{ errorMessage }}
      </div>
    </template>
    <template v-else>
      <!-- 欢迎文字 -->
      <div class="welcome-text">
        欢迎登录 资海云AIGC
      </div>

      <!-- 装饰图形 -->
      <div class="decoration-container">
        <img src="./images/video.png" alt="">
      </div>

      <!-- 登录按钮 -->
      <button class="login-btn" onclick="douyinLogin()" id="loginBtn">
        一键授权登录
      </button>
    </template>
    
  </main>

  <!-- 版权信息 -->
  <footer class="copyright">
    Copyright © 2025 资海云
  </footer>

  <script>
    var loginApp = new Vue({
      el: '#app',
      data: {
        isError: false, // 错误状态
        errorMessage: '', // 错误消息
        objectId: '', // 视频ID
      },
      created() {
        this.getVideoInfo(); // 初始化时获取视频信息
      },
      methods: {
        // 获取视频信息
        getVideoInfo() {
          const that = this
          let id = getUrlParam('id');
          showLoading('加载中...');
          if (!id) {
            hideLoading();
            that.isError = true;
            that.errorMessage = '视频ID不存在';
            return;
          }
          this.objectId = id;
          // 调用接口获取视频信息
          $.ajax({
            url: '/newapi/share/sharevideo?id=' + id,
            type: 'GET',
            success: (res) => {
              console.log(res, '获取到的视频信息');
              hideLoading();
              if (res.code == 200) {
                that.isError = false;
                that.errorMessage = '';
                that.initUserInfo()
              } else {
                that.isError = true;
                that.errorMessage = res.message || res.msg || '获取视频信息失败';
              }
            },
            error: (err) => {
              hideLoading();
              that.isError = true;
              that.errorMessage = '获取视频信息失败';
              // console.log(err, '获取视频信息失败');
              // showErrorToastWithShake('获取视频信息失败', 'error', 3000);
            }
          })
        },
        // 初始化用户信息
        initUserInfo() {
          // window.localStorage.clear();
          const that = this
          let userInfo = window.localStorage.getItem('userInfo');
          let id = getUrlParam('id')
          if (userInfo && id) {
            window.location.href = './index.html?id=' + id;
          } else {
            showLoading('加载中...');
            initDouyinOpen().then((result) => {
              console.log(result, '抖音相关配置初始化成功！');
              that.isError = false;
            }).catch((err) => {
              console.log(err, '抖音相关配置初始化失败！');
              // showErrorToastWithShake('抖音相关配置初始化失败！', 'error', 3000);
              that.isError = true;
              that.errorMessage = '抖音相关配置初始化失败！';
            }).finally(() => {
              hideLoading();
            })
          }
        },
        // 点击登录
        async douyinLogin() {

          // 显示按钮加载状态
          document.getElementById('loginBtn').classList.add('loading');

          try {

            // 调用抖音授权登录
            const res = await login();

            // 登录成功
            console.log(res, '登录成功---');
            window.localStorage.setItem('userInfo', JSON.stringify(res));
            showLoading('登录成功，正在跳转');

            // 模拟跳转延迟
            setTimeout(() => {
              hideLoading();
              // 跳转到首页
              let id = getUrlParam('id')
              if (id) window.location.href = './index.html?id=' + id;
            }, 1500);

          } catch (error) {
            console.log(error, '登录失败---');
            hideLoading();

            // 根据错误类型显示不同的提示
            let errorMessage = '抖音授权失败，请重试';
            if (error.message) {
              if (error.message.includes('网络')) {
                errorMessage = '网络连接失败，请检查网络设置';
              } else if (error.message.includes('取消')) {
                errorMessage = '用户取消了授权';
              } else if (error.message.includes('超时')) {
                errorMessage = '授权超时，请重新尝试';
              }
            }

            showErrorToastWithShake(errorMessage, 'error', 4000);

          } finally {
            // 隐藏按钮加载状态
            document.getElementById('loginBtn').classList.remove('loading');
          }
        }
      }
    })

  </script>
</body>

</html>