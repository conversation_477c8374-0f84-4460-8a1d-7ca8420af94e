import{k as T,A as r,c as p,o,q as z,b,J,a as c,E as O,w as n,f as u,m as h,d as Q,W,F as x,s as V,Y,e as H,t as D,p as K,a9 as X,aF as Z,h as ee,j as R,Q as le,a2 as ae,_ as te}from"./index-BBeD0eDz.js";/* empty css                *//* empty css               *//* empty css                     *//* empty css                  */import{P as oe}from"./ProductSelector-BTeBhjvS.js";import{a as re,c as ne}from"./aiDocument-BqsEIq7y.js";const se={class:"ai-form h-full flex flex-col"},ue={class:"form-container flex-1 overflow-y-auto"},ce={class:"ai-form h-full flex flex-col"},de={class:"absolute bottom-[-25px] text-[13px] text-[--el-color-danger]"},ie={class:"action-wrap mt-[20px] flex items-center"},pe=T({__name:"AIDocumentForm",emits:["generate"],setup(fe,{emit:L}){const k=X("isMultiple",!1),l=r({id:"",type:"",number:3,content:{}}),S=r({id:[{required:!0,message:"请选择产品",trigger:"change"}],type:[{required:!0,message:"请选择发布场景",trigger:"change"}],number:[{required:!0,message:"请设置生成条数",trigger:"change"},{type:"number",message:"生成条数必须为数字值",trigger:"change"},{min:1,message:"生成条数不能小于1",trigger:"change"},{max:50,message:"生成条数不能大于50",trigger:"change"}]}),v=r(),w=r(),B=t=>{l.value.id=t},g=r(!1),f=r([]),i=r([]),C=r([]),_=r({});(async()=>{var t;g.value=!0;try{const a=await re({});console.log(a,"表单初始化"),f.value=a.type,C.value=a.form,l.value.type=(t=f.value[0])==null?void 0:t.id;try{i.value=a.form[l.value.type],E()}catch(d){console.log(d,"表单初始化失败"),i.value=[]}}catch(a){console.error("表单初始化失败",a)}finally{g.value=!1}})();const E=()=>{_.value={},i.value.forEach(t=>{_.value[t.field]=[{required:t.required,message:`请${t.type=="select"?"选择":"填写"}${t.title}`,trigger:"change"}]})},N=t=>{i.value=C.value[t],l.value.content={},E()},P=L,y=r(!1),m=r(!1),M=()=>{y.value=!1,l.value={id:"",type:"",number:3,content:{}}},$=async()=>{if(v.value&&!m.value){console.log("表单数据:",l.value);try{if(await v.value.validate(),await w.value.validate(),console.log("表单数据:",l.value),l.value.number>50){le.error("生成条数不能大于50");return}m.value=!0;let t=ae.service({fullscreen:!0,text:"生成中...",background:"rgba(255, 255, 255, 0.7)"});y.value=!0,k||(l.value.number=1);try{const a=await ne(l.value);console.log(a,"文案生成结果"),P("generate",a)}catch(a){console.error("文案生成失败",a)}finally{m.value=!1,t.close()}}catch(t){console.error("表单验证失败",t)}}};return(t,a)=>{const d=Q,F=Y,q=W,I=H,A=O,j=Z,U=ee,G=J;return o(),p("div",se,[z((o(),p("div",ue,[b("div",ce,[c(A,{ref_key:"formRef",ref:v,model:l.value,rules:S.value,"label-position":"top"},{default:n(()=>[c(d,{label:"选择产品：",prop:"id"},{default:n(()=>[c(oe,{class:"w-full",ref:"productSelectorRef",selectId:l.value.id,onChange:B,showLabel:!1},null,8,["selectId"])]),_:1}),f.value.length>0?(o(),u(d,{key:0,label:"文案发布场景：",prop:"type"},{default:n(()=>[c(q,{modelValue:l.value.type,"onUpdate:modelValue":a[0]||(a[0]=e=>l.value.type=e),class:"w-full",onChange:N},{default:n(()=>[(o(!0),p(x,null,V(f.value,e=>(o(),u(F,{key:e.id,label:e.title,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):h("",!0),c(A,{ref_key:"formConfigRef",ref:w,model:l.value.content,rules:_.value,"label-position":"top"},{default:n(()=>[(o(!0),p(x,null,V(i.value,e=>(o(),u(d,{key:e.id,label:`${e.title}：`,prop:e.field,required:e.required},{error:n(()=>[b("span",de,"请"+D(e.type=="select"?"选择":"填写")+D(e.title),1)]),default:n(()=>[e.type==="textarea"?(o(),u(I,{key:0,type:"textarea",modelValue:l.value.content[e.field],"onUpdate:modelValue":s=>l.value.content[e.field]=s,placeholder:e.placeholder,rows:4,resize:"none"},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="select"?(o(),u(q,{key:1,modelValue:l.value.content[e.field],"onUpdate:modelValue":s=>l.value.content[e.field]=s,class:"w-full"},{default:n(()=>[(o(!0),p(x,null,V(e.options,s=>(o(),u(F,{key:s,label:s,value:s},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])):(o(),u(I,{key:2,type:"text",modelValue:l.value.content[e.field],"onUpdate:modelValue":s=>l.value.content[e.field]=s,placeholder:e.placeholder},null,8,["modelValue","onUpdate:modelValue","placeholder"]))]),_:2},1032,["label","prop","required"]))),128))]),_:1},8,["model","rules"]),K(k)?(o(),u(d,{key:1,label:"生成条数：",props:"number",required:""},{default:n(()=>[c(j,{modelValue:l.value.number,"onUpdate:modelValue":a[1]||(a[1]=e=>l.value.number=e),precision:0,min:1,max:50,class:"w-[100px]"},null,8,["modelValue"])]),_:1})):h("",!0)]),_:1},8,["model","rules"])])])),[[G,g.value]]),b("div",ie,[c(U,{loading:m.value,type:"primary",class:"w-[130px] !h-[38px] setting-btn mb-4",onClick:$},{default:n(()=>a[2]||(a[2]=[R("立即生成")])),_:1},8,["loading"]),y.value?(o(),u(U,{key:0,class:"w-[130px] !h-[38px] setting-btn mb-4",onClick:M},{default:n(()=>a[3]||(a[3]=[R("重置")])),_:1})):h("",!0)])])}}}),Ve=te(pe,[["__scopeId","data-v-77c9faff"]]);export{Ve as A};
