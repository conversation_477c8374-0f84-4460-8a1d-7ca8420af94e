import{k as r,A as n,f as s,w as p,H as i,o as m,a as _,_ as c}from"./index-BBeD0eDz.js";import{L as d}from"./List-CZfQXqH2.js";/* empty css                *//* empty css               *//* empty css                 *//* empty css                *//* empty css               *//* empty css                */import"./request-Ciyrqj7N.js";import"./video-cover-DBcJ77EJ.js";/* empty css                  */const l=r({__name:"Index",setup(f){const e=n("material");return(u,t)=>{const o=i;return m(),s(o,{"show-header":!1,class:"h-full"},{default:p(()=>[_(d,{"active-index":e.value,"onUpdate:activeIndex":t[0]||(t[0]=a=>e.value=a)},null,8,["active-index"])]),_:1})}}}),A=c(l,[["__scopeId","data-v-b18b0ee1"]]);export{A as default};
