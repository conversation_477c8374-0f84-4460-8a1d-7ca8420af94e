import{I as c}from"./ImportFromMaterial-lo8a_mzn.js";import{k as d,A as u,l as p,c as m,m as h,a as f,n as v,o,_ as W}from"./index-BBeD0eDz.js";const y={class:"image-wrapper"},C=["src"],k=d({__name:"AddMaterial",props:{image:{type:String,default:""},imgWidth:{type:String,default:"92px"},imgHeight:{type:String,default:"92px"},plusWidth:{type:Number,default:32}},emits:["update:image","change"],setup(e,{emit:n}){const s=e,a=n,i=u(),r=p(()=>s.image?"更换封面":"添加封面"),g=t=>{t.length&&(console.log("image",t),a("update:image",t[0].url),a("change",t[0]))};return(t,l)=>(o(),m("div",y,[e.image?(o(),m("img",{key:0,src:e.image,alt:"",class:"image",onClick:l[0]||(l[0]=b=>i.value.openDialog())},null,8,C)):h("",!0),f(c,{ref_key:"importCoverRef",ref:i,class:v(["upload",{"upload-hover":e.image}]),imgWidth:e.imgWidth,imgHeight:e.imgHeight||e.imgWidth,plusWidth:e.plusWidth,label:r.value,type:"image",theme:"img",multiSelect:!1,onSubmit:g},null,8,["class","imgWidth","imgHeight","plusWidth","label"])]))}}),_=W(k,[["__scopeId","data-v-9d587324"]]);export{_};
